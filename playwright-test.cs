using Microsoft.Playwright;
using System;
using System.Threading.Tasks;

class Program 
{
    static async Task Main() 
    {
        try 
        {
            Console.WriteLine("🚀 Testing Playwright...");
            Environment.SetEnvironmentVariable("PLAYWRIGHT_BROWSERS_PATH", "/ms-playwright");
            
            using var playwright = await Playwright.CreateAsync();
            Console.WriteLine("✅ Playwright initialized!");
            
            await using var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions { Headless = true });
            Console.WriteLine("✅ Browser launched!");
            
            var page = await browser.NewPageAsync();
            await page.GotoAsync("https://example.com");
            var title = await page.TitleAsync();
            Console.WriteLine($"✅ Page title: {title}");
            Console.WriteLine("🎉 SUCCESS: Basic web navigation works!");
        } 
        catch (Exception ex) 
        {
            Console.WriteLine($"❌ Error: {ex.Message}");
            Console.WriteLine($"Stack: {ex.StackTrace}");
        }
    }
}
