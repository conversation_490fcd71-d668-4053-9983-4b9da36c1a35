````markdown
# Motor de Testing en Lenguaje Natural - Guía de Implementación

## 🎯 Objetivo

Implementar un motor que permita ejecutar tests smoke y scripts descritos en lenguaje natural, tanto para web como para mobile, con capacidades de interpretación inteligente y conversión automática a acciones ejecutables **integrado con la arquitectura C# existente**.

## 🏗️ Arquitectura de Implementación

### Ubicación en la Solución Actual

```
satelites/AutomationSolution/src/
├── Automation.Intelligence.CSharp/          # NUEVO
│   ├── NaturalLanguage/
│   │   ├── INaturalLanguageProcessor.cs
│   │   ├── NaturalLanguageProcessor.cs
│   │   ├── InstructionParser.cs
│   │   └── ActionConverter.cs
│   ├── Models/
│   │   ├── NLInstruction.cs
│   │   ├── TestIntent.cs
│   │   └── ConversionResult.cs
│   └── Extensions/
│       └── ServiceCollectionExtensions.cs
├── Automation.AI.Infrastructure.CSharp/     # EXTENDER
│   ├── Services/
│   │   └── EnhancedAIProcessor.cs          # NUEVO
│   └── Prompts/
│       └── NaturalLanguagePrompts.cs       # NUEVO
└── Automation.Web.CSharp/                  # EXTENDER
    └── Executors/
        └── IntelligentWebTaskExecutor.cs   # NUEVO
```

## 📋 Guía de Implementación Paso a Paso

### Paso 1: Crear el Proyecto Base (Día 1)

```bash
# Crear nuevo proyecto
cd satelites/AutomationSolution/src/
dotnet new classlib -n Automation.Intelligence.CSharp
cd Automation.Intelligence.CSharp

# Agregar dependencias
dotnet add package Microsoft.SemanticKernel
dotnet add package System.Text.Json
dotnet add package Microsoft.Extensions.DependencyInjection
dotnet add package Microsoft.Extensions.Logging

# Agregar referencia al proyecto AI existente
dotnet add reference ../Automation.AI.Infrastructure.CSharp/Automation.AI.Infrastructure.CSharp.csproj
dotnet add reference ../Automation.Contracts/Automation.Contracts.csproj
```

### Paso 2: Definir Modelos de Datos (Día 2)

```csharp
// Models/NLInstruction.cs
namespace Automation.Intelligence.NaturalLanguage.Models;

public class NLInstruction
{
    public TestIntent Intent { get; set; } = TestIntent.Unknown;
    public string? Target { get; set; }
    public ActionType Action { get; set; } = ActionType.None;
    public Dictionary<string, string> Parameters { get; set; } = new();
    public List<ValidationCondition> Conditions { get; set; } = new();
    public TestContext Context { get; set; } = new();
    public float Confidence { get; set; }
}

public enum TestIntent
{
    Unknown,
    Navigate,
    Interact,
    Validate,
    Wait,
    Screenshot,
    Custom
}

public enum ActionType
{
    None,
    Click,
    Type,
    Select,
    Scroll,
    Swipe,
    DoubleTap,
    LongPress
}

public class ValidationCondition
{
    public ConditionType Type { get; set; }
    public string Target { get; set; } = string.Empty;
    public string Expected { get; set; } = string.Empty;
    public TimeSpan? Timeout { get; set; }
}

public enum ConditionType
{
    ElementExists,
    ElementVisible,
    TextContains,
    UrlContains,
    PageLoaded
}

public class TestContext
{
    public string? CurrentUrl { get; set; }
    public Dictionary<string, object> Variables { get; set; } = new();
    public List<string> CompletedSteps { get; set; } = new();
}
```

### Paso 3: Implementar el Procesador de Lenguaje Natural (Días 3-4)

```csharp
// NaturalLanguage/INaturalLanguageProcessor.cs
namespace Automation.Intelligence.NaturalLanguage;

public interface INaturalLanguageProcessor
{
    Task<NLInstruction> ParseInstructionAsync(string instruction, CancellationToken cancellationToken = default);
    Task<List<NLInstruction>> ParseMultipleInstructionsAsync(string instructions, CancellationToken cancellationToken = default);
    Task<ConversionResult> ConvertToActionsAsync(NLInstruction instruction, CancellationToken cancellationToken = default);
}

// NaturalLanguage/NaturalLanguageProcessor.cs
public class NaturalLanguageProcessor : INaturalLanguageProcessor
{
    private readonly IAIProcessor _aiProcessor;
    private readonly ILogger<NaturalLanguageProcessor> _logger;
    private readonly NLCache _cache;

    public NaturalLanguageProcessor(
        IAIProcessor aiProcessor,
        ILogger<NaturalLanguageProcessor> logger,
        NLCache cache)
    {
        _aiProcessor = aiProcessor;
        _logger = logger;
        _cache = cache;
    }

    public async Task<NLInstruction> ParseInstructionAsync(string instruction, CancellationToken cancellationToken = default)
    {
        // Verificar caché primero
        var cacheKey = $"nl_instruction_{instruction.GetHashCode()}";
        if (await _cache.TryGetAsync<NLInstruction>(cacheKey) is { } cached)
        {
            _logger.LogDebug("Found cached instruction for: {Instruction}", instruction);
            return cached;
        }

        var prompt = CreateParsingPrompt(instruction);
        
        try
        {
            var response = await _aiProcessor.ProcessAsync(prompt, cancellationToken);
            var parsedInstruction = JsonSerializer.Deserialize<NLInstruction>(response);
            
            // Validar resultado
            if (parsedInstruction != null && parsedInstruction.Confidence > 0.7f)
            {
                await _cache.SetAsync(cacheKey, parsedInstruction, TimeSpan.FromHours(1));
                _logger.LogInformation("Successfully parsed instruction: {Instruction}", instruction);
                return parsedInstruction;
            }
            
            _logger.LogWarning("Low confidence parsing result for: {Instruction}", instruction);
            return new NLInstruction { Intent = TestIntent.Unknown, Confidence = 0.0f };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing instruction: {Instruction}", instruction);
            throw;
        }
    }

    private string CreateParsingPrompt(string instruction)
    {
        return $"""
        Analiza esta instrucción de testing y extrae la información estructurada.

        Instrucción: "{instruction}"

        Extrae y responde en formato JSON exacto:
        {{
            "intent": "Navigate|Interact|Validate|Wait|Screenshot|Custom",
            "target": "elemento objetivo o URL",
            "action": "Click|Type|Select|Scroll|Swipe|DoubleTap|LongPress|None",
            "parameters": {{"key": "value"}},
            "conditions": [
                {{
                    "type": "ElementExists|ElementVisible|TextContains|UrlContains|PageLoaded",
                    "target": "selector o texto",
                    "expected": "valor esperado",
                    "timeout": "00:00:30"
                }}
            ],
            "confidence": 0.95
        }}

        Ejemplos:
        - "Navega a https://ejemplo.com" → intent: Navigate, target: "https://ejemplo.com"
        - "Haz clic en el botón Login" → intent: Interact, action: Click, target: "botón Login"
        - "Escribe '<EMAIL>' en el campo email" → intent: Interact, action: Type, target: "campo email", parameters: {{"text": "<EMAIL>"}}
        - "Verifica que aparezca el texto 'Bienvenido'" → intent: Validate, conditions: [{{"type": "TextContains", "expected": "Bienvenido"}}]
        """;
    }
}
```

### Paso 4: Implementar el Convertidor de Acciones (Día 5)

```csharp
// NaturalLanguage/ActionConverter.cs
public class ActionConverter
{
    private readonly IAIProcessor _aiProcessor;
    private readonly ILogger<ActionConverter> _logger;

    public async Task<ConversionResult> ConvertToActionsAsync(NLInstruction instruction, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var actions = new List<object>();
        var warnings = new List<string>();

        try
        {
            switch (instruction.Intent)
            {
                case TestIntent.Navigate:
                    actions.Add(new NavigateAction(instruction.Target!));
                    break;

                case TestIntent.Interact:
                    var interactionAction = await ConvertInteractionAsync(instruction);
                    actions.Add(interactionAction);
                    break;

                case TestIntent.Validate:
                    var validationAction = await ConvertValidationAsync(instruction);
                    actions.Add(validationAction);
                    break;

                case TestIntent.Wait:
                    var waitAction = ConvertWaitAction(instruction);
                    actions.Add(waitAction);
                    break;

                case TestIntent.Screenshot:
                    var screenshotAction = ConvertScreenshotAction(instruction);
                    actions.Add(screenshotAction);
                    break;

                default:
                    warnings.Add($"Unknown intent: {instruction.Intent}");
                    break;
            }

            stopwatch.Stop();

            return new ConversionResult
            {
                Actions = actions,
                Metadata = new ConversionMetadata
                {
                    OriginalInstruction = instruction.Target ?? "Unknown",
                    ProcessingTime = stopwatch.Elapsed,
                    AIProvider = "Current",
                    ElementsIdentified = actions.Count
                },
                Confidence = instruction.Confidence,
                Warnings = warnings
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting instruction to actions");
            throw;
        }
    }

    private async Task<object> ConvertInteractionAsync(NLInstruction instruction)
    {
        // Usar AI para encontrar el mejor selector
        var selector = await FindBestSelectorAsync(instruction.Target!);
        
        return instruction.Action switch
        {
            ActionType.Click => new ClickAction(selector),
            ActionType.Type => new TypeAction(selector, instruction.Parameters.GetValueOrDefault("text", "")),
            ActionType.Select => new SelectAction(selector, instruction.Parameters.GetValueOrDefault("option", "")),
            ActionType.Scroll => new ScrollAction(instruction.Parameters.GetValueOrDefault("direction", "down")),
            _ => throw new NotSupportedException($"Action type {instruction.Action} not supported")
        };
    }

    private async Task<string> FindBestSelectorAsync(string elementDescription)
    {
        var prompt = $"""
        Necesito encontrar el mejor selector CSS para este elemento:
        Descripción: "{elementDescription}"
        
        Proporciona el selector más robusto considerando:
        1. Atributos estables (id, data-*, class específicas)
        2. Estructura jerárquica si es necesario
        3. Texto visible como alternativa
        4. Evitar selectores frágiles basados en posición
        
        Responde solo con el selector CSS, sin explicaciones.
        """;

        var response = await _aiProcessor.ProcessAsync(prompt, CancellationToken.None);
        return response.Trim().Trim('"');
    }
}
```

### Paso 5: Extender WebTaskExecutor (Día 6)

```csharp
// Automation.Web.CSharp/Executors/IntelligentWebTaskExecutor.cs
public class IntelligentWebTaskExecutor : WebTaskExecutor
{
    private readonly INaturalLanguageProcessor _nlProcessor;
    private readonly ILogger<IntelligentWebTaskExecutor> _logger;

    public IntelligentWebTaskExecutor(
        IBrowserPool browserPool,
        WebAutomationConfiguration configuration,
        INaturalLanguageProcessor nlProcessor,
        ILogger<IntelligentWebTaskExecutor> logger)
        : base(browserPool, configuration, logger)
    {
        _nlProcessor = nlProcessor;
        _logger = logger;
    }

    public async Task<TaskResult> ExecuteNaturalLanguageAsync(
        string naturalLanguageTest,
        CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting natural language test execution: {Test}", naturalLanguageTest);

            // 1. Parsear instrucciones múltiples
            var instructions = await _nlProcessor.ParseMultipleInstructionsAsync(naturalLanguageTest, cancellationToken);
            
            if (!instructions.Any())
            {
                return TaskResult.Failure("No se pudieron parsear las instrucciones");
            }

            // 2. Convertir cada instrucción a acciones
            var allActions = new List<object>();
            var conversionResults = new List<ConversionResult>();

            foreach (var instruction in instructions)
            {
                var conversionResult = await _nlProcessor.ConvertToActionsAsync(instruction, cancellationToken);
                allActions.AddRange(conversionResult.Actions);
                conversionResults.Add(conversionResult);
            }

            // 3. Ejecutar usando la infraestructura existente
            var executionResult = await ExecuteAsync(allActions, cancellationToken);

            // 4. Enriquecer el resultado con información de NL
            if (executionResult.IsSuccess)
            {
                var nlMetadata = new
                {
                    OriginalInstructions = naturalLanguageTest,
                    ParsedInstructions = instructions.Count,
                    GeneratedActions = allActions.Count,
                    AverageConfidence = instructions.Average(i => i.Confidence),
                    ProcessingTime = stopwatch.Elapsed
                };

                _logger.LogInformation("Natural language test completed successfully: {@Metadata}", nlMetadata);
                
                return TaskResult.Success(
                    $"Test ejecutado exitosamente. {instructions.Count} instrucciones procesadas.",
                    stopwatch.Elapsed,
                    nlMetadata);
            }
            else
            {
                return executionResult;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing natural language test");
            return TaskResult.Failure($"Error ejecutando test: {ex.Message}");
        }
        finally
        {
            stopwatch.Stop();
        }
    }
}
```

### Paso 6: Configurar Inyección de Dependencias (Día 7)

```csharp
// Extensions/ServiceCollectionExtensions.cs
namespace Automation.Intelligence.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddNaturalLanguageProcessing(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // Registrar servicios de NL
        services.AddScoped<INaturalLanguageProcessor, NaturalLanguageProcessor>();
        services.AddScoped<ActionConverter>();
        services.AddSingleton<NLCache>();

        // Extender el WebTaskExecutor existente
        services.AddScoped<IntelligentWebTaskExecutor>();

        // Configurar opciones
        services.Configure<NaturalLanguageOptions>(
            configuration.GetSection("NaturalLanguage"));

        return services;
    }
}

// Automation.Worker.CSharp/Program.cs (MODIFICAR)
public class Program
{
    public static void Main(string[] args)
    {
        var builder = Host.CreateDefaultBuilder(args);

        builder.ConfigureServices((context, services) =>
        {
            // Configuración existente...
            services.AddAutomationServices(context.Configuration);
            
            // AGREGAR: Servicios de lenguaje natural
            services.AddNaturalLanguageProcessing(context.Configuration);
        });

        var host = builder.Build();
        host.Run();
    }
}
```

### Paso 7: Crear Tests de Integración (Día 8)

```csharp
// tests/Automation.Integration.Tests/NaturalLanguageIntegrationTests.cs
[TestFixture]
public class NaturalLanguageIntegrationTests
{
    private IntelligentWebTaskExecutor _executor;
    private TestContainerHelper _containerHelper;

    [SetUp]
    public async Task Setup()
    {
        _containerHelper = new TestContainerHelper();
        await _containerHelper.StartAsync();
        
        var services = new ServiceCollection();
        services.AddAutomationServices(_containerHelper.Configuration);
        services.AddNaturalLanguageProcessing(_containerHelper.Configuration);
        
        var provider = services.BuildServiceProvider();
        _executor = provider.GetRequiredService<IntelligentWebTaskExecutor>();
    }

    [Test]
    public async Task ExecuteNaturalLanguageTest_SimpleNavigation_Success()
    {
        // Arrange
        var testInstructions = """
        Navega a https://example.com
        Haz clic en el enlace "More information"
        Verifica que aparezca el texto "Example Domain"
        """;

        // Act
        var result = await _executor.ExecuteNaturalLanguageAsync(testInstructions, CancellationToken.None);

        // Assert
        Assert.IsTrue(result.IsSuccess);
        Assert.That(result.Message, Contains.Substring("instrucciones procesadas"));
    }

    [Test]
    public async Task ExecuteNaturalLanguageTest_FormInteraction_Success()
    {
        // Arrange
        var testInstructions = """
        Navega a https://httpbin.org/forms/post
        Escribe "Test User" en el campo "Customer name"
        Escribe "<EMAIL>" en el campo "Email"
        Selecciona "Medium" en el dropdown "Size"
        Haz clic en el botón "Submit order"
        Verifica que aparezca el texto "<EMAIL>"
        """;

        // Act
        var result = await _executor.ExecuteNaturalLanguageAsync(testInstructions, CancellationToken.None);

        // Assert
        Assert.IsTrue(result.IsSuccess);
    }

    [Test]
    public async Task ExecuteNaturalLanguageTest_InvalidInstruction_HandlesGracefully()
    {
        // Arrange
        var testInstructions = "Esta instrucción no tiene sentido para testing";

        // Act
        var result = await _executor.ExecuteNaturalLanguageAsync(testInstructions, CancellationToken.None);

        // Assert
        Assert.IsFalse(result.IsSuccess);
        Assert.That(result.Message, Contains.Substring("parsear"));
    }

    [TearDown]
    public async Task TearDown()
    {
        await _containerHelper.DisposeAsync();
    }
}
```

## 🔧 Configuración en appsettings.json

```json
{
  "NaturalLanguage": {
    "CacheExpirationHours": 1,
    "MinimumConfidenceThreshold": 0.7,
    "MaxInstructionsPerTest": 20,
    "DefaultTimeout": "00:00:30",
    "SupportedLanguages": ["es", "en"],
    "EnableInstructionCache": true
  },
  "AI": {
    "DefaultProvider": "OpenAI",
    "OpenAI": {
      "ApiKey": "your-api-key",
      "Model": "gpt-4o-mini",
      "MaxTokens": 2000
    }
  }
}
```

## 📊 Métricas y Monitoreo

```csharp
// Agregar métricas específicas para NL
public class NaturalLanguageMetrics
{
    private readonly IMetricsRecorder _metricsRecorder;

    public async Task RecordParsingAttempt(string instruction, bool success, TimeSpan duration)
    {
        await _metricsRecorder.RecordCounterAsync("nl_parsing_attempts_total", 1, new[]
        {
            KeyValuePair.Create("success", success.ToString()),
            KeyValuePair.Create("instruction_length", instruction.Length.ToString())
        });

        await _metricsRecorder.RecordHistogramAsync("nl_parsing_duration_seconds", duration.TotalSeconds);
    }

    public async Task RecordConfidenceScore(float confidence)
    {
        await _metricsRecorder.RecordHistogramAsync("nl_confidence_score", confidence);
    }
}
```

## 🎯 Ejemplos de Uso

### Tests Smoke en Lenguaje Natural

```csharp
// Ejemplo 1: Test de login básico
var smokeTestLogin = """
Navega a https://ejemplo.com
Haz clic en el botón "Iniciar Sesión"
Escribe "<EMAIL>" en el campo de email
Escribe "password123" en el campo de contraseña
Haz clic en "Entrar"
Verifica que aparezca el texto "Bienvenido"
Toma una captura de pantalla llamada "login-exitoso"
""";

// Ejemplo 2: Test de búsqueda
var smokeTestSearch = """
Ve a la página principal
Busca "producto test" en la barra de búsqueda
Presiona Enter
Verifica que aparezcan resultados
Haz clic en el primer resultado
Confirma que la página del producto se carga correctamente
""";

// Ejemplo 3: Test de formulario
var formTest = """
Navega a https://httpbin.org/forms/post
Escribe "Juan Pérez" en el campo "Customer name"
Escribe "<EMAIL>" en el campo "Email"
Selecciona "Large" en el dropdown "Size"
Escribe "Comentario de prueba" en el área de texto
Haz clic en "Submit order"
Verifica que la respuesta contenga "<EMAIL>"
""";
```

### Procesamiento Automático

```csharp
// Uso en código
var executor = serviceProvider.GetRequiredService<IntelligentWebTaskExecutor>();
var result = await executor.ExecuteNaturalLanguageAsync(smokeTestLogin, CancellationToken.None);

if (result.IsSuccess)
{
    Console.WriteLine($"Test ejecutado exitosamente: {result.Message}");
}
else
{
    Console.WriteLine($"Error en test: {result.Message}");
}
```

## ✅ Checklist de Implementación

### Semana 1: Fundamentos
- [ ] ✅ Crear proyecto Automation.Intelligence.CSharp
- [ ] ✅ Definir modelos de datos (NLInstruction, TestIntent, etc.)
- [ ] ✅ Implementar NaturalLanguageProcessor básico
- [ ] ✅ Crear sistema de caché para instrucciones
- [ ] ✅ Configurar logging y métricas

### Semana 2: Conversión de Acciones
- [ ] ✅ Implementar ActionConverter
- [ ] ✅ Crear lógica de resolución de selectores
- [ ] ✅ Manejar diferentes tipos de acciones (click, type, select, etc.)
- [ ] ✅ Agregar validación de resultados
- [ ] ✅ Implementar manejo de errores

### Semana 3: Integración
- [ ] ✅ Crear IntelligentWebTaskExecutor
- [ ] ✅ Integrar con infraestructura existente
- [ ] ✅ Configurar inyección de dependencias
- [ ] ✅ Actualizar Program.cs y configuración
- [ ] ✅ Crear tests de integración

### Semana 4: Optimización
- [ ] ✅ Optimizar prompts de AI
- [ ] ✅ Implementar caché avanzado
- [ ] ✅ Agregar soporte para múltiples idiomas
- [ ] ✅ Crear documentación de uso
- [ ] ✅ Realizar pruebas de rendimiento

---

*Esta implementación permite que los usuarios escriban tests en lenguaje natural y los ejecuten automáticamente con alta precisión, integrándose perfectamente con la arquitectura C# existente.*

````
