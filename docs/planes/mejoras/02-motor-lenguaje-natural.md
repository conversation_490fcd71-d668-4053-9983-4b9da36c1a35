# Motor de Testing en Lenguaje Natural

## 🎯 Objetivo

Implementar un motor que permita ejecutar tests smoke y scripts descritos en lenguaje natural, tanto para web como para mobile, con capacidades de interpretación inteligente y conversión automática a acciones ejecutables.

## 🧠 Componentes del Motor

### 1. Natural Language Processor (NLP)

**Ubicación**: `satelites/AutomationSolution/src/Automation.AI/NaturalLanguageProcessor.fs`

```fsharp
module NaturalLanguageProcessor =
    
    type TestInstruction = {
        Intent: TestIntent
        Target: string option
        Action: ActionType
        Parameters: Map<string, string>
        Conditions: Condition list
        Context: TestContext
    }
    
    type TestIntent =
        | Navigate of url: string
        | Interact of element: string * interaction: InteractionType
        | Validate of element: string * expectation: string
        | Wait of duration: TimeSpan option * condition: string option
        | Screenshot of name: string option
        | Custom of description: string
    
    type InteractionType =
        | Click | Tap | DoubleClick
        | Type of text: string
        | Select of option: string
        | Scroll of direction: ScrollDirection
        | Swipe of direction: SwipeDirection
    
    type Condition =
        | ElementExists of selector: string
        | ElementVisible of selector: string
        | TextContains of text: string
        | UrlContains of pattern: string
        | PageLoaded
```

### 2. Instruction Parser

**Funcionalidad**: Convertir texto en inglés/español a instrucciones estructuradas

```fsharp
type InstructionParser() =
    
    member _.ParseAsync(instruction: string) : Async<TestInstruction> =
        async {
            // Usar AI para interpretar la instrucción
            let! aiResponse = this.AnalyzeWithAI(instruction)
            return this.ConvertToInstruction(aiResponse)
        }
    
    member private _.AnalyzeWithAI(instruction: string) =
        async {
            let prompt = $"""
            Analiza esta instrucción de testing y extrae:
            1. Intención (navegar, hacer clic, validar, etc.)
            2. Elemento objetivo (si aplica)
            3. Parámetros adicionales
            4. Condiciones de éxito
            
            Instrucción: "{instruction}"
            
            Responde en formato JSON estructurado.
            """
            
            // Usar el sistema de AI existente
            return! AIFramework.processCommand prompt
        }
```

### 3. Script Converter

**Funcionalidad**: Convertir instrucciones a acciones ejecutables

```fsharp
module ScriptConverter =
    
    type ConversionResult = {
        Actions: Action list
        Metadata: ConversionMetadata
        Confidence: float
        Warnings: string list
    }
    
    type ConversionMetadata = {
        OriginalInstruction: string
        ProcessingTime: TimeSpan
        AIProvider: string
        ElementsIdentified: int
    }
    
    let convertToActions (instruction: TestInstruction) : Async<ConversionResult> =
        async {
            match instruction.Intent with
            | Navigate url -> 
                return { 
                    Actions = [Navigate url]
                    Metadata = createMetadata instruction
                    Confidence = 0.95
                    Warnings = []
                }
            | Interact (element, interactionType) ->
                let! selector = ElementResolver.findBestSelector element
                let action = convertInteractionToAction selector interactionType
                return {
                    Actions = [action]
                    Metadata = createMetadata instruction
                    Confidence = selector.Confidence
                    Warnings = if selector.Confidence < 0.8 then ["Low confidence selector"] else []
                }
            // ... más casos
        }
```

## 🔍 Integración con Auto-Exploración

### Context-Aware Processing

```fsharp
type TestContext = {
    CurrentPage: PageInfo option
    AvailableElements: ElementInfo list
    UserSession: SessionInfo
    ExplorationData: ExplorationResult option
}

type ElementInfo = {
    Selector: string
    ElementType: ElementType
    Text: string option
    Attributes: Map<string, string>
    Position: Position
    Confidence: float
}
```

### Smart Element Resolution

```fsharp
module ElementResolver =
    
    let findBestSelector (description: string) (context: TestContext) : Async<SelectorResult> =
        async {
            // 1. Buscar en elementos conocidos de la exploración
            let! fromExploration = findInExplorationData description context.ExplorationData
            
            // 2. Si no se encuentra, usar AI para generar selector
            let! fromAI = generateSelectorWithAI description context.CurrentPage
            
            // 3. Combinar resultados y seleccionar el mejor
            return selectBestSelector [fromExploration; fromAI]
        }
    
    let generateSelectorWithAI (description: string) (pageInfo: PageInfo option) =
        async {
            let prompt = $"""
            Basándote en esta descripción del elemento: "{description}"
            Y la información de la página actual: {pageInfo}
            
            Genera los mejores selectores CSS/XPath posibles, ordenados por confiabilidad.
            Incluye selectores alternativos para self-healing.
            """
            
            let! response = AIFramework.processCommand prompt
            return parseSelectorsFromResponse response
        }
```

## 🎯 Ejemplos de Uso

### Tests Smoke en Lenguaje Natural

```fsharp
// Ejemplo 1: Test de login básico
let smokeTestLogin = """
Navega a https://ejemplo.com
Haz clic en el botón "Iniciar Sesión"
Escribe "<EMAIL>" en el campo de email
Escribe "password123" en el campo de contraseña
Haz clic en "Entrar"
Verifica que aparezca el texto "Bienvenido"
Toma una captura de pantalla llamada "login-exitoso"
"""

// Ejemplo 2: Test de búsqueda
let smokeTestSearch = """
Ve a la página principal
Busca "producto test" en la barra de búsqueda
Presiona Enter
Verifica que aparezcan resultados
Haz clic en el primer resultado
Confirma que la página del producto se carga correctamente
"""

// Ejemplo 3: Test mobile
let mobileTest = """
Abre la aplicación
Toca el menú hamburguesa
Selecciona "Configuración"
Desliza hacia abajo hasta encontrar "Notificaciones"
Activa las notificaciones push
Verifica que el toggle esté activado
Regresa a la pantalla principal
"""
```

### Procesamiento Automático

```fsharp
let executeNaturalLanguageTest (testDescription: string) (platform: Platform) =
    async {
        // 1. Parsear instrucciones
        let! instructions = NaturalLanguageProcessor.parseInstructions testDescription
        
        // 2. Convertir a acciones
        let! conversionResults = 
            instructions 
            |> List.map ScriptConverter.convertToActions
            |> Async.Parallel
        
        // 3. Ejecutar con self-healing
        let actions = conversionResults |> List.collect (fun r -> r.Actions)
        let! result = executeWithSelfHealing actions platform
        
        // 4. Generar reporte
        return createDetailedReport instructions conversionResults result
    }
```

## 🛠️ Implementación Técnica

### 1. Prompts Especializados

**Crear**: `scripts/prompts/natural-language-testing.json`

```json
{
  "instruction_parser": {
    "system": "Eres un experto en testing que convierte instrucciones en lenguaje natural a acciones estructuradas.",
    "user_template": "Analiza esta instrucción: '{instruction}' y extrae la intención, elemento objetivo, y parámetros."
  },
  "element_finder": {
    "system": "Especialista en encontrar elementos web/mobile basándote en descripciones naturales.",
    "user_template": "Encuentra el mejor selector para: '{description}' en esta página: {page_context}"
  },
  "validation_generator": {
    "system": "Generas validaciones apropiadas para verificar el éxito de acciones de testing.",
    "user_template": "Para la acción '{action}', genera validaciones que confirmen su éxito."
  }
}
```

### 2. Extensión del Sistema de Caché

```fsharp
// Extender CacheOptimizer para instrucciones NL
type NLInstructionCache = {
    Instruction: string
    ParsedResult: TestInstruction
    Confidence: float
    LastUsed: DateTime
    UsageCount: int
}

module NLCache =
    let cacheInstruction (instruction: string) (result: TestInstruction) =
        async {
            let cacheKey = $"nl_instruction_{instruction.GetHashCode()}"
            let cacheData = {
                Instruction = instruction
                ParsedResult = result
                Confidence = result.Confidence
                LastUsed = DateTime.UtcNow
                UsageCount = 1
            }
            do! ActionCache.set cacheKey cacheData
        }
```

### 3. Métricas y Monitoreo

```fsharp
type NLProcessingMetrics = {
    InstructionsParsed: int64
    AverageParsingTime: TimeSpan
    SuccessfulConversions: int64
    FailedConversions: int64
    CacheHitRate: float
    AverageConfidence: float
}

module NLMetrics =
    let recordParsingAttempt (instruction: string) (result: Result<TestInstruction, string>) (duration: TimeSpan) =
        // Integrar con PerformanceMonitor existente
        match result with
        | Ok instruction -> 
            PerformanceMonitor.recordSuccess "NL_Parsing" duration
        | Error error -> 
            PerformanceMonitor.recordFailure "NL_Parsing" duration error
```

## ✅ Checklist de Implementación

### Fase 1: Fundamentos
- [ ] Crear módulo `NaturalLanguageProcessor`
- [ ] Implementar `InstructionParser` básico
- [ ] Desarrollar `ScriptConverter` para acciones simples
- [ ] Integrar con sistema de AI existente
- [ ] Crear prompts especializados

### Fase 2: Resolución Inteligente
- [ ] Implementar `ElementResolver` con AI
- [ ] Crear sistema de selectores alternativos
- [ ] Integrar con datos de auto-exploración
- [ ] Desarrollar validaciones automáticas

### Fase 3: Optimización
- [ ] Implementar caché de instrucciones
- [ ] Agregar métricas de rendimiento
- [ ] Optimizar prompts basándose en resultados
- [ ] Implementar aprendizaje de patrones

### Fase 4: Extensiones
- [ ] Soporte para mobile (Appium)
- [ ] Instrucciones en múltiples idiomas
- [ ] Validaciones complejas
- [ ] Integración con reportes HTML

---

*Este motor permitirá que los usuarios escriban tests en lenguaje natural y los ejecuten automáticamente con alta precisión.*
