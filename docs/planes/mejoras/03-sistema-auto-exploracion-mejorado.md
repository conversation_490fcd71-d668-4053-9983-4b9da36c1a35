# Sistema de Auto-Exploración Inteligente - Guía de Implementación

## 🎯 Objetivo

Desarrollar un agente de IA que explore automáticamente sitios web y aplicaciones móviles para mapear funcionalidades, identificar elementos, y recopilar información que permita generar test cases, detectar bugs, y sugerir mejoras **integrado con la arquitectura C# existente**.

## 🏗️ Arquitectura de Implementación

### Ubicación en la Solución Actual

```
satelites/AutomationSolution/src/
├── Automation.Explorer.CSharp/               # NUEVO
│   ├── Web/
│   │   ├── IWebExplorer.cs
│   │   ├── WebExplorer.cs
│   │   ├── ElementDiscovery.cs
│   │   └── NavigationAnalyzer.cs
│   ├── Mobile/
│   │   ├── IMobileExplorer.cs
│   │   ├── MobileExplorer.cs
│   │   └── ScreenAnalyzer.cs
│   ├── Mapping/
│   │   ├── SiteMappingEngine.cs
│   │   ├── NavigationGraph.cs
│   │   └── UserJourneyAnalyzer.cs
│   ├── Models/
│   │   ├── ExplorationResult.cs
│   │   ├── DiscoveredElement.cs
│   │   ├── SiteMap.cs
│   │   └── NavigationNode.cs
│   └── Intelligence/
│       ├── ExplorationStrategy.cs
│       ├── IntelligentDecisionEngine.cs
│       └── PatternRecognizer.cs
├── Automation.AI.Infrastructure.CSharp/      # EXTENDER
│   ├── Services/
│   │   └── ExplorationAIService.cs          # NUEVO
│   └── Prompts/
│       └── ExplorationPrompts.cs            # NUEVO
└── Automation.Web.CSharp/                   # EXTENDER
    └── Services/
        └── EnhancedBrowserService.cs        # NUEVO
```

## 📋 Guía de Implementación Paso a Paso

### Paso 1: Crear el Proyecto Base (Días 1-2)

```bash
# Crear nuevo proyecto
cd satelites/AutomationSolution/src/
dotnet new classlib -n Automation.Explorer.CSharp
cd Automation.Explorer.CSharp

# Agregar dependencias
dotnet add package Microsoft.Playwright
dotnet add package Microsoft.SemanticKernel
dotnet add package System.Text.Json
dotnet add package Microsoft.Extensions.DependencyInjection
dotnet add package Microsoft.Extensions.Logging
dotnet add package Microsoft.Extensions.Caching.Memory
dotnet add package HtmlAgilityPack
dotnet add package Appium.WebDriver

# Agregar referencias
dotnet add reference ../Automation.AI.Infrastructure.CSharp/Automation.AI.Infrastructure.CSharp.csproj
dotnet add reference ../Automation.Contracts/Automation.Contracts.csproj
dotnet add reference ../Automation.Web.CSharp/Automation.Web.CSharp.csproj
```

### Paso 2: Definir Modelos de Datos (Días 3-4)

```csharp
// Models/ExplorationResult.cs
namespace Automation.Explorer.Models;

public class ExplorationResult
{
    public string StartUrl { get; set; } = string.Empty;
    public List<DiscoveredPage> Pages { get; set; } = new();
    public List<DiscoveredElement> Elements { get; set; } = new();
    public List<UserJourney> UserJourneys { get; set; } = new();
    public List<Functionality> Functionalities { get; set; } = new();
    public SiteMap SiteMap { get; set; } = new();
    public ExplorationMetrics Metrics { get; set; } = new();
    public List<ExplorationIssue> Issues { get; set; } = new();
    public DateTime ExplorationDate { get; set; } = DateTime.UtcNow;
    public TimeSpan Duration { get; set; }
}

// Models/DiscoveredElement.cs
public class DiscoveredElement
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Selector { get; set; } = string.Empty;
    public List<string> AlternativeSelectors { get; set; } = new();
    public ElementType Type { get; set; }
    public string? Text { get; set; }
    public Dictionary<string, string> Attributes { get; set; } = new();
    public Rectangle Position { get; set; }
    public bool IsVisible { get; set; }
    public bool IsInteractable { get; set; }
    public ElementPurpose? Purpose { get; set; }
    public float Confidence { get; set; }
    public string PageUrl { get; set; } = string.Empty;
    public List<string> TestingRecommendations { get; set; } = new();
}

public enum ElementType
{
    Button, Link, Input, Select, Checkbox, Radio,
    Image, Video, Text, Container, Navigation,
    Form, Table, List, Modal, Dropdown, Menu
}

public enum ElementPurpose
{
    Login, Logout, Register, Search, Submit,
    Navigation, Filter, Sort, Pagination,
    AddToCart, Checkout, Payment, Profile,
    Settings, Help, Contact, Social, Download
}

// Models/SiteMap.cs
public class SiteMap
{
    public string RootUrl { get; set; } = string.Empty;
    public Dictionary<string, PageInfo> Pages { get; set; } = new();
    public NavigationGraph NavigationGraph { get; set; } = new();
    public List<UserJourney> UserJourneys { get; set; } = new();
    public List<Functionality> Functionalities { get; set; } = new();
    public SiteArchitecture Architecture { get; set; } = new();
}

public class NavigationGraph
{
    public List<NavigationNode> Nodes { get; set; } = new();
    public List<NavigationEdge> Edges { get; set; } = new();
    public List<NavigationCluster> Clusters { get; set; } = new();
}

public class NavigationNode
{
    public string Url { get; set; } = string.Empty;
    public PageType PageType { get; set; }
    public ImportanceLevel Importance { get; set; }
    public AccessLevel AccessLevel { get; set; }
    public TimeSpan LoadTime { get; set; }
    public List<string> IncomingLinks { get; set; } = new();
    public List<string> OutgoingLinks { get; set; } = new();
}

public enum PageType
{
    HomePage, ProductPage, CategoryPage, SearchResults,
    UserProfile, Settings, Checkout, Payment,
    Login, Register, Contact, About,
    Admin, Dashboard, Reports, Error
}

public enum ImportanceLevel
{
    Critical, High, Medium, Low
}

public enum AccessLevel
{
    Public, RequiresLogin, RequiresAdmin, RequiresSpecialPermission
}
```

### Paso 3: Implementar el Web Explorer (Días 5-7)

```csharp
// Web/IWebExplorer.cs
namespace Automation.Explorer.Web;

public interface IWebExplorer
{
    Task<ExplorationResult> ExploreAsync(ExplorationConfig config, CancellationToken cancellationToken = default);
    Task<List<DiscoveredElement>> DiscoverElementsAsync(string url, CancellationToken cancellationToken = default);
    Task<NavigationGraph> BuildNavigationGraphAsync(string startUrl, int maxDepth, CancellationToken cancellationToken = default);
}

// Web/WebExplorer.cs
public class WebExplorer : IWebExplorer
{
    private readonly IBrowserPool _browserPool;
    private readonly IAIProcessor _aiProcessor;
    private readonly ILogger<WebExplorer> _logger;
    private readonly IMemoryCache _cache;
    private readonly ElementDiscovery _elementDiscovery;
    private readonly NavigationAnalyzer _navigationAnalyzer;

    public WebExplorer(
        IBrowserPool browserPool,
        IAIProcessor aiProcessor,
        ILogger<WebExplorer> logger,
        IMemoryCache cache,
        ElementDiscovery elementDiscovery,
        NavigationAnalyzer navigationAnalyzer)
    {
        _browserPool = browserPool;
        _aiProcessor = aiProcessor;
        _logger = logger;
        _cache = cache;
        _elementDiscovery = elementDiscovery;
        _navigationAnalyzer = navigationAnalyzer;
    }

    public async Task<ExplorationResult> ExploreAsync(ExplorationConfig config, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new ExplorationResult { StartUrl = config.StartUrl };
        
        try
        {
            _logger.LogInformation("Starting web exploration for: {StartUrl}", config.StartUrl);
            
            using var browser = await _browserPool.GetBrowserAsync(cancellationToken);
            var explorationState = new ExplorationState(config);
            
            // 1. Exploración inicial
            var initialPage = await ExplorePageAsync(browser, config.StartUrl, explorationState, cancellationToken);
            result.Pages.Add(initialPage);
            
            // 2. Descubrimiento de navegación
            var navigationGraph = await _navigationAnalyzer.BuildNavigationGraphAsync(
                browser, config.StartUrl, config.MaxDepth, cancellationToken);
            result.SiteMap.NavigationGraph = navigationGraph;
            
            // 3. Exploración inteligente basada en estrategia
            var additionalPages = await ExploreIntelligentlyAsync(
                browser, navigationGraph, config, explorationState, cancellationToken);
            result.Pages.AddRange(additionalPages);
            
            // 4. Análisis y clasificación con AI
            var analysis = await AnalyzeDiscoveredContentAsync(result.Pages, cancellationToken);
            result.Elements = analysis.Elements;
            result.UserJourneys = analysis.UserJourneys;
            result.Functionalities = analysis.Functionalities;
            result.Issues = analysis.Issues;
            
            // 5. Construir mapa del sitio
            result.SiteMap = await BuildSiteMapAsync(result, cancellationToken);
            
            stopwatch.Stop();
            result.Duration = stopwatch.Elapsed;
            result.Metrics = CalculateMetrics(result);
            
            _logger.LogInformation("Web exploration completed in {Duration}ms. Found {PageCount} pages, {ElementCount} elements",
                stopwatch.ElapsedMilliseconds, result.Pages.Count, result.Elements.Count);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during web exploration");
            throw;
        }
    }

    private async Task<DiscoveredPage> ExplorePageAsync(
        IBrowser browser, 
        string url, 
        ExplorationState state, 
        CancellationToken cancellationToken)
    {
        var page = await browser.NewPageAsync();
        
        try
        {
            // Configurar timeouts y handlers
            page.SetDefaultTimeout(30000);
            page.SetDefaultNavigationTimeout(30000);
            
            // Navegar a la página
            await page.GotoAsync(url, new PageGotoOptions { WaitUntil = WaitUntilState.NetworkIdle });
            
            // Esperar carga completa
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            
            // Descubrir elementos
            var elements = await _elementDiscovery.DiscoverElementsAsync(page, cancellationToken);
            
            // Analizar interacciones
            var interactions = await AnalyzeInteractionsAsync(page, elements, cancellationToken);
            
            // Detectar formularios
            var forms = await AnalyzeFormsAsync(page, cancellationToken);
            
            // Capturar métricas
            var metrics = await CapturePerformanceMetricsAsync(page, cancellationToken);
            
            // Tomar screenshot
            var screenshot = await page.ScreenshotAsync(new PageScreenshotOptions
            {
                FullPage = true,
                Type = ScreenshotType.Png
            });
            
            return new DiscoveredPage
            {
                Url = url,
                Title = await page.TitleAsync(),
                Elements = elements,
                Interactions = interactions,
                Forms = forms,
                Metrics = metrics,
                Screenshot = screenshot,
                Html = await page.ContentAsync(),
                LoadTime = metrics.LoadTime,
                Status = await GetPageStatusAsync(page)
            };
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    private async Task<List<DiscoveredPage>> ExploreIntelligentlyAsync(
        IBrowser browser,
        NavigationGraph navigationGraph,
        ExplorationConfig config,
        ExplorationState state,
        CancellationToken cancellationToken)
    {
        var pages = new List<DiscoveredPage>();
        var strategy = new IntelligentExplorationStrategy(_aiProcessor, _logger);
        
        var urlsToExplore = await strategy.PrioritizeUrlsAsync(
            navigationGraph.Nodes.Select(n => n.Url).ToList(),
            config,
            cancellationToken);
        
        var semaphore = new SemaphoreSlim(config.MaxConcurrency);
        var tasks = urlsToExplore.Take(config.MaxPages).Select(async url =>
        {
            await semaphore.WaitAsync(cancellationToken);
            try
            {
                if (!state.IsVisited(url))
                {
                    var page = await ExplorePageAsync(browser, url, state, cancellationToken);
                    state.MarkAsVisited(url);
                    return page;
                }
                return null;
            }
            finally
            {
                semaphore.Release();
            }
        });
        
        var results = await Task.WhenAll(tasks);
        pages.AddRange(results.Where(p => p != null)!);
        
        return pages;
    }
}
```

### Paso 4: Implementar Element Discovery con AI (Días 8-9)

```csharp
// Web/ElementDiscovery.cs
public class ElementDiscovery
{
    private readonly IAIProcessor _aiProcessor;
    private readonly ILogger<ElementDiscovery> _logger;
    private readonly IMemoryCache _cache;

    public ElementDiscovery(IAIProcessor aiProcessor, ILogger<ElementDiscovery> logger, IMemoryCache cache)
    {
        _aiProcessor = aiProcessor;
        _logger = logger;
        _cache = cache;
    }

    public async Task<List<DiscoveredElement>> DiscoverElementsAsync(IPage page, CancellationToken cancellationToken = default)
    {
        var elements = new List<DiscoveredElement>();
        
        try
        {
            // 1. Obtener elementos básicos usando selectores comunes
            var basicElements = await DiscoverBasicElementsAsync(page);
            elements.AddRange(basicElements);
            
            // 2. Usar AI para clasificar y enriquecer elementos
            var enrichedElements = await EnrichElementsWithAIAsync(page, basicElements, cancellationToken);
            
            // 3. Encontrar elementos complejos/dinámicos
            var complexElements = await DiscoverComplexElementsAsync(page, cancellationToken);
            elements.AddRange(complexElements);
            
            // 4. Validar y filtrar elementos
            var validatedElements = await ValidateElementsAsync(page, elements);
            
            return validatedElements;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error discovering elements on page: {Url}", page.Url);
            throw;
        }
    }

    private async Task<List<DiscoveredElement>> DiscoverBasicElementsAsync(IPage page)
    {
        var elements = new List<DiscoveredElement>();
        
        // Selectores comunes para elementos interactivos
        var commonSelectors = new Dictionary<string, ElementType>
        {
            ["button, input[type='button'], input[type='submit']"] = ElementType.Button,
            ["a[href]"] = ElementType.Link,
            ["input[type='text'], input[type='email'], input[type='password'], textarea"] = ElementType.Input,
            ["select"] = ElementType.Select,
            ["input[type='checkbox']"] = ElementType.Checkbox,
            ["input[type='radio']"] = ElementType.Radio,
            ["img"] = ElementType.Image,
            ["video"] = ElementType.Video,
            ["form"] = ElementType.Form,
            ["table"] = ElementType.Table,
            ["ul, ol"] = ElementType.List,
            ["nav"] = ElementType.Navigation,
            ["[role='button']"] = ElementType.Button,
            ["[role='link']"] = ElementType.Link,
            ["[role='menu']"] = ElementType.Menu
        };

        foreach (var (selector, elementType) in commonSelectors)
        {
            var pageElements = await page.QuerySelectorAllAsync(selector);
            
            foreach (var element in pageElements)
            {
                var discoveredElement = await CreateDiscoveredElementAsync(element, elementType, selector);
                if (discoveredElement != null)
                {
                    elements.Add(discoveredElement);
                }
            }
        }

        return elements;
    }

    private async Task<List<DiscoveredElement>> EnrichElementsWithAIAsync(
        IPage page, 
        List<DiscoveredElement> basicElements, 
        CancellationToken cancellationToken)
    {
        var enrichedElements = new List<DiscoveredElement>();
        
        // Procesar elementos en lotes para optimizar llamadas a AI
        var batches = basicElements.Chunk(10);
        
        foreach (var batch in batches)
        {
            var enriched = await EnrichElementBatchAsync(page, batch, cancellationToken);
            enrichedElements.AddRange(enriched);
        }
        
        return enrichedElements;
    }

    private async Task<List<DiscoveredElement>> EnrichElementBatchAsync(
        IPage page, 
        DiscoveredElement[] batch, 
        CancellationToken cancellationToken)
    {
        var prompt = CreateElementAnalysisPrompt(batch);
        
        try
        {
            var response = await _aiProcessor.ProcessAsync(prompt, cancellationToken);
            var aiAnalysis = JsonSerializer.Deserialize<ElementAnalysisResult>(response);
            
            // Enriquecer elementos con análisis de AI
            for (int i = 0; i < batch.Length && i < aiAnalysis.Elements.Count; i++)
            {
                var element = batch[i];
                var analysis = aiAnalysis.Elements[i];
                
                element.Purpose = Enum.TryParse<ElementPurpose>(analysis.Purpose, out var purpose) ? purpose : null;
                element.Confidence = analysis.Confidence;
                element.TestingRecommendations = analysis.TestingRecommendations;
                element.AlternativeSelectors = analysis.AlternativeSelectors;
            }
            
            return batch.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error enriching elements with AI, using basic analysis");
            return batch.ToList();
        }
    }

    private string CreateElementAnalysisPrompt(DiscoveredElement[] elements)
    {
        var elementsJson = JsonSerializer.Serialize(elements.Select(e => new
        {
            e.Selector,
            e.Type,
            e.Text,
            e.Attributes
        }));

        return $"""
        Analiza estos elementos HTML y proporciona información enriquecida:

        {elementsJson}

        Para cada elemento, determina:
        1. Propósito probable (Login, Search, Navigation, etc.)
        2. Confianza en la clasificación (0.0-1.0)
        3. Selectores alternativos más robustos
        4. Recomendaciones específicas para testing

        Responde en formato JSON:
        {{
            "elements": [
                {{
                    "purpose": "Login|Search|Navigation|Submit|etc",
                    "confidence": 0.85,
                    "alternativeSelectors": ["selector1", "selector2"],
                    "testingRecommendations": ["recommendation1", "recommendation2"]
                }}
            ]
        }}
        """;
    }

    private async Task<DiscoveredElement?> CreateDiscoveredElementAsync(
        IElementHandle element, 
        ElementType type, 
        string selector)
    {
        try
        {
            var isVisible = await element.IsVisibleAsync();
            var text = await element.TextContentAsync();
            var attributes = new Dictionary<string, string>();
            
            // Obtener atributos importantes
            var importantAttributes = new[] { "id", "class", "name", "type", "href", "src", "alt", "title", "placeholder", "data-testid" };
            
            foreach (var attr in importantAttributes)
            {
                var value = await element.GetAttributeAsync(attr);
                if (!string.IsNullOrEmpty(value))
                {
                    attributes[attr] = value;
                }
            }
            
            // Obtener posición
            var boundingBox = await element.BoundingBoxAsync();
            var position = boundingBox != null 
                ? new Rectangle((int)boundingBox.X, (int)boundingBox.Y, (int)boundingBox.Width, (int)boundingBox.Height)
                : new Rectangle();
            
            return new DiscoveredElement
            {
                Selector = selector,
                Type = type,
                Text = text,
                Attributes = attributes,
                Position = position,
                IsVisible = isVisible,
                IsInteractable = await element.IsEnabledAsync() && isVisible,
                Confidence = 0.8f // Valor base, se enriquece con AI
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error creating discovered element for selector: {Selector}", selector);
            return null;
        }
    }
}
```

### Paso 5: Implementar Estrategia de Exploración Inteligente (Días 10-11)

```csharp
// Intelligence/IntelligentExplorationStrategy.cs
public class IntelligentExplorationStrategy
{
    private readonly IAIProcessor _aiProcessor;
    private readonly ILogger<IntelligentExplorationStrategy> _logger;

    public IntelligentExplorationStrategy(IAIProcessor aiProcessor, ILogger<IntelligentExplorationStrategy> logger)
    {
        _aiProcessor = aiProcessor;
        _logger = logger;
    }

    public async Task<List<string>> PrioritizeUrlsAsync(
        List<string> candidateUrls, 
        ExplorationConfig config, 
        CancellationToken cancellationToken)
    {
        if (candidateUrls.Count <= config.MaxPages)
        {
            return candidateUrls;
        }

        var prompt = CreatePrioritizationPrompt(candidateUrls, config);
        
        try
        {
            var response = await _aiProcessor.ProcessAsync(prompt, cancellationToken);
            var prioritization = JsonSerializer.Deserialize<UrlPrioritization>(response);
            
            return prioritization.PrioritizedUrls
                .Take(config.MaxPages)
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error prioritizing URLs with AI, using default strategy");
            return candidateUrls.Take(config.MaxPages).ToList();
        }
    }

    private string CreatePrioritizationPrompt(List<string> urls, ExplorationConfig config)
    {
        var urlsJson = JsonSerializer.Serialize(urls);
        
        return $"""
        Necesito priorizar estas URLs para exploración de testing:

        URLs candidatas:
        {urlsJson}

        Criterios de priorización:
        1. Páginas críticas del negocio (login, checkout, registro)
        2. Funcionalidades principales del sitio
        3. Páginas con formularios complejos
        4. Flujos de usuario comunes
        5. Páginas con mucha interactividad

        Estrategia: {config.Strategy}
        Áreas de enfoque: {string.Join(", ", config.FocusAreas)}
        Máximo de páginas: {config.MaxPages}

        Responde en formato JSON:
        {{
            "prioritizedUrls": ["url1", "url2", "url3"],
            "reasoning": "Explicación de la priorización"
        }}
        """;
    }

    public async Task<ExplorationDecision> DecideNextActionAsync(
        ExplorationState state, 
        DiscoveredPage currentPage, 
        CancellationToken cancellationToken)
    {
        var context = CreateDecisionContext(state, currentPage);
        
        try
        {
            var response = await _aiProcessor.ProcessAsync(context, cancellationToken);
            return JsonSerializer.Deserialize<ExplorationDecision>(response);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error making exploration decision, using default");
            return new ExplorationDecision
            {
                Action = ExplorationAction.Continue,
                Reason = "Default decision due to AI error",
                Confidence = 0.5f
            };
        }
    }

    private string CreateDecisionContext(ExplorationState state, DiscoveredPage currentPage)
    {
        return $"""
        Estado actual de exploración:
        - Páginas visitadas: {state.VisitedPages.Count}
        - Tiempo transcurrido: {state.ElapsedTime}
        - Funcionalidades encontradas: {state.DiscoveredFunctionalities.Count}
        - Errores encontrados: {state.Errors.Count}

        Página actual:
        - URL: {currentPage.Url}
        - Título: {currentPage.Title}
        - Elementos encontrados: {currentPage.Elements.Count}
        - Tiempo de carga: {currentPage.LoadTime}

        Decide la siguiente acción:
        1. Continue - Seguir explorando
        2. Skip - Saltar esta página
        3. Stop - Terminar exploración
        4. Focus - Enfocarse en esta área

        Responde en formato JSON:
        {{
            "action": "Continue|Skip|Stop|Focus",
            "reason": "Explicación de la decisión",
            "confidence": 0.85,
            "suggestedFocus": "área específica si action=Focus"
        }}
        """;
    }
}

// Intelligence/PatternRecognizer.cs
public class PatternRecognizer
{
    private readonly IAIProcessor _aiProcessor;
    private readonly ILogger<PatternRecognizer> _logger;

    public async Task<List<Pattern>> RecognizePatternsAsync(
        ExplorationResult explorationResult, 
        CancellationToken cancellationToken)
    {
        var patterns = new List<Pattern>();
        
        // Reconocer patrones de navegación
        var navigationPatterns = await RecognizeNavigationPatternsAsync(explorationResult.SiteMap.NavigationGraph, cancellationToken);
        patterns.AddRange(navigationPatterns);
        
        // Reconocer patrones de UI
        var uiPatterns = await RecognizeUIPatterns(explorationResult.Elements, cancellationToken);
        patterns.AddRange(uiPatterns);
        
        // Reconocer patrones de funcionalidad
        var functionalityPatterns = await RecognizeFunctionalityPatterns(explorationResult.Functionalities, cancellationToken);
        patterns.AddRange(functionalityPatterns);
        
        return patterns;
    }

    private async Task<List<Pattern>> RecognizeNavigationPatternsAsync(
        NavigationGraph graph, 
        CancellationToken cancellationToken)
    {
        var prompt = $"""
        Analiza este grafo de navegación y encuentra patrones:
        
        {JsonSerializer.Serialize(graph)}
        
        Identifica:
        1. Patrones de navegación común
        2. Puntos de entrada principales
        3. Rutas críticas
        4. Páginas huérfanas o aisladas
        5. Problemas de navegación
        
        Responde en formato JSON con los patrones encontrados.
        """;
        
        var response = await _aiProcessor.ProcessAsync(prompt, cancellationToken);
        return JsonSerializer.Deserialize<List<Pattern>>(response) ?? new List<Pattern>();
    }
}
```

### Paso 6: Configurar Servicios y DI (Día 12)

```csharp
// Extensions/ServiceCollectionExtensions.cs
namespace Automation.Explorer.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddWebExploration(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // Servicios principales
        services.AddScoped<IWebExplorer, WebExplorer>();
        services.AddScoped<ElementDiscovery>();
        services.AddScoped<NavigationAnalyzer>();
        services.AddScoped<SiteMappingEngine>();
        
        // Servicios de inteligencia
        services.AddScoped<IntelligentExplorationStrategy>();
        services.AddScoped<PatternRecognizer>();
        services.AddScoped<UserJourneyAnalyzer>();
        
        // Caché para optimización
        services.AddMemoryCache();
        
        // Configuración
        services.Configure<ExplorationConfig>(configuration.GetSection("Exploration"));
        
        return services;
    }

    public static IServiceCollection AddMobileExploration(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddScoped<IMobileExplorer, MobileExplorer>();
        services.AddScoped<ScreenAnalyzer>();
        services.Configure<MobileExplorationConfig>(configuration.GetSection("MobileExploration"));
        
        return services;
    }
}

// Program.cs en Worker (MODIFICAR)
public class Program
{
    public static void Main(string[] args)
    {
        var builder = Host.CreateDefaultBuilder(args);

        builder.ConfigureServices((context, services) =>
        {
            // Configuración existente...
            services.AddAutomationServices(context.Configuration);
            services.AddNaturalLanguageProcessing(context.Configuration);
            
            // AGREGAR: Servicios de exploración
            services.AddWebExploration(context.Configuration);
            services.AddMobileExploration(context.Configuration);
        });

        var host = builder.Build();
        host.Run();
    }
}
```

### Paso 7: Crear Tests de Integración (Día 13)

```csharp
// tests/Automation.Integration.Tests/ExplorationIntegrationTests.cs
[TestFixture]
public class ExplorationIntegrationTests
{
    private IWebExplorer _webExplorer;
    private TestContainerHelper _containerHelper;

    [SetUp]
    public async Task Setup()
    {
        _containerHelper = new TestContainerHelper();
        await _containerHelper.StartAsync();
        
        var services = new ServiceCollection();
        services.AddAutomationServices(_containerHelper.Configuration);
        services.AddWebExploration(_containerHelper.Configuration);
        
        var provider = services.BuildServiceProvider();
        _webExplorer = provider.GetRequiredService<IWebExplorer>();
    }

    [Test]
    public async Task ExploreAsync_SimpleWebsite_ReturnsExplorationResult()
    {
        // Arrange
        var config = new ExplorationConfig
        {
            StartUrl = "https://example.com",
            MaxPages = 5,
            MaxDepth = 2,
            Strategy = ExplorationStrategy.BreadthFirst,
            FocusAreas = new[] { FocusArea.Navigation, FocusArea.Content }
        };

        // Act
        var result = await _webExplorer.ExploreAsync(config, CancellationToken.None);

        // Assert
        Assert.IsNotNull(result);
        Assert.That(result.Pages.Count, Is.GreaterThan(0));
        Assert.That(result.Elements.Count, Is.GreaterThan(0));
        Assert.That(result.SiteMap.NavigationGraph.Nodes.Count, Is.GreaterThan(0));
    }

    [Test]
    public async Task ExploreAsync_EcommerceWebsite_IdentifiesKeyFunctionalities()
    {
        // Arrange
        var config = new ExplorationConfig
        {
            StartUrl = "https://demo.opencart.com",
            MaxPages = 10,
            MaxDepth = 3,
            Strategy = ExplorationStrategy.Intelligent,
            FocusAreas = new[] { FocusArea.Commerce, FocusArea.Search, FocusArea.Navigation }
        };

        // Act
        var result = await _webExplorer.ExploreAsync(config, CancellationToken.None);

        // Assert
        Assert.IsNotNull(result);
        Assert.That(result.Functionalities.Any(f => f.Type == FunctionalityType.Search), Is.True);
        Assert.That(result.Functionalities.Any(f => f.Type == FunctionalityType.ProductCatalog), Is.True);
        Assert.That(result.Elements.Any(e => e.Purpose == ElementPurpose.AddToCart), Is.True);
    }

    [Test]
    public async Task DiscoverElementsAsync_FormPage_IdentifiesFormElements()
    {
        // Arrange
        var url = "https://httpbin.org/forms/post";

        // Act
        var elements = await _webExplorer.DiscoverElementsAsync(url, CancellationToken.None);

        // Assert
        Assert.IsNotNull(elements);
        Assert.That(elements.Any(e => e.Type == ElementType.Input), Is.True);
        Assert.That(elements.Any(e => e.Type == ElementType.Select), Is.True);
        Assert.That(elements.Any(e => e.Type == ElementType.Button), Is.True);
        Assert.That(elements.Any(e => e.Purpose == ElementPurpose.Submit), Is.True);
    }

    [TearDown]
    public async Task TearDown()
    {
        await _containerHelper.DisposeAsync();
    }
}
```

## 🔧 Configuración

### appsettings.json

```json
{
  "Exploration": {
    "MaxPages": 50,
    "MaxDepth": 3,
    "MaxConcurrency": 3,
    "TimeoutPerPage": "00:00:30",
    "Strategy": "Intelligent",
    "FocusAreas": ["Navigation", "Forms", "Commerce"],
    "ExcludePatterns": [
      "*/admin/*",
      "*/private/*",
      "*.pdf",
      "*.jpg",
      "*.png"
    ],
    "CacheExpirationMinutes": 60,
    "EnableScreenshots": true,
    "EnablePerformanceMetrics": true
  },
  "MobileExploration": {
    "MaxScreens": 30,
    "MaxDepth": 5,
    "GesturePatterns": ["Tap", "Swipe", "Scroll"],
    "ScreenshotInterval": "00:00:02",
    "EnableAccessibilityAnalysis": true
  }
}
```

## 📊 Métricas y Monitoreo

```csharp
// Métricas específicas para exploración
public class ExplorationMetrics
{
    public int TotalPagesExplored { get; set; }
    public int ElementsDiscovered { get; set; }
    public int FunctionalitiesFound { get; set; }
    public int IssuesDetected { get; set; }
    public TimeSpan TotalExplorationTime { get; set; }
    public TimeSpan AveragePageLoadTime { get; set; }
    public float CoveragePercentage { get; set; }
    public Dictionary<string, int> ElementTypeDistribution { get; set; } = new();
    public Dictionary<string, int> PageTypeDistribution { get; set; } = new();
    public List<string> SlowestPages { get; set; } = new();
    public List<string> ErrorPages { get; set; } = new();
}
```

## 🎯 Ejemplos de Uso

### Exploración Básica

```csharp
// Configuración básica
var config = new ExplorationConfig
{
    StartUrl = "https://example.com",
    MaxPages = 20,
    MaxDepth = 3,
    Strategy = ExplorationStrategy.BreadthFirst,
    FocusAreas = new[] { FocusArea.Navigation, FocusArea.Forms }
};

// Ejecutar exploración
var result = await webExplorer.ExploreAsync(config);

// Revisar resultados
Console.WriteLine($"Páginas exploradas: {result.Pages.Count}");
Console.WriteLine($"Elementos encontrados: {result.Elements.Count}");
Console.WriteLine($"Funcionalidades identificadas: {result.Functionalities.Count}");
```

### Exploración Enfocada en E-commerce

```csharp
var ecommerceConfig = new ExplorationConfig
{
    StartUrl = "https://shop.example.com",
    Strategy = ExplorationStrategy.Intelligent,
    FocusAreas = new[] { FocusArea.Commerce, FocusArea.Search, FocusArea.Authentication },
    MaxPages = 30,
    Credentials = new Credentials { Username = "<EMAIL>", Password = "password" }
};

var result = await webExplorer.ExploreAsync(ecommerceConfig);

// Analizar resultados específicos de e-commerce
var addToCartButtons = result.Elements.Where(e => e.Purpose == ElementPurpose.AddToCart);
var checkoutFlow = result.UserJourneys.FirstOrDefault(j => j.Name.Contains("Checkout"));
```

## ✅ Checklist de Implementación

### Semana 1: Fundamentos
- [ ] ✅ Crear proyecto Automation.Explorer.CSharp
- [ ] ✅ Definir modelos de datos principales
- [ ] ✅ Implementar WebExplorer básico
- [ ] ✅ Crear ElementDiscovery con selectores comunes
- [ ] ✅ Configurar logging y métricas básicas

### Semana 2: Inteligencia AI
- [ ] ✅ Implementar enriquecimiento de elementos con AI
- [ ] ✅ Crear estrategia de exploración inteligente
- [ ] ✅ Desarrollar sistema de priorización de URLs
- [ ] ✅ Implementar reconocimiento de patrones
- [ ] ✅ Agregar análisis de contexto

### Semana 3: Navegación y Mapeo
- [ ] ✅ Implementar NavigationAnalyzer
- [ ] ✅ Crear SiteMappingEngine
- [ ] ✅ Desarrollar construcción de grafo de navegación
- [ ] ✅ Implementar detección de User Journeys
- [ ] ✅ Crear análisis de arquitectura del sitio

### Semana 4: Integración y Optimización
- [ ] ✅ Integrar con infraestructura existente
- [ ] ✅ Configurar inyección de dependencias
- [ ] ✅ Crear tests de integración
- [ ] ✅ Implementar caché y optimizaciones
- [ ] ✅ Agregar configuración avanzada

### Semana 5: Mobile y Avanzado
- [ ] ✅ Implementar MobileExplorer
- [ ] ✅ Crear análisis de pantallas móviles
- [ ] ✅ Desarrollar detección de gestos
- [ ] ✅ Implementar exploración paralela
- [ ] ✅ Crear reportes detallados

---

*Este sistema permitirá explorar automáticamente cualquier sitio web o aplicación móvil, generando mapas detallados de funcionalidades que servirán como base para generación de tests, detección de bugs y sugerencias de mejora.*
