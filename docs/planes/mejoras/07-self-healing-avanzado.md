# Self-Healing Avanzado para Web y Mobile

## 🎯 Objetivo

Expandir y mejorar las capacidades de auto-reparación para manejar cambios en UI tanto web como mobile, creando un sistema inteligente que adapte automáticamente los tests cuando detecte modificaciones en la interfaz.

## 🛡️ Arquitectura del Self-Healing System

### Componentes Principales

```
Advanced Self-Healing System
├── 🔍 Change Detection Engine
│   ├── Element Mutation Detector
│   ├── Layout Change Analyzer
│   └── Functionality Shift Detector
├── 🧠 Adaptation Intelligence
│   ├── Selector Evolution Engine
│   ├── Flow Adaptation Manager
│   └── Context Understanding AI
├── 📱 Platform-Specific Healers
│   ├── Web Self-Healer
│   ├── Mobile Self-Healer
│   └── Cross-Platform Coordinator
└── 📊 Learning & Optimization
    ├── Pattern Learning Engine
    ├── Success Rate Tracker
    └── Adaptation Strategy Optimizer
```

## 🔍 Enhanced Change Detection

### Ubicación: `satelites/AutomationSolution/src/Automation.AI/AdvancedSelfHealing.fs`

```fsharp
module AdvancedSelfHealing =
    
    type ChangeDetectionResult = {
        ChangeType: ChangeType
        AffectedElements: ElementChange list
        Severity: ChangeSeverity
        AdaptationStrategy: AdaptationStrategy
        Confidence: float
        Evidence: ChangeEvidence list
    }
    
    type ChangeType =
        | ElementRemoved | ElementMoved | ElementModified
        | SelectorChanged | AttributeChanged | TextChanged
        | LayoutRestructured | FunctionalityMoved | NewElementAdded
    
    type ChangeSeverity =
        | Minor      // Cambios cosméticos que no afectan funcionalidad
        | Moderate   // Cambios que requieren adaptación de selectores
        | Major      // Cambios que requieren reescribir pasos
        | Critical   // Cambios que requieren rediseño del test
    
    type ElementChange = {
        OriginalElement: ElementInfo
        NewElement: ElementInfo option
        ChangeDescription: string
        SuggestedSelectors: string list
        AlternativeElements: ElementInfo list
    }
    
    type AdaptationStrategy =
        | UpdateSelector of newSelector: string
        | UseAlternativeElement of element: ElementInfo
        | ModifyTestFlow of newSteps: TestStep list
        | SplitTestCase of testCases: TestCase list
        | RequireManualIntervention of reason: string
```

### Intelligent Change Detection Engine

```fsharp
type ChangeDetectionEngine(aiProvider: IAIProvider, config: SelfHealingConfig) =
    
    member _.DetectChangesAsync(originalPage: PageInfo, currentPage: PageInfo) : Async<ChangeDetectionResult> =
        async {
            // 1. Comparar estructuras de página
            let! structuralChanges = this.DetectStructuralChanges originalPage currentPage
            
            // 2. Analizar cambios en elementos específicos
            let! elementChanges = this.DetectElementChanges originalPage.Elements currentPage.Elements
            
            // 3. Detectar cambios funcionales
            let! functionalChanges = this.DetectFunctionalChanges originalPage currentPage
            
            // 4. Usar AI para entender el contexto del cambio
            let! contextAnalysis = this.AnalyzeChangeContext originalPage currentPage structuralChanges elementChanges
            
            // 5. Determinar estrategia de adaptación
            let adaptationStrategy = this.DetermineAdaptationStrategy contextAnalysis
            
            return {
                ChangeType = contextAnalysis.PrimaryChangeType
                AffectedElements = elementChanges
                Severity = contextAnalysis.Severity
                AdaptationStrategy = adaptationStrategy
                Confidence = contextAnalysis.Confidence
                Evidence = contextAnalysis.Evidence
            }
        }
    
    member private _.AnalyzeChangeContext (originalPage: PageInfo) (currentPage: PageInfo) (structuralChanges: StructuralChange list) (elementChanges: ElementChange list) =
        async {
            let prompt = $"""
            Analiza estos cambios detectados en la página para entender el contexto y determinar la mejor estrategia de adaptación:
            
            Página original: {originalPage.Url}
            Título original: {originalPage.Title}
            Elementos originales: {originalPage.Elements.Length}
            
            Página actual: {currentPage.Url}
            Título actual: {currentPage.Title}
            Elementos actuales: {currentPage.Elements.Length}
            
            Cambios estructurales detectados:
            {serializeStructuralChanges structuralChanges}
            
            Cambios en elementos:
            {serializeElementChanges elementChanges}
            
            Analiza:
            1. ¿Qué tipo de cambio es este? (rediseño, actualización menor, reorganización, etc.)
            2. ¿Los cambios afectan la funcionalidad principal?
            3. ¿Hay patrones que indiquen una migración sistemática?
            4. ¿Los elementos críticos siguen siendo accesibles?
            5. ¿Qué estrategia de adaptación sería más efectiva?
            
            Proporciona:
            - Tipo de cambio principal
            - Severidad del impacto
            - Confianza en el análisis
            - Estrategia de adaptación recomendada
            - Elementos de evidencia que soportan el análisis
            """
            
            let! response = aiProvider.GenerateResponse {
                Prompt = prompt
                MaxTokens = Some 2000
                Temperature = Some 0.3
            }
            
            return parseChangeContextAnalysis response
        }
```

## 🌐 Web Self-Healing Engine

### Advanced Web Healer

```fsharp
module WebSelfHealer =
    
    type WebHealingResult = {
        Success: bool
        AdaptedActions: Action list
        NewSelectors: Map<string, string>
        FailedAdaptations: FailedAdaptation list
        HealingStrategy: HealingStrategy
        ExecutionTime: TimeSpan
    }
    
    type HealingStrategy =
        | SelectorEvolution    // Evolucionar selectores existentes
        | SemanticMatching     // Buscar por significado semántico
        | VisualMatching       // Buscar por apariencia visual
        | ContextualSearch     // Buscar por contexto circundante
        | AIGuidedDiscovery    // Usar AI para encontrar elementos
    
    let healWebTest (failedAction: Action) (currentPage: IPage) (aiProvider: IAIProvider) =
        async {
            match failedAction with
            | Click selector -> 
                return! healClickAction selector currentPage aiProvider
            | TypeText (selector, text) -> 
                return! healTypeAction selector text currentPage aiProvider
            | Navigate url -> 
                return! healNavigateAction url currentPage aiProvider
            | _ -> 
                return! healGenericAction failedAction currentPage aiProvider
        }
    
    let healClickAction (originalSelector: string) (page: IPage) (aiProvider: IAIProvider) =
        async {
            // 1. Intentar selectores evolutivos
            let! evolutionResult = tryEvolutionarySelectors originalSelector page
            
            match evolutionResult with
            | Some newSelector -> 
                return { 
                    Success = true
                    AdaptedActions = [Click newSelector]
                    NewSelectors = Map.ofList [(originalSelector, newSelector)]
                    FailedAdaptations = []
                    HealingStrategy = SelectorEvolution
                    ExecutionTime = TimeSpan.FromMilliseconds(100.0)
                }
            | None ->
                // 2. Usar AI para encontrar elemento equivalente
                let! aiResult = findElementWithAI originalSelector page aiProvider
                
                match aiResult with
                | Some element ->
                    return {
                        Success = true
                        AdaptedActions = [Click element.Selector]
                        NewSelectors = Map.ofList [(originalSelector, element.Selector)]
                        FailedAdaptations = []
                        HealingStrategy = AIGuidedDiscovery
                        ExecutionTime = TimeSpan.FromSeconds(2.0)
                    }
                | None ->
                    return {
                        Success = false
                        AdaptedActions = []
                        NewSelectors = Map.empty
                        FailedAdaptations = [{ OriginalAction = Click originalSelector; Reason = "Element not found"; Attempts = 2 }]
                        HealingStrategy = AIGuidedDiscovery
                        ExecutionTime = TimeSpan.FromSeconds(3.0)
                    }
        }
    
    let findElementWithAI (originalSelector: string) (page: IPage) (aiProvider: IAIProvider) =
        async {
            // Capturar screenshot y HTML actual
            let! screenshot = page.ScreenshotAsync() |> Async.AwaitTask
            let! html = page.ContentAsync() |> Async.AwaitTask
            
            let prompt = $"""
            El selector original "{originalSelector}" ya no funciona en esta página.
            
            HTML actual: {html}
            Screenshot: [Imagen adjunta]
            
            Basándote en el selector original, encuentra el elemento equivalente en la página actual:
            
            1. Analiza qué tipo de elemento era (botón, enlace, input, etc.)
            2. Busca elementos similares por:
               - Texto visible
               - Función aparente
               - Posición relativa
               - Atributos similares
            
            3. Proporciona selectores alternativos ordenados por confiabilidad:
               - CSS selector más robusto
               - XPath alternativo
               - Selector por texto
               - Selector por atributos
            
            4. Explica por qué cada selector es una buena opción
            
            Responde en formato JSON con los selectores y sus niveles de confianza.
            """
            
            let! response = aiProvider.GenerateResponse {
                Prompt = prompt
                MaxTokens = Some 1500
                Temperature = Some 0.3
                Images = Some [screenshot]
            }
            
            return parseElementDiscoveryResponse response
        }
```

## 📱 Mobile Self-Healing Engine

### Mobile-Specific Healing

```fsharp
module MobileSelfHealer =
    
    type MobileHealingContext = {
        Platform: MobilePlatform
        AppVersion: string option
        DeviceInfo: DeviceInfo
        ScreenSize: (int * int)
        Orientation: DeviceOrientation
    }
    
    type MobileHealingResult = {
        Success: bool
        AdaptedActions: MobileAction list
        NewLocators: Map<string, string>
        ContextChanges: ContextChange list
        HealingReason: string
    }
    
    type MobileAction =
        | Tap of locator: string
        | Swipe of direction: SwipeDirection * locator: string option
        | TypeText of locator: string * text: string
        | WaitForElement of locator: string * timeout: TimeSpan
    
    let healMobileTest (failedAction: MobileAction) (driver: AppiumDriver) (context: MobileHealingContext) (aiProvider: IAIProvider) =
        async {
            match failedAction with
            | Tap locator -> 
                return! healTapAction locator driver context aiProvider
            | Swipe (direction, locator) -> 
                return! healSwipeAction direction locator driver context aiProvider
            | TypeText (locator, text) -> 
                return! healTypeAction locator text driver context aiProvider
            | WaitForElement (locator, timeout) -> 
                return! healWaitAction locator timeout driver context aiProvider
        }
    
    let healTapAction (originalLocator: string) (driver: AppiumDriver) (context: MobileHealingContext) (aiProvider: IAIProvider) =
        async {
            // 1. Intentar locators evolutivos específicos para mobile
            let! evolutionResult = tryMobileEvolutionaryLocators originalLocator driver context
            
            match evolutionResult with
            | Some newLocator -> 
                return { 
                    Success = true
                    AdaptedActions = [Tap newLocator]
                    NewLocators = Map.ofList [(originalLocator, newLocator)]
                    ContextChanges = []
                    HealingReason = "Evolutionary locator adaptation"
                }
            | None ->
                // 2. Analizar jerarquía de elementos actual
                let! pageSource = driver.PageSourceAsync |> Async.AwaitTask
                let! screenshot = driver.GetScreenshotAsync() |> Async.AwaitTask
                
                // 3. Usar AI para encontrar elemento en contexto móvil
                let! aiResult = findMobileElementWithAI originalLocator pageSource screenshot context aiProvider
                
                match aiResult with
                | Some element ->
                    return {
                        Success = true
                        AdaptedActions = [Tap element.Locator]
                        NewLocators = Map.ofList [(originalLocator, element.Locator)]
                        ContextChanges = element.ContextChanges
                        HealingReason = "AI-guided mobile element discovery"
                    }
                | None ->
                    return {
                        Success = false
                        AdaptedActions = []
                        NewLocators = Map.empty
                        ContextChanges = []
                        HealingReason = "Element not found after exhaustive search"
                    }
        }
    
    let findMobileElementWithAI (originalLocator: string) (pageSource: string) (screenshot: Screenshot) (context: MobileHealingContext) (aiProvider: IAIProvider) =
        async {
            let prompt = $"""
            El locator móvil original "{originalLocator}" ya no funciona en esta app.
            
            Contexto de la app:
            Plataforma: {context.Platform}
            Versión: {context.AppVersion}
            Tamaño de pantalla: {fst context.ScreenSize}x{snd context.ScreenSize}
            Orientación: {context.Orientation}
            
            Jerarquía de elementos actual:
            {pageSource}
            
            Screenshot: [Imagen adjunta]
            
            Analiza el locator original y encuentra el elemento equivalente:
            
            1. Determina qué tipo de elemento era:
               - Botón, texto, imagen, lista, etc.
               - Función aparente en la app
            
            2. Busca elementos similares por:
               - Texto visible o content-desc
               - resource-id similar
               - Posición relativa en la pantalla
               - Jerarquía de elementos padre/hijo
            
            3. Considera cambios comunes en apps móviles:
               - IDs generados dinámicamente
               - Elementos que cambian de posición
               - Nuevas versiones de la app
               - Diferentes tamaños de pantalla
            
            4. Proporciona locators alternativos para {context.Platform}:
               - Por resource-id
               - Por content-desc/accessibility-id
               - Por texto visible
               - Por XPath relativo
               - Por coordenadas como último recurso
            
            Responde en formato JSON con locators y confianza.
            """
            
            let! response = aiProvider.GenerateResponse {
                Prompt = prompt
                MaxTokens = Some 2000
                Temperature = Some 0.3
                Images = Some [screenshot]
            }
            
            return parseMobileElementDiscovery response context
        }
```

## 🧠 Learning and Pattern Recognition

### Self-Healing Learning Engine

```fsharp
module SelfHealingLearning =
    
    type HealingPattern = {
        OriginalSelector: string
        SuccessfulReplacement: string
        ChangeContext: ChangeContext
        SuccessRate: float
        UsageCount: int
        LastUsed: DateTime
    }
    
    type ChangeContext = {
        SiteUrl: string
        ChangeType: ChangeType
        ElementType: ElementType
        PageSection: string option
        FrameworkDetected: string option
    }
    
    let learnFromSuccessfulHealing (originalSelector: string) (newSelector: string) (context: ChangeContext) =
        async {
            // Almacenar patrón exitoso para uso futuro
            let pattern = {
                OriginalSelector = originalSelector
                SuccessfulReplacement = newSelector
                ChangeContext = context
                SuccessRate = 1.0
                UsageCount = 1
                LastUsed = DateTime.UtcNow
            }
            
            do! HealingPatternCache.store pattern
            
            // Analizar si es parte de un patrón más amplio
            let! similarPatterns = HealingPatternCache.findSimilar pattern
            
            if similarPatterns.Length > 3 then
                // Suficientes datos para crear una regla general
                let! generalRule = createGeneralHealingRule similarPatterns
                do! HealingRuleEngine.addRule generalRule
        }
    
    let predictBestHealing (failedSelector: string) (context: ChangeContext) =
        async {
            // 1. Buscar patrones similares exitosos
            let! similarPatterns = HealingPatternCache.findByContext context
            
            // 2. Aplicar reglas generales aprendidas
            let! applicableRules = HealingRuleEngine.findApplicableRules failedSelector context
            
            // 3. Combinar predicciones
            let predictions = 
                [
                    yield! similarPatterns |> List.map (fun p -> (p.SuccessfulReplacement, p.SuccessRate))
                    yield! applicableRules |> List.map (fun r -> (r.SuggestedSelector, r.Confidence))
                ]
                |> List.sortByDescending snd
            
            return predictions |> List.take 3  // Top 3 predicciones
        }
```

## 🔄 Cross-Platform Coordination

### Unified Healing Coordinator

```fsharp
module CrossPlatformHealing =
    
    type PlatformHealingCoordinator(webHealer: WebSelfHealer, mobileHealer: MobileSelfHealer, aiProvider: IAIProvider) =
        
        member _.HealTestAsync(failedTest: TestCase, platform: Platform) : Async<HealingResult> =
            async {
                match platform with
                | Web browserInfo -> 
                    return! this.HealWebTest failedTest browserInfo
                | Mobile deviceInfo -> 
                    return! this.HealMobileTest failedTest deviceInfo
                | CrossPlatform platforms -> 
                    return! this.HealCrossPlatformTest failedTest platforms
            }
        
        member private _.HealCrossPlatformTest (test: TestCase) (platforms: Platform list) =
            async {
                let! healingResults = 
                    platforms
                    |> List.map (fun platform -> this.HealTestAsync test platform)
                    |> Async.Parallel
                
                // Analizar resultados y crear estrategia unificada
                let successfulHealings = healingResults |> Array.filter (fun r -> r.Success)
                
                if successfulHealings.Length > 0 then
                    // Crear test adaptado que funcione en múltiples plataformas
                    let! unifiedTest = this.CreateUnifiedTest test successfulHealings
                    
                    return {
                        Success = true
                        AdaptedTest = Some unifiedTest
                        PlatformResults = healingResults |> Array.toList
                        HealingStrategy = CrossPlatformAdaptation
                        Confidence = calculateUnifiedConfidence successfulHealings
                    }
                else
                    return {
                        Success = false
                        AdaptedTest = None
                        PlatformResults = healingResults |> Array.toList
                        HealingStrategy = RequireManualIntervention
                        Confidence = 0.0
                    }
            }
```

## ✅ Checklist de Implementación

### Fase 1: Detección Avanzada de Cambios
- [ ] Expandir `ChangeDetectionEngine` con AI
- [ ] Implementar análisis de contexto de cambios
- [ ] Desarrollar clasificación de severidad inteligente
- [ ] Crear sistema de evidencia de cambios
- [ ] Integrar con exploración automática

### Fase 2: Web Self-Healing Avanzado
- [ ] Mejorar `WebSelfHealer` con múltiples estrategias
- [ ] Implementar búsqueda semántica de elementos
- [ ] Desarrollar selectores evolutivos inteligentes
- [ ] Crear sistema de fallback en cascada
- [ ] Agregar healing visual con screenshots

### Fase 3: Mobile Self-Healing
- [ ] Crear `MobileSelfHealer` específico para Appium
- [ ] Implementar adaptación por plataforma (iOS/Android)
- [ ] Desarrollar healing por jerarquía de elementos
- [ ] Crear manejo de orientación y tamaños
- [ ] Implementar healing por gestos

### Fase 4: Aprendizaje y Optimización
- [ ] Desarrollar `SelfHealingLearning` engine
- [ ] Implementar caché de patrones exitosos
- [ ] Crear predictor de mejores estrategias
- [ ] Desarrollar reglas generales automáticas
- [ ] Implementar métricas de éxito

### Fase 5: Coordinación Cross-Platform
- [ ] Crear `CrossPlatformHealing` coordinator
- [ ] Implementar estrategias unificadas
- [ ] Desarrollar tests adaptativos multiplataforma
- [ ] Crear sincronización de aprendizaje
- [ ] Implementar reportes unificados

---

*Este sistema de self-healing avanzado permitirá que los tests se adapten automáticamente a cambios en la UI, reduciendo significativamente el mantenimiento manual.*
