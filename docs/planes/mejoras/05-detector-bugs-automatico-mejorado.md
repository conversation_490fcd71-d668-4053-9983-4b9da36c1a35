# Detector de Bugs Automático - Guía de Implementación

## 🎯 Objetivo

Desarrollar un sistema de IA que detecte automáticamente problemas, bugs, y anomalías durante la exploración de sitios web y aplicaciones móviles, identificando issues antes de que lleguen a producción **integrado con la arquitectura C# existente**.

## 🏗️ Arquitectura de Implementación

### Ubicación en la Solución Actual

```
satelites/AutomationSolution/src/
├── Automation.BugDetection.CSharp/           # NUEVO
│   ├── Core/
│   │   ├── IBugDetector.cs
│   │   ├── BugDetector.cs
│   │   └── IssueClassifier.cs
│   ├── Detectors/
│   │   ├── UIIssueDetector.cs
│   │   ├── PerformanceIssueDetector.cs
│   │   ├── SecurityIssueDetector.cs
│   │   ├── AccessibilityIssueDetector.cs
│   │   └── FunctionalBugDetector.cs
│   ├── Analysis/
│   │   ├── AnomalyDetector.cs
│   │   ├── PatternRecognizer.cs
│   │   ├── SeverityAnalyzer.cs
│   │   └── ImpactAssessor.cs
│   ├── Models/
│   │   ├── DetectedIssue.cs
│   │   ├── Evidence.cs
│   │   ├── IssueReport.cs
│   │   └── BugDetectionResult.cs
│   ├── Evidence/
│   │   ├── IEvidenceCollector.cs
│   │   ├── EvidenceCollector.cs
│   │   ├── ScreenshotAnalyzer.cs
│   │   └── ConsoleLogAnalyzer.cs
│   └── Reporting/
│       ├── IBugReporter.cs
│       ├── BugReporter.cs
│       ├── FixSuggestionEngine.cs
│       └── IssueAggregator.cs
├── Automation.AI.Infrastructure.CSharp/      # EXTENDER
│   ├── Services/
│   │   └── BugDetectionAIService.cs         # NUEVO
│   └── Prompts/
│       └── BugDetectionPrompts.cs           # NUEVO
└── Automation.Explorer.CSharp/               # DEPENDE DE
    └── Models/
        └── ExplorationResult.cs
```

## 📋 Guía de Implementación Paso a Paso

### Paso 1: Crear el Proyecto Base (Días 1-2)

```bash
# Crear nuevo proyecto
cd satelites/AutomationSolution/src/
dotnet new classlib -n Automation.BugDetection.CSharp
cd Automation.BugDetection.CSharp

# Agregar dependencias
dotnet add package Microsoft.SemanticKernel
dotnet add package System.Text.Json
dotnet add package Microsoft.Extensions.DependencyInjection
dotnet add package Microsoft.Extensions.Logging
dotnet add package Microsoft.Extensions.Caching.Memory
dotnet add package HtmlAgilityPack
dotnet add package SixLabors.ImageSharp
dotnet add package System.Drawing.Common
dotnet add package Microsoft.Playwright

# Agregar referencias
dotnet add reference ../Automation.AI.Infrastructure.CSharp/Automation.AI.Infrastructure.CSharp.csproj
dotnet add reference ../Automation.Explorer.CSharp/Automation.Explorer.CSharp.csproj
dotnet add reference ../Automation.Contracts/Automation.Contracts.csproj
```

### Paso 2: Definir Modelos de Datos (Días 3-4)

```csharp
// Models/DetectedIssue.cs
namespace Automation.BugDetection.Models;

public class DetectedIssue
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public IssueType Type { get; set; }
    public IssueSeverity Severity { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public IssueLocation Location { get; set; } = new();
    public List<Evidence> Evidence { get; set; } = new();
    public List<string> ReproductionSteps { get; set; } = new();
    public string? SuggestedFix { get; set; }
    public float Confidence { get; set; }
    public DetectionMethod DetectionMethod { get; set; }
    public ImpactAssessment Impact { get; set; } = new();
    public DateTime DetectedAt { get; set; } = DateTime.UtcNow;
    public Dictionary<string, string> Metadata { get; set; } = new();
    public List<string> Tags { get; set; } = new();
    public string? AssignedTo { get; set; }
    public IssueStatus Status { get; set; } = IssueStatus.Open;
}

public enum IssueType
{
    UIBug,
    FunctionalBug,
    PerformanceBug,
    SecurityVulnerability,
    AccessibilityIssue,
    UsabilityProblem,
    CompatibilityIssue,
    DataValidationError,
    NavigationProblem,
    ContentIssue,
    IntegrationError,
    ConfigurationError
}

public enum IssueSeverity
{
    Critical,      // Bloquea funcionalidad principal
    High,          // Afecta experiencia significativamente
    Medium,        // Problema notable pero no crítico
    Low,           // Mejora menor
    Informational  // Solo información
}

public enum IssueStatus
{
    Open,
    InProgress,
    Resolved,
    Closed,
    Duplicate,
    WontFix,
    NeedsVerification
}

public enum DetectionMethod
{
    AIAnalysis,
    RuleBasedCheck,
    PatternMatching,
    PerformanceThreshold,
    SecurityScan,
    AccessibilityAudit,
    AnomalyDetection,
    UserFlowAnalysis
}

// Models/IssueLocation.cs
public class IssueLocation
{
    public string Url { get; set; } = string.Empty;
    public string? ElementSelector { get; set; }
    public Point? ScreenCoordinates { get; set; }
    public string? PageSection { get; set; }
    public string? UserFlow { get; set; }
    public string? ComponentName { get; set; }
    public Dictionary<string, string> Context { get; set; } = new();
}

// Models/Evidence.cs
public class Evidence
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public EvidenceType Type { get; set; }
    public object Data { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public string Description { get; set; } = string.Empty;
    public string? FilePath { get; set; }
    public Dictionary<string, string> Metadata { get; set; } = new();
}

public enum EvidenceType
{
    Screenshot,
    ConsoleLog,
    NetworkRequest,
    HTMLSnapshot,
    PerformanceMetric,
    UserInteraction,
    ErrorMessage,
    VideoRecording,
    HARFile,
    SourceCode
}

// Models/ImpactAssessment.cs
public class ImpactAssessment
{
    public BusinessImpact BusinessImpact { get; set; }
    public UserImpact UserImpact { get; set; }
    public int AffectedUsers { get; set; }
    public float RevenueImpact { get; set; }
    public List<string> AffectedFeatures { get; set; } = new();
    public List<string> AffectedUserJourneys { get; set; } = new();
    public string ImpactDescription { get; set; } = string.Empty;
}

public enum BusinessImpact
{
    Critical,    // Pérdida de ingresos, funcionalidad principal rota
    High,        // Afecta métricas clave, experiencia deteriorada
    Medium,      // Impacto moderado en métricas
    Low,         // Impacto mínimo
    None         // Sin impacto significativo
}

public enum UserImpact
{
    Blocking,    // Usuario no puede completar tarea
    Degrading,   // Usuario puede completar pero con dificultad
    Annoying,    // Usuario molesto pero puede continuar
    Minor,       // Usuario apenas nota el problema
    None         // Sin impacto en usuario
}

// Models/BugDetectionResult.cs
public class BugDetectionResult
{
    public string SessionId { get; set; } = Guid.NewGuid().ToString();
    public string SourceUrl { get; set; } = string.Empty;
    public List<DetectedIssue> Issues { get; set; } = new();
    public BugDetectionStatistics Statistics { get; set; } = new();
    public DateTime DetectionDate { get; set; } = DateTime.UtcNow;
    public TimeSpan DetectionTime { get; set; }
    public string AIModel { get; set; } = string.Empty;
    public Dictionary<string, int> IssuesByType { get; set; } = new();
    public Dictionary<string, int> IssuesBySeverity { get; set; } = new();
    public List<string> DetectionMethods { get; set; } = new();
}

public class BugDetectionStatistics
{
    public int TotalIssues { get; set; }
    public int CriticalIssues { get; set; }
    public int HighSeverityIssues { get; set; }
    public int SecurityVulnerabilities { get; set; }
    public int AccessibilityIssues { get; set; }
    public int PerformanceIssues { get; set; }
    public float AverageConfidence { get; set; }
    public int PagesAnalyzed { get; set; }
    public int ElementsAnalyzed { get; set; }
}
```

### Paso 3: Implementar el Detector Principal (Días 5-7)

```csharp
// Core/IBugDetector.cs
namespace Automation.BugDetection.Core;

public interface IBugDetector
{
    Task<BugDetectionResult> DetectIssuesAsync(
        ExplorationResult explorationResult, 
        BugDetectionConfig config, 
        CancellationToken cancellationToken = default);
    
    Task<List<DetectedIssue>> DetectIssuesForPageAsync(
        DiscoveredPage page, 
        BugDetectionConfig config, 
        CancellationToken cancellationToken = default);
    
    Task<DetectedIssue?> AnalyzeElementAsync(
        DiscoveredElement element, 
        DiscoveredPage page, 
        CancellationToken cancellationToken = default);
}

// Core/BugDetector.cs
public class BugDetector : IBugDetector
{
    private readonly IAIProcessor _aiProcessor;
    private readonly ILogger<BugDetector> _logger;
    private readonly IEvidenceCollector _evidenceCollector;
    private readonly IssueClassifier _issueClassifier;
    private readonly IMemoryCache _cache;
    private readonly Dictionary<IssueType, IIssueDetector> _detectors;

    public BugDetector(
        IAIProcessor aiProcessor,
        ILogger<BugDetector> logger,
        IEvidenceCollector evidenceCollector,
        IssueClassifier issueClassifier,
        IMemoryCache cache,
        UIIssueDetector uiIssueDetector,
        PerformanceIssueDetector performanceIssueDetector,
        SecurityIssueDetector securityIssueDetector,
        AccessibilityIssueDetector accessibilityIssueDetector,
        FunctionalBugDetector functionalBugDetector)
    {
        _aiProcessor = aiProcessor;
        _logger = logger;
        _evidenceCollector = evidenceCollector;
        _issueClassifier = issueClassifier;
        _cache = cache;
        
        _detectors = new Dictionary<IssueType, IIssueDetector>
        {
            [IssueType.UIBug] = uiIssueDetector,
            [IssueType.PerformanceBug] = performanceIssueDetector,
            [IssueType.SecurityVulnerability] = securityIssueDetector,
            [IssueType.AccessibilityIssue] = accessibilityIssueDetector,
            [IssueType.FunctionalBug] = functionalBugDetector
        };
    }

    public async Task<BugDetectionResult> DetectIssuesAsync(
        ExplorationResult explorationResult, 
        BugDetectionConfig config, 
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Starting bug detection for: {Url}", explorationResult.StartUrl);
            
            var allIssues = new List<DetectedIssue>();
            
            // 1. Ejecutar detectores especializados en paralelo
            var detectionTasks = new List<Task<List<DetectedIssue>>>();
            
            foreach (var issueType in config.EnabledDetectors)
            {
                if (_detectors.TryGetValue(issueType, out var detector))
                {
                    detectionTasks.Add(detector.DetectIssuesAsync(explorationResult, config, cancellationToken));
                }
            }
            
            var detectionResults = await Task.WhenAll(detectionTasks);
            foreach (var issues in detectionResults)
            {
                allIssues.AddRange(issues);
            }
            
            // 2. Detectar patrones anómalos con AI
            if (config.EnableAnomalyDetection)
            {
                var anomalyIssues = await DetectAnomaliesAsync(explorationResult, cancellationToken);
                allIssues.AddRange(anomalyIssues);
            }
            
            // 3. Clasificar y priorizar issues
            var classifiedIssues = await _issueClassifier.ClassifyIssuesAsync(allIssues, cancellationToken);
            
            // 4. Filtrar falsos positivos
            var filteredIssues = await FilterFalsePositivesAsync(classifiedIssues, config, cancellationToken);
            
            // 5. Agrupar issues similares
            var groupedIssues = await GroupSimilarIssuesAsync(filteredIssues, cancellationToken);
            
            // 6. Generar sugerencias de solución
            await GenerateFixSuggestionsAsync(groupedIssues, cancellationToken);
            
            stopwatch.Stop();
            
            var result = new BugDetectionResult
            {
                SourceUrl = explorationResult.StartUrl,
                Issues = groupedIssues,
                DetectionTime = stopwatch.Elapsed,
                AIModel = "Current",
                Statistics = CalculateStatistics(groupedIssues, explorationResult),
                IssuesByType = groupedIssues.GroupBy(i => i.Type.ToString())
                    .ToDictionary(g => g.Key, g => g.Count()),
                IssuesBySeverity = groupedIssues.GroupBy(i => i.Severity.ToString())
                    .ToDictionary(g => g.Key, g => g.Count()),
                DetectionMethods = groupedIssues.Select(i => i.DetectionMethod.ToString())
                    .Distinct().ToList()
            };
            
            _logger.LogInformation("Bug detection completed in {Duration}ms. Found {IssueCount} issues",
                stopwatch.ElapsedMilliseconds, groupedIssues.Count);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during bug detection");
            throw;
        }
    }

    private async Task<List<DetectedIssue>> DetectAnomaliesAsync(
        ExplorationResult explorationResult, 
        CancellationToken cancellationToken)
    {
        var anomalyPrompt = $"""
        Analiza estos datos de exploración para detectar patrones anómalos o problemas no obvios:

        Sitio: {explorationResult.StartUrl}
        Páginas exploradas: {explorationResult.Pages.Count}
        Elementos encontrados: {explorationResult.Elements.Count}

        Estadísticas por página:
        {JsonSerializer.Serialize(explorationResult.Pages.Select(p => new {
            p.Url,
            ElementCount = p.Elements.Count,
            LoadTime = p.LoadTime.TotalMilliseconds,
            ErrorCount = p.Errors?.Count ?? 0
        }).Take(10))}

        Busca patrones que indiquen:
        1. Inconsistencias en el diseño entre páginas
        2. Flujos de usuario rotos o confusos
        3. Elementos que aparecen/desaparecen inconsistentemente
        4. Patrones de error que sugieren problemas sistemáticos
        5. Comportamientos inesperados en la navegación
        6. Datos faltantes o inconsistentes
        7. Problemas de rendimiento correlacionados

        Para cada anomalía detectada, responde en formato JSON:
        {{
            "issues": [
                {{
                    "type": "UIBug|PerformanceBug|FunctionalBug|NavigationProblem",
                    "severity": "Critical|High|Medium|Low",
                    "title": "Título descriptivo",
                    "description": "Descripción detallada del problema",
                    "evidence": "Evidencia que soporta el hallazgo",
                    "affectedPages": ["url1", "url2"],
                    "confidence": 0.85,
                    "suggestedFix": "Sugerencia de solución"
                }}
            ]
        }}
        """;

        try
        {
            var response = await _aiProcessor.ProcessAsync(anomalyPrompt, cancellationToken);
            var anomalyResult = JsonSerializer.Deserialize<AnomalyDetectionResult>(response);
            
            var issues = new List<DetectedIssue>();
            
            if (anomalyResult?.Issues != null)
            {
                foreach (var anomaly in anomalyResult.Issues)
                {
                    var issue = new DetectedIssue
                    {
                        Type = Enum.Parse<IssueType>(anomaly.Type),
                        Severity = Enum.Parse<IssueSeverity>(anomaly.Severity),
                        Title = anomaly.Title,
                        Description = anomaly.Description,
                        SuggestedFix = anomaly.SuggestedFix,
                        Confidence = anomaly.Confidence,
                        DetectionMethod = DetectionMethod.AnomalyDetection,
                        Location = new IssueLocation 
                        { 
                            Url = explorationResult.StartUrl,
                            Context = new Dictionary<string, string>
                            {
                                ["affectedPages"] = string.Join(", ", anomaly.AffectedPages ?? new List<string>())
                            }
                        },
                        Evidence = new List<Evidence>
                        {
                            new Evidence
                            {
                                Type = EvidenceType.SourceCode,
                                Data = anomaly.Evidence,
                                Description = "AI-detected pattern anomaly"
                            }
                        },
                        Tags = new List<string> { "anomaly", "ai-detected", "pattern" }
                    };
                    
                    issues.Add(issue);
                }
            }
            
            return issues;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to detect anomalies with AI");
            return new List<DetectedIssue>();
        }
    }

    private async Task<List<DetectedIssue>> FilterFalsePositivesAsync(
        List<DetectedIssue> issues, 
        BugDetectionConfig config, 
        CancellationToken cancellationToken)
    {
        var filteredIssues = new List<DetectedIssue>();
        
        foreach (var issue in issues)
        {
            // Filtrar por confianza mínima
            if (issue.Confidence < config.MinimumConfidence)
            {
                _logger.LogDebug("Filtering low confidence issue: {Title} (Confidence: {Confidence})", 
                    issue.Title, issue.Confidence);
                continue;
            }
            
            // Filtrar por severidad mínima
            if (issue.Severity < config.MinimumSeverity)
            {
                _logger.LogDebug("Filtering low severity issue: {Title} (Severity: {Severity})", 
                    issue.Title, issue.Severity);
                continue;
            }
            
            // Filtrar duplicados basados en ubicación y tipo
            var isDuplicate = filteredIssues.Any(existing => 
                existing.Type == issue.Type && 
                existing.Location.Url == issue.Location.Url &&
                existing.Location.ElementSelector == issue.Location.ElementSelector &&
                SimilarityScore(existing.Description, issue.Description) > 0.8f);
            
            if (isDuplicate)
            {
                _logger.LogDebug("Filtering duplicate issue: {Title}", issue.Title);
                continue;
            }
            
            filteredIssues.Add(issue);
        }
        
        return filteredIssues;
    }

    private static float SimilarityScore(string text1, string text2)
    {
        // Implementación simple de similitud basada en palabras comunes
        var words1 = text1.ToLower().Split(' ', StringSplitOptions.RemoveEmptyEntries);
        var words2 = text2.ToLower().Split(' ', StringSplitOptions.RemoveEmptyEntries);
        
        var common = words1.Intersect(words2).Count();
        var total = words1.Union(words2).Count();
        
        return total > 0 ? (float)common / total : 0f;
    }

    private async Task GenerateFixSuggestionsAsync(List<DetectedIssue> issues, CancellationToken cancellationToken)
    {
        var issuesWithoutFixes = issues.Where(i => string.IsNullOrEmpty(i.SuggestedFix)).ToList();
        
        if (!issuesWithoutFixes.Any()) return;
        
        var fixPrompt = $"""
        Genera sugerencias de solución para estos problemas detectados:

        {JsonSerializer.Serialize(issuesWithoutFixes.Select(i => new {
            i.Type,
            i.Title,
            i.Description,
            i.Location.Url,
            i.Location.ElementSelector
        }).Take(10))}

        Para cada problema, proporciona:
        1. Análisis de la causa raíz probable
        2. Pasos específicos para solucionarlo
        3. Código de ejemplo si es aplicable
        4. Mejores prácticas relacionadas
        5. Herramientas recomendadas

        Responde en formato JSON con sugerencias prácticas.
        """;

        try
        {
            var response = await _aiProcessor.ProcessAsync(fixPrompt, cancellationToken);
            var fixSuggestions = JsonSerializer.Deserialize<FixSuggestionsResult>(response);
            
            if (fixSuggestions?.Suggestions != null)
            {
                for (int i = 0; i < Math.Min(issuesWithoutFixes.Count, fixSuggestions.Suggestions.Count); i++)
                {
                    issuesWithoutFixes[i].SuggestedFix = fixSuggestions.Suggestions[i];
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to generate fix suggestions");
        }
    }

    private static BugDetectionStatistics CalculateStatistics(List<DetectedIssue> issues, ExplorationResult explorationResult)
    {
        return new BugDetectionStatistics
        {
            TotalIssues = issues.Count,
            CriticalIssues = issues.Count(i => i.Severity == IssueSeverity.Critical),
            HighSeverityIssues = issues.Count(i => i.Severity == IssueSeverity.High),
            SecurityVulnerabilities = issues.Count(i => i.Type == IssueType.SecurityVulnerability),
            AccessibilityIssues = issues.Count(i => i.Type == IssueType.AccessibilityIssue),
            PerformanceIssues = issues.Count(i => i.Type == IssueType.PerformanceBug),
            AverageConfidence = issues.Count > 0 ? issues.Average(i => i.Confidence) : 0f,
            PagesAnalyzed = explorationResult.Pages.Count,
            ElementsAnalyzed = explorationResult.Elements.Count
        };
    }
}
```

### Paso 4: Implementar Detectores Especializados (Días 8-10)

```csharp
// Detectors/UIIssueDetector.cs
public class UIIssueDetector : IIssueDetector
{
    private readonly IAIProcessor _aiProcessor;
    private readonly ILogger<UIIssueDetector> _logger;
    private readonly IEvidenceCollector _evidenceCollector;

    public UIIssueDetector(
        IAIProcessor aiProcessor,
        ILogger<UIIssueDetector> logger,
        IEvidenceCollector evidenceCollector)
    {
        _aiProcessor = aiProcessor;
        _logger = logger;
        _evidenceCollector = evidenceCollector;
    }

    public async Task<List<DetectedIssue>> DetectIssuesAsync(
        ExplorationResult explorationResult, 
        BugDetectionConfig config, 
        CancellationToken cancellationToken = default)
    {
        var issues = new List<DetectedIssue>();
        
        try
        {
            foreach (var page in explorationResult.Pages)
            {
                // Detectar elementos rotos o mal posicionados
                var brokenElements = await DetectBrokenElementsAsync(page, cancellationToken);
                issues.AddRange(brokenElements);
                
                // Detectar problemas de layout
                var layoutIssues = await DetectLayoutIssuesAsync(page, cancellationToken);
                issues.AddRange(layoutIssues);
                
                // Detectar problemas de responsive design
                var responsiveIssues = await DetectResponsiveIssuesAsync(page, cancellationToken);
                issues.AddRange(responsiveIssues);
                
                // Detectar inconsistencias visuales
                var visualInconsistencies = await DetectVisualInconsistenciesAsync(page, cancellationToken);
                issues.AddRange(visualInconsistencies);
            }
            
            return issues;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error detecting UI issues");
            throw;
        }
    }

    private async Task<List<DetectedIssue>> DetectBrokenElementsAsync(DiscoveredPage page, CancellationToken cancellationToken)
    {
        var issues = new List<DetectedIssue>();
        
        // Detectar imágenes rotas
        var brokenImages = page.Elements
            .Where(e => e.Type == ElementType.Image && 
                       (e.Attributes.ContainsKey("src") && string.IsNullOrEmpty(e.Attributes["src"]) ||
                        e.Attributes.ContainsKey("alt") && string.IsNullOrEmpty(e.Attributes["alt"])))
            .ToList();
        
        foreach (var image in brokenImages)
        {
            var issue = new DetectedIssue
            {
                Type = IssueType.UIBug,
                Severity = IssueSeverity.Medium,
                Title = "Broken or Missing Image",
                Description = $"Image element with missing src or alt attribute found",
                Location = new IssueLocation
                {
                    Url = page.Url,
                    ElementSelector = image.Selector,
                    ScreenCoordinates = new Point(image.Position.X, image.Position.Y)
                },
                Evidence = new List<Evidence>
                {
                    new Evidence
                    {
                        Type = EvidenceType.Screenshot,
                        Data = page.Screenshot,
                        Description = "Page screenshot showing broken image"
                    }
                },
                ReproductionSteps = new List<string>
                {
                    $"Navigate to {page.Url}",
                    $"Locate image element with selector: {image.Selector}",
                    "Observe missing image or alt text"
                },
                SuggestedFix = "Ensure all image elements have valid src attributes and descriptive alt text",
                Confidence = 0.9f,
                DetectionMethod = DetectionMethod.RuleBasedCheck,
                Tags = new List<string> { "ui", "accessibility", "image" }
            };
            
            issues.Add(issue);
        }
        
        // Detectar enlaces rotos
        var brokenLinks = page.Elements
            .Where(e => e.Type == ElementType.Link && 
                       e.Attributes.ContainsKey("href") && 
                       (string.IsNullOrEmpty(e.Attributes["href"]) || e.Attributes["href"] == "#"))
            .ToList();
        
        foreach (var link in brokenLinks)
        {
            var issue = new DetectedIssue
            {
                Type = IssueType.UIBug,
                Severity = IssueSeverity.Low,
                Title = "Broken or Empty Link",
                Description = $"Link element with empty or placeholder href found",
                Location = new IssueLocation
                {
                    Url = page.Url,
                    ElementSelector = link.Selector,
                    ScreenCoordinates = new Point(link.Position.X, link.Position.Y)
                },
                Evidence = new List<Evidence>
                {
                    new Evidence
                    {
                        Type = EvidenceType.HTMLSnapshot,
                        Data = $"<a href=\"{link.Attributes.GetValueOrDefault("href", "")}\">{link.Text}</a>",
                        Description = "HTML of broken link element"
                    }
                },
                ReproductionSteps = new List<string>
                {
                    $"Navigate to {page.Url}",
                    $"Locate link element with selector: {link.Selector}",
                    "Attempt to click the link"
                },
                SuggestedFix = "Ensure all link elements have valid href attributes pointing to accessible resources",
                Confidence = 0.85f,
                DetectionMethod = DetectionMethod.RuleBasedCheck,
                Tags = new List<string> { "ui", "navigation", "link" }
            };
            
            issues.Add(issue);
        }
        
        return issues;
    }

    private async Task<List<DetectedIssue>> DetectLayoutIssuesAsync(DiscoveredPage page, CancellationToken cancellationToken)
    {
        var prompt = $"""
        Analiza esta página web para detectar problemas de layout y diseño:

        URL: {page.Url}
        Título: {page.Title}
        Elementos interactivos: {page.Elements.Count(e => e.IsInteractable)}
        Viewport: {page.Viewport?.Width}x{page.Viewport?.Height}

        Elementos principales:
        {JsonSerializer.Serialize(page.Elements.Where(e => e.IsVisible).Take(20).Select(e => new {
            e.Type,
            e.Text,
            Position = new { e.Position.X, e.Position.Y, e.Position.Width, e.Position.Height },
            e.IsInteractable
        }))}

        Busca específicamente:
        1. Elementos superpuestos o que se solapan
        2. Elementos fuera del viewport
        3. Botones o enlaces demasiado pequeños para interactuar
        4. Espaciado inconsistente entre elementos
        5. Alineación incorrecta de elementos
        6. Elementos que se salen de sus contenedores
        7. Problemas de jerarquía visual
        8. Contraste insuficiente entre elementos

        Para cada problema, responde en formato JSON:
        {{
            "issues": [
                {{
                    "title": "Título descriptivo",
                    "description": "Descripción detallada",
                    "severity": "Critical|High|Medium|Low",
                    "affectedElements": ["selector1", "selector2"],
                    "suggestedFix": "Sugerencia de solución",
                    "confidence": 0.85
                }}
            ]
        }}
        """;

        try
        {
            var response = await _aiProcessor.ProcessAsync(prompt, cancellationToken);
            var layoutResult = JsonSerializer.Deserialize<LayoutAnalysisResult>(response);
            
            var issues = new List<DetectedIssue>();
            
            if (layoutResult?.Issues != null)
            {
                foreach (var layoutIssue in layoutResult.Issues)
                {
                    var issue = new DetectedIssue
                    {
                        Type = IssueType.UIBug,
                        Severity = Enum.Parse<IssueSeverity>(layoutIssue.Severity),
                        Title = layoutIssue.Title,
                        Description = layoutIssue.Description,
                        Location = new IssueLocation
                        {
                            Url = page.Url,
                            ElementSelector = layoutIssue.AffectedElements?.FirstOrDefault(),
                            Context = new Dictionary<string, string>
                            {
                                ["affectedElements"] = string.Join(", ", layoutIssue.AffectedElements ?? new List<string>())
                            }
                        },
                        Evidence = new List<Evidence>
                        {
                            new Evidence
                            {
                                Type = EvidenceType.Screenshot,
                                Data = page.Screenshot,
                                Description = "Page screenshot showing layout issues"
                            }
                        },
                        SuggestedFix = layoutIssue.SuggestedFix,
                        Confidence = layoutIssue.Confidence,
                        DetectionMethod = DetectionMethod.AIAnalysis,
                        Tags = new List<string> { "ui", "layout", "design" }
                    };
                    
                    issues.Add(issue);
                }
            }
            
            return issues;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to detect layout issues with AI");
            return new List<DetectedIssue>();
        }
    }
}

// Detectors/PerformanceIssueDetector.cs
public class PerformanceIssueDetector : IIssueDetector
{
    private readonly ILogger<PerformanceIssueDetector> _logger;
    private readonly PerformanceThresholds _thresholds;

    public PerformanceIssueDetector(
        ILogger<PerformanceIssueDetector> logger,
        IOptions<PerformanceThresholds> thresholds)
    {
        _logger = logger;
        _thresholds = thresholds.Value;
    }

    public async Task<List<DetectedIssue>> DetectIssuesAsync(
        ExplorationResult explorationResult, 
        BugDetectionConfig config, 
        CancellationToken cancellationToken = default)
    {
        var issues = new List<DetectedIssue>();
        
        foreach (var page in explorationResult.Pages)
        {
            // Detectar tiempo de carga lento
            if (page.LoadTime > _thresholds.MaxLoadTime)
            {
                var severity = page.LoadTime > TimeSpan.FromSeconds(10) ? IssueSeverity.High : IssueSeverity.Medium;
                
                var issue = new DetectedIssue
                {
                    Type = IssueType.PerformanceBug,
                    Severity = severity,
                    Title = "Slow Page Load Time",
                    Description = $"Page loads in {page.LoadTime.TotalSeconds:F2}s, exceeding threshold of {_thresholds.MaxLoadTime.TotalSeconds:F2}s",
                    Location = new IssueLocation { Url = page.Url },
                    Evidence = new List<Evidence>
                    {
                        new Evidence
                        {
                            Type = EvidenceType.PerformanceMetric,
                            Data = new { LoadTime = page.LoadTime, Threshold = _thresholds.MaxLoadTime },
                            Description = "Page load time measurement"
                        }
                    },
                    ReproductionSteps = new List<string>
                    {
                        $"Navigate to {page.Url}",
                        "Measure load time with browser dev tools",
                        "Compare against performance threshold"
                    },
                    SuggestedFix = "Optimize images, minify CSS/JS, enable compression, use CDN, reduce HTTP requests",
                    Confidence = 0.95f,
                    DetectionMethod = DetectionMethod.PerformanceThreshold,
                    Impact = new ImpactAssessment
                    {
                        UserImpact = UserImpact.Degrading,
                        BusinessImpact = BusinessImpact.Medium,
                        ImpactDescription = "Slow load times increase bounce rate and decrease user satisfaction"
                    },
                    Tags = new List<string> { "performance", "load-time", "user-experience" }
                };
                
                issues.Add(issue);
            }
            
            // Detectar recursos grandes
            if (page.Resources != null)
            {
                var largeResources = page.Resources
                    .Where(r => r.Size > _thresholds.MaxResourceSize)
                    .OrderByDescending(r => r.Size)
                    .Take(5);
                
                foreach (var resource in largeResources)
                {
                    var issue = new DetectedIssue
                    {
                        Type = IssueType.PerformanceBug,
                        Severity = resource.Size > _thresholds.MaxResourceSize * 2 ? IssueSeverity.High : IssueSeverity.Medium,
                        Title = $"Large Resource: {resource.Type}",
                        Description = $"Resource {resource.Url} is {resource.Size / 1024:N0}KB, exceeding {_thresholds.MaxResourceSize / 1024:N0}KB threshold",
                        Location = new IssueLocation 
                        { 
                            Url = page.Url,
                            Context = new Dictionary<string, string>
                            {
                                ["resourceUrl"] = resource.Url,
                                ["resourceType"] = resource.Type.ToString()
                            }
                        },
                        Evidence = new List<Evidence>
                        {
                            new Evidence
                            {
                                Type = EvidenceType.NetworkRequest,
                                Data = resource,
                                Description = "Large resource details"
                            }
                        },
                        SuggestedFix = GenerateResourceOptimizationSuggestion(resource),
                        Confidence = 0.90f,
                        DetectionMethod = DetectionMethod.PerformanceThreshold,
                        Tags = new List<string> { "performance", "resource-size", resource.Type.ToString().ToLower() }
                    };
                    
                    issues.Add(issue);
                }
            }
        }
        
        return issues;
    }

    private static string GenerateResourceOptimizationSuggestion(Resource resource)
    {
        return resource.Type switch
        {
            ResourceType.Image => "Optimize image compression, use modern formats (WebP, AVIF), implement lazy loading",
            ResourceType.JavaScript => "Minify JavaScript, enable tree shaking, split code into chunks, use async/defer loading",
            ResourceType.CSS => "Minify CSS, remove unused styles, use critical CSS inline, lazy load non-critical styles",
            ResourceType.Font => "Use font-display: swap, preload critical fonts, subset fonts, use variable fonts",
            ResourceType.Video => "Optimize video compression, use appropriate codecs, implement progressive loading",
            _ => "Optimize resource size, enable compression, use CDN, implement caching"
        };
    }
}
```

### Paso 5: Configurar Servicios y Tests (Días 11-12)

```csharp
// Extensions/ServiceCollectionExtensions.cs
public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddBugDetection(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // Servicios principales
        services.AddScoped<IBugDetector, BugDetector>();
        services.AddScoped<IssueClassifier>();
        services.AddScoped<IEvidenceCollector, EvidenceCollector>();
        
        // Detectores especializados
        services.AddScoped<UIIssueDetector>();
        services.AddScoped<PerformanceIssueDetector>();
        services.AddScoped<SecurityIssueDetector>();
        services.AddScoped<AccessibilityIssueDetector>();
        services.AddScoped<FunctionalBugDetector>();
        
        // Análisis y reportes
        services.AddScoped<AnomalyDetector>();
        services.AddScoped<PatternRecognizer>();
        services.AddScoped<SeverityAnalyzer>();
        services.AddScoped<IBugReporter, BugReporter>();
        services.AddScoped<FixSuggestionEngine>();
        
        // Configuración
        services.Configure<BugDetectionConfig>(configuration.GetSection("BugDetection"));
        services.Configure<PerformanceThresholds>(configuration.GetSection("PerformanceThresholds"));
        
        return services;
    }
}

// Program.cs (MODIFICAR)
builder.ConfigureServices((context, services) =>
{
    // Configuración existente...
    services.AddAutomationServices(context.Configuration);
    services.AddNaturalLanguageProcessing(context.Configuration);
    services.AddWebExploration(context.Configuration);
    services.AddTestGeneration(context.Configuration);
    
    // AGREGAR: Servicios de detección de bugs
    services.AddBugDetection(context.Configuration);
});
```

### Configuración (appsettings.json)

```json
{
  "BugDetection": {
    "EnabledDetectors": ["UIBug", "PerformanceBug", "SecurityVulnerability", "AccessibilityIssue", "FunctionalBug"],
    "EnableAnomalyDetection": true,
    "MinimumConfidence": 0.7,
    "MinimumSeverity": "Low",
    "MaxIssuesPerType": 50,
    "FilterFalsePositives": true,
    "GroupSimilarIssues": true,
    "GenerateFixSuggestions": true
  },
  "PerformanceThresholds": {
    "MaxLoadTime": "00:00:05",
    "MaxFirstContentfulPaint": "00:00:02",
    "MaxLargestContentfulPaint": "00:00:04",
    "MaxCumulativeLayoutShift": 0.1,
    "MaxFirstInputDelay": "00:00:00.100",
    "MaxResourceSize": 1048576
  }
}
```

### Test de Integración

```csharp
[Test]
public async Task DetectIssuesAsync_ExplorationResult_DetectsVariousIssues()
{
    // Arrange
    var explorationResult = CreateSampleExplorationResult();
    var config = new BugDetectionConfig
    {
        EnabledDetectors = new[] { IssueType.UIBug, IssueType.PerformanceBug },
        MinimumConfidence = 0.7f,
        MinimumSeverity = IssueSeverity.Low
    };

    // Act
    var result = await _bugDetector.DetectIssuesAsync(explorationResult, config);

    // Assert
    Assert.IsNotNull(result);
    Assert.That(result.Issues.Count, Is.GreaterThan(0));
    Assert.That(result.Issues.Any(i => i.Type == IssueType.UIBug), Is.True);
    Assert.That(result.Statistics.TotalIssues, Is.EqualTo(result.Issues.Count));
}
```

## ✅ Checklist de Implementación

### Semana 1: Fundamentos
- [ ] ✅ Crear proyecto Automation.BugDetection.CSharp
- [ ] ✅ Definir modelos de datos principales
- [ ] ✅ Implementar BugDetector principal
- [ ] ✅ Crear UIIssueDetector básico
- [ ] ✅ Configurar logging y evidencia

### Semana 2: Detectores Especializados
- [ ] ✅ Implementar PerformanceIssueDetector
- [ ] ✅ Crear SecurityIssueDetector
- [ ] ✅ Desarrollar AccessibilityIssueDetector
- [ ] ✅ Implementar FunctionalBugDetector
- [ ] ✅ Crear sistema de clasificación

### Semana 3: Análisis Avanzado
- [ ] ✅ Implementar AnomalyDetector con AI
- [ ] ✅ Crear PatternRecognizer
- [ ] ✅ Desarrollar SeverityAnalyzer
- [ ] ✅ Implementar filtros de falsos positivos
- [ ] ✅ Crear sistema de agrupación

### Semana 4: Reportes y Sugerencias
- [ ] ✅ Implementar BugReporter
- [ ] ✅ Crear FixSuggestionEngine
- [ ] ✅ Desarrollar sistema de evidencia
- [ ] ✅ Implementar métricas de calidad
- [ ] ✅ Crear reportes detallados

### Semana 5: Integración Final
- [ ] ✅ Integrar con ExplorationResult
- [ ] ✅ Configurar inyección de dependencias
- [ ] ✅ Crear tests de integración
- [ ] ✅ Implementar validación de issues
- [ ] ✅ Crear documentación completa

---

*Este detector identificará automáticamente una amplia gama de problemas durante la exploración, desde bugs de UI hasta vulnerabilidades de seguridad, mejorando significativamente la calidad del software antes de llegar a producción.*
