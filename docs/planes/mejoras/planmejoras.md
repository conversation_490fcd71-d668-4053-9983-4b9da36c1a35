# 🔄 AutomationSolution: Task Flow Documentation

## Flujo Completo: Redis → Browser

Este documento detalla el flujo completo de ejecución de tareas desde que ingresan por Redis hasta su ejecución en el browser.

---

## 📋 Resumen Ejecutivo

**Arquitectura Actual**: Sistema distribuido basado en mensajería Redis con workers C# y automatización browser via Playwright.

**Flujo Principal**: 
```
[Task Source] → [Redis Channel] → [Worker Service] → [Task Processor] → [Web/Mobile Executor] → [Browser Pool] → [Playwright Browser]
```

---

## 🏗️ Componentes del Sistema

### 1. **Punto de Entrada: Redis Message Queue**

**Canal de Configuración:**
- Canal principal: `automation_channel`
- Dead letter queue: `automation_dlq` 
- Conexión: `localhost:6379` (configurable)

**Estructura de Mensaje:**
```json
{
  "taskId": "guid-string",
  "taskType": "web|mobile",
  "actions": [
    {
      "type": "navigate|click|type|screenshot|waitforelement|gettext",
      "selector": "css-selector",
      "text": "optional-text",
      "url": "optional-url"
    }
  ],
  "createdAt": "2025-01-01T00:00:00Z",
  "priority": 0,
  "timeout": "00:05:00",
  "metadata": {}
}
```

### 2. **Worker Service: Consumo de Mensajes**

**Archivo**: `/src/Automation.Worker.CSharp/Program.cs`

**RedisSubscriberService** (`/src/Automation.Worker.CSharp/Services/RedisSubscriberService.cs`):
- Se suscribe al canal Redis usando `StackExchange.Redis`
- Implementa control de concurrencia con `SemaphoreSlim`
- Maneja hasta 3 tareas concurrentes por defecto (configurable)
- Gestión de lifecycle de mensajes con timeout

**Características Clave:**
```csharp
// Gestión de concurrencia
_concurrencySemaphore = new SemaphoreSlim(_config.Concurrency.MaxConcurrentTasks);

// Manejo de mensajes con timeout
if (_config.Concurrency.EnableTaskCancellation)
{
    taskCts.CancelAfter(_config.Concurrency.TaskTimeoutMs);
}
```

### 3. **Capa de Procesamiento de Tareas**

**Archivo**: `/src/Automation.Worker.CSharp/Services/TaskProcessingService.cs`

**Pipeline de Procesamiento:**
1. **Circuit Breaker Check** - Previene fallas en cascada
2. **Deserialización de Mensajes** - Convierte JSON a objeto `TaskMessage`
3. **Aplicación de Retry Policy** - Implementa exponential backoff
4. **Selección de Executor** - Ruta a Web o Mobile executor basado en `taskType`

**Lógica de Procesamiento Principal:**
```csharp
return taskMessage.TaskType?.ToLowerInvariant() switch
{
    "web" => await _webExecutor.ExecuteAsync(taskMessage.Actions, cancellationToken),
    "mobile" => await _mobileExecutor.ExecuteAsync(taskMessage.Actions, cancellationToken),
    _ => TaskResult.Failure($"Unsupported task type: {taskMessage.TaskType}")
};
```

### 4. **Web Executor: Automatización Browser**

**Archivo**: `/src/Automation.Web.CSharp/Executors/WebTaskExecutor.cs`

**Flujo de Ejecución:**
1. **Adquisición de Browser** - Obtiene browser del pool
2. **Creación de Page** - Crea página Playwright configurada
3. **Ejecución de Acciones** - Procesa cada acción secuencialmente
4. **Limpieza de Recursos** - Retorna browser al pool

**Acciones Soportadas:**
- `navigate` - Navegación de página con wait network idle
- `click` - Click en elemento con selector
- `type` - Input de texto con form filling
- `screenshot` - Screenshots full page
- `waitforelement` - Espera de visibilidad de elemento
- `gettext` - Extracción de contenido de texto

**Ejemplo de Ejecución de Acción:**
```csharp
case "navigate":
    await page.GotoAsync(url, new PageGotoOptions
    {
        WaitUntil = WaitUntilState.NetworkIdle,
        Timeout = _configuration.PageOptions.NavigationTimeoutMs
    });
    break;
```

### 5. **Browser Pool Management**

**Archivo**: `/src/Automation.Web.CSharp/ResourceManagement/BrowserPool.cs`

**Características de Gestión de Recursos:**
- **Reutilización de Browser** - Mantiene pool de browsers activos
- **Límites de Concurrencia** - Máximo browsers concurrentes (configurable)
- **Limpieza Idle** - Disposal automático de browsers no usados
- **Reset de Estado** - Limpia estado del browser entre tareas

**Lifecycle del Browser:**
```csharp
public async Task<IBrowser> GetBrowserAsync(CancellationToken cancellationToken)
{
    // Intenta reutilizar browser existente
    if (_availableBrowsers.TryDequeue(out var pooledBrowser))
    {
        if (pooledBrowser.Browser.IsConnected)
        {
            pooledBrowser.IsInUse = true;
            return pooledBrowser.Browser;
        }
    }
    
    // Crea nuevo browser si es necesario
    var browser = await CreateBrowserAsync(cancellationToken);
    return browser;
}
```

### 6. **Browser Session Management**

**Integración Playwright:**
- Usa librería `Microsoft.Playwright`
- Soporta browsers Chromium, Firefox, y WebKit
- Opciones de launch configurables (headless, viewport, timeouts)
- Aislamiento de contexto para seguridad

**Configuración de Page:**
```csharp
var contextOptions = new BrowserNewContextOptions
{
    UserAgent = _configuration.PageOptions.UserAgent,
    ExtraHTTPHeaders = _configuration.PageOptions.ExtraHeaders,
    JavaScriptEnabled = _configuration.PageOptions.JavaScriptEnabled,
    ViewportSize = new ViewportSize { Width = 1920, Height = 1080 }
};
```

---

## 🚨 Manejo de Errores y Reporte de Resultados

### Mecanismos de Captura de Errores

1. **Screenshot on Error** - Screenshots automáticos en errores
2. **Captura de Contenido de Page** - HTML completo para debugging
3. **Contexto de Error** - Información detallada de errores con stack traces
4. **Dead Letter Queue** - Mensajes fallidos enviados a DLQ para análisis

**Estructura de Información de Error:**
```csharp
public record ActionExecutionError
{
    public string Message { get; init; }
    public string? ScreenshotPath { get; init; }
    public string? CurrentUrl { get; init; }
    public string? HtmlContent { get; init; }
    public Exception? InnerException { get; init; }
    public DateTime OccurredAt { get; init; }
}
```

### Procesamiento de Resultados

**Estructura de Task Result:**
```csharp
public record TaskResult
{
    public bool IsSuccess { get; init; }
    public string Message { get; init; }
    public ActionExecutionError? Error { get; init; }
    public TimeSpan Duration { get; init; }
}
```

---

## ⚙️ Configuración y Escalabilidad

### Configuración del Worker

**Archivo**: `/src/Automation.Worker.CSharp/Configuration/WorkerConfiguration.cs`

**Settings Clave:**
- **Concurrencia:** Máx 3 tareas concurrentes por worker
- **Timeouts:** 5 minutos timeout de tarea, 30 segundos navegación
- **Circuit Breaker:** 5 fallas en 1 minuto abre circuito
- **Retry Policy:** 3 intentos con exponential backoff

### Monitoreo y Métricas

**Health Checks:**
- Worker health endpoint: `/health`
- Checks de conectividad Redis
- Monitoreo de estado de circuit breaker

**Colección de Métricas:**
- Estadísticas de procesamiento de tareas
- Utilización de browser pool
- Tracking de tasa de errores
- Métricas de performance

---

## 📝 Ejemplo de Flujo Completo

### 1. Publicación de Tarea a Redis
```csharp
var taskMessage = new {
    TaskId = Guid.NewGuid().ToString(),
    TaskType = "web",
    Actions = new[] {
        new { Type = "navigate", Url = "https://example.com" },
        new { Type = "click", Selector = "#login-button" },
        new { Type = "screenshot", FileName = "result.png" }
    }
};

await subscriber.PublishAsync("automation_channel", JsonSerializer.Serialize(taskMessage));
```

### 2. Procesamiento del Worker
```csharp
// RedisSubscriberService recibe mensaje
await HandleMessageAsync(message, cancellationToken);

// TaskProcessingService procesa
var result = await ProcessTaskAsync(message, cancellationToken);

// WebTaskExecutor ejecuta acciones
var browser = await _browserPool.GetBrowserAsync();
var page = await CreateConfiguredPageAsync(browser);
foreach (var action in actions) {
    await ExecuteSingleActionAsync(page, action);
}
```

### 3. Ejecución en Browser
```csharp
// Navegar a página
await page.GotoAsync("https://example.com", new PageGotoOptions {
    WaitUntil = WaitUntilState.NetworkIdle
});

// Click en elemento
await page.ClickAsync("#login-button");

// Tomar screenshot
await page.ScreenshotAsync(new PageScreenshotOptions {
    Path = "result.png",
    FullPage = true
});
```

---

## 📊 Características de Performance

- **Acciones Simples:** < 2 segundos tiempo de respuesta
- **Workflows Complejos:** < 10 segundos completación típica
- **Tareas Concurrentes:** Hasta 3 por instancia de worker
- **Startup de Browser:** ~2-3 segundos para creación de nuevo browser
- **Uso de Memoria:** ~50MB por instancia de browser

---

## 📁 Referencia de Archivos Clave

- **Entry Point del Worker:** `/src/Automation.Worker.CSharp/Program.cs`
- **Redis Subscriber:** `/src/Automation.Worker.CSharp/Services/RedisSubscriberService.cs`
- **Task Processor:** `/src/Automation.Worker.CSharp/Services/TaskProcessingService.cs`
- **Web Executor:** `/src/Automation.Web.CSharp/Executors/WebTaskExecutor.cs`
- **Browser Pool:** `/src/Automation.Web.CSharp/ResourceManagement/BrowserPool.cs`
- **Configuración:** `/src/Automation.Worker.CSharp/Configuration/WorkerConfiguration.cs`
- **Contratos:** `/src/Automation.Contracts/ITaskExecutor.cs`

---

## 🎯 Conclusión

Tu implementación actual en C# con Playwright provee una base robusta y escalable para automatización browser con manejo comprehensivo de errores, gestión de recursos, y capacidades de monitoreo.

**El sistema ya tiene:**
✅ Arquitectura distribuida escalable  
✅ Pool de recursos optimizado  
✅ Manejo robusto de errores  
✅ Configuración flexible  
✅ Monitoreo y métricas  

**Próximas mejoras pueden enfocarse en:**
🔮 Procesamiento de lenguaje natural  
🔮 Auto-exploración inteligente  
🔮 Self-healing automático  
🔮 Generación automática de tests  

---

*Documentación generada: 16 de julio de 2025*