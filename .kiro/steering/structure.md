# Project Structure & Organization

## Repository Layout

```
├── satelites/AutomationSolution/          # Main F# automation solution
│   ├── src/                               # Source code projects
│   │   ├── Automation.Core/               # Shared models, interfaces, task dispatching
│   │   ├── Automation.AI/                 # AI integration and command processing
│   │   ├── Automation.Web/                # Web browser automation (Playwright)
│   │   ├── Automation.Mobile/             # Mobile app automation (Appium)
│   │   ├── Automation.Data/               # Data handling and persistence
│   │   ├── Automation.Utilities/          # Cross-cutting utilities and logging
│   │   ├── Automation.Worker/             # Worker service (executable)
│   │   ├── Automation.Dispatcher/         # Task dispatcher service (executable)
│   │   └── Automation.Prompts/            # Prompt management and caching
│   ├── AutomationSolution.sln             # Visual Studio solution file
│   ├── docker-compose.yml                 # Development environment
│   ├── Dockerfile.*                       # Container definitions
│   └── README.md                          # Project documentation
├── .taskmaster/                           # Task Master AI integration
│   ├── tasks/                             # Task definitions and tracking
│   ├── docs/                              # Project requirements documents
│   └── config.json                        # AI model configuration
├── docs/                                  # Additional documentation
└── .kiro/steering/                        # AI assistant guidance rules
```

## F# Project Architecture

### Core Layer (`Automation.Core`)
- **Purpose**: Shared domain models, interfaces, and core business logic
- **Key Files**: `Library.fs`, `TaskDispatcher.fs`, `SecurityTests.fs`
- **Dependencies**: Automation.Utilities only
- **Exports**: Task models, dispatcher logic, security validation

### AI Layer (`Automation.AI`)
- **Purpose**: AI provider integration and natural language processing
- **Key Components**: Cost optimization, provider selection, command parsing
- **Dependencies**: Multiple AI provider SDKs
- **Exports**: Command processing, AI orchestration

### Automation Layers
- **`Automation.Web`**: Playwright-based web automation
- **`Automation.Mobile`**: Appium-based mobile automation
- **Both export**: Action execution interfaces for Worker consumption

### Infrastructure Layers
- **`Automation.Data`**: Database and persistence logic
- **`Automation.Utilities`**: Logging, configuration, shared utilities
- **`Automation.Prompts`**: Prompt caching and management

### Executable Services
- **`Automation.Worker`**: Main worker process (Program.fs, Metrics.fs)
- **`Automation.Dispatcher`**: Task routing service (Program.fs)

## File Organization Patterns

### F# Project Structure
```
ProjectName/
├── ProjectName.fsproj                     # Project file with dependencies
├── Library.fs                             # Main module (if library)
├── Program.fs                             # Entry point (if executable)
├── [Feature].fs                           # Feature-specific modules
└── README.md                              # Project-specific documentation
```

### Naming Conventions
- **Projects**: `Automation.[Domain]` pattern
- **Files**: PascalCase with descriptive names
- **Modules**: Match file names, organized by feature
- **Dependencies**: Core → Utilities, Worker → All others

### Configuration Management
- **Environment Variables**: For runtime configuration
- **appsettings.json**: For structured configuration (when applicable)
- **Docker Compose**: For development environment setup
- **Redis**: For runtime coordination and state

## Development Workflow Structure

### Local Development
1. Work in `satelites/AutomationSolution/` directory
2. Use `dotnet` CLI for building and testing
3. Use Docker Compose for full environment testing
4. Individual projects can be run independently

### Task Management Integration
- `.taskmaster/` contains AI-assisted project management
- Task definitions link to specific code areas
- Use Task Master for complex feature development

### Multi-AI Assistant Support
- `.claude/`, `.cursor/`, `.windsurf/`, `.trae/` directories contain assistant-specific rules
- Each assistant has tailored guidance for the project
- Consistent patterns across all assistant configurations