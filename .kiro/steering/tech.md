# Technology Stack & Build System

## Core Technologies

### Language & Runtime
- **F# (.NET 10.0)**: Primary development language
- **.NET SDK**: Required for building and running applications
- **Target Framework**: net10.0 across all projects

### Key Dependencies
- **StackExchange.Redis**: Redis client for message queuing and coordination
- **Microsoft.Playwright**: Web browser automation
- **Appium.WebDriver**: Mobile application automation
- **System.Text.Json**: JSON serialization
- **Microsoft.AspNetCore.App**: Web framework for metrics and health endpoints

### Infrastructure
- **Redis 7**: Message broker and coordination layer
- **Docker & Docker Compose**: Containerization and orchestration
- **Prometheus**: Metrics collection
- **Grafana**: Monitoring dashboards
- **Nginx**: Load balancing (production)

### AI Providers
- OpenAI GPT models
- Anthropic Claude
- Google Gemini
- Multiple provider support with cost optimization

## Build System

### Solution Structure
```bash
# Build entire solution
dotnet build AutomationSolution.sln --configuration Release

# Restore dependencies
dotnet restore AutomationSolution.sln

# Run tests
dotnet test AutomationSolution.sln --configuration Release

# Run specific project
dotnet run --project src/Automation.Worker
dotnet run --project src/Automation.Dispatcher
```

### Docker Commands
```bash
# Development environment
docker-compose up -d

# Build specific services
docker-compose build worker
docker-compose build dispatcher

# Scale workers
docker-compose up -d --scale worker=5

# View logs
docker-compose logs -f worker
```

### Environment Setup
```bash
# Required environment variables
export REDIS_CONNECTION_STRING="localhost:6379"
export OPENAI_API_KEY="your-key"
export ANTHROPIC_API_KEY="your-key"
export GOOGLE_AI_API_KEY="your-key"

# Start Redis for development
docker run -d -p 6379:6379 redis:7-alpine
```

### Project Dependencies
- All projects target .NET 10.0
- Worker depends on Core, Web, AI, Utilities, Data, Mobile
- Core depends on Utilities
- Use ProjectReference for internal dependencies
- PackageReference for external NuGet packages

### Development Workflow
1. Make changes to F# source files
2. Build with `dotnet build`
3. Test with `dotnet test`
4. Run locally or with Docker Compose
5. Monitor with Prometheus/Grafana at localhost:3000