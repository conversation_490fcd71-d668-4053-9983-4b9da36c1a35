# Task ID: 6
# Title: Configure Centralized Logging and Data Handling
# Status: done
# Dependencies: 1
# Priority: medium
# Description: Set up `Serilog` for logging activities and errors in `Automation.Utilities`, and establish basic data handling/storage capabilities in `Automation.Data`, potentially using `System.Data.SQLite`.
# Details:
Add `Serilog` NuGet package to `Automation.Utilities`. Configure `Serilog` to log to console and/or file. Implement `Logger.logInfo` and `Logger.logError` functions. Add `System.Data.SQLite` and `FSharp.Data` to `Automation.Data`. Implement basic functions in `Automation.Data` to store `TaskResult` objects (e.g., to a SQLite database or a simple JSON file for persistence).

# Test Strategy:
Verify that `Logger.logInfo` and `Logger.logError` correctly write messages to the configured sinks (console, file). Test `Automation.Data` functions by storing and retrieving sample `TaskResult` data, ensuring data integrity and correct persistence.
