# Task ID: 2
# Title: Develop Redis Event Listener in Worker
# Status: done
# Dependencies: 1
# Priority: high
# Description: Implement the Redis client in the `Automation.Worker` project to connect to a Redis instance and subscribe to the `automation_channel` for incoming commands.
# Details:
Add `StackExchange.Redis` NuGet package to `Automation.Worker`. In the worker's main logic, use `ConnectionMultiplexer.Connect("localhost")` to establish a connection. Obtain a subscriber using `redis.GetSubscriber()`. Implement `subscriber.Subscribe("automation_channel", fun _ msg -> handleEvent msg)` to listen for messages, where `handleEvent` will be defined later.

# Test Strategy:
Start a local Redis instance. Write a simple test that publishes a message to `automation_channel` and verifies the worker's subscription mechanism correctly receives it (e.g., by logging the received message to console).
