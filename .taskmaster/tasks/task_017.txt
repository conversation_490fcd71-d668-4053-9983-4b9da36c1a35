# Task ID: 17
# Title: Multi-Provider AI Framework Core Implementation
# Status: done
# Dependencies: None
# Priority: high
# Description: Establish the foundational multi-provider AI framework, including abstraction and initial provider integration.
# Details:
Create ProviderManager and AIOrchestrator components. Define provider interface and base classes. Implement OpenAI provider (GPT-3.5, GPT-4, GPT-4 Vision). Include basic dynamic configuration and health monitoring.

# Test Strategy:
Unit tests for provider abstraction, integration tests for OpenAI API calls, basic end-to-end test for text-to-action conversion.

# Subtasks:
## 1. Initialize Project & Install Core Dependencies [done]
### Dependencies: None
### Description: Set up the basic project structure (e.g., Node.js/Express, Python/Flask) and install fundamental packages required for a web application and authentication (e.g., web framework, database driver, bcrypt, jsonwebtoken).
### Details:
Create a new project directory, initialize version control (git), set up a basic server entry point (e.g., `app.js` or `main.py`), and install core dependencies like Express/Flask, a database client (e.g., `pg`, `mysql2`), `bcrypt` for password hashing, and `jsonwebtoken` for token management.

## 2. Design & Implement Database Schema for Users [done]
### Dependencies: 17.1
### Description: Define and create the necessary database tables for storing user information, including fields for email, hashed password, and timestamps.
### Details:
Design the `users` table with columns such as `id` (primary key), `email` (unique, indexed), `password_hash`, `created_at`, and `updated_at`. Use SQL migration scripts or an ORM to apply this schema to the development database.

## 3. Develop Password Hashing & Verification Module [done]
### Dependencies: 17.1
### Description: Create a dedicated utility module responsible for securely hashing user passwords before storage and verifying provided passwords against stored hashes.
### Details:
Implement two core functions: `hashPassword(password)` which takes a plain-text password and returns its bcrypt hash, and `verifyPassword(password, hashedPassword)` which compares a plain-text password with a stored hash, returning true or false. Ensure appropriate salt rounds are used for bcrypt.

## 4. Implement User Registration Endpoint [done]
### Dependencies: 17.1, 17.2, 17.3
### Description: Develop the API endpoint (`/api/register`) that allows new users to create an account by providing an email and password.
### Details:
Create a POST endpoint `/api/register`. Validate incoming data (email format, password strength). Use the password hashing module (Subtask 3) to hash the password. Store the new user's email and hashed password in the `users` table (Subtask 2). Handle potential errors like duplicate email addresses.

## 5. Implement JWT Generation & Verification Module [done]
### Dependencies: 17.1
### Description: Create a utility module for generating JSON Web Tokens (JWTs) upon successful login and for verifying incoming JWTs to authenticate requests.
### Details:
Implement functions `generateToken(payload)` which signs a payload (e.g., user ID) into a JWT with a secret key and expiration time, and `verifyToken(token)` which decodes and verifies a JWT, returning the payload if valid or throwing an error if invalid/expired.

## 6. Implement User Login Endpoint [done]
### Dependencies: 17.1, 17.2, 17.3, 17.5
### Description: Develop the API endpoint (`/api/login`) that authenticates users based on their email and password, returning a JWT upon successful login.
### Details:
Create a POST endpoint `/api/login`. Retrieve the user from the database by email. Use the password verification module (Subtask 3) to compare the provided password with the stored hash. If credentials are valid, use the JWT generation module (Subtask 5) to create and return an authentication token.

## 7. Develop Authentication Middleware for Protected Routes [done]
### Dependencies: 17.5, 17.6
### Description: Create a middleware function that can be applied to API routes to ensure only authenticated users with valid JWTs can access them.
### Details:
The middleware should extract the JWT from the `Authorization` header (e.g., 'Bearer TOKEN'), use the JWT verification module (Subtask 5) to validate it, and if valid, attach the decoded user information (e.g., user ID) to the request object (`req.user`). If the token is missing or invalid, return a 401 Unauthorized response.

## 8. Implement User Logout Functionality [done]
### Dependencies: 17.6, 17.7
### Description: Develop an API endpoint (`/api/logout`) that allows users to invalidate their current session/JWT, effectively logging them out.
### Details:
Create a POST endpoint `/api/logout`. While JWTs are stateless, for a more robust logout, consider implementing a token blacklist or revoking mechanism (e.g., storing invalidated tokens in a temporary cache/DB). Alternatively, simply instruct the client to discard the token. Ensure the endpoint is protected by the authentication middleware (Subtask 7) to identify the user logging out.

