# Task ID: 4
# Title: Develop Playwright-based Web Automation Executor
# Status: done
# Dependencies: 1
# Priority: medium
# Description: Implement the `WebAutomator` module in `Automation.Web` to execute a list of `Action` objects using `Microsoft.Playwright` for web automation tasks.
# Details:
Add `Microsoft.Playwright` NuGet package to `Automation.Web`. Implement `WebAutomator.executeActions (actions: Action list) : TaskResult`. Inside `executeActions`, initialize a headless browser instance: `use! playwright = Playwright.CreateAsync()`, `use! browser = playwright.Chromium.LaunchAsync()`, `use! page = browser.NewPageAsync()`. Implement logic to interpret `Action` types (e.g., "navigate", "type", "click") and call corresponding Playwright methods (`page.GotoAsync`, `page.FillAsync`, `page.ClickAsync`). Ensure browser sessions are properly closed using `use!` or `DisposeAsync()`.

# Test Strategy:
Create a simple local web page for testing. Write integration tests that pass a list of web-specific `Action` objects to `WebAutomator.executeActions` and assert that the actions are performed correctly on the mock page (e.g., text input is filled, button is clicked, navigation is successful).
