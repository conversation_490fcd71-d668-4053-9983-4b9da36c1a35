# Task ID: 3
# Title: Implement Natural Language to Action Conversion with AI
# Status: done
# Dependencies: 1
# Priority: medium
# Description: Create the `AIProcessor` module within `Automation.AI` responsible for sending natural language commands to an AI API (e.g., OpenAI) and parsing the JSON response into a list of `Action` objects.
# Details:
Add `FSharp.Data` NuGet package to `Automation.AI`. Implement a function `AIProcessor.processCommand (command: string) : Action list`. This function should make an HTTP request to the AI API endpoint (e.g., OpenAI's completions API). Parse the JSON response using `FSharp.Data.JsonValue` or similar to extract the sequence of actions and map them to `Automation.Core.Action` types.

# Test Strategy:
Mock the AI API response to simulate various action sequences. Test `AIProcessor.processCommand` with different natural language inputs to ensure correct JSON parsing and accurate `Action` object creation. Verify edge cases like empty or malformed responses.
