# Task ID: 5
# Title: Develop Appium-based Mobile Automation Executor
# Status: in-progress
# Dependencies: 1
# Priority: low
# Description: Implement the `MobileAutomator` module in `Automation.Mobile` to execute a list of `Action` objects using `Appium.WebDriver` for mobile application automation tasks.
# Details:
Add `Appium.WebDriver` NuGet package to `Automation.Mobile`. Implement `MobileAutomator.executeActions (actions: Action list) : TaskResult`. Inside `executeActions`, initialize an Appium driver: `let driver = new AndroidDriver<AppiumWebElement>(new Uri("http://127.0.0.1:4723/wd/hub"), appiumOptions)`. Implement logic to interpret `Action` types (e.g., "tap", "sendKeys", "scroll") and call corresponding Appium driver methods. Ensure driver sessions are properly quit using `driver.Quit()`.

# Test Strategy:
Set up an Appium server and a mobile emulator/device. Write integration tests that pass a list of mobile-specific `Action` objects to `MobileAutomator.executeActions` and assert that the actions are performed correctly on the emulator/device (e.g., element is tapped, text is entered, screen is scrolled).

# Subtasks:
## 1. Add Appium NuGet packages and project reference [done]
### Dependencies: None
### Description: Add Appium.WebDriver and Selenium.WebDriver as dependencies in Automation.Mobile fsproj and ensure it compiles.
### Details:


## 2. Implement basic MobileTaskExecutor [pending]
### Dependencies: None
### Description: Create MobileTaskExecutor implementing ITaskExecutor; supports Navigate, Tap, TypeText actions via Appium driver.
### Details:


## 3. Resource cleanup and error handling [pending]
### Dependencies: None
### Description: Ensure driver.Quit() and session disposal in finally; wrap actions in try/with and return Failure on exception.
### Details:


## 4. Integrate Mobile executor into Worker [pending]
### Dependencies: None
### Description: Update Program.fs to select executor based on message target (mobile vs web) and apply concurrency/circuit logic.
### Details:


