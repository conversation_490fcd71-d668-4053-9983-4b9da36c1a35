# Task ID: 14
# Title: QoS & Circuit Breaker
# Status: done
# Dependencies: 8, 9
# Priority: medium
# Description: Limit concurrency, implement exponential back-off on AI stalls, and fallback to dead-letter queue.
# Details:
limits: maxWorkers 4, maxRetries 3; circuit: latency>2s or 404>5x

# Test Strategy:


# Subtasks:
## 1. SemaphoreSlim concurrency limiting [done]
### Dependencies: None
### Description: Limit concurrent message processing to 3 using SemaphoreSlim.
### Details:


## 2. Exponential backoff retry [done]
### Dependencies: None
### Description: Implement retry logic with exponential backoff (base 500ms, max 3 retries).
### Details:


## 3. Circuit breaker logic [done]
### Dependencies: None
### Description: Stop processing new messages and open circuit when 5 consecutive execution failures occur within 1 minute; half-open after cooldown.
### Details:


## 4. Metrics tracking [done]
### Dependencies: None
### Description: Collect and expose counts for successes, failures, retries, DLQ pushes.
### Details:


## 5. Configuration parameters [done]
### Dependencies: None
### Description: Make concurrency limit, backoff parameters and circuit breaker thresholds configurable via environment variables.
### Details:


