# Task ID: 8
# Title: Enable Asynchronous Event Processing and Concurrency Control
# Status: done
# Dependencies: 7
# Priority: medium
# Description: Modify the worker to process multiple Redis events concurrently using F# `Async` or .NET `Task`, and optionally implement a semaphore to limit the number of simultaneous automation tasks.
# Details:
Change `handleEvent` to return an `Async<unit>` or `Task<unit>`. When subscribing to Redis, use `subscriber.Subscribe("automation_channel", fun _ msg -> Async.Start (handleEvent msg))` or similar to run handlers asynchronously. To limit concurrency, introduce a `System.Threading.SemaphoreSlim` and use `semaphore.WaitAsync()` before starting an automation task, and `semaphore.Release()` after completion.

# Test Strategy:
Publish multiple Redis messages in quick succession. Verify that the worker processes them concurrently (e.g., by observing interleaved log messages or by monitoring system resource usage). If a semaphore is used, test that the number of concurrently running automation tasks does not exceed the configured limit.
