# Task ID: 27
# Title: Document environment variables & metrics endpoint
# Status: pending
# Dependencies: 14
# Priority: low
# Description: Update README to include details of configuration environment variables (CONCURRENCY_LIMIT, MAX_RETRIES, etc.) and the new /metrics & /health endpoints.
# Details:
Add a section in root README listing all worker-related env vars with defaults. Provide example docker-compose environment settings. Describe metrics HTTP endpoint.

# Test Strategy:

