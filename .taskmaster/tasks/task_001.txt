# Task ID: 1
# Title: Initialize F# Project Structure and Core Module
# Status: done
# Dependencies: None
# Priority: high
# Description: Set up the F# solution, create the specified project folders (Automation.Core, Automation.AI, etc.), and define shared models and interfaces within Automation.Core.
# Details:
Create the `AutomationSolution` root folder. Inside `src`, create `Automation.Core`, `Automation.AI`, `Automation.Web`, `Automation.Mobile`, `Automation.Data`, `Automation.Utilities`, `Automation.Worker` as F# projects (e.g., `dotnet new classlib -lang F#` for libraries and `dotnet new console -lang F#` for the worker). Define `Action` and `TaskResult` records/types and `ITaskExecutor` interface in `Automation.Core`. Add `paket.dependencies` file for dependency management.

# Test Strategy:
Verify the project structure is correctly created and all F# projects compile without errors. Ensure `Automation.Core` models and interfaces are accessible from other projects.
