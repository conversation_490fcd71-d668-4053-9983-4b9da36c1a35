{"permissions": {"allow": ["<PERSON><PERSON>(task-master show:*)", "<PERSON><PERSON>(task-master list:*)", "<PERSON><PERSON>(task-master set-status:*)", "Bash(dotnet build)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(task-master:*)", "Bash(dotnet sln add:*)", "Bash(dotnet build:*)", "Bash(dotnet add:*)", "Bash(find:*)", "Bash(grep:*)", "mcp__Deep_Graph_MCP__folder-tree-structure", "mcp__Deep_Graph_MCP__docs-semantic-search", "mcp__Deep_Graph_MCP__nodes-semantic-search", "mcp__Deep_Graph_MCP__get-code"], "deny": []}}