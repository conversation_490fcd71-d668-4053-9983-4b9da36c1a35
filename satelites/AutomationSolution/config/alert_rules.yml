# Prometheus Alert Rules for Automation Solution

groups:
  - name: automation_worker_alerts
    rules:
      # High Error Rate
      - alert: HighErrorRate
        expr: rate(automation_tasks_failed_total[5m]) / rate(automation_tasks_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: automation-worker
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes"

      # Service Down
      - alert: ServiceDown
        expr: up{job="automation-worker"} == 0
        for: 1m
        labels:
          severity: critical
          service: automation-worker
        annotations:
          summary: "Automation Worker service is down"
          description: "Automation Worker service has been down for more than 1 minute"

      # High Memory Usage
      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes{job="automation-worker"} / 1024 / 1024 > 1500
        for: 5m
        labels:
          severity: warning
          service: automation-worker
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }}MB, which is above the 1.5GB threshold"

      # High CPU Usage
      - alert: HighCPUUsage
        expr: rate(process_cpu_seconds_total{job="automation-worker"}[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: automation-worker
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}% for the last 5 minutes"

      # Circuit Breaker Open
      - alert: CircuitBreakerOpen
        expr: automation_circuit_breaker_state{job="automation-worker"} == 2
        for: 1m
        labels:
          severity: warning
          service: automation-worker
        annotations:
          summary: "Circuit breaker is open"
          description: "Circuit breaker has been open for more than 1 minute"

      # Task Queue Backlog
      - alert: TaskQueueBacklog
        expr: automation_task_queue_size{job="automation-worker"} > 100
        for: 5m
        labels:
          severity: warning
          service: automation-worker
        annotations:
          summary: "Task queue backlog detected"
          description: "Task queue has {{ $value }} pending tasks"

  - name: redis_alerts
    rules:
      # Redis Down
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
          service: redis
        annotations:
          summary: "Redis service is down"
          description: "Redis service has been down for more than 1 minute"

      # High Redis Memory Usage
      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes{job="redis"} / redis_memory_max_bytes{job="redis"} > 0.9
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis high memory usage"
          description: "Redis memory usage is {{ $value | humanizePercentage }}"

      # Redis Connection Issues
      - alert: RedisConnectionIssues
        expr: rate(redis_rejected_connections_total{job="redis"}[5m]) > 0
        for: 2m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis connection issues detected"
          description: "Redis is rejecting connections at a rate of {{ $value }} per second"

  - name: system_alerts
    rules:
      # Disk Space Low
      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"}) * 100 < 10
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "Low disk space"
          description: "Disk space is {{ $value }}% available"

      # High Load Average
      - alert: HighLoadAverage
        expr: node_load1 > 2
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High load average"
          description: "Load average is {{ $value }} for the last minute"
