# Prometheus Configuration for Automation Solution
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'automation-solution'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  - "alert_rules.yml"

# Scrape configuration
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # Automation Worker Service
  - job_name: 'automation-worker'
    static_configs:
      - targets: ['automation-worker:8080']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # Automation Worker Detailed Metrics
  - job_name: 'automation-worker-detailed'
    static_configs:
      - targets: ['automation-worker:8080']
    scrape_interval: 30s
    metrics_path: /metrics/detailed
    scrape_timeout: 15s

  # Redis Exporter (if using redis_exporter)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    metrics_path: /metrics

  # Node Exporter (if monitoring host metrics)
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    metrics_path: /metrics

  # Docker Container Metrics (if using cAdvisor)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: /metrics

  # Nginx Metrics (if using nginx-prometheus-exporter)
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s
    metrics_path: /metrics

# Remote write configuration (for long-term storage)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint/api/v1/write"
#     basic_auth:
#       username: "username"
#       password: "password"

# Remote read configuration
# remote_read:
#   - url: "https://prometheus-remote-read-endpoint/api/v1/read"
#     basic_auth:
#       username: "username"
#       password: "password"
