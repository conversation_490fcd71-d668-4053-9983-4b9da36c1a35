{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.AspNetCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "/app/logs/automation-worker-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "fileSizeLimitBytes": 104857600, "rollOnFileSizeLimit": true, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "Worker": {"Redis": {"ConnectionString": "${REDIS_CONNECTION_STRING}", "TaskChannel": "automation_channel", "DeadLetterQueue": "automation_dlq", "CommandTimeout": 30, "ConnectTimeout": 10, "SyncTimeout": 10, "AsyncTimeout": 10, "ConnectRetry": 3, "KeepAlive": 180}, "Concurrency": {"MaxConcurrentTasks": 10, "TaskTimeoutMs": 600000, "EnableTaskCancellation": true, "TaskQueueCapacity": 1000}, "CircuitBreaker": {"Enabled": true, "FailureThreshold": 10, "FailureWindow": "00:02:00", "OpenDuration": "00:02:00", "HalfOpenTestRequests": 5}, "Retry": {"MaxAttempts": 3, "BaseDelay": "00:00:01", "MaxDelay": "00:00:30", "UseJitter": true, "JitterFactor": 0.2}, "HealthCheck": {"Enabled": true, "Port": 8080, "Path": "/health", "Timeout": "00:00:30"}, "Metrics": {"Enabled": true, "Port": 8080, "Path": "/metrics", "EnableDetailedMetrics": true}}, "WebAutomation": {"Headless": true, "DefaultTimeout": 30000, "NavigationTimeout": 60000, "ResourceManagement": {"MaxConcurrentBrowsers": 5, "BrowserIdleTimeoutMinutes": 5, "EnableBrowserPooling": true, "BrowserPoolSize": 3, "MaxBrowserMemoryMB": 512}, "LaunchOptions": {"Args": ["--no-sandbox", "--disable-dev-shm-usage", "--disable-gpu", "--disable-background-timer-throttling", "--disable-backgrounding-occluded-windows", "--disable-renderer-backgrounding", "--memory-pressure-off", "--max_old_space_size=4096"], "IgnoreDefaultArgs": ["--enable-automation"], "SlowMo": 0}, "Screenshots": {"Enabled": true, "Path": "/app/screenshots", "Format": "png", "Quality": 80, "FullPage": false}}, "MobileAutomation": {"AppiumServerUrl": "http://localhost:4723", "DefaultTimeout": 30000, "ImplicitWait": 5000, "PageLoadTimeout": 60000, "ScriptTimeout": 30000, "ResourceManagement": {"MaxConcurrentSessions": 3, "SessionIdleTimeoutMinutes": 10, "EnableSessionPooling": true}, "Screenshots": {"Enabled": true, "Path": "/app/screenshots/mobile", "Format": "png"}}, "AIInfrastructure": {"HttpClient": {"DefaultTimeout": "00:00:30", "MaxRetryAttempts": 3, "ConnectionPoolSize": 10, "MaxConnectionsPerServer": 5}, "Caching": {"Enabled": true, "Provider": "Redis", "DefaultTtl": "00:30:00", "MaxCacheSize": "100MB"}}, "Security": {"EnableInputValidation": true, "MaxRequestSize": 10485760, "AllowedHosts": ["*"], "EnableCors": false, "TrustedProxies": ["**********/16", "**********/16"]}, "Performance": {"EnableCompression": true, "EnableResponseCaching": true, "MaxMemoryUsageMB": 2048, "GarbageCollectionMode": "Server"}}