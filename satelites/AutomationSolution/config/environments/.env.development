# =============================================================================
# Development Environment Configuration
# =============================================================================

# Application Configuration
ENVIRONMENT=Development
VERSION=dev

# Service Ports
WORKER_PORT=8080
REDIS_PORT=6379
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# Redis Configuration
REDIS_CONNECTION_STRING=redis-dev:6379

# Worker Configuration
MAX_CONCURRENT_TASKS=2
WORKER_TIMEOUT_MS=300000

# Security Configuration (Development only - not secure!)
GRAFANA_ADMIN_PASSWORD=admin123

# Resource Limits (Relaxed for development)
WORKER_MEMORY_LIMIT=1G
WORKER_CPU_LIMIT=0.5
REDIS_MEMORY_LIMIT=256M
REDIS_CPU_LIMIT=0.25

# Development Features
ENABLE_HOT_RELOAD=true
ENABLE_DEBUGGING=true
SHOW_BROWSER_UI=true

# Monitoring Configuration
PROMETHEUS_RETENTION_DAYS=7
GRAFANA_ENABLE_PLUGINS=false

# Logging Configuration
LOG_LEVEL=Debug
LOG_RETENTION_DAYS=7
LOG_MAX_FILE_SIZE=50MB

# Development URLs
AUTOMATION_WORKER_URL=http://localhost:8080
REDIS_COMMANDER_URL=http://localhost:8081
PROMETHEUS_URL=http://localhost:9090
GRAFANA_URL=http://localhost:3000
