# =============================================================================
# Production Environment Configuration
# =============================================================================

# Application Configuration
ENVIRONMENT=Production
VERSION=1.0.0

# Service Ports
WORKER_PORT=8080
REDIS_PORT=6379
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
HTTP_PORT=80
HTTPS_PORT=443

# Redis Configuration
REDIS_CONNECTION_STRING=redis:6379

# Worker Configuration
MAX_CONCURRENT_TASKS=10
WORKER_TIMEOUT_MS=600000

# Security Configuration
GRAFANA_ADMIN_PASSWORD=SecurePassword123!

# Resource Limits
WORKER_MEMORY_LIMIT=2G
WORKER_CPU_LIMIT=1.0
REDIS_MEMORY_LIMIT=512M
REDIS_CPU_LIMIT=0.5

# Monitoring Configuration
PROMETHEUS_RETENTION_DAYS=30
GRAFANA_ENABLE_PLUGINS=true

# SSL Configuration (if using HTTPS)
# SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
# SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Logging Configuration
LOG_LEVEL=Information
LOG_RETENTION_DAYS=30
LOG_MAX_FILE_SIZE=100MB
