# Prometheus Configuration for Automation Solution - Development
global:
  scrape_interval: 10s
  evaluation_interval: 10s
  external_labels:
    monitor: 'automation-solution'
    environment: 'development'

# Scrape configuration for development
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 15s

  # Automation Worker Service - Development
  - job_name: 'automation-worker-dev'
    static_configs:
      - targets: ['automation-worker-dev:8080']
    scrape_interval: 10s
    metrics_path: /metrics
    scrape_timeout: 5s

  # Automation Worker Health Checks
  - job_name: 'automation-worker-health'
    static_configs:
      - targets: ['automation-worker-dev:8080']
    scrape_interval: 5s
    metrics_path: /health
    scrape_timeout: 3s
