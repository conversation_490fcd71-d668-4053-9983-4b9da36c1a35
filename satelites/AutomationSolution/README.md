# Automation Solution - AI-Powered Browser Automation

A sophisticated, scalable automation framework built with F# that provides AI-powered browser automation, load balancing, and cost optimization.

## 🚀 Features

- **AI-Powered Automation**: Integrates with OpenAI, Anthropic, and Google Gemini for intelligent automation
- **Horizontal Scaling**: Auto-scaling worker instances based on load
- **Cost Optimization**: ML-based provider selection and resource optimization
- **Real-time Monitoring**: Prometheus metrics and Grafana dashboards
- **Circuit Breaker Pattern**: Fault tolerance and resilience
- **Load Balancing**: Multiple strategies (Round Robin, Least Connections, Health-aware)
- **Docker Support**: Full containerization with multi-stage builds
- **CI/CD Pipeline**: GitHub Actions with testing, security scanning, and deployment

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Nginx       │    │   Dispatcher    │    │   Coordinator   │
│  Load Balancer  │◄──►│    Service      │◄──►│    Service      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Worker Pool   │    │     Redis       │    │   Prometheus    │
│  (Auto-scaling) │◄──►│  Coordination   │    │   Monitoring    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🛠️ Prerequisites

- .NET 8.0 SDK
- Docker & Docker Compose
- Redis (for coordination)
- Node.js (for browser automation)

## ⚡ Quick Start

### Using Docker Compose (Recommended)

1. **Clone and build**:
   ```bash
   git clone <repository-url>
   cd AutomationSolution
   ```

2. **Set environment variables**:
   ```bash
   export OPENAI_API_KEY="your-openai-key"
   export ANTHROPIC_API_KEY="your-anthropic-key"
   export GOOGLE_AI_API_KEY="your-google-ai-key"
   ```

3. **Start all services**:
   ```bash
   docker-compose up -d
   ```

4. **Verify deployment**:
   ```bash
   curl http://localhost/health
   ```

### Manual Development Setup

1. **Restore dependencies**:
   ```bash
   dotnet restore AutomationSolution.sln
   ```

2. **Build solution**:
   ```bash
   dotnet build AutomationSolution.sln --configuration Release
   ```

3. **Run tests**:
   ```bash
   dotnet test AutomationSolution.sln --configuration Release
   ```

4. **Start Redis**:
   ```bash
   docker run -d -p 6379:6379 redis:7-alpine
   ```

5. **Run services**:
   ```bash
   # Terminal 1 - Dispatcher
   cd src/Automation.Dispatcher
   dotnet run

   # Terminal 2 - Worker
   cd src/Automation.Worker
   dotnet run
   ```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `REDIS_CONNECTION_STRING` | Redis connection string | `localhost:6379` | Yes |
| `OPENAI_API_KEY` | OpenAI API key | - | Yes |
| `ANTHROPIC_API_KEY` | Anthropic API key | - | Yes |
| `GOOGLE_AI_API_KEY` | Google AI API key | - | Yes |
| `CONCURRENCY_LIMIT` | Max concurrent tasks per worker | `10` | No |
| `MAX_RETRIES` | Maximum retry attempts | `3` | No |
| `REQUEST_TIMEOUT` | Request timeout in milliseconds | `30000` | No |
| `AUTO_SCALING_ENABLED` | Enable auto-scaling | `true` | No |
| `MIN_INSTANCES` | Minimum worker instances | `2` | No |
| `MAX_INSTANCES` | Maximum worker instances | `10` | No |
| `SCALE_UP_THRESHOLD` | CPU threshold for scaling up | `0.8` | No |
| `SCALE_DOWN_THRESHOLD` | CPU threshold for scaling down | `0.3` | No |

### Performance Tuning

The system is optimized for:
- **Simple commands**: < 2 seconds response time
- **Complex workflows**: < 10 seconds response time
- **High availability**: 99.9% uptime target
- **Cost efficiency**: Dynamic provider selection based on cost/performance

## 📊 Monitoring

### Metrics Endpoints

- **Health Check**: `GET /health`
- **Metrics**: `GET /metrics` (Prometheus format)
- **Metrics JSON**: `GET /metrics/json`

### Grafana Dashboards

Access Grafana at `http://localhost:3000` (admin/admin):

1. **System Overview**: Overall system health and performance
2. **Worker Performance**: Individual worker metrics and scaling
3. **Cost Analysis**: AI provider costs and optimization
4. **Error Tracking**: Error rates and patterns

### Key Metrics

- `automation_tasks_total`: Total tasks processed
- `automation_tasks_duration_seconds`: Task execution time
- `automation_worker_count`: Active worker instances
- `automation_ai_provider_cost`: Cost per AI provider
- `automation_error_rate`: Error rate percentage

## 🔐 Security

### Features

- **API Key Management**: Secure credential storage
- **Rate Limiting**: Request throttling per endpoint
- **Input Validation**: Comprehensive input sanitization
- **Circuit Breaker**: Prevents cascade failures
- **Audit Logging**: Complete operation audit trail

### Security Scanning

The CI pipeline includes:
- Dependency vulnerability scanning
- Static code analysis
- Container image security scanning

## 🚀 Deployment

### CI/CD Pipeline

The GitHub Actions pipeline includes:

1. **Test Stage**: Unit and integration tests
2. **Security Stage**: Vulnerability and dependency scanning
3. **Build Stage**: Multi-platform Docker images
4. **Integration Stage**: End-to-end testing
5. **Deploy Stage**: Staging and production deployment

### Production Deployment

The solution includes comprehensive production deployment guides and tooling:

#### **Deployment Options**

1. **Kubernetes** (recommended):
   ```bash
   kubectl apply -f k8s/production/
   ```

2. **Docker Swarm**:
   ```bash
   docker stack deploy -c docker-compose.prod.yml automation
   ```

3. **Manual deployment**:
   ```bash
   docker-compose -f docker-compose.yml up -d
   ```

4. **Systemd Service** (Linux):
   ```bash
   # Build and deploy C# worker service
   dotnet publish src/Automation.Worker.CSharp -c Release -o ./publish
   sudo cp automation-worker.service /etc/systemd/system/
   sudo systemctl enable automation-worker
   sudo systemctl start automation-worker
   ```

#### **Production Documentation**

- **[Production Deployment Guide](docs/PRODUCTION_DEPLOYMENT.md)**: Comprehensive deployment instructions
- **[Testing Guide](docs/TESTING_GUIDE.md)**: Complete testing strategy and execution
- **[Performance Optimization Guide](docs/PERFORMANCE_OPTIMIZATION.md)**: Performance tuning and optimization

## 🧪 Testing

### Test Categories

- **Unit Tests**: Individual component testing
- **Integration Tests**: End-to-end workflow validation with Redis containers
- **Performance Tests**: Load testing and benchmarking with throughput validation
- **Security Tests**: Input validation, resource limits, and security compliance
- **Production Readiness**: Configuration validation and deployment verification

### Comprehensive Test Suite

The solution includes a robust C# integration test suite (`tests/Automation.Integration.Tests/`) featuring:

- **Container-based Testing**: Automatic Redis test containers using Testcontainers
- **End-to-End Workflows**: Complete task processing pipeline validation
- **Performance Benchmarking**: Load testing with 50+ concurrent tasks
- **Security Validation**: Malicious input handling and resource protection
- **Production Readiness**: Configuration, health checks, and monitoring validation

### Running Tests

```bash
# All tests including integration tests
dotnet test

# Run specific test categories
dotnet test --filter "Category=Integration"
dotnet test --filter "Category=Performance"
dotnet test --filter "Category=Security"
dotnet test --filter "Category=ProductionReadiness"

# Integration tests with detailed output
dotnet test tests/Automation.Integration.Tests/ --logger "console;verbosity=detailed"

# With coverage reporting
dotnet test --collect:"XPlat Code Coverage"
```

### Test Performance Targets

- **Task Processing**: < 10s average response time
- **Throughput**: > 10 tasks/second concurrent processing
- **Success Rate**: > 95% under normal load
- **Memory Usage**: < 50MB increase over 100 tasks

## 📈 Scaling

### Auto-scaling Configuration

Workers automatically scale based on:
- CPU utilization (target: 70%)
- Queue depth (target: < 10 pending tasks)
- Response time (target: < 5 seconds)

### Manual Scaling

```bash
# Scale workers to 5 instances
docker-compose up -d --scale worker=5

# Kubernetes scaling
kubectl scale deployment worker --replicas=5
```

## 🐛 Troubleshooting

### Common Issues

1. **Redis Connection Errors**:
   ```bash
   # Check Redis status
   docker logs automation-redis
   redis-cli ping
   ```

2. **Worker Registration Issues**:
   ```bash
   # Check worker logs
   docker logs automation-worker
   # Verify Redis connectivity
   ```

3. **High Memory Usage**:
   ```bash
   # Monitor memory usage
   docker stats
   # Adjust worker limits in docker-compose.yml
   ```

### Performance Issues

1. **Slow Response Times**:
   - Check AI provider status
   - Monitor worker utilization
   - Review cache hit rates

2. **High Error Rates**:
   - Check circuit breaker status
   - Review AI provider quotas
   - Validate input data

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

### Development Guidelines

- Follow F# coding conventions
- Add unit tests for new features
- Update documentation
- Ensure CI pipeline passes

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Documentation**: Check this README and inline code comments
- **Issues**: Create GitHub issues for bugs and feature requests
- **Discussions**: Use GitHub Discussions for questions

---

**Built with ❤️ using F#, .NET, Docker, and modern cloud technologies**
