using Microsoft.Playwright;
using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;

public class RealAppTestResult
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TestName { get; set; } = string.Empty;
    public string AppName { get; set; } = string.Empty;
    public string AppUrl { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string DeviceType { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string Status { get; set; } = "Running";
    public string? ErrorMessage { get; set; }
    public List<string> Screenshots { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public List<string> TestSteps { get; set; } = new();
    public List<Dictionary<string, object>> AppFeatures { get; set; } = new();
}

class RealAppTestRunner 
{
    public static async Task<RealAppTestResult> RunRealAppTest(string appName, string appUrl, string deviceType, string? testName = null)
    {
        testName ??= $"Real {appName} App Test";
        
        var testResult = new RealAppTestResult
        {
            TestName = testName,
            AppName = appName,
            AppUrl = appUrl,
            Platform = deviceType.Contains("iPhone") ? "iOS" : "Android",
            DeviceType = deviceType,
            StartTime = DateTime.UtcNow
        };

        try 
        {
            Console.WriteLine($"📱 Starting REAL app test: {testName}");
            Console.WriteLine($"📋 Test ID: {testResult.Id}");
            Console.WriteLine($"🌐 App: {appName}");
            Console.WriteLine($"📱 Device: {deviceType}");
            Console.WriteLine($"🔗 URL: {appUrl}");
            
            Environment.SetEnvironmentVariable("PLAYWRIGHT_BROWSERS_PATH", "/ms-playwright");
            
            using var playwright = await Playwright.CreateAsync();
            Console.WriteLine("✅ Playwright initialized!");
            
            // Get device configuration
            var device = playwright.Devices[deviceType];
            
            await using var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions 
            { 
                Headless = true,
                Args = new[] { "--no-sandbox", "--disable-dev-shm-usage" }
            });
            
            // Create context with real device emulation
            await using var context = await browser.NewContextAsync(new BrowserNewContextOptions
            {
                ViewportSize = device.ViewportSize,
                UserAgent = device.UserAgent,
                DeviceScaleFactor = device.DeviceScaleFactor,
                IsMobile = device.IsMobile,
                HasTouch = device.HasTouch,
                Locale = "en-US",
                TimezoneId = "America/New_York"
            });
            
            var page = await context.NewPageAsync();
            Console.WriteLine($"✅ Real app context created: {device.ViewportSize?.Width}x{device.ViewportSize?.Height}");
            testResult.TestSteps.Add("Real app context initialized successfully");
            
            // Navigate to real app
            Console.WriteLine($"🚀 Launching real app: {appUrl}");
            await page.GotoAsync(appUrl, new PageGotoOptions 
            { 
                WaitUntil = WaitUntilState.NetworkIdle,
                Timeout = 30000
            });
            
            // Wait for app to fully load
            await Task.Delay(3000);
            Console.WriteLine("✅ Real app launched successfully!");
            testResult.TestSteps.Add("Real app launched and loaded");
            
            // Take initial screenshot
            var screenshotDir = "/tmp/screenshots";
            Directory.CreateDirectory(screenshotDir);
            
            var initialScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-real-{appName.ToLower()}-initial.png");
            await page.ScreenshotAsync(new PageScreenshotOptions
            {
                Path = initialScreenshot,
                FullPage = true
            });
            testResult.Screenshots.Add(initialScreenshot);
            Console.WriteLine($"📸 Initial real app screenshot captured");
            
            // Analyze app structure
            var title = await page.TitleAsync();
            var url = page.Url;
            
            // Check for PWA features
            var hasServiceWorker = await page.EvaluateAsync<bool>("() => 'serviceWorker' in navigator");
            var hasManifest = await page.Locator("link[rel='manifest']").CountAsync() > 0;
            var hasAppleTouchIcon = await page.Locator("link[rel='apple-touch-icon']").CountAsync() > 0;
            var hasViewportMeta = await page.Locator("meta[name='viewport']").CountAsync() > 0;
            
            testResult.Metadata["title"] = title;
            testResult.Metadata["finalUrl"] = url;
            testResult.Metadata["hasServiceWorker"] = hasServiceWorker;
            testResult.Metadata["hasManifest"] = hasManifest;
            testResult.Metadata["hasAppleTouchIcon"] = hasAppleTouchIcon;
            testResult.Metadata["hasViewportMeta"] = hasViewportMeta;
            testResult.Metadata["isPWA"] = hasServiceWorker && hasManifest;
            
            Console.WriteLine($"📄 App title: {title}");
            Console.WriteLine($"🔧 PWA features: SW={hasServiceWorker}, Manifest={hasManifest}");
            
            // App-specific tests based on app name
            if (appName.ToLower().Contains("wikipedia"))
            {
                await RunWikipediaTests(page, testResult, screenshotDir);
            }
            else if (appName.ToLower().Contains("twitter"))
            {
                await RunTwitterTests(page, testResult, screenshotDir);
            }
            else if (appName.ToLower().Contains("github"))
            {
                await RunGitHubTests(page, testResult, screenshotDir);
            }
            else
            {
                await RunGenericAppTests(page, testResult, screenshotDir);
            }
            
            // Final app analysis
            var linkCount = await page.Locator("a").CountAsync();
            var buttonCount = await page.Locator("button").CountAsync();
            var inputCount = await page.Locator("input").CountAsync();
            var imageCount = await page.Locator("img").CountAsync();
            
            testResult.Metadata["linkCount"] = linkCount;
            testResult.Metadata["buttonCount"] = buttonCount;
            testResult.Metadata["inputCount"] = inputCount;
            testResult.Metadata["imageCount"] = imageCount;
            testResult.Metadata["totalTestSteps"] = testResult.TestSteps.Count;
            testResult.Metadata["successfulSteps"] = testResult.TestSteps.Count(s => s.Contains("successfully"));
            testResult.Metadata["failedSteps"] = testResult.TestSteps.Count(s => s.Contains("failed"));
            
            Console.WriteLine($"📊 App elements: {linkCount} links, {buttonCount} buttons, {inputCount} inputs, {imageCount} images");
            Console.WriteLine($"✅ Test steps completed: {testResult.TestSteps.Count}");
            Console.WriteLine($"✅ Successful steps: {testResult.Metadata["successfulSteps"]}");
            Console.WriteLine($"❌ Failed steps: {testResult.Metadata["failedSteps"]}");
            
            // Mark test as passed
            testResult.Status = "Passed";
            testResult.EndTime = DateTime.UtcNow;
            
            Console.WriteLine($"🎉 Real {appName} app test completed successfully!");
        } 
        catch (Exception ex) 
        {
            testResult.Status = "Failed";
            testResult.ErrorMessage = ex.Message;
            testResult.EndTime = DateTime.UtcNow;
            testResult.TestSteps.Add($"Test failed with error: {ex.Message}");
            
            Console.WriteLine($"❌ Real {appName} app test failed: {ex.Message}");
        }
        
        // Save test results to JSON
        var resultsDir = "/tmp/test-results";
        Directory.CreateDirectory(resultsDir);
        
        var resultPath = Path.Combine(resultsDir, $"{testResult.Id}.json");
        var json = JsonSerializer.Serialize(testResult, new JsonSerializerOptions 
        { 
            WriteIndented = true 
        });
        
        await File.WriteAllTextAsync(resultPath, json);
        Console.WriteLine($"💾 Real app test results saved: {resultPath}");
        
        return testResult;
    }

    static async Task RunWikipediaTests(IPage page, RealAppTestResult testResult, string screenshotDir)
    {
        Console.WriteLine("🧪 Running Wikipedia-specific tests...");
        
        try
        {
            // Test 1: Search functionality
            Console.WriteLine("🔍 Testing Wikipedia search...");
            var searchInput = await page.WaitForSelectorAsync("input[type='search'], input[placeholder*='Search'], #searchInput", new PageWaitForSelectorOptions { Timeout = 10000 });
            if (searchInput != null)
            {
                await searchInput.FillAsync("Artificial Intelligence");
                await searchInput.PressAsync("Enter");
                await Task.Delay(3000);
                
                var searchScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-wikipedia-search.png");
                await page.ScreenshotAsync(new PageScreenshotOptions { Path = searchScreenshot, FullPage = true });
                testResult.Screenshots.Add(searchScreenshot);
                
                testResult.TestSteps.Add("Wikipedia search test completed successfully");
                Console.WriteLine("✅ Wikipedia search test completed");
            }
        }
        catch (Exception ex)
        {
            testResult.TestSteps.Add($"Wikipedia search test failed: {ex.Message}");
            Console.WriteLine($"⚠️ Wikipedia search test failed: {ex.Message}");
        }

        try
        {
            // Test 2: Article navigation
            Console.WriteLine("📖 Testing article navigation...");
            var firstLink = await page.Locator("a[href*='/wiki/']").First.ElementHandleAsync();
            if (firstLink != null)
            {
                await firstLink.ClickAsync();
                await Task.Delay(2000);
                
                var articleScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-wikipedia-article.png");
                await page.ScreenshotAsync(new PageScreenshotOptions { Path = articleScreenshot, FullPage = true });
                testResult.Screenshots.Add(articleScreenshot);
                
                testResult.TestSteps.Add("Wikipedia article navigation completed successfully");
                Console.WriteLine("✅ Wikipedia article navigation completed");
            }
        }
        catch (Exception ex)
        {
            testResult.TestSteps.Add($"Wikipedia article navigation failed: {ex.Message}");
            Console.WriteLine($"⚠️ Wikipedia article navigation failed: {ex.Message}");
        }
    }

    static async Task RunTwitterTests(IPage page, RealAppTestResult testResult, string screenshotDir)
    {
        Console.WriteLine("🧪 Running Twitter-specific tests...");
        
        try
        {
            // Test Twitter interface
            var tweetButton = await page.Locator("button, a").Filter(new LocatorFilterOptions { HasText = "Tweet" }).CountAsync();
            if (tweetButton > 0)
            {
                var twitterScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-twitter-interface.png");
                await page.ScreenshotAsync(new PageScreenshotOptions { Path = twitterScreenshot, FullPage = true });
                testResult.Screenshots.Add(twitterScreenshot);
                
                testResult.TestSteps.Add("Twitter interface test completed successfully");
                Console.WriteLine("✅ Twitter interface test completed");
            }
        }
        catch (Exception ex)
        {
            testResult.TestSteps.Add($"Twitter interface test failed: {ex.Message}");
            Console.WriteLine($"⚠️ Twitter interface test failed: {ex.Message}");
        }
    }

    static async Task RunGitHubTests(IPage page, RealAppTestResult testResult, string screenshotDir)
    {
        Console.WriteLine("🧪 Running GitHub-specific tests...");
        
        try
        {
            // Test GitHub search
            var searchInput = await page.Locator("input[placeholder*='Search'], input[name='q']").First.ElementHandleAsync();
            if (searchInput != null)
            {
                await searchInput.FillAsync("playwright automation");
                await searchInput.PressAsync("Enter");
                await Task.Delay(3000);
                
                var githubScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-github-search.png");
                await page.ScreenshotAsync(new PageScreenshotOptions { Path = githubScreenshot, FullPage = true });
                testResult.Screenshots.Add(githubScreenshot);
                
                testResult.TestSteps.Add("GitHub search test completed successfully");
                Console.WriteLine("✅ GitHub search test completed");
            }
        }
        catch (Exception ex)
        {
            testResult.TestSteps.Add($"GitHub search test failed: {ex.Message}");
            Console.WriteLine($"⚠️ GitHub search test failed: {ex.Message}");
        }
    }

    static async Task RunGenericAppTests(IPage page, RealAppTestResult testResult, string screenshotDir)
    {
        Console.WriteLine("🧪 Running generic app tests...");
        
        try
        {
            // Test scroll behavior
            await page.EvaluateAsync("window.scrollTo(0, document.body.scrollHeight / 2)");
            await Task.Delay(1000);
            
            var scrollScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-generic-scroll.png");
            await page.ScreenshotAsync(new PageScreenshotOptions { Path = scrollScreenshot, FullPage = false });
            testResult.Screenshots.Add(scrollScreenshot);
            
            testResult.TestSteps.Add("Generic scroll test completed successfully");
            Console.WriteLine("✅ Generic scroll test completed");
        }
        catch (Exception ex)
        {
            testResult.TestSteps.Add($"Generic scroll test failed: {ex.Message}");
            Console.WriteLine($"⚠️ Generic scroll test failed: {ex.Message}");
        }
    }
}

class Program 
{
    static async Task Main(string[] args) 
    {
        var apps = new[]
        {
            new { Name = "Wikipedia", Url = "https://en.m.wikipedia.org", Device = "iPhone 13" },
            new { Name = "Wikipedia", Url = "https://en.m.wikipedia.org", Device = "Galaxy S8" },
            new { Name = "Twitter", Url = "https://mobile.twitter.com", Device = "iPhone 12" },
            new { Name = "GitHub", Url = "https://github.com", Device = "Galaxy S8" }
        };

        Console.WriteLine("🚀 Starting REAL APP TEST SUITE");
        Console.WriteLine($"📱 Testing {apps.Length} real apps on different devices\n");

        var results = new List<RealAppTestResult>();

        foreach (var app in apps)
        {
            try
            {
                var result = await RealAppTestRunner.RunRealAppTest(app.Name, app.Url, app.Device);
                results.Add(result);
                
                // Small delay between tests
                await Task.Delay(3000);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to test {app.Name} on {app.Device}: {ex.Message}");
            }
        }

        // Display final summary
        var totalTests = results.Count;
        var passedTests = results.Count(r => r.Status == "Passed");
        var failedTests = totalTests - passedTests;
        var totalScreenshots = results.Sum(r => r.Screenshots.Count);

        Console.WriteLine("\n🎯 REAL APP TEST SUITE SUMMARY:");
        Console.WriteLine($"   Total Apps Tested: {totalTests}");
        Console.WriteLine($"   ✅ Passed: {passedTests}");
        Console.WriteLine($"   ❌ Failed: {failedTests}");
        Console.WriteLine($"   📸 Screenshots: {totalScreenshots}");
        Console.WriteLine($"   📊 Success Rate: {(totalTests > 0 ? (double)passedTests / totalTests * 100 : 0):F1}%");

        if (failedTests == 0)
        {
            Console.WriteLine("\n🎉 ALL REAL APP TESTS PASSED! 📱🎉");
        }
        else
        {
            Console.WriteLine($"\n⚠️ {failedTests} real app test(s) failed.");
        }
    }
}
