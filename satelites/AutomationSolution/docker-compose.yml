version: '3.8'

services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    networks:
      - automation_network

  # Load balancer coordinator
  coordinator:
    build:
      context: .
      dockerfile: Dockerfile.coordinator
    environment:
      - REDIS_CONNECTION_STRING=redis:6379
      - WORKER_REGISTRATION_CHANNEL=worker_registration
      - TASK_DISTRIBUTION_CHANNEL=automation_channel
      - HEARTBEAT_INTERVAL_SECONDS=15
      - MIN_INSTANCES=2
      - MAX_INSTANCES=10
      - SCALE_UP_THRESHOLD=0.8
      - SCALE_DOWN_THRESHOLD=0.3
      - COOLDOWN_PERIOD_MINUTES=5
      - AUTO_SCALING_ENABLED=true
    depends_on:
      redis:
        condition: service_healthy
    ports:
      - "8081:8080"  # Metrics port
    networks:
      - automation_network
    restart: unless-stopped

  # Worker instances (can be scaled)
  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
    environment:
      - REDIS_CONNECTION_STRING=redis:6379
      - CONCURRENCY_LIMIT=3
      - MAX_RETRIES=3
      - BASE_BACKOFF_MS=500
      - FAILURE_THRESHOLD=5
      - FAILURE_WINDOW_SEC=60
      - CIRCUIT_OPEN_SEC=60
      - METRICS_PORT=8080
      - WORKER_ID=${HOSTNAME}
      - MAX_CAPACITY=5
      - HEARTBEAT_INTERVAL=15
    depends_on:
      redis:
        condition: service_healthy
      coordinator:
        condition: service_started
    ports:
      - "8080-8090:8080"  # Dynamic port mapping for multiple instances
    networks:
      - automation_network
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Task dispatcher
  dispatcher:
    build:
      context: .
      dockerfile: Dockerfile.dispatcher
    environment:
      - REDIS_CONNECTION_STRING=redis:6379
      - INPUT_CHANNEL=automation_channel
      - LEARN_CHANNEL=learn_channel
      - REPLAY_CHANNEL=replay_channel
      - ENABLE_LOGGING=true
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - automation_network
    restart: unless-stopped

  # Monitoring and metrics
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - automation_network

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - automation_network

volumes:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  automation_network:
    driver: bridge
