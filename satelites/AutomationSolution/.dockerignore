# =============================================================================
# Docker Ignore File for Automation Solution
# Excludes unnecessary files from Docker build context
# =============================================================================

# Build outputs
**/bin/
**/obj/
**/out/
**/publish/

# Test results
**/TestResults/
**/test-results/
**/*.trx
**/*.coverage
**/*.coveragexml

# IDE and editor files
.vs/
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore
.gitattributes

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentation
README.md
*.md
docs/

# Scripts (except those needed for container)
scripts/
*.sh

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
*.tmp

# Package managers
node_modules/
packages/
.nuget/

# Environment files
.env*
*.env

# Backup files
backups/
*.bak

# Screenshots and test data
screenshots/
test-data/

# Configuration files (will be mounted)
config/
appsettings.*.json

# SSL certificates
*.pem
*.key
*.crt
*.p12
*.pfx

# Database files
*.db
*.sqlite
*.mdf
*.ldf

# Cache directories
.cache/
.npm/
.yarn/

# Runtime directories
runtime/
runtimes/

# Legacy files
legacy/
old/
archive/

# Large files
*.iso
*.dmg
*.zip
*.tar.gz
*.rar

# IDE specific
*.suo
*.user
*.userosscache
*.sln.docstates
*.userprefs

# ReSharper
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user

# JetBrains Rider
.idea/
*.sln.iml

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Monitoring and metrics
prometheus_data/
grafana_data/

# Container volumes
redis_data/
automation_logs/
automation_screenshots/
