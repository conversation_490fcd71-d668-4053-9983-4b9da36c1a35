#!/bin/bash

# Automation Worker Scaling Script
# Usage: ./scale-workers.sh [up|down] [count]

set -e

COMPOSE_FILE="docker-compose.yml"
SERVICE_NAME="worker"

# Default values
ACTION="${1:-up}"
COUNT="${2:-1}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to get current number of worker instances
get_current_instances() {
    docker-compose ps -q $SERVICE_NAME | wc -l
}

# Function to scale up workers
scale_up() {
    local current_instances=$(get_current_instances)
    local new_instances=$((current_instances + COUNT))
    
    print_info "Scaling up workers from $current_instances to $new_instances instances"
    
    docker-compose up -d --scale $SERVICE_NAME=$new_instances
    
    if [ $? -eq 0 ]; then
        print_info "Successfully scaled up to $new_instances worker instances"
        
        # Wait a bit for services to be ready
        sleep 5
        
        # Show status
        docker-compose ps $SERVICE_NAME
        
        # Show load balancer metrics
        print_info "Load balancer metrics:"
        curl -s http://localhost:8081/metrics | grep -E "(total_workers|healthy_workers|current_load)"
    else
        print_error "Failed to scale up workers"
        exit 1
    fi
}

# Function to scale down workers
scale_down() {
    local current_instances=$(get_current_instances)
    local new_instances=$((current_instances - COUNT))
    
    # Ensure we don't go below 1 instance
    if [ $new_instances -lt 1 ]; then
        print_warning "Cannot scale down below 1 instance. Setting to 1."
        new_instances=1
    fi
    
    print_info "Scaling down workers from $current_instances to $new_instances instances"
    
    docker-compose up -d --scale $SERVICE_NAME=$new_instances
    
    if [ $? -eq 0 ]; then
        print_info "Successfully scaled down to $new_instances worker instances"
        
        # Wait a bit for services to be ready
        sleep 5
        
        # Show status
        docker-compose ps $SERVICE_NAME
        
        # Show load balancer metrics
        print_info "Load balancer metrics:"
        curl -s http://localhost:8081/metrics | grep -E "(total_workers|healthy_workers|current_load)"
    else
        print_error "Failed to scale down workers"
        exit 1
    fi
}

# Function to show current status
show_status() {
    local current_instances=$(get_current_instances)
    
    print_info "Current worker instances: $current_instances"
    
    echo ""
    print_info "Worker container status:"
    docker-compose ps $SERVICE_NAME
    
    echo ""
    print_info "Load balancer metrics:"
    curl -s http://localhost:8081/metrics 2>/dev/null | grep -E "(total_workers|healthy_workers|current_load|avg_processing_rate)" || print_warning "Could not fetch metrics from coordinator"
    
    echo ""
    print_info "Recent worker logs:"
    docker-compose logs --tail=10 $SERVICE_NAME
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [ACTION] [COUNT]"
    echo ""
    echo "Actions:"
    echo "  up [count]    - Scale up by count instances (default: 1)"
    echo "  down [count]  - Scale down by count instances (default: 1)"
    echo "  status        - Show current scaling status"
    echo "  help          - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 up 3       - Scale up by 3 worker instances"
    echo "  $0 down 2     - Scale down by 2 worker instances"
    echo "  $0 status     - Show current status"
}

# Function to check prerequisites
check_prerequisites() {
    if ! command -v docker-compose &> /dev/null; then
        print_error "docker-compose is not installed or not in PATH"
        exit 1
    fi
    
    if [ ! -f "$COMPOSE_FILE" ]; then
        print_error "Docker compose file '$COMPOSE_FILE' not found"
        exit 1
    fi
}

# Main script logic
main() {
    check_prerequisites
    
    case $ACTION in
        "up")
            scale_up
            ;;
        "down")
            scale_down
            ;;
        "status")
            show_status
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            print_error "Unknown action: $ACTION"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main
