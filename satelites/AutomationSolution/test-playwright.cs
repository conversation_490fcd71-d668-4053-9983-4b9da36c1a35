using Microsoft.Playwright;
using System;
using System.Threading.Tasks;

class Program
{
    static async Task Main(string[] args)
    {
        try
        {
            Console.WriteLine("Testing Playwright initialization...");
            
            // Set environment variables
            Environment.SetEnvironmentVariable("PLAYWRIGHT_BROWSERS_PATH", "/ms-playwright");
            Environment.SetEnvironmentVariable("PLAYWRIGHT_NODEJS_PATH", "/usr/bin/node");
            
            Console.WriteLine($"PLAYWRIGHT_BROWSERS_PATH: {Environment.GetEnvironmentVariable("PLAYWRIGHT_BROWSERS_PATH")}");
            Console.WriteLine($"PLAYWRIGHT_NODEJS_PATH: {Environment.GetEnvironmentVariable("PLAYWRIGHT_NODEJS_PATH")}");
            
            // Initialize Playwright
            using var playwright = await Playwright.CreateAsync();
            Console.WriteLine($"Playwright initialized successfully! Version: {playwright.Version}");
            
            // Launch browser
            using var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions
            {
                Headless = true
            });
            Console.WriteLine("Browser launched successfully!");
            
            // Create page and navigate
            var page = await browser.NewPageAsync();
            await page.GotoAsync("https://example.com");
            var title = await page.TitleAsync();
            Console.WriteLine($"Page title: {title}");
            
            Console.WriteLine("✅ Playwright test completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
}
