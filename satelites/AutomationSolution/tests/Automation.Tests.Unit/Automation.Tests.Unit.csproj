<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
    <RootNamespace>Automation.Tests.Unit</RootNamespace>
    <AssemblyName>Automation.Tests.Unit</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\Automation.Contracts\Automation.Contracts.csproj" />
    <ProjectReference Include="..\..\src\Automation.Core\Automation.Core.fsproj" />
    <ProjectReference Include="..\..\src\Automation.Data\Automation.Data.fsproj" />
    <ProjectReference Include="..\..\src\Automation.AI.Infrastructure\Automation.AI.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="BenchmarkDotNet" Version="0.13.12" />
  </ItemGroup>

</Project>