using Xunit;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Automation.Contracts;
using Automation.AI.Infrastructure;
using Automation.Tests.Unit.Infrastructure;

namespace Automation.Tests.Unit.AI.Infrastructure;

/// <summary>
/// Tests for the AI Infrastructure components
/// </summary>
public class AIInfrastructureTests : UnitTestBase
{
    protected override void ConfigureServices(IServiceCollection services)
    {
        base.ConfigureServices(services);
        
        // Configure AI Infrastructure services
        services.Configure<ResourcePoolOptions>(options =>
        {
            options.MinSize = 1;
            options.MaxSize = 3;
            options.DefaultTimeout = TimeSpan.FromSeconds(10);
        });
        
        services.Configure<PerformanceMonitorOptions>(options =>
        {
            options.EnableDebugLogging = true;
            options.FlushIntervalSeconds = 5;
        });
        
        services.AddSingleton<ResourcePoolManager>();
        services.AddSingleton<IPerformanceMonitor, PerformanceMonitor>();
    }

    [Fact]
    public async Task ResourcePoolManager_Should_CreateAndManageHttpClients()
    {
        // Arrange
        var resourceManager = GetService<ResourcePoolManager>();
        const string providerId = "test-provider";

        // Act
        var client1 = await resourceManager.GetHttpClientAsync(providerId);
        var client2 = await resourceManager.GetHttpClientAsync(providerId);

        // Assert
        client1.Should().NotBeNull();
        client2.Should().NotBeNull();
        client1.Should().NotBeSameAs(client2);

        // Cleanup
        await resourceManager.ReturnHttpClientAsync(providerId, client1);
        await resourceManager.ReturnHttpClientAsync(providerId, client2);
    }

    [Fact]
    public async Task ResourcePoolManager_Should_ReturnPoolMetrics()
    {
        // Arrange
        var resourceManager = GetService<ResourcePoolManager>();
        const string providerId = "test-provider";
        
        // Act
        var client = await resourceManager.GetHttpClientAsync(providerId);
        var metrics = await resourceManager.GetAllPoolMetricsAsync();

        // Assert
        metrics.Should().ContainKey(providerId);
        metrics[providerId].AcquiredResources.Should().Be(1);
        metrics[providerId].TotalAcquisitions.Should().Be(1);

        // Cleanup
        await resourceManager.ReturnHttpClientAsync(providerId, client);
    }

    [Fact]
    public async Task PerformanceMonitor_Should_RecordMetrics()
    {
        // Arrange
        var monitor = GetService<IPerformanceMonitor>();
        const string metricName = "test-metric";
        const double metricValue = 42.5;

        // Act
        monitor.RecordMetric(metricName, metricValue);
        
        // Give some time for processing
        await Task.Delay(100);
        
        var from = DateTime.UtcNow.AddMinutes(-1);
        var to = DateTime.UtcNow.AddMinutes(1);
        var aggregated = await monitor.GetAggregatedMetricsAsync(metricName, from, to);

        // Assert
        aggregated.MetricName.Should().Be(metricName);
        aggregated.Count.Should().Be(1);
        aggregated.Average.Should().Be(metricValue);
        aggregated.Minimum.Should().Be(metricValue);
        aggregated.Maximum.Should().Be(metricValue);
        aggregated.Sum.Should().Be(metricValue);
    }

    [Fact]
    public async Task PerformanceMonitor_Should_IncrementCounters()
    {
        // Arrange
        var monitor = GetService<IPerformanceMonitor>();
        const string counterName = "test-counter";

        // Act
        monitor.IncrementCounter(counterName, 5);
        monitor.IncrementCounter(counterName, 3);

        // Assert
        // Note: Counter values are not directly retrievable in this implementation
        // In a real scenario, you'd have a method to get counter values
        // For now, we just verify no exceptions are thrown
        await Task.CompletedTask;
    }

    [Fact]
    public async Task PerformanceMonitor_Should_RecordDuration()
    {
        // Arrange
        var monitor = GetService<IPerformanceMonitor>();
        const string durationName = "test-duration";
        var duration = TimeSpan.FromMilliseconds(500);

        // Act
        monitor.RecordDuration(durationName, duration);
        
        var from = DateTime.UtcNow.AddMinutes(-1);
        var to = DateTime.UtcNow.AddMinutes(1);
        var aggregated = await monitor.GetAggregatedMetricsAsync(durationName, from, to);

        // Assert
        aggregated.MetricName.Should().Be(durationName);
        aggregated.Count.Should().Be(1);
        aggregated.Average.Should().Be(duration.TotalMilliseconds);
    }

    [Fact]
    public async Task PerformanceMonitor_Should_ReportHealthStatus()
    {
        // Arrange
        var monitor = GetService<IPerformanceMonitor>();

        // Act
        var healthStatus = await monitor.CheckHealthAsync();

        // Assert
        healthStatus.Should().NotBeNull();
        healthStatus.IsHealthy.Should().BeTrue();
        healthStatus.Status.Should().Be("Healthy");
        healthStatus.ResponseTime.Should().BePositive();
        healthStatus.Data.Should().NotBeEmpty();
    }

    [Fact]
    public async Task Contracts_Should_HaveCorrectTaskResultFactory()
    {
        // Arrange & Act
        var successResult = TaskResult.Success("Operation completed successfully", TimeSpan.FromSeconds(1));
        var failureResult = TaskResult.Failure("Operation failed");
        var errorResult = TaskResult.Failure(new ActionExecutionError { Message = "Test error" });

        // Assert
        successResult.IsSuccess.Should().BeTrue();
        successResult.Message.Should().Be("Operation completed successfully");
        successResult.Duration.Should().Be(TimeSpan.FromSeconds(1));

        failureResult.IsSuccess.Should().BeFalse();
        failureResult.Message.Should().Be("Operation failed");

        errorResult.IsSuccess.Should().BeFalse();
        errorResult.Message.Should().Be("Test error");
        errorResult.Error.Should().NotBeNull();
        errorResult.Error!.Message.Should().Be("Test error");
    }
}

/// <summary>
/// Integration tests for the full stack
/// </summary>
public class HybridArchitectureIntegrationTests : UnitTestBase
{
    protected override void ConfigureServices(IServiceCollection services)
    {
        base.ConfigureServices(services);
        
        // Configure all services for integration testing
        services.Configure<ResourcePoolOptions>(options =>
        {
            options.MinSize = 1;
            options.MaxSize = 5;
            options.DefaultTimeout = TimeSpan.FromSeconds(30);
        });
        
        services.Configure<PerformanceMonitorOptions>(options =>
        {
            options.EnableDebugLogging = false;
            options.FlushIntervalSeconds = 10;
        });
        
        services.AddSingleton<ResourcePoolManager>();
        services.AddSingleton<IPerformanceMonitor, PerformanceMonitor>();
    }

    [Fact]
    public async Task HybridArchitecture_Should_IntegrateSuccessfully()
    {
        // Arrange
        var resourceManager = GetService<ResourcePoolManager>();
        var performanceMonitor = GetService<IPerformanceMonitor>();
        
        // Act
        var startTime = DateTime.UtcNow;
        
        // Test resource management
        var client = await resourceManager.GetHttpClientAsync("integration-test");
        performanceMonitor.RecordMetric("client-acquisition", 1);
        
        // Test performance monitoring
        var healthStatus = await performanceMonitor.CheckHealthAsync();
        performanceMonitor.RecordDuration("health-check", DateTime.UtcNow - startTime);
        
        // Test cleanup
        await resourceManager.ReturnHttpClientAsync("integration-test", client);
        performanceMonitor.IncrementCounter("cleanup-operations");
        
        // Assert
        healthStatus.IsHealthy.Should().BeTrue();
        
        var metrics = await resourceManager.GetAllPoolMetricsAsync();
        metrics.Should().ContainKey("integration-test");
        
        // Verify metrics were recorded
        var from = startTime.AddMinutes(-1);
        var to = DateTime.UtcNow.AddMinutes(1);
        var aggregated = await performanceMonitor.GetAggregatedMetricsAsync("client-acquisition", from, to);
        aggregated.Count.Should().Be(1);
    }
}