using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Automation.Contracts;

namespace Automation.Tests.Unit.Infrastructure;

/// <summary>
/// Base class for unit tests that provides common setup and utilities
/// </summary>
public abstract class UnitTestBase : IDisposable
{
    protected IServiceProvider ServiceProvider { get; private set; }
    protected ILogger Logger { get; private set; }
    
    private readonly ServiceCollection _services = new();
    private bool _disposed = false;

    protected UnitTestBase()
    {
        ConfigureServices(_services);
        ServiceProvider = _services.BuildServiceProvider();
        Logger = ServiceProvider.GetRequiredService<ILogger<UnitTestBase>>();
    }

    /// <summary>
    /// Override this method to configure test-specific services
    /// </summary>
    protected virtual void ConfigureServices(IServiceCollection services)
    {
        // Add logging
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Debug);
        });

        // Add common test services
        services.AddTransient<IPerformanceMonitor, MockPerformanceMonitor>();
    }

    /// <summary>
    /// Gets a service from the test service container
    /// </summary>
    protected T GetService<T>() where T : notnull
        => ServiceProvider.GetRequiredService<T>();

    /// <summary>
    /// Gets a service from the test service container or null if not found
    /// </summary>
    protected T? GetOptionalService<T>() where T : class
        => ServiceProvider.GetService<T>();

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                if (ServiceProvider is IDisposable disposableProvider)
                {
                    disposableProvider.Dispose();
                }
            }
            _disposed = true;
        }
    }

    public void Dispose()
    {
        Dispose(disposing: true);
        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// Mock implementation of IPerformanceMonitor for testing
/// </summary>
public class MockPerformanceMonitor : IPerformanceMonitor
{
    private readonly List<MetricRecord> _metrics = new();
    private readonly List<CounterRecord> _counters = new();
    private readonly List<DurationRecord> _durations = new();

    public void RecordMetric(string name, double value, IDictionary<string, object>? tags = null, DateTime? timestamp = null)
    {
        _metrics.Add(new MetricRecord(name, value, tags ?? new Dictionary<string, object>(), timestamp ?? DateTime.UtcNow));
    }

    public void IncrementCounter(string name, long increment = 1, IDictionary<string, object>? tags = null)
    {
        _counters.Add(new CounterRecord(name, increment, tags ?? new Dictionary<string, object>(), DateTime.UtcNow));
    }

    public void RecordDuration(string name, TimeSpan duration, IDictionary<string, object>? tags = null)
    {
        _durations.Add(new DurationRecord(name, duration, tags ?? new Dictionary<string, object>(), DateTime.UtcNow));
    }

    public Task<HealthStatus> CheckHealthAsync(CancellationToken cancellationToken = default)
    {
        return Task.FromResult(new HealthStatus
        {
            IsHealthy = true,
            Status = "Healthy",
            Description = "Mock performance monitor is always healthy",
            ResponseTime = TimeSpan.FromMilliseconds(1)
        });
    }

    public Task<AggregatedMetrics> GetAggregatedMetricsAsync(string metricName, DateTime from, DateTime to, CancellationToken cancellationToken = default)
    {
        var matchingMetrics = _metrics.Where(m => m.Name == metricName && m.Timestamp >= from && m.Timestamp <= to).ToList();
        
        if (matchingMetrics.Count == 0)
        {
            return Task.FromResult(new AggregatedMetrics
            {
                MetricName = metricName,
                FromTime = from,
                ToTime = to
            });
        }

        var values = matchingMetrics.Select(m => m.Value).ToList();
        return Task.FromResult(new AggregatedMetrics
        {
            MetricName = metricName,
            FromTime = from,
            ToTime = to,
            Average = values.Average(),
            Minimum = values.Min(),
            Maximum = values.Max(),
            Sum = values.Sum(),
            Count = values.Count,
            StandardDeviation = CalculateStandardDeviation(values),
            Percentile95 = CalculatePercentile(values, 0.95),
            Percentile99 = CalculatePercentile(values, 0.99)
        });
    }

    private static double CalculateStandardDeviation(List<double> values)
    {
        if (values.Count < 2) return 0;
        
        var mean = values.Average();
        var variance = values.Select(x => Math.Pow(x - mean, 2)).Average();
        return Math.Sqrt(variance);
    }

    private static double CalculatePercentile(List<double> values, double percentile)
    {
        if (values.Count == 0) return 0;
        
        var sorted = values.OrderBy(x => x).ToList();
        var index = (int)Math.Ceiling(percentile * sorted.Count) - 1;
        return sorted[Math.Max(0, Math.Min(index, sorted.Count - 1))];
    }

    public IReadOnlyList<MetricRecord> GetRecordedMetrics() => _metrics.AsReadOnly();
    public IReadOnlyList<CounterRecord> GetRecordedCounters() => _counters.AsReadOnly();
    public IReadOnlyList<DurationRecord> GetRecordedDurations() => _durations.AsReadOnly();
}

public record MetricRecord(string Name, double Value, IDictionary<string, object> Tags, DateTime Timestamp);
public record CounterRecord(string Name, long Increment, IDictionary<string, object> Tags, DateTime Timestamp);
public record DurationRecord(string Name, TimeSpan Duration, IDictionary<string, object> Tags, DateTime Timestamp);