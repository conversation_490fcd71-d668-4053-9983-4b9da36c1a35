<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
    <RootNamespace>Automation.Tests.Integration</RootNamespace>
    <AssemblyName>Automation.Tests.Integration</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\Automation.Contracts\Automation.Contracts.csproj" />
    <ProjectReference Include="..\..\src\Automation.Core\Automation.Core.fsproj" />
    <ProjectReference Include="..\..\src\Automation.Data\Automation.Data.fsproj" />
    <ProjectReference Include="..\Automation.Tests.Unit\Automation.Tests.Unit.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    <PackageReference Include="Testcontainers" Version="3.6.0" />
    <PackageReference Include="Testcontainers.Redis" Version="3.6.0" />
  </ItemGroup>

</Project>