using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Reflection;
using System.Text.Json;
using Automation.Integration.Tests.Infrastructure;
using Automation.Worker.Configuration;
using Automation.Worker.Services;
using Automation.Web.Extensions;
using Automation.Mobile.Extensions;
using Automation.AI.Infrastructure.Extensions;
using Xunit;
using Xunit.Abstractions;

namespace Automation.Integration.Tests;

/// <summary>
/// Production readiness validation tests
/// </summary>
public class ProductionReadinessTests : IntegrationTestBase
{
    private readonly ITestOutputHelper _output;

    public ProductionReadinessTests(ITestOutputHelper output)
    {
        _output = output;
    }

    [Fact]
    public void Configuration_ShouldHaveProductionDefaults()
    {
        // Arrange & Act
        var config = WorkerConfiguration.CreateDefault();

        // Assert
        config.Should().NotBeNull();
        config.Redis.Should().NotBeNull();
        config.Concurrency.Should().NotBeNull();
        config.CircuitBreaker.Should().NotBeNull();
        config.Retry.Should().NotBeNull();
        config.HealthCheck.Should().NotBeNull();
        config.Metrics.Should().NotBeNull();

        // Validate sensible defaults
        config.Concurrency.MaxConcurrentTasks.Should().BeGreaterThan(0);
        config.CircuitBreaker.FailureThreshold.Should().BeGreaterThan(0);
        config.Retry.MaxAttempts.Should().BeGreaterThan(0);
        config.HealthCheck.Port.Should().BeGreaterThan(0);
        config.Metrics.Port.Should().BeGreaterThan(0);

        _output.WriteLine("✅ Configuration has valid production defaults");
    }

    [Fact]
    public void Assemblies_ShouldHaveCorrectVersions()
    {
        // Arrange
        var assemblies = new[]
        {
            typeof(Automation.Contracts.TaskResult).Assembly,
            typeof(Automation.Utilities.Logging.LoggerFactory).Assembly,
            typeof(Automation.Web.Executors.WebTaskExecutor).Assembly,
            typeof(Automation.Mobile.Executors.MobileTaskExecutor).Assembly,
            typeof(Automation.AI.Infrastructure.Http.AIHttpClient).Assembly,
            typeof(Automation.Worker.Services.TaskProcessingService).Assembly
        };

        // Act & Assert
        foreach (var assembly in assemblies)
        {
            var version = assembly.GetName().Version;
            version.Should().NotBeNull($"Assembly {assembly.GetName().Name} should have a version");
            version!.Major.Should().BeGreaterThan(0, $"Assembly {assembly.GetName().Name} should have a valid major version");

            _output.WriteLine($"✅ {assembly.GetName().Name}: v{version}");
        }
    }

    [Fact]
    public void Dependencies_ShouldBeCompatible()
    {
        // Arrange
        var host = Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder()
            .ConfigureServices(services =>
            {
                services.AddSingleton(WorkerConfiguration.CreateDefault());
                services.AddWebAutomationServicesHeadless();
                services.AddMobileAutomationServicesForAndroid();
                services.AddAIInfrastructureServicesForTesting();
            })
            .Build();

        // Act & Assert
        using (host)
        {
            // Verify all services can be resolved
            var serviceTypes = new[]
            {
                typeof(WorkerConfiguration),
                typeof(ILogger<ProductionReadinessTests>),
                typeof(IConfiguration)
            };

            foreach (var serviceType in serviceTypes)
            {
                var service = host.Services.GetService(serviceType);
                service.Should().NotBeNull($"Service {serviceType.Name} should be resolvable");
            }

            _output.WriteLine("✅ All dependencies are compatible and resolvable");
        }
    }

    [Fact]
    public void Logging_ShouldBeConfiguredCorrectly()
    {
        // Arrange
        var logger = GetService<ILogger<ProductionReadinessTests>>();

        // Act
        logger.LogInformation("Test log message");
        logger.LogWarning("Test warning message");
        logger.LogError("Test error message");

        // Assert
        logger.Should().NotBeNull();
        
        // Verify structured logging works
        using (logger.BeginScope(new Dictionary<string, object> { ["TestProperty"] = "TestValue" }))
        {
            logger.LogInformation("Scoped log message");
        }

        _output.WriteLine("✅ Logging is configured correctly");
    }

    [Fact]
    public async Task HealthChecks_ShouldBeImplemented()
    {
        // Arrange
        var circuitBreaker = GetOptionalService<ICircuitBreakerService>();

        // Act & Assert
        if (circuitBreaker != null)
        {
            var stats = circuitBreaker.GetStatistics();
            stats.Should().NotBeNull();
            stats.State.Should().BeDefined();
            
            _output.WriteLine($"✅ Circuit breaker health check available: {stats.State}");
        }

        // Redis health check
        if (Redis.IsConnected)
        {
            var database = Redis.GetDatabase();
            await database.PingAsync();
            _output.WriteLine("✅ Redis health check passed");
        }
    }

    [Fact]
    public void ErrorHandling_ShouldBeRobust()
    {
        // Arrange
        var testCases = new[]
        {
            // Null inputs
            (Input: (string?)null, Description: "Null input"),
            (Input: "", Description: "Empty input"),
            (Input: "   ", Description: "Whitespace input"),
            (Input: "invalid json", Description: "Invalid JSON"),
            (Input: "{}", Description: "Empty JSON object"),
            (Input: """{"invalid": "structure"}""", Description: "Invalid structure")
        };

        // Act & Assert
        foreach (var (input, description) in testCases)
        {
            try
            {
                if (input != null)
                {
                    JsonSerializer.Deserialize<object>(input);
                }
            }
            catch (Exception ex)
            {
                ex.Should().Match(e => e is JsonException || e is ArgumentNullException || e is ArgumentException);
                
                _output.WriteLine($"✅ {description}: Handled gracefully ({ex.GetType().Name})");
            }
        }
    }

    [Fact]
    public void Security_ShouldMeetBasicRequirements()
    {
        // Arrange
        var config = GetService<WorkerConfiguration>();

        // Act & Assert
        // Verify no hardcoded secrets
        var configJson = JsonSerializer.Serialize(config);
        configJson.Should().NotContain("password");
        configJson.Should().NotContain("secret");
        configJson.Should().NotContain("key=");

        // Verify secure defaults
        config.Redis.AbortOnConnectFail.Should().BeFalse("Should not abort on connection failure");
        config.Concurrency.EnableTaskCancellation.Should().BeTrue("Should support task cancellation");
        config.CircuitBreaker.Enabled.Should().BeTrue("Circuit breaker should be enabled");

        _output.WriteLine("✅ Basic security requirements met");
    }

    [Fact]
    public void Performance_ShouldMeetMinimumRequirements()
    {
        // Arrange
        var config = GetService<WorkerConfiguration>();

        // Act & Assert
        // Verify reasonable performance settings
        config.Concurrency.MaxConcurrentTasks.Should().BeInRange(1, 100, "Concurrency should be reasonable");
        config.Concurrency.TaskTimeoutMs.Should().BeInRange(10000, 3600000, "Timeout should be reasonable (10s-1h)");
        config.Retry.MaxAttempts.Should().BeInRange(1, 10, "Retry attempts should be reasonable");
        config.CircuitBreaker.FailureThreshold.Should().BeInRange(1, 50, "Failure threshold should be reasonable");

        _output.WriteLine("✅ Performance settings are within acceptable ranges");
    }

    [Fact]
    public void Monitoring_ShouldBeEnabled()
    {
        // Arrange
        var config = GetService<WorkerConfiguration>();

        // Act & Assert
        config.HealthCheck.Enabled.Should().BeTrue("Health checks should be enabled");
        config.Metrics.Enabled.Should().BeTrue("Metrics should be enabled");
        config.HealthCheck.Port.Should().BePositive("Health check port should be valid");
        config.Metrics.Port.Should().BePositive("Metrics port should be valid");

        _output.WriteLine($"✅ Monitoring enabled on ports {config.HealthCheck.Port}/{config.Metrics.Port}");
    }

    [Fact]
    public void Documentation_ShouldExist()
    {
        // Arrange
        var assemblies = AppDomain.CurrentDomain.GetAssemblies()
            .Where(a => a.GetName().Name?.StartsWith("Automation.") == true)
            .ToList();

        // Act & Assert
        foreach (var assembly in assemblies)
        {
            var types = assembly.GetTypes()
                .Where(t => t.IsPublic && !t.IsNested)
                .Take(5) // Check first 5 public types
                .ToList();

            foreach (var type in types)
            {
                // Check if type has XML documentation (this is a basic check)
                var hasDocumentation = type.GetCustomAttributes<System.ComponentModel.DescriptionAttribute>().Any() ||
                                     type.Name.Contains("Test") || // Test classes are exempt
                                     type.Name.Contains("Benchmark"); // Benchmark classes are exempt

                if (!hasDocumentation)
                {
                    _output.WriteLine($"⚠️  {type.FullName} may need documentation");
                }
            }
        }

        _output.WriteLine("✅ Documentation check completed");
    }

    [Fact]
    public void Deployment_ShouldSupportMultipleEnvironments()
    {
        // Arrange
        var environments = new[] { "Development", "Staging", "Production" };

        // Act & Assert
        foreach (var env in environments)
        {
            var config = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ASPNETCORE_ENVIRONMENT"] = env,
                    ["Worker:Redis:ConnectionString"] = "localhost:6379",
                    ["Worker:Concurrency:MaxConcurrentTasks"] = env == "Production" ? "10" : "2"
                })
                .Build();

            var workerConfig = WorkerConfiguration.CreateDefault();
            config.GetSection("Worker").Bind(workerConfig);

            workerConfig.Should().NotBeNull();
            _output.WriteLine($"✅ Configuration valid for {env} environment");
        }
    }

    [Fact]
    public void ResourceManagement_ShouldBeOptimal()
    {
        // Arrange
        var initialMemory = GC.GetTotalMemory(false);

        // Act - Create and dispose multiple service instances
        for (int i = 0; i < 10; i++)
        {
            using var host = Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder()
                .ConfigureServices(services =>
                {
                    services.AddSingleton(WorkerConfiguration.CreateDefault());
                })
                .Build();

            var config = host.Services.GetRequiredService<WorkerConfiguration>();
            config.Should().NotBeNull();
        }

        // Force garbage collection
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();

        var finalMemory = GC.GetTotalMemory(false);
        var memoryIncrease = finalMemory - initialMemory;

        // Assert
        memoryIncrease.Should().BeLessThan(10 * 1024 * 1024, "Memory increase should be minimal");

        _output.WriteLine($"✅ Resource management: Memory increase {memoryIncrease / 1024}KB");
    }

    [Fact]
    public void Compatibility_ShouldSupportTargetFramework()
    {
        // Arrange & Act
        var targetFramework = Assembly.GetExecutingAssembly()
            .GetCustomAttribute<System.Runtime.Versioning.TargetFrameworkAttribute>()?.FrameworkName;

        // Assert
        targetFramework.Should().NotBeNullOrEmpty();
        targetFramework.Should().Contain("net8.0", "Should target .NET 8.0");

        _output.WriteLine($"✅ Target framework: {targetFramework}");
    }
}
