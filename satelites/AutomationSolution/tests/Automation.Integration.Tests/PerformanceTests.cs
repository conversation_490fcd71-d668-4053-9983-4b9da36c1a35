using System.Text.Json;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Automation.Integration.Tests.Infrastructure;
using Automation.Worker.Services;
using Automation.Worker.Configuration;
using Automation.Web.Extensions;
using Automation.Mobile.Extensions;
using Automation.AI.Infrastructure.Extensions;
using Xunit;
using Xunit.Abstractions;

namespace Automation.Integration.Tests;

/// <summary>
/// Performance tests for the automation solution
/// </summary>
public class PerformanceTests : IntegrationTestBase
{
    private readonly ITestOutputHelper _output;

    public PerformanceTests(ITestOutputHelper output)
    {
        _output = output;
    }

    protected override void ConfigureTestServices(IServiceCollection services)
    {
        var workerConfig = WorkerConfiguration.CreateDefault();
        workerConfig.Concurrency.MaxConcurrentTasks = 10; // Higher concurrency for performance tests
        services.AddSingleton(workerConfig);

        services.AddWebAutomationServicesHeadless();
        services.AddMobileAutomationServicesForAndroid();
        services.AddAIInfrastructureServicesForTesting();

        services.AddSingleton<ICircuitBreakerService>(provider =>
        {
            var config = provider.GetRequiredService<WorkerConfiguration>();
            var logger = provider.GetRequiredService<Microsoft.Extensions.Logging.ILogger<CircuitBreakerService>>();
            return new CircuitBreakerService(config.CircuitBreaker, logger);
        });

        services.AddTransient<ITaskProcessingService, TaskProcessingService>();
    }

    [Fact]
    public async Task LoadTest_TaskProcessing_ShouldHandleLoad()
    {
        // Arrange
        var taskProcessor = GetService<ITaskProcessingService>();
        var tasks = new List<Task<TaskProcessingResult>>();
        var startTime = DateTime.UtcNow;

        // Act - Create 50 concurrent tasks
        for (int i = 0; i < 50; i++)
        {
            var testTask = CreateTestTaskMessage("web",
                CreateWebAction("Navigate", "", "https://httpbin.org/get"));
            var taskJson = JsonSerializer.Serialize(testTask);

            tasks.Add(taskProcessor.ProcessTaskAsync(taskJson));
        }

        var results = await Task.WhenAll(tasks);
        var endTime = DateTime.UtcNow;
        var totalTime = endTime - startTime;

        // Assert
        results.Should().HaveCount(50);
        var successCount = results.Count(r => r.IsSuccess);
        var failureCount = results.Count(r => !r.IsSuccess);

        successCount.Should().BeGreaterThan(0);
        failureCount.Should().BeLessThan((int)(successCount * 0.1)); // Less than 10% failures

        var avgResponseTime = results.Where(r => r.IsSuccess).Average(r => r.ProcessingTime.TotalMilliseconds);
        avgResponseTime.Should().BeLessThan(10000); // Average response time < 10s

        _output.WriteLine($"Load test completed: {successCount} successful, {failureCount} failed in {totalTime.TotalSeconds:F2}s");
        _output.WriteLine($"Average response time: {avgResponseTime:F0}ms");
    }

    [Fact]
    public async Task StressTest_CircuitBreaker_ShouldProtectSystem()
    {
        // Arrange
        var taskProcessor = GetService<ITaskProcessingService>();
        var circuitBreaker = GetService<ICircuitBreakerService>();
        var tasks = new List<Task<TaskProcessingResult>>();

        // Act - Create mix of valid and invalid tasks
        for (int i = 0; i < 30; i++)
        {
            var isValidTask = i % 3 != 0; // 2/3 valid, 1/3 invalid

            var testTask = isValidTask
                ? CreateTestTaskMessage("web", CreateWebAction("Navigate", "", "https://httpbin.org/get"))
                : CreateTestTaskMessage("invalid_type");

            var taskJson = JsonSerializer.Serialize(testTask);
            tasks.Add(taskProcessor.ProcessTaskAsync(taskJson));
        }

        await Task.WhenAll(tasks);

        // Assert
        var cbStats = circuitBreaker.GetStatistics();
        cbStats.FailureCount.Should().BeGreaterThan(0);

        _output.WriteLine($"Stress test completed: Circuit breaker state: {cbStats.State}");
        _output.WriteLine($"Failures recorded: {cbStats.FailureCount}");
    }

    [Fact]
    public async Task ThroughputTest_Redis_ShouldHandleHighVolume()
    {
        // Arrange
        await ClearRedisAsync();
        var channel = "test_automation_channel";
        var tasks = new List<Task>();
        var startTime = DateTime.UtcNow;

        // Act - Publish many messages concurrently
        for (int i = 0; i < 100; i++)
        {
            var testTask = CreateTestTaskMessage("web",
                CreateWebAction("Navigate", "", $"https://httpbin.org/uuid"));

            tasks.Add(PublishMessageAsync(channel, testTask));
        }

        await Task.WhenAll(tasks);
        var endTime = DateTime.UtcNow;
        var totalTime = endTime - startTime;

        // Assert
        tasks.Should().HaveCount(100);
        var throughput = tasks.Count / totalTime.TotalSeconds;

        throughput.Should().BeGreaterThan(10); // Should handle at least 10 messages per second
        totalTime.TotalMilliseconds.Should().BeLessThan(30000); // Should complete within 30 seconds

        _output.WriteLine($"Redis throughput test: {tasks.Count} messages published in {totalTime.TotalSeconds:F2}s");
        _output.WriteLine($"Throughput: {throughput:F2} messages/second");
    }

    [Fact]
    public async Task MemoryTest_LongRunning_ShouldNotLeak()
    {
        // Arrange
        var taskProcessor = GetService<ITaskProcessingService>();
        var initialMemory = GC.GetTotalMemory(true);

        // Act - Run many tasks to check for memory leaks
        for (int i = 0; i < 100; i++)
        {
            var testTask = CreateTestTaskMessage("web", 
                CreateWebAction("Navigate", "", "https://httpbin.org/get"));
            var taskJson = JsonSerializer.Serialize(testTask);

            await taskProcessor.ProcessTaskAsync(taskJson);

            if (i % 20 == 0)
            {
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }
        }

        // Force garbage collection
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();

        var finalMemory = GC.GetTotalMemory(false);
        var memoryIncrease = finalMemory - initialMemory;

        // Assert
        memoryIncrease.Should().BeLessThan(50 * 1024 * 1024); // Less than 50MB increase

        _output.WriteLine($"Memory test: Initial: {initialMemory / 1024 / 1024}MB, Final: {finalMemory / 1024 / 1024}MB");
        _output.WriteLine($"Memory increase: {memoryIncrease / 1024 / 1024}MB");
    }

    [Fact]
    public async Task ConcurrencyTest_MaxThroughput_ShouldScale()
    {
        // Arrange
        var taskProcessor = GetService<ITaskProcessingService>();
        var concurrentTasks = new List<Task<TaskProcessingResult>>();

        // Act - Create many concurrent tasks
        var startTime = DateTime.UtcNow;
        
        for (int i = 0; i < 50; i++)
        {
            var task = Task.Run(async () =>
            {
                var testTask = CreateTestTaskMessage("web", 
                    CreateWebAction("Navigate", "", "https://httpbin.org/delay/1"));
                var taskJson = JsonSerializer.Serialize(testTask);

                return await taskProcessor.ProcessTaskAsync(taskJson);
            });
            
            concurrentTasks.Add(task);
        }

        var results = await Task.WhenAll(concurrentTasks);
        var endTime = DateTime.UtcNow;
        var totalTime = endTime - startTime;

        // Assert
        results.Should().HaveCount(50);
        var successCount = results.Count(r => r.IsSuccess);
        var throughput = successCount / totalTime.TotalSeconds;

        throughput.Should().BeGreaterThan(1.0); // At least 1 task per second
        successCount.Should().BeGreaterThan(40); // At least 80% success rate

        _output.WriteLine($"Concurrency test: {successCount}/{results.Length} successful in {totalTime.TotalSeconds:F2}s");
        _output.WriteLine($"Throughput: {throughput:F2} tasks/second");
    }

    [Fact]
    public async Task ResponseTimeTest_ShouldMeetSLA()
    {
        // Arrange
        var taskProcessor = GetService<ITaskProcessingService>();
        var responseTimes = new List<TimeSpan>();

        // Act - Measure response times for multiple tasks
        for (int i = 0; i < 20; i++)
        {
            var testTask = CreateTestTaskMessage("web",
                CreateWebAction("Navigate", "", "https://httpbin.org/get"));
            var taskJson = JsonSerializer.Serialize(testTask);

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var result = await taskProcessor.ProcessTaskAsync(taskJson);
            stopwatch.Stop();

            if (result.IsSuccess)
            {
                responseTimes.Add(stopwatch.Elapsed);
            }
        }

        // Assert
        responseTimes.Should().NotBeEmpty();
        
        var averageTime = responseTimes.Average(t => t.TotalMilliseconds);
        var p95Time = responseTimes.OrderBy(t => t).Skip((int)(responseTimes.Count * 0.95)).First().TotalMilliseconds;
        var maxTime = responseTimes.Max(t => t.TotalMilliseconds);

        averageTime.Should().BeLessThan(10000); // Average < 10s
        p95Time.Should().BeLessThan(15000); // 95th percentile < 15s
        maxTime.Should().BeLessThan(30000); // Max < 30s

        _output.WriteLine($"Response time test: Avg: {averageTime:F0}ms, P95: {p95Time:F0}ms, Max: {maxTime:F0}ms");
    }
}
