using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using Testcontainers.Redis;
using Xunit;

namespace Automation.Integration.Tests.Infrastructure;

/// <summary>
/// Base class for integration tests with common setup and teardown
/// </summary>
public abstract class IntegrationTestBase : IAsyncLifetime
{
    protected IServiceProvider ServiceProvider { get; private set; } = null!;
    protected IHost Host { get; private set; } = null!;
    protected ILogger Logger { get; private set; } = null!;
    protected RedisContainer RedisContainer { get; private set; } = null!;
    protected IConnectionMultiplexer Redis { get; private set; } = null!;

    public virtual async Task InitializeAsync()
    {
        // Start Redis container
        RedisContainer = new RedisBuilder()
            .WithImage("redis:7-alpine")
            .WithPortBinding(6379, true)
            .Build();

        await RedisContainer.StartAsync();

        // Setup host and services
        var hostBuilder = Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["Redis:ConnectionString"] = RedisContainer.GetConnectionString(),
                    ["Worker:Redis:ConnectionString"] = RedisContainer.GetConnectionString(),
                    ["Worker:Redis:TaskChannel"] = "test_automation_channel",
                    ["Worker:Redis:DeadLetterQueue"] = "test_automation_dlq",
                    ["Worker:Concurrency:MaxConcurrentTasks"] = "2",
                    ["Worker:CircuitBreaker:FailureThreshold"] = "3",
                    ["Worker:Retry:MaxAttempts"] = "2",
                    ["WebAutomation:Headless"] = "true",
                    ["MobileAutomation:Platform"] = "Android",
                    ["AIInfrastructure:Caching:Enabled"] = "false"
                });
            })
            .ConfigureLogging(logging =>
            {
                logging.ClearProviders();
                logging.AddConsole();
            })
            .ConfigureServices(ConfigureTestServices);

        Host = hostBuilder.Build();
        ServiceProvider = Host.Services;
        Logger = ServiceProvider.GetRequiredService<ILogger<IntegrationTestBase>>();

        // Setup Redis connection
        Redis = ConnectionMultiplexer.Connect(RedisContainer.GetConnectionString());

        await Host.StartAsync();
    }

    public virtual async Task DisposeAsync()
    {
        if (Host != null)
        {
            await Host.StopAsync();
            Host.Dispose();
        }

        Redis?.Dispose();

        if (RedisContainer != null)
        {
            await RedisContainer.DisposeAsync();
        }
    }

    /// <summary>
    /// Configure test-specific services
    /// </summary>
    protected virtual void ConfigureTestServices(IServiceCollection services)
    {
        // Override with test-specific services in derived classes
    }

    /// <summary>
    /// Get a service from the DI container
    /// </summary>
    protected T GetService<T>() where T : notnull => ServiceProvider.GetRequiredService<T>();

    /// <summary>
    /// Get a service from the DI container (optional)
    /// </summary>
    protected T? GetOptionalService<T>() => ServiceProvider.GetService<T>();

    /// <summary>
    /// Clear Redis database
    /// </summary>
    protected async Task ClearRedisAsync()
    {
        var server = Redis.GetServer(Redis.GetEndPoints().First());
        await server.FlushDatabaseAsync();
    }

    /// <summary>
    /// Wait for a condition to be true with timeout
    /// </summary>
    protected async Task<bool> WaitForConditionAsync(
        Func<Task<bool>> condition, 
        TimeSpan timeout, 
        TimeSpan? interval = null)
    {
        interval ??= TimeSpan.FromMilliseconds(100);
        var endTime = DateTime.UtcNow.Add(timeout);

        while (DateTime.UtcNow < endTime)
        {
            if (await condition())
                return true;

            await Task.Delay(interval.Value);
        }

        return false;
    }

    /// <summary>
    /// Create a test task message
    /// </summary>
    protected static object CreateTestTaskMessage(string taskType = "web", params object[] actions)
    {
        return new
        {
            TaskId = Guid.NewGuid().ToString(),
            TaskType = taskType,
            Actions = actions.ToList(),
            CreatedAt = DateTime.UtcNow,
            Priority = 0,
            Metadata = new Dictionary<string, object>()
        };
    }

    /// <summary>
    /// Create a test web action
    /// </summary>
    protected static object CreateWebAction(string type, string selector = "", string? text = null)
    {
        var action = new Dictionary<string, object>
        {
            ["Type"] = type,
            ["Selector"] = selector
        };

        if (text != null)
            action["Text"] = text;

        return action;
    }

    /// <summary>
    /// Publish a message to Redis channel
    /// </summary>
    protected async Task PublishMessageAsync(string channel, object message)
    {
        var subscriber = Redis.GetSubscriber();
        var json = System.Text.Json.JsonSerializer.Serialize(message);
        await subscriber.PublishAsync(RedisChannel.Literal(channel), json);
    }

    /// <summary>
    /// Get messages from Redis list (like DLQ)
    /// </summary>
    protected async Task<List<string>> GetRedisListMessagesAsync(string listKey)
    {
        var database = Redis.GetDatabase();
        var messages = await database.ListRangeAsync(listKey);
        return messages.Select(m => m.ToString()).ToList();
    }
}
