{"webTasks": [{"taskId": "web-001", "taskType": "web", "description": "Simple navigation test", "actions": [{"type": "Navigate", "selector": "", "text": "https://httpbin.org/get"}, {"type": "Screenshot", "selector": "", "text": "navigation-test.png"}]}, {"taskId": "web-002", "taskType": "web", "description": "Form interaction test", "actions": [{"type": "Navigate", "selector": "", "text": "https://httpbin.org/forms/post"}, {"type": "Type", "selector": "input[name='custname']", "text": "<PERSON>"}, {"type": "Type", "selector": "input[name='custtel']", "text": "************"}, {"type": "Type", "selector": "input[name='custemail']", "text": "<EMAIL>"}, {"type": "Click", "selector": "input[type='submit']", "text": ""}, {"type": "Screenshot", "selector": "", "text": "form-submission.png"}]}, {"taskId": "web-003", "taskType": "web", "description": "Element interaction test", "actions": [{"type": "Navigate", "selector": "", "text": "https://example.com"}, {"type": "WaitForElement", "selector": "h1", "text": ""}, {"type": "GetText", "selector": "h1", "text": ""}, {"type": "Screenshot", "selector": "", "text": "element-interaction.png"}]}], "mobileTasks": [{"taskId": "mobile-001", "taskType": "mobile", "description": "Simple mobile app test", "actions": [{"type": "Tap", "selector": "//android.widget.Button[@text='Click Me']", "text": ""}, {"type": "Type", "selector": "//android.widget.EditText[@resource-id='input']", "text": "Test Input"}, {"type": "Screenshot", "selector": "", "text": "mobile-test.png"}]}], "errorTasks": [{"taskId": "error-001", "taskType": "web", "description": "Invalid selector test", "actions": [{"type": "Navigate", "selector": "", "text": "https://example.com"}, {"type": "Click", "selector": "invalid-selector-that-does-not-exist", "text": ""}]}, {"taskId": "error-002", "taskType": "invalid_type", "description": "Invalid task type test", "actions": [{"type": "SomeAction", "selector": "", "text": ""}]}], "performanceTasks": [{"taskId": "perf-001", "taskType": "web", "description": "Heavy page load test", "actions": [{"type": "Navigate", "selector": "", "text": "https://httpbin.org/delay/2"}, {"type": "Screenshot", "selector": "", "text": "heavy-load.png"}]}, {"taskId": "perf-002", "taskType": "web", "description": "Multiple actions test", "actions": [{"type": "Navigate", "selector": "", "text": "https://httpbin.org/html"}, {"type": "WaitForElement", "selector": "h1", "text": ""}, {"type": "GetText", "selector": "h1", "text": ""}, {"type": "GetText", "selector": "p", "text": ""}, {"type": "Screenshot", "selector": "", "text": "multiple-actions.png"}]}]}