using System.Diagnostics;
using System.Text.Json;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Automation.Worker.Services;
using Automation.Worker.Configuration;
using Automation.Web.Extensions;
using Automation.Mobile.Extensions;
using Automation.AI.Infrastructure.Extensions;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;

namespace Automation.Integration.Tests;

/// <summary>
/// Simple performance measurement tests
/// </summary>
public class BenchmarkTests
{
    private readonly ITestOutputHelper _output;

    public BenchmarkTests(ITestOutputHelper output)
    {
        _output = output;
    }

    [Fact]
    public async Task TaskProcessing_PerformanceMeasurement()
    {
        // Simple performance test without BenchmarkDotNet
        var benchmark = new TaskProcessingBenchmarks();
        benchmark.Setup();

        var stopwatch = Stopwatch.StartNew();
        await benchmark.ProcessSimpleTask();
        stopwatch.Stop();

        stopwatch.ElapsedMilliseconds.Should().BeLessThan(30000); // Should complete within 30 seconds
        _output.WriteLine($"Simple task processing completed in {stopwatch.ElapsedMilliseconds}ms");

        benchmark.Cleanup();
    }
}

public class TaskProcessingBenchmarks
{
    private IServiceProvider _serviceProvider = null!;
    private ITaskProcessingService _taskProcessor = null!;
    private string _simpleTaskJson = null!;
    private string _complexTaskJson = null!;

    public void Setup()
    {
        var hostBuilder = Host.CreateDefaultBuilder()
            .ConfigureLogging(logging => logging.ClearProviders())
            .ConfigureServices(services =>
            {
                var workerConfig = WorkerConfiguration.CreateDefault();
                services.AddSingleton(workerConfig);

                services.AddWebAutomationServicesHeadless();
                services.AddMobileAutomationServicesForAndroid();
                services.AddAIInfrastructureServicesForTesting();

                services.AddSingleton<ICircuitBreakerService>(provider =>
                {
                    var config = provider.GetRequiredService<WorkerConfiguration>();
                    var logger = provider.GetRequiredService<ILogger<CircuitBreakerService>>();
                    return new CircuitBreakerService(config.CircuitBreaker, logger);
                });

                services.AddTransient<ITaskProcessingService, TaskProcessingService>();
            });

        var host = hostBuilder.Build();
        _serviceProvider = host.Services;
        _taskProcessor = _serviceProvider.GetRequiredService<ITaskProcessingService>();

        // Prepare test data
        var simpleTask = new
        {
            TaskId = Guid.NewGuid().ToString(),
            TaskType = "web",
            Actions = new[]
            {
                new { Type = "Navigate", Selector = "", Text = "https://httpbin.org/get" }
            }
        };

        var complexTask = new
        {
            TaskId = Guid.NewGuid().ToString(),
            TaskType = "web",
            Actions = new[]
            {
                new { Type = "Navigate", Selector = "", Text = "https://httpbin.org/forms/post" },
                new { Type = "Type", Selector = "input[name='custname']", Text = "John Doe" },
                new { Type = "Type", Selector = "input[name='custtel']", Text = "************" },
                new { Type = "Type", Selector = "input[name='custemail']", Text = "<EMAIL>" },
                new { Type = "Click", Selector = "input[type='submit']", Text = "" },
                new { Type = "Screenshot", Selector = "", Text = "benchmark-result.png" }
            }
        };

        _simpleTaskJson = JsonSerializer.Serialize(simpleTask);
        _complexTaskJson = JsonSerializer.Serialize(complexTask);
    }

    public async Task<object> ProcessSimpleTask()
    {
        return await _taskProcessor.ProcessTaskAsync(_simpleTaskJson);
    }

    public async Task<object> ProcessComplexTask()
    {
        return await _taskProcessor.ProcessTaskAsync(_complexTaskJson);
    }

    public void SerializeTaskMessage()
    {
        var task = new
        {
            TaskId = Guid.NewGuid().ToString(),
            TaskType = "web",
            Actions = new[]
            {
                new { Type = "Navigate", Selector = "", Text = "https://example.com" },
                new { Type = "Click", Selector = "#button", Text = "" }
            },
            CreatedAt = DateTime.UtcNow,
            Priority = 0,
            Metadata = new Dictionary<string, object>
            {
                ["source"] = "benchmark",
                ["version"] = "1.0"
            }
        };

        JsonSerializer.Serialize(task);
    }

    public void DeserializeTaskMessage()
    {
        JsonSerializer.Deserialize<object>(_simpleTaskJson);
    }

    public void Cleanup()
    {
        if (_serviceProvider is IDisposable disposable)
        {
            disposable.Dispose();
        }
    }
}


