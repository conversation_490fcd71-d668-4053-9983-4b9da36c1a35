<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="xunit" Version="2.6.1" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.5.3">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.0" />
    <PackageReference Include="Testcontainers.Redis" Version="3.6.0" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="Moq" Version="4.20.69" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />

  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\Automation.Contracts\Automation.Contracts.csproj" />
    <ProjectReference Include="..\..\src\Automation.Utilities.CSharp\Automation.Utilities.CSharp.csproj" />
    <ProjectReference Include="..\..\src\Automation.Web.CSharp\Automation.Web.CSharp.csproj" />
    <ProjectReference Include="..\..\src\Automation.Mobile.CSharp\Automation.Mobile.CSharp.csproj" />
    <ProjectReference Include="..\..\src\Automation.AI.Infrastructure.CSharp\Automation.AI.Infrastructure.CSharp.csproj" />
    <ProjectReference Include="..\..\src\Automation.Worker.CSharp\Automation.Worker.CSharp.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="TestData\**\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
