using System.Text.Json;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Automation.Integration.Tests.Infrastructure;
using Automation.Worker.Services;
using Automation.Worker.Configuration;
using Automation.Web.Extensions;
using Automation.Mobile.Extensions;
using Automation.AI.Infrastructure.Extensions;
using Automation.Utilities.Logging;
using Xunit;
using Xunit.Abstractions;

namespace Automation.Integration.Tests;

/// <summary>
/// End-to-end integration tests for the complete automation solution
/// </summary>
public class EndToEndTests : IntegrationTestBase
{
    private readonly ITestOutputHelper _output;

    public EndToEndTests(ITestOutputHelper output)
    {
        _output = output;
    }

    protected override void ConfigureTestServices(IServiceCollection services)
    {
        // Configure worker configuration
        var workerConfig = WorkerConfiguration.CreateDefault();
        services.AddSingleton(workerConfig);

        // Add automation services
        services.AddWebAutomationServicesHeadless();
        services.AddMobileAutomationServicesForAndroid();
        services.AddAIInfrastructureServicesForTesting();

        // Add worker services
        services.AddSingleton<ICircuitBreakerService>(provider =>
        {
            var config = provider.GetRequiredService<WorkerConfiguration>();
            var logger = provider.GetRequiredService<Microsoft.Extensions.Logging.ILogger<CircuitBreakerService>>();
            return new CircuitBreakerService(config.CircuitBreaker, logger);
        });

        services.AddTransient<ITaskProcessingService, TaskProcessingService>();
    }

    [Fact]
    public async Task CompleteWorkflow_ShouldProcessTaskSuccessfully()
    {
        // Arrange
        await ClearRedisAsync();
        
        var taskProcessor = GetService<ITaskProcessingService>();
        var testTask = CreateTestTaskMessage("web", 
            CreateWebAction("Navigate", "", "https://example.com"),
            CreateWebAction("Screenshot", "", "test-screenshot.png"));

        var taskJson = JsonSerializer.Serialize(testTask);

        // Act
        var result = await taskProcessor.ProcessTaskAsync(taskJson);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.ErrorMessage.Should().BeNullOrEmpty();
        result.ProcessingTime.Should().BeGreaterThan(TimeSpan.Zero);
        result.ActionsExecuted.Should().Be(2);

        _output.WriteLine($"Task processed successfully in {result.ProcessingTime.TotalMilliseconds}ms");
    }

    [Fact]
    public async Task WorkerService_ShouldProcessRedisMessages()
    {
        // Arrange
        await ClearRedisAsync();
        
        var channel = "test_automation_channel";
        var testTask = CreateTestTaskMessage("web", 
            CreateWebAction("Navigate", "", "https://httpbin.org/get"));

        // Act
        await PublishMessageAsync(channel, testTask);

        // Wait for processing (in a real scenario, the worker would be running)
        await Task.Delay(1000);

        // Assert - Check that no messages ended up in DLQ
        var dlqMessages = await GetRedisListMessagesAsync("test_automation_dlq");
        dlqMessages.Should().BeEmpty("No messages should be in DLQ for successful processing");
    }

    [Fact]
    public async Task CircuitBreaker_ShouldOpenOnRepeatedFailures()
    {
        // Arrange
        var circuitBreaker = GetService<ICircuitBreakerService>();
        var taskProcessor = GetService<ITaskProcessingService>();

        // Create an invalid task that will fail
        var invalidTask = CreateTestTaskMessage("invalid_type");
        var taskJson = JsonSerializer.Serialize(invalidTask);

        // Act - Trigger multiple failures
        for (int i = 0; i < 5; i++)
        {
            var result = await taskProcessor.ProcessTaskAsync(taskJson);
            result.IsSuccess.Should().BeFalse();
        }

        // Assert
        var stats = circuitBreaker.GetStatistics();
        stats.State.Should().Be(CircuitBreakerState.Open);
        stats.FailureCount.Should().BeGreaterOrEqualTo(5);

        _output.WriteLine($"Circuit breaker opened after {stats.FailureCount} failures");
    }

    [Fact]
    public async Task TaskProcessing_ShouldHandleHighConcurrency()
    {
        // Arrange
        var taskProcessor = GetService<ITaskProcessingService>();
        var tasks = new List<Task<TaskProcessingResult>>();

        // Create multiple concurrent tasks
        for (int i = 0; i < 10; i++)
        {
            var testTask = CreateTestTaskMessage("web",
                CreateWebAction("Navigate", "", $"https://httpbin.org/delay/1"));
            var taskJson = JsonSerializer.Serialize(testTask);

            tasks.Add(Task.Run(async () => await taskProcessor.ProcessTaskAsync(taskJson)));
        }

        // Act
        var results = await Task.WhenAll(tasks);

        // Assert
        results.Should().HaveCount(10);
        results.Should().OnlyContain(r => r != null);

        var successCount = results.Count(r => r.IsSuccess);
        _output.WriteLine($"Processed {results.Length} concurrent tasks, {successCount} successful");
    }

    [Fact]
    public async Task ErrorHandling_ShouldCaptureDetailedContext()
    {
        // Arrange
        var taskProcessor = GetService<ITaskProcessingService>();
        
        // Create a task with an invalid selector that will fail
        var testTask = CreateTestTaskMessage("web", 
            CreateWebAction("Navigate", "", "https://example.com"),
            CreateWebAction("Click", "invalid-selector-that-does-not-exist"));

        var taskJson = JsonSerializer.Serialize(testTask);

        // Act
        var result = await taskProcessor.ProcessTaskAsync(taskJson);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().NotBeNullOrEmpty();
        result.Exception.Should().NotBeNull();

        _output.WriteLine($"Error captured: {result.ErrorMessage}");
    }

    [Fact]
    public async Task HealthChecks_ShouldReportSystemStatus()
    {
        // Arrange
        var circuitBreaker = GetService<ICircuitBreakerService>();

        // Act
        var stats = circuitBreaker.GetStatistics();

        // Assert
        stats.Should().NotBeNull();
        stats.State.Should().Be(CircuitBreakerState.Closed);

        _output.WriteLine($"Circuit breaker state: {stats.State}");
    }

    [Fact]
    public async Task Configuration_ShouldLoadCorrectly()
    {
        // Arrange & Act
        var workerConfig = GetService<WorkerConfiguration>();

        // Assert
        workerConfig.Should().NotBeNull();
        workerConfig.Redis.TaskChannel.Should().Be("test_automation_channel");
        workerConfig.Redis.DeadLetterQueue.Should().Be("test_automation_dlq");
        workerConfig.Concurrency.MaxConcurrentTasks.Should().Be(2);
        workerConfig.CircuitBreaker.FailureThreshold.Should().Be(3);

        _output.WriteLine($"Configuration loaded: {workerConfig.Redis.ConnectionString}");
    }

    [Fact]
    public async Task Logging_ShouldCaptureEvents()
    {
        // Arrange
        var taskProcessor = GetService<ITaskProcessingService>();
        var testTask = CreateTestTaskMessage("web", 
            CreateWebAction("Navigate", "", "https://example.com"));

        var taskJson = JsonSerializer.Serialize(testTask);

        // Act
        await taskProcessor.ProcessTaskAsync(taskJson);

        // Assert
        // Note: In a real scenario, you would check log aggregation systems
        // For this test, we just verify the task completed successfully
        var result = await taskProcessor.ProcessTaskAsync(taskJson);
        result.IsSuccess.Should().BeTrue();
        _output.WriteLine("Logging test completed - logs would be captured by configured providers");
    }

    [Fact]
    public async Task Performance_ShouldMeetBasicRequirements()
    {
        // Arrange
        var taskProcessor = GetService<ITaskProcessingService>();
        var testTask = CreateTestTaskMessage("web", 
            CreateWebAction("Navigate", "", "https://httpbin.org/get"));

        var taskJson = JsonSerializer.Serialize(testTask);

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var result = await taskProcessor.ProcessTaskAsync(taskJson);
        stopwatch.Stop();

        // Assert
        result.IsSuccess.Should().BeTrue();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(30000, "Task should complete within 30 seconds");

        _output.WriteLine($"Task completed in {stopwatch.ElapsedMilliseconds}ms");
    }
}
