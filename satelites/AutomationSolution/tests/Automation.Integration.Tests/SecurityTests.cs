using System.Text.Json;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Automation.Integration.Tests.Infrastructure;
using Automation.Worker.Services;
using Automation.Worker.Configuration;
using Automation.Web.Extensions;
using Automation.Mobile.Extensions;
using Automation.AI.Infrastructure.Extensions;
using Xunit;
using Xunit.Abstractions;

namespace Automation.Integration.Tests;

/// <summary>
/// Security tests for the automation solution
/// </summary>
public class SecurityTests : IntegrationTestBase
{
    private readonly ITestOutputHelper _output;

    public SecurityTests(ITestOutputHelper output)
    {
        _output = output;
    }

    protected override void ConfigureTestServices(IServiceCollection services)
    {
        var workerConfig = WorkerConfiguration.CreateDefault();
        services.AddSingleton(workerConfig);

        services.AddWebAutomationServicesHeadless();
        services.AddMobileAutomationServicesForAndroid();
        services.AddAIInfrastructureServicesForTesting();

        services.AddSingleton<ICircuitBreakerService>(provider =>
        {
            var config = provider.GetRequiredService<WorkerConfiguration>();
            var logger = provider.GetRequiredService<Microsoft.Extensions.Logging.ILogger<CircuitBreakerService>>();
            return new CircuitBreakerService(config.CircuitBreaker, logger);
        });

        services.AddTransient<ITaskProcessingService, TaskProcessingService>();
    }

    [Fact]
    public async Task InputValidation_ShouldRejectMaliciousPayloads()
    {
        // Arrange
        var taskProcessor = GetService<ITaskProcessingService>();
        
        var maliciousPayloads = new[]
        {
            // JSON injection attempts
            """{"TaskId": "test", "TaskType": "web", "Actions": [{"Type": "Navigate", "Selector": "'; DROP TABLE users; --"}]}""",
            
            // XSS attempts
            """{"TaskId": "test", "TaskType": "web", "Actions": [{"Type": "Navigate", "Selector": "<script>alert('xss')</script>"}]}""",
            
            // Path traversal attempts
            """{"TaskId": "test", "TaskType": "web", "Actions": [{"Type": "Screenshot", "Selector": "../../../etc/passwd"}]}""",
            
            // Command injection attempts
            """{"TaskId": "test", "TaskType": "web", "Actions": [{"Type": "Navigate", "Selector": "https://example.com; rm -rf /"}]}""",
            
            // Oversized payload
            new string('A', 1024 * 1024), // 1MB of 'A' characters
            
            // Invalid JSON
            """{"TaskId": "test", "TaskType": "web", "Actions": [{"Type": "Navigate", "Selector":""",
            
            // Null bytes
            "{\0\"TaskId\": \"test\", \"TaskType\": \"web\"}",
        };

        // Act & Assert
        foreach (var payload in maliciousPayloads)
        {
            var result = await taskProcessor.ProcessTaskAsync(payload);
            
            // Should either fail gracefully or reject the input
            if (result.IsSuccess)
            {
                // If it succeeds, it should not have executed any dangerous operations
                result.ActionsExecuted.Should().Be(0, "Malicious payload should not execute actions");
            }
            else
            {
                // Failure is expected and acceptable
                result.ErrorMessage.Should().NotBeNullOrEmpty();
            }
            
            _output.WriteLine($"Malicious payload handled: Success={result.IsSuccess}, Error={result.ErrorMessage}");
        }
    }

    [Fact]
    public async Task ResourceLimits_ShouldPreventDoSAttacks()
    {
        // Arrange
        var taskProcessor = GetService<ITaskProcessingService>();
        
        // Create a task with many actions to test resource limits
        var manyActions = Enumerable.Range(0, 1000)
            .Select(i => CreateWebAction("Navigate", "", $"https://httpbin.org/delay/10"))
            .ToArray();
        
        var testTask = CreateTestTaskMessage("web", manyActions);
        var taskJson = JsonSerializer.Serialize(testTask);

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var result = await taskProcessor.ProcessTaskAsync(taskJson);
        stopwatch.Stop();

        // Assert
        // Should either timeout or limit the number of actions processed
        stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromMinutes(10), "Should not run indefinitely");
        
        if (!result.IsSuccess)
        {
            result.ErrorMessage.Should().NotBeNullOrEmpty();
        }

        _output.WriteLine($"Resource limit test: Completed in {stopwatch.Elapsed.TotalSeconds:F2}s, Success={result.IsSuccess}");
    }

    [Fact]
    public async Task URLValidation_ShouldBlockDangerousURLs()
    {
        // Arrange
        var taskProcessor = GetService<ITaskProcessingService>();
        
        var dangerousUrls = new[]
        {
            "file:///etc/passwd",
            "ftp://internal-server/sensitive-data",
            "javascript:alert('xss')",
            "data:text/html,<script>alert('xss')</script>",
            "http://localhost:22/ssh-attack",
            "https://malware-site.example.com/payload",
            "http://***************/latest/meta-data/", // AWS metadata
            "http://metadata.google.internal/", // GCP metadata
        };

        // Act & Assert
        foreach (var url in dangerousUrls)
        {
            var testTask = CreateTestTaskMessage("web", 
                CreateWebAction("Navigate", "", url));
            var taskJson = JsonSerializer.Serialize(testTask);

            var result = await taskProcessor.ProcessTaskAsync(taskJson);
            
            // Should either block the URL or fail safely
            if (result.IsSuccess)
            {
                // If it succeeds, verify it didn't actually navigate to dangerous URLs
                _output.WriteLine($"URL {url} was processed but should be validated");
            }
            else
            {
                result.ErrorMessage.Should().NotBeNullOrEmpty();
                _output.WriteLine($"Dangerous URL blocked: {url}");
            }
        }
    }

    [Fact]
    public async Task ConfigurationSecurity_ShouldNotExposeSecrets()
    {
        // Arrange
        var workerConfig = GetService<WorkerConfiguration>();

        // Act & Assert
        // Verify that sensitive configuration is not exposed in logs or errors
        var configJson = JsonSerializer.Serialize(workerConfig);
        
        // Should not contain obvious secrets (this is a basic check)
        configJson.Should().NotContain("password", "Password should not be in config");
        configJson.Should().NotContain("secret", "Secret should not be in config");
        configJson.Should().NotContain("key=", "API keys should not be in config");
        
        _output.WriteLine("Configuration security check passed");
    }

    [Fact]
    public async Task ErrorHandling_ShouldNotLeakInformation()
    {
        // Arrange
        var taskProcessor = GetService<ITaskProcessingService>();
        
        // Create tasks that will cause various types of errors
        var errorCausingTasks = new[]
        {
            // Invalid task type
            CreateTestTaskMessage("invalid_type"),
            
            // Invalid action
            new { TaskId = "test", TaskType = "web", Actions = new[] { new { Type = "InvalidAction" } } },
            
            // Malformed action
            new { TaskId = "test", TaskType = "web", Actions = new[] { new { WrongProperty = "value" } } }
        };

        // Act & Assert
        foreach (var task in errorCausingTasks)
        {
            var taskJson = JsonSerializer.Serialize(task);
            var result = await taskProcessor.ProcessTaskAsync(taskJson);
            
            result.IsSuccess.Should().BeFalse();
            result.ErrorMessage.Should().NotBeNullOrEmpty();
            
            // Error messages should not contain sensitive information
            result.ErrorMessage.Should().NotContain("password");
            result.ErrorMessage.Should().NotContain("secret");
            result.ErrorMessage.Should().NotContain("token");
            result.ErrorMessage.Should().NotContain("key");
            
            // Should not contain full stack traces in production
            result.ErrorMessage.Should().NotContain("at System.");
            result.ErrorMessage.Should().NotContain("at Microsoft.");
            
            _output.WriteLine($"Error message validated: {result.ErrorMessage}");
        }
    }

    [Fact]
    public async Task ConcurrencyLimits_ShouldPreventResourceExhaustion()
    {
        // Arrange
        var taskProcessor = GetService<ITaskProcessingService>();
        var workerConfig = GetService<WorkerConfiguration>();
        
        // Try to exceed the configured concurrency limit
        var tasks = new List<Task>();
        var maxConcurrency = workerConfig.Concurrency.MaxConcurrentTasks;
        
        for (int i = 0; i < maxConcurrency * 3; i++)
        {
            var testTask = CreateTestTaskMessage("web", 
                CreateWebAction("Navigate", "", "https://httpbin.org/delay/2"));
            var taskJson = JsonSerializer.Serialize(testTask);

            tasks.Add(Task.Run(async () => await taskProcessor.ProcessTaskAsync(taskJson)));
        }

        // Act
        var startTime = DateTime.UtcNow;
        await Task.WhenAll(tasks);
        var endTime = DateTime.UtcNow;
        var totalTime = endTime - startTime;

        // Assert
        // With proper concurrency limiting, this should take longer than if all tasks ran simultaneously
        var expectedMinTime = TimeSpan.FromSeconds(2 * 3); // 3 batches of 2-second tasks
        totalTime.Should().BeGreaterThan(expectedMinTime.Subtract(TimeSpan.FromSeconds(1)));

        _output.WriteLine($"Concurrency test: {tasks.Count} tasks completed in {totalTime.TotalSeconds:F2}s");
        _output.WriteLine($"Max concurrency setting: {maxConcurrency}");
    }

    [Fact]
    public async Task RedisConnection_ShouldBeSecure()
    {
        // Arrange & Act
        var isConnected = Redis.IsConnected;
        var configuration = Redis.Configuration;

        // Assert
        isConnected.Should().BeTrue("Redis should be connected");
        
        // Verify connection string doesn't contain plaintext passwords
        if (!string.IsNullOrEmpty(configuration))
        {
            configuration.Should().NotContain("password=", "Redis connection should not expose passwords");
        }

        _output.WriteLine($"Redis connection security check passed. Connected: {isConnected}");
    }

    [Fact]
    public async Task TaskTimeout_ShouldPreventHangingOperations()
    {
        // Arrange
        var taskProcessor = GetService<ITaskProcessingService>();
        
        // Create a task that would normally take a very long time
        var testTask = CreateTestTaskMessage("web", 
            CreateWebAction("Navigate", "", "https://httpbin.org/delay/300")); // 5 minutes
        var taskJson = JsonSerializer.Serialize(testTask);

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var result = await taskProcessor.ProcessTaskAsync(taskJson);
        stopwatch.Stop();

        // Assert
        // Should timeout before 5 minutes
        stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromMinutes(6), "Task should timeout");
        
        if (!result.IsSuccess)
        {
            result.ErrorMessage.Should().NotBeNullOrEmpty();
            _output.WriteLine($"Task properly timed out: {result.ErrorMessage}");
        }
        else
        {
            _output.WriteLine($"Task completed in {stopwatch.Elapsed.TotalSeconds:F2}s");
        }
    }
}
