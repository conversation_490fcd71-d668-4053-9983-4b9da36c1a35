using Microsoft.Playwright;
using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;

public class TestResult
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TestName { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string Status { get; set; } = "Running";
    public string? ErrorMessage { get; set; }
    public List<string> Screenshots { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

class Program 
{
    static async Task Main() 
    {
        var testResult = new TestResult
        {
            TestName = "Basic Navigation Test with Screenshots",
            Url = "https://example.com",
            StartTime = DateTime.UtcNow
        };

        try 
        {
            Console.WriteLine($"🚀 Starting test: {testResult.TestName}");
            Console.WriteLine($"📋 Test ID: {testResult.Id}");
            
            // Set environment variables
            Environment.SetEnvironmentVariable("PLAYWRIGHT_BROWSERS_PATH", "/ms-playwright");
            
            using var playwright = await Playwright.CreateAsync();
            Console.WriteLine("✅ Playwright initialized!");
            
            await using var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions { Headless = true });
            Console.WriteLine("✅ Browser launched!");
            
            var page = await browser.NewPageAsync();
            
            // Navigate to URL
            Console.WriteLine($"🌐 Navigating to {testResult.Url}...");
            await page.GotoAsync(testResult.Url, new PageGotoOptions { WaitUntil = WaitUntilState.NetworkIdle });
            Console.WriteLine("✅ Navigation completed!");
            
            // Take screenshot
            var screenshotDir = "/tmp/screenshots";
            Directory.CreateDirectory(screenshotDir);
            
            var screenshotPath = Path.Combine(screenshotDir, $"{testResult.Id}-navigation.png");
            await page.ScreenshotAsync(new PageScreenshotOptions
            {
                Path = screenshotPath,
                FullPage = true
            });
            testResult.Screenshots.Add(screenshotPath);
            Console.WriteLine($"📸 Screenshot saved: {screenshotPath}");
            
            // Get page information
            var title = await page.TitleAsync();
            var currentUrl = page.Url;
            
            // Analyze page elements
            var linkCount = await page.Locator("a").CountAsync();
            var imageCount = await page.Locator("img").CountAsync();
            var formCount = await page.Locator("form").CountAsync();
            
            // Store metadata
            testResult.Metadata["title"] = title;
            testResult.Metadata["finalUrl"] = currentUrl;
            testResult.Metadata["linkCount"] = linkCount;
            testResult.Metadata["imageCount"] = imageCount;
            testResult.Metadata["formCount"] = formCount;
            
            Console.WriteLine($"📄 Page title: {title}");
            Console.WriteLine($"🔗 Links found: {linkCount}");
            Console.WriteLine($"🖼️ Images found: {imageCount}");
            Console.WriteLine($"📝 Forms found: {formCount}");
            
            // Take another screenshot after analysis
            var analysisScreenshotPath = Path.Combine(screenshotDir, $"{testResult.Id}-analysis.png");
            await page.ScreenshotAsync(new PageScreenshotOptions
            {
                Path = analysisScreenshotPath,
                FullPage = true
            });
            testResult.Screenshots.Add(analysisScreenshotPath);
            Console.WriteLine($"📸 Analysis screenshot saved: {analysisScreenshotPath}");
            
            // Mark test as passed
            testResult.Status = "Passed";
            testResult.EndTime = DateTime.UtcNow;
            
            Console.WriteLine("🎉 Test completed successfully!");
        } 
        catch (Exception ex) 
        {
            testResult.Status = "Failed";
            testResult.ErrorMessage = ex.Message;
            testResult.EndTime = DateTime.UtcNow;
            
            Console.WriteLine($"❌ Test failed: {ex.Message}");
        }
        
        // Save test results to JSON
        var resultsDir = "/tmp/test-results";
        Directory.CreateDirectory(resultsDir);
        
        var resultPath = Path.Combine(resultsDir, $"{testResult.Id}.json");
        var json = JsonSerializer.Serialize(testResult, new JsonSerializerOptions 
        { 
            WriteIndented = true 
        });
        
        await File.WriteAllTextAsync(resultPath, json);
        Console.WriteLine($"💾 Test results saved: {resultPath}");
        
        // Display summary
        var duration = testResult.EndTime - testResult.StartTime;
        Console.WriteLine("\n📊 TEST SUMMARY:");
        Console.WriteLine($"   Test ID: {testResult.Id}");
        Console.WriteLine($"   Status: {testResult.Status}");
        Console.WriteLine($"   Duration: {duration.TotalSeconds:F2} seconds");
        Console.WriteLine($"   Screenshots: {testResult.Screenshots.Count}");
        Console.WriteLine($"   Results file: {resultPath}");
        
        if (testResult.Status == "Passed")
        {
            Console.WriteLine("\n✅ All tests passed! 🎉");
        }
        else
        {
            Console.WriteLine($"\n❌ Test failed: {testResult.ErrorMessage}");
        }
    }
}
