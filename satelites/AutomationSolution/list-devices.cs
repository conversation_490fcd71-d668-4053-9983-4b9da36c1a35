using Microsoft.Playwright;
using System;
using System.Threading.Tasks;

class Program 
{
    static async Task Main() 
    {
        try 
        {
            Environment.SetEnvironmentVariable("PLAYWRIGHT_BROWSERS_PATH", "/ms-playwright");
            
            using var playwright = await Playwright.CreateAsync();
            Console.WriteLine("📱 Available Playwright Devices:");
            Console.WriteLine("================================");
            
            var devices = playwright.Devices;
            var deviceNames = devices.Keys.OrderBy(k => k).ToList();
            
            foreach (var deviceName in deviceNames)
            {
                var device = devices[deviceName];
                Console.WriteLine($"📱 {deviceName}");
                Console.WriteLine($"   Viewport: {device.ViewportSize?.Width}x{device.ViewportSize?.Height}");
                Console.WriteLine($"   Mobile: {device.IsMobile}");
                Console.WriteLine($"   Touch: {device.HasTouch}");
                Console.WriteLine($"   Scale: {device.DeviceScaleFactor}");
                Console.WriteLine($"   User Agent: {device.UserAgent?.Substring(0, Math.Min(80, device.UserAgent?.Length ?? 0))}...");
                Console.WriteLine();
            }
            
            Console.WriteLine($"Total devices available: {deviceNames.Count}");
        } 
        catch (Exception ex) 
        {
            Console.WriteLine($"❌ Error: {ex.Message}");
        }
    }
}
