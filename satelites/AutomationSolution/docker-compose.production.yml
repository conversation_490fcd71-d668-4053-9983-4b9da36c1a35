# =============================================================================
# Docker Compose Configuration for Automation Solution - Production
# Production-ready orchestration with monitoring and observability
# =============================================================================

version: '3.8'

# =============================================================================
# Networks
# =============================================================================
networks:
  automation-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  monitoring-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# =============================================================================
# Volumes
# =============================================================================
volumes:
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  automation_logs:
    driver: local
  automation_screenshots:
    driver: local

# =============================================================================
# Services
# =============================================================================
services:
  # ---------------------------------------------------------------------------
  # Redis - Message Queue and Caching
  # ---------------------------------------------------------------------------
  redis:
    image: redis:7.2-alpine
    container_name: automation-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - automation-network
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_REPLICATION_MODE=master
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    security_opt:
      - no-new-privileges:true
    user: "999:999"

  # ---------------------------------------------------------------------------
  # Automation Worker - Main Application Service
  # ---------------------------------------------------------------------------
  automation-worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
      target: runtime
      args:
        - BUILD_CONFIGURATION=Release
    image: automation-solution/worker:${VERSION:-latest}
    container_name: automation-worker
    restart: unless-stopped
    ports:
      - "${WORKER_PORT:-8080}:8080"
    networks:
      - automation-network
      - monitoring-network
    depends_on:
      redis:
        condition: service_healthy
    environment:
      # Application Configuration
      - ASPNETCORE_ENVIRONMENT=${ENVIRONMENT:-Production}
      - ASPNETCORE_URLS=http://+:8080
      
      # Redis Configuration
      - Worker__Redis__ConnectionString=redis:6379
      - Worker__Redis__TaskChannel=automation_channel
      - Worker__Redis__DeadLetterQueue=automation_dlq
      
      # Concurrency Configuration
      - Worker__Concurrency__MaxConcurrentTasks=${MAX_CONCURRENT_TASKS:-5}
      - Worker__Concurrency__TaskTimeoutMs=300000
      
      # Circuit Breaker Configuration
      - Worker__CircuitBreaker__FailureThreshold=10
      - Worker__CircuitBreaker__OpenDuration=00:02:00
      
      # Health Check Configuration
      - Worker__HealthCheck__Enabled=true
      - Worker__HealthCheck__Port=8080

      # Playwright Configuration
      - PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
      - PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=0
      - PLAYWRIGHT_HEADLESS=true

      # Appium Configuration
      - APPIUM_HOME=/usr/local/lib/node_modules/appium
      - ANDROID_HOME=/opt/android-sdk

      # Display Configuration for headless mode
      - DISPLAY=:99

      # Metrics Configuration
      - Worker__Metrics__Enabled=true
      - Worker__Metrics__Port=8080
      
      # Web Automation Configuration
      - WebAutomation__Headless=true
      - WebAutomation__ResourceManagement__MaxConcurrentBrowsers=3
      - WebAutomation__ResourceManagement__BrowserIdleTimeoutMinutes=5
      
      # Logging Configuration
      - Serilog__MinimumLevel__Default=Information
      - Serilog__WriteTo__0__Name=Console
      - Serilog__WriteTo__1__Name=File
      - Serilog__WriteTo__1__Args__path=/app/logs/worker-.log
      - Serilog__WriteTo__1__Args__rollingInterval=Day
      - Serilog__WriteTo__1__Args__retainedFileCountLimit=30
    volumes:
      - automation_logs:/app/logs
      - automation_screenshots:/app/screenshots
      - ./config/appsettings.Production.json:/app/appsettings.Production.json:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 120s
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
    read_only: false
    tmpfs:
      - /tmp:noexec,nosuid,size=100m

  # ---------------------------------------------------------------------------
  # Prometheus - Metrics Collection
  # ---------------------------------------------------------------------------
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: automation-prometheus
    restart: unless-stopped
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    networks:
      - monitoring-network
    volumes:
      - prometheus_data:/prometheus
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./config/alert_rules.yml:/etc/prometheus/alert_rules.yml:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
    security_opt:
      - no-new-privileges:true

  # ---------------------------------------------------------------------------
  # Grafana - Metrics Visualization
  # ---------------------------------------------------------------------------
  grafana:
    image: grafana/grafana:10.1.0
    container_name: automation-grafana
    restart: unless-stopped
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    networks:
      - monitoring-network
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards:ro
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=redis-datasource
    depends_on:
      - prometheus
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.3'
    security_opt:
      - no-new-privileges:true

  # ---------------------------------------------------------------------------
  # Nginx - Load Balancer and Reverse Proxy
  # ---------------------------------------------------------------------------
  nginx:
    image: nginx:1.25-alpine
    container_name: automation-nginx
    restart: unless-stopped
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    networks:
      - automation-network
      - monitoring-network
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/ssl:/etc/nginx/ssl:ro
    depends_on:
      - automation-worker
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 5s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.2'
    security_opt:
      - no-new-privileges:true
