# Multi-stage Dockerfile for Worker Coordinator
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy project files  
COPY src/Automation.AI/Automation.AI.fsproj src/Automation.AI/
COPY src/Automation.Core/Automation.Core.fsproj src/Automation.Core/
COPY src/Automation.Utilities/Automation.Utilities.fsproj src/Automation.Utilities/

# Restore dependencies
RUN dotnet restore src/Automation.AI/Automation.AI.fsproj

# Copy source code
COPY src/ src/

# Build application
RUN dotnet build src/Automation.AI/Automation.AI.fsproj -c Release --no-restore

# Runtime stage
FROM mcr.microsoft.com/dotnet/runtime:8.0 AS runtime
WORKDIR /app

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Create coordinator application
RUN echo 'open Automation.AI.WorkerCoordination\nopen System.Threading\nopen Automation.Utilities.Logging\n\nlet main () =\n    try\n        info "[Coordinator] Starting worker coordinator..."\n        let config = defaultCoordinatorConfig\n        use coordinator = new WorkerCoordinator(config)\n        coordinator.Start() |> Async.RunSynchronously\n        \n        info "[Coordinator] Worker coordinator started successfully"\n        Thread.Sleep(Timeout.Infinite)\n    with\n    | ex -> \n        error (sprintf "[Coordinator] Failed to start: %s" ex.Message)\n        \nmain ()' > /tmp/coordinator.fs

# Copy assemblies from build stage
COPY --from=build /root/.nuget/packages /root/.nuget/packages
COPY --from=build /src/src/Automation.AI/bin/Release/net8.0/* .
COPY --from=build /src/src/Automation.Core/bin/Release/net8.0/* .
COPY --from=build /src/src/Automation.Utilities/bin/Release/net8.0/* .

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

# Expose metrics port
EXPOSE 8080

# Set environment variables
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=1

# Run the coordinator
ENTRYPOINT ["dotnet", "Automation.AI.dll"]
