# =============================================================================
# Docker Compose Override for Local Development
# This file is automatically loaded by docker-compose for local development
# =============================================================================

version: '3.8'

services:
  # Development overrides for automation worker
  automation-worker:
    build:
      target: development
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - DOTNET_USE_POLLING_FILE_WATCHER=true
      - DOTNET_EnableDiagnostics=1
    volumes:
      - ./src:/src:ro
      - ./logs:/app/logs
    ports:
      - "5000:5000"
      - "5001:5001"

  # Development Redis with commander
  redis:
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --loglevel notice

  # Add Redis Commander for development
  redis-commander:
    image: rediscommander/redis-commander:latest
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
      - HTTP_USER=admin
      - HTTP_PASSWORD=admin123
    depends_on:
      - redis
