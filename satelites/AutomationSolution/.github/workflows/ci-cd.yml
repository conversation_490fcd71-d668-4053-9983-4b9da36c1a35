name: Build and Deploy Automation Solution

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: automation-solution

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'
    
    - name: Restore dependencies
      run: dotnet restore AutomationSolution.sln
    
    - name: Build solution
      run: dotnet build AutomationSolution.sln --no-restore --configuration Release
    
    - name: Run tests
      run: dotnet test AutomationSolution.sln --no-build --configuration Release --verbosity normal --logger trx --results-directory "TestResults-${{ github.sha }}"
    
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results
        path: TestResults-${{ github.sha }}/*.trx

  security-scan:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Run security scan
      uses: securecodewarrior/github-action-add-sarif@v1
      with:
        sarif-file: 'security-scan-results.sarif'
    
    - name: Run dependency check
      run: |
        dotnet list package --vulnerable --include-transitive || true

  build-images:
    runs-on: ubuntu-latest
    needs: [test, security-scan]
    if: github.event_name == 'push'
    
    strategy:
      matrix:
        service: [dispatcher, worker]
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ github.repository }}/${{ matrix.service }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        target: ${{ matrix.service }}
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

  integration-tests:
    runs-on: ubuntu-latest
    needs: build-images
    if: github.event_name == 'push'
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Build test images
      run: |
        docker build --target dispatcher -t test-dispatcher .
        docker build --target worker -t test-worker .
    
    - name: Run integration tests
      run: |
        # Start services
        docker run -d --name test-dispatcher --network host \
          -e REDIS_CONNECTION_STRING=localhost:6379 \
          test-dispatcher
        
        docker run -d --name test-worker --network host \
          -e REDIS_CONNECTION_STRING=localhost:6379 \
          test-worker
        
        # Wait for services to be ready
        sleep 30
        
        # Run health checks
        curl -f http://localhost:8080/health || exit 1
        curl -f http://localhost:8081/health || exit 1
        
        # Run basic automation test
        dotnet test src/Automation.Core/Automation.Core.fsproj --configuration Release --filter "Category=Integration"
    
    - name: Cleanup
      if: always()
      run: |
        docker stop test-dispatcher test-worker || true
        docker rm test-dispatcher test-worker || true

  deploy-staging:
    runs-on: ubuntu-latest
    needs: [build-images, integration-tests]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment commands here
        # Example: kubectl apply -f k8s/staging/
    
    - name: Run smoke tests
      run: |
        echo "Running smoke tests..."
        # Add smoke test commands here

  deploy-production:
    runs-on: ubuntu-latest
    needs: [build-images, integration-tests]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Add your production deployment commands here
        # Example: kubectl apply -f k8s/production/
    
    - name: Run smoke tests
      run: |
        echo "Running production smoke tests..."
        # Add smoke test commands here
    
    - name: Notify deployment
      uses: 8398a7/action-slack@v3
      if: always()
      with:
        status: ${{ job.status }}
        text: "Automation Solution deployment ${{ job.status }}"
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  cleanup:
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    
    steps:
    - name: Cleanup old images
      run: |
        echo "Cleaning up old container images..."
        # Add cleanup commands here
