<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Demo App">
    <title>Mobile Demo App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            touch-action: manipulation;
        }
        
        .app-container {
            max-width: 400px;
            margin: 0 auto;
            min-height: 100vh;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .header {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .header p {
            opacity: 0.8;
            font-size: 14px;
        }
        
        .content {
            padding: 20px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 16px;
        }
        
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            background: white;
            box-shadow: 0 0 0 2px #667eea;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            width: 100%;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .btn:hover, .btn:active {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-icon {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            background: #667eea;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .status {
            padding: 10px;
            border-radius: 8px;
            margin-top: 10px;
            text-align: center;
            display: none;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
            color: #4CAF50;
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.5);
            color: #F44336;
        }
        
        .tabs {
            display: flex;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .tab {
            flex: 1;
            padding: 10px;
            text-align: center;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .tab.active {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <h1>📱 Demo Mobile App</h1>
            <p>Automation Testing Demo</p>
        </div>
        
        <div class="content">
            <div class="tabs">
                <div class="tab active" onclick="showTab('login')">Login</div>
                <div class="tab" onclick="showTab('features')">Features</div>
                <div class="tab" onclick="showTab('settings')">Settings</div>
            </div>
            
            <!-- Login Tab -->
            <div id="login-tab" class="tab-content active">
                <div class="card">
                    <h3>🔐 User Login</h3>
                    <form id="loginForm">
                        <div class="form-group">
                            <label for="username">Username:</label>
                            <input type="text" id="username" name="username" placeholder="Enter your username" required>
                        </div>
                        <div class="form-group">
                            <label for="password">Password:</label>
                            <input type="password" id="password" name="password" placeholder="Enter your password" required>
                        </div>
                        <div class="form-group">
                            <label for="remember">
                                <input type="checkbox" id="remember" name="remember"> Remember me
                            </label>
                        </div>
                        <button type="submit" class="btn">Login</button>
                        <button type="button" class="btn btn-secondary" onclick="fillDemoData()">Fill Demo Data</button>
                    </form>
                    <div id="loginStatus" class="status"></div>
                </div>
            </div>
            
            <!-- Features Tab -->
            <div id="features-tab" class="tab-content">
                <div class="card">
                    <h3>✨ App Features</h3>
                    <ul class="feature-list">
                        <li><span class="feature-icon">📱</span> Mobile-First Design</li>
                        <li><span class="feature-icon">🔒</span> Secure Authentication</li>
                        <li><span class="feature-icon">📊</span> Real-time Analytics</li>
                        <li><span class="feature-icon">🌐</span> Offline Support</li>
                        <li><span class="feature-icon">🔔</span> Push Notifications</li>
                        <li><span class="feature-icon">⚡</span> Fast Performance</li>
                    </ul>
                    <button class="btn" onclick="testFeature()">Test Feature</button>
                    <div id="featureStatus" class="status"></div>
                </div>
            </div>
            
            <!-- Settings Tab -->
            <div id="settings-tab" class="tab-content">
                <div class="card">
                    <h3>⚙️ Settings</h3>
                    <div class="form-group">
                        <label for="theme">Theme:</label>
                        <select id="theme" name="theme">
                            <option value="auto">Auto</option>
                            <option value="light">Light</option>
                            <option value="dark">Dark</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="notifications">
                            <input type="checkbox" id="notifications" name="notifications" checked> Enable Notifications
                        </label>
                    </div>
                    <div class="form-group">
                        <label for="language">Language:</label>
                        <select id="language" name="language">
                            <option value="en">English</option>
                            <option value="es">Español</option>
                            <option value="fr">Français</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="feedback">Feedback:</label>
                        <textarea id="feedback" name="feedback" rows="3" placeholder="Tell us what you think..."></textarea>
                    </div>
                    <button class="btn" onclick="saveSettings()">Save Settings</button>
                    <div id="settingsStatus" class="status"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
        }
        
        function fillDemoData() {
            document.getElementById('username').value = 'demo_user';
            document.getElementById('password').value = 'demo123';
            document.getElementById('remember').checked = true;
            showStatus('loginStatus', 'Demo data filled!', 'success');
        }
        
        function testFeature() {
            showStatus('featureStatus', 'Feature test completed successfully!', 'success');
        }
        
        function saveSettings() {
            showStatus('settingsStatus', 'Settings saved successfully!', 'success');
        }
        
        function showStatus(elementId, message, type) {
            const statusEl = document.getElementById(elementId);
            statusEl.textContent = message;
            statusEl.className = 'status ' + type;
            statusEl.style.display = 'block';
            
            setTimeout(() => {
                statusEl.style.display = 'none';
            }, 3000);
        }
        
        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (username && password) {
                showStatus('loginStatus', 'Login successful! Welcome ' + username, 'success');
            } else {
                showStatus('loginStatus', 'Please fill in all fields', 'error');
            }
        });
        
        // Add touch feedback
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.95)';
            });
            btn.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
