using Microsoft.Playwright;
using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;

public class iOSAppTestResult
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TestName { get; set; } = string.Empty;
    public string AppName { get; set; } = string.Empty;
    public string AppUrl { get; set; } = string.Empty;
    public string iOSDevice { get; set; } = string.Empty;
    public string iOSVersion { get; set; } = "17.0";
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string Status { get; set; } = "Running";
    public string? ErrorMessage { get; set; }
    public List<string> Screenshots { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public List<string> TestSteps { get; set; } = new();
    public Dictionary<string, object> iOSFeatures { get; set; } = new();
}

class iOSAppTestRunner 
{
    public static async Task<iOSAppTestResult> RuniOSAppTest(string appName, string appUrl, string iOSDevice, string? testName = null)
    {
        testName ??= $"iOS {appName} App Test";
        
        var testResult = new iOSAppTestResult
        {
            TestName = testName,
            AppName = appName,
            AppUrl = appUrl,
            iOSDevice = iOSDevice,
            StartTime = DateTime.UtcNow
        };

        try 
        {
            Console.WriteLine($"🍎 Starting iOS app test: {testName}");
            Console.WriteLine($"📋 Test ID: {testResult.Id}");
            Console.WriteLine($"🌐 App: {appName}");
            Console.WriteLine($"📱 iOS Device: {iOSDevice}");
            Console.WriteLine($"🔗 URL: {appUrl}");
            
            Environment.SetEnvironmentVariable("PLAYWRIGHT_BROWSERS_PATH", "/ms-playwright");
            
            using var playwright = await Playwright.CreateAsync();
            Console.WriteLine("✅ Playwright initialized!");
            
            // Get iOS device configuration
            var device = playwright.Devices[iOSDevice];
            
            // Use WebKit (Safari) for iOS simulation
            await using var browser = await playwright.Webkit.LaunchAsync(new BrowserTypeLaunchOptions 
            { 
                Headless = true
            });
            
            // Create iOS context with Safari-specific settings
            await using var context = await browser.NewContextAsync(new BrowserNewContextOptions
            {
                ViewportSize = device.ViewportSize,
                UserAgent = device.UserAgent,
                DeviceScaleFactor = device.DeviceScaleFactor,
                IsMobile = device.IsMobile,
                HasTouch = device.HasTouch,
                Locale = "en-US",
                TimezoneId = "America/New_York",
                // iOS-specific settings
                ExtraHTTPHeaders = new Dictionary<string, string>
                {
                    ["Accept-Language"] = "en-US,en;q=0.9"
                }
            });
            
            var page = await context.NewPageAsync();
            Console.WriteLine($"✅ iOS Safari context created: {device.ViewportSize?.Width}x{device.ViewportSize?.Height}");
            testResult.TestSteps.Add("iOS Safari context initialized successfully");
            
            // Navigate to iOS app
            Console.WriteLine($"🚀 Launching iOS app: {appUrl}");
            await page.GotoAsync(appUrl, new PageGotoOptions 
            { 
                WaitUntil = WaitUntilState.NetworkIdle,
                Timeout = 30000
            });
            
            // Wait for iOS app to fully load
            await Task.Delay(3000);
            Console.WriteLine("✅ iOS app launched successfully!");
            testResult.TestSteps.Add("iOS app launched and loaded");
            
            // Take initial screenshot
            var screenshotDir = "/tmp/screenshots";
            Directory.CreateDirectory(screenshotDir);
            
            var initialScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-ios-{appName.ToLower()}-initial.png");
            await page.ScreenshotAsync(new PageScreenshotOptions
            {
                Path = initialScreenshot,
                FullPage = true
            });
            testResult.Screenshots.Add(initialScreenshot);
            Console.WriteLine($"📸 Initial iOS app screenshot captured");
            
            // Analyze iOS-specific features
            var title = await page.TitleAsync();
            var url = page.Url;
            
            // Check for iOS PWA features
            var hasAppleMobileWebAppCapable = await page.Locator("meta[name='apple-mobile-web-app-capable']").CountAsync() > 0;
            var hasAppleMobileWebAppTitle = await page.Locator("meta[name='apple-mobile-web-app-title']").CountAsync() > 0;
            var hasAppleMobileWebAppStatusBarStyle = await page.Locator("meta[name='apple-mobile-web-app-status-bar-style']").CountAsync() > 0;
            var hasAppleTouchIcon = await page.Locator("link[rel='apple-touch-icon']").CountAsync() > 0;
            var hasAppleTouchStartupImage = await page.Locator("link[rel='apple-touch-startup-image']").CountAsync() > 0;
            var hasServiceWorker = await page.EvaluateAsync<bool>("() => 'serviceWorker' in navigator");
            var hasManifest = await page.Locator("link[rel='manifest']").CountAsync() > 0;
            
            // iOS-specific JavaScript features
            var hasTouchEvents = await page.EvaluateAsync<bool>("() => 'ontouchstart' in window");
            var hasDeviceMotion = await page.EvaluateAsync<bool>("() => 'DeviceMotionEvent' in window");
            var hasDeviceOrientation = await page.EvaluateAsync<bool>("() => 'DeviceOrientationEvent' in window");
            var hasWebKit = await page.EvaluateAsync<bool>("() => 'webkitRequestAnimationFrame' in window");
            
            testResult.Metadata["title"] = title;
            testResult.Metadata["finalUrl"] = url;
            testResult.iOSFeatures["hasAppleMobileWebAppCapable"] = hasAppleMobileWebAppCapable;
            testResult.iOSFeatures["hasAppleMobileWebAppTitle"] = hasAppleMobileWebAppTitle;
            testResult.iOSFeatures["hasAppleMobileWebAppStatusBarStyle"] = hasAppleMobileWebAppStatusBarStyle;
            testResult.iOSFeatures["hasAppleTouchIcon"] = hasAppleTouchIcon;
            testResult.iOSFeatures["hasAppleTouchStartupImage"] = hasAppleTouchStartupImage;
            testResult.iOSFeatures["hasServiceWorker"] = hasServiceWorker;
            testResult.iOSFeatures["hasManifest"] = hasManifest;
            testResult.iOSFeatures["hasTouchEvents"] = hasTouchEvents;
            testResult.iOSFeatures["hasDeviceMotion"] = hasDeviceMotion;
            testResult.iOSFeatures["hasDeviceOrientation"] = hasDeviceOrientation;
            testResult.iOSFeatures["hasWebKit"] = hasWebKit;
            testResult.iOSFeatures["isiOSPWA"] = hasAppleMobileWebAppCapable && hasAppleTouchIcon;
            
            Console.WriteLine($"📄 iOS App title: {title}");
            Console.WriteLine($"🍎 iOS PWA features: Apple-capable={hasAppleMobileWebAppCapable}, Touch-icon={hasAppleTouchIcon}");
            Console.WriteLine($"📱 iOS capabilities: Touch={hasTouchEvents}, Motion={hasDeviceMotion}, WebKit={hasWebKit}");
            
            // iOS-specific tests
            await RuniOSSpecificTests(page, testResult, screenshotDir, appName);
            
            // Test iOS gestures and interactions
            await TestiOSGestures(page, testResult, screenshotDir);
            
            // Final iOS app analysis
            var linkCount = await page.Locator("a").CountAsync();
            var buttonCount = await page.Locator("button").CountAsync();
            var inputCount = await page.Locator("input").CountAsync();
            var imageCount = await page.Locator("img").CountAsync();
            
            testResult.Metadata["linkCount"] = linkCount;
            testResult.Metadata["buttonCount"] = buttonCount;
            testResult.Metadata["inputCount"] = inputCount;
            testResult.Metadata["imageCount"] = imageCount;
            testResult.Metadata["totalTestSteps"] = testResult.TestSteps.Count;
            testResult.Metadata["successfulSteps"] = testResult.TestSteps.Count(s => s.Contains("successfully"));
            testResult.Metadata["failedSteps"] = testResult.TestSteps.Count(s => s.Contains("failed"));
            
            Console.WriteLine($"📊 iOS App elements: {linkCount} links, {buttonCount} buttons, {inputCount} inputs, {imageCount} images");
            Console.WriteLine($"✅ Test steps completed: {testResult.TestSteps.Count}");
            Console.WriteLine($"✅ Successful steps: {testResult.Metadata["successfulSteps"]}");
            Console.WriteLine($"❌ Failed steps: {testResult.Metadata["failedSteps"]}");
            
            // Mark test as passed
            testResult.Status = "Passed";
            testResult.EndTime = DateTime.UtcNow;
            
            Console.WriteLine($"🎉 iOS {appName} app test completed successfully!");
        } 
        catch (Exception ex) 
        {
            testResult.Status = "Failed";
            testResult.ErrorMessage = ex.Message;
            testResult.EndTime = DateTime.UtcNow;
            testResult.TestSteps.Add($"Test failed with error: {ex.Message}");
            
            Console.WriteLine($"❌ iOS {appName} app test failed: {ex.Message}");
        }
        
        // Save test results to JSON
        var resultsDir = "/tmp/test-results";
        Directory.CreateDirectory(resultsDir);
        
        var resultPath = Path.Combine(resultsDir, $"{testResult.Id}.json");
        var json = JsonSerializer.Serialize(testResult, new JsonSerializerOptions 
        { 
            WriteIndented = true 
        });
        
        await File.WriteAllTextAsync(resultPath, json);
        Console.WriteLine($"💾 iOS app test results saved: {resultPath}");
        
        return testResult;
    }

    static async Task RuniOSSpecificTests(IPage page, iOSAppTestResult testResult, string screenshotDir, string appName)
    {
        Console.WriteLine("🧪 Running iOS-specific tests...");
        
        try
        {
            // Test iOS scroll behavior (momentum scrolling)
            Console.WriteLine("📱 Testing iOS momentum scrolling...");
            await page.EvaluateAsync(@"
                window.scrollTo({ 
                    top: document.body.scrollHeight / 3, 
                    behavior: 'smooth' 
                });
            ");
            await Task.Delay(2000);
            
            var scrollScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-ios-scroll.png");
            await page.ScreenshotAsync(new PageScreenshotOptions { Path = scrollScreenshot, FullPage = false });
            testResult.Screenshots.Add(scrollScreenshot);
            
            testResult.TestSteps.Add("iOS momentum scrolling test completed successfully");
            Console.WriteLine("✅ iOS momentum scrolling test completed");
        }
        catch (Exception ex)
        {
            testResult.TestSteps.Add($"iOS momentum scrolling test failed: {ex.Message}");
            Console.WriteLine($"⚠️ iOS momentum scrolling test failed: {ex.Message}");
        }

        try
        {
            // Test iOS safe area
            Console.WriteLine("📱 Testing iOS safe area...");
            var safeAreaTop = await page.EvaluateAsync<string>("() => getComputedStyle(document.documentElement).getPropertyValue('--safe-area-inset-top')");
            var safeAreaBottom = await page.EvaluateAsync<string>("() => getComputedStyle(document.documentElement).getPropertyValue('--safe-area-inset-bottom')");
            
            testResult.iOSFeatures["safeAreaTop"] = safeAreaTop ?? "0px";
            testResult.iOSFeatures["safeAreaBottom"] = safeAreaBottom ?? "0px";
            
            testResult.TestSteps.Add("iOS safe area test completed successfully");
            Console.WriteLine($"✅ iOS safe area: top={safeAreaTop}, bottom={safeAreaBottom}");
        }
        catch (Exception ex)
        {
            testResult.TestSteps.Add($"iOS safe area test failed: {ex.Message}");
            Console.WriteLine($"⚠️ iOS safe area test failed: {ex.Message}");
        }
    }

    static async Task TestiOSGestures(IPage page, iOSAppTestResult testResult, string screenshotDir)
    {
        Console.WriteLine("🧪 Testing iOS gestures...");
        
        try
        {
            // Test tap gesture
            Console.WriteLine("👆 Testing iOS tap gesture...");
            var firstTappableElement = await page.Locator("button, a, [onclick], [role='button']").First.ElementHandleAsync();
            if (firstTappableElement != null)
            {
                await firstTappableElement.TapAsync();
                await Task.Delay(1000);
                
                var tapScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-ios-tap.png");
                await page.ScreenshotAsync(new PageScreenshotOptions { Path = tapScreenshot, FullPage = true });
                testResult.Screenshots.Add(tapScreenshot);
                
                testResult.TestSteps.Add("iOS tap gesture test completed successfully");
                Console.WriteLine("✅ iOS tap gesture test completed");
            }
        }
        catch (Exception ex)
        {
            testResult.TestSteps.Add($"iOS tap gesture test failed: {ex.Message}");
            Console.WriteLine($"⚠️ iOS tap gesture test failed: {ex.Message}");
        }

        try
        {
            // Test iOS pinch zoom (if supported)
            Console.WriteLine("🤏 Testing iOS pinch zoom simulation...");
            await page.EvaluateAsync(@"
                document.body.style.transform = 'scale(1.2)';
                document.body.style.transformOrigin = 'center center';
            ");
            await Task.Delay(1000);
            
            var zoomScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-ios-zoom.png");
            await page.ScreenshotAsync(new PageScreenshotOptions { Path = zoomScreenshot, FullPage = false });
            testResult.Screenshots.Add(zoomScreenshot);
            
            // Reset zoom
            await page.EvaluateAsync("document.body.style.transform = 'scale(1)'");
            
            testResult.TestSteps.Add("iOS pinch zoom simulation completed successfully");
            Console.WriteLine("✅ iOS pinch zoom simulation completed");
        }
        catch (Exception ex)
        {
            testResult.TestSteps.Add($"iOS pinch zoom simulation failed: {ex.Message}");
            Console.WriteLine($"⚠️ iOS pinch zoom simulation failed: {ex.Message}");
        }
    }
}

class Program 
{
    static async Task Main(string[] args) 
    {
        var iOSApps = new[]
        {
            new { Name = "Wikipedia", Url = "https://en.m.wikipedia.org", Device = "iPhone 14" },
            new { Name = "Wikipedia", Url = "https://en.m.wikipedia.org", Device = "iPhone 13" },
            new { Name = "Apple", Url = "https://www.apple.com", Device = "iPhone 14 Pro" },
            new { Name = "GitHub", Url = "https://github.com", Device = "iPhone 12" }
        };

        Console.WriteLine("🍎 Starting iOS APP TEST SUITE");
        Console.WriteLine($"📱 Testing {iOSApps.Length} iOS apps on different devices\n");

        var results = new List<iOSAppTestResult>();

        foreach (var app in iOSApps)
        {
            try
            {
                var result = await iOSAppTestRunner.RuniOSAppTest(app.Name, app.Url, app.Device);
                results.Add(result);
                
                // Small delay between tests
                await Task.Delay(3000);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to test {app.Name} on {app.Device}: {ex.Message}");
            }
        }

        // Display final summary
        var totalTests = results.Count;
        var passedTests = results.Count(r => r.Status == "Passed");
        var failedTests = totalTests - passedTests;
        var totalScreenshots = results.Sum(r => r.Screenshots.Count);

        Console.WriteLine("\n🎯 iOS APP TEST SUITE SUMMARY:");
        Console.WriteLine($"   Total iOS Apps Tested: {totalTests}");
        Console.WriteLine($"   ✅ Passed: {passedTests}");
        Console.WriteLine($"   ❌ Failed: {failedTests}");
        Console.WriteLine($"   📸 Screenshots: {totalScreenshots}");
        Console.WriteLine($"   📊 Success Rate: {(totalTests > 0 ? (double)passedTests / totalTests * 100 : 0):F1}%");

        if (failedTests == 0)
        {
            Console.WriteLine("\n🎉 ALL iOS APP TESTS PASSED! 🍎📱🎉");
        }
        else
        {
            Console.WriteLine($"\n⚠️ {failedTests} iOS app test(s) failed.");
        }
    }
}
