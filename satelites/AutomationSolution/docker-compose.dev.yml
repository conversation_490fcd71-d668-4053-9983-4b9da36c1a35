# =============================================================================
# Docker Compose Configuration for Automation Solution - Development
# Simplified setup for local development and testing
# =============================================================================

version: '3.8'

networks:
  automation-dev:
    driver: bridge

volumes:
  redis_dev_data:
    driver: local
  automation_dev_logs:
    driver: local

services:
  # ---------------------------------------------------------------------------
  # Redis - Development Instance
  # ---------------------------------------------------------------------------
  redis-dev:
    image: redis:7.2-alpine
    container_name: automation-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - automation-dev
    volumes:
      - redis_dev_data:/data
    command: redis-server --appendonly yes --loglevel notice
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # ---------------------------------------------------------------------------
  # Automation Worker - Development Build
  # ---------------------------------------------------------------------------
  automation-worker-dev:
    build:
      context: .
      dockerfile: Dockerfile.worker
      target: development
    container_name: automation-worker-dev
    restart: unless-stopped
    ports:
      - "8080:8080"
      - "5000:5000"   # Additional dev port
      - "5001:5001"   # HTTPS dev port
    networks:
      - automation-dev
    depends_on:
      redis-dev:
        condition: service_healthy
    environment:
      # Development Configuration
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080;http://+:5000;https://+:5001
      - ASPNETCORE_HTTPS_PORT=5001
      
      # Redis Configuration
      - Worker__Redis__ConnectionString=redis-dev:6379
      - Worker__Redis__TaskChannel=automation_dev_channel
      - Worker__Redis__DeadLetterQueue=automation_dev_dlq
      
      # Development Settings
      - Worker__Concurrency__MaxConcurrentTasks=2
      - Worker__CircuitBreaker__FailureThreshold=3
      - WebAutomation__Headless=false  # Show browser for debugging
      - WebAutomation__ResourceManagement__MaxConcurrentBrowsers=1
      
      # Logging Configuration
      - Serilog__MinimumLevel__Default=Debug
      - Serilog__WriteTo__0__Name=Console
      - Serilog__WriteTo__0__Args__outputTemplate=[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}
      
      # Development Features
      - DOTNET_USE_POLLING_FILE_WATCHER=true
      - DOTNET_EnableDiagnostics=1
    volumes:
      - automation_dev_logs:/app/logs
      - ./src:/src:ro  # Mount source for hot reload
      - ./config:/app/config:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # ---------------------------------------------------------------------------
  # Redis Commander - Redis Management UI
  # ---------------------------------------------------------------------------
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: automation-redis-commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    networks:
      - automation-dev
    environment:
      - REDIS_HOSTS=local:redis-dev:6379
      - HTTP_USER=admin
      - HTTP_PASSWORD=admin123
    depends_on:
      - redis-dev

  # ---------------------------------------------------------------------------
  # Prometheus - Development Metrics
  # ---------------------------------------------------------------------------
  prometheus-dev:
    image: prom/prometheus:v2.47.0
    container_name: automation-prometheus-dev
    restart: unless-stopped
    ports:
      - "9090:9090"
    networks:
      - automation-dev
    volumes:
      - ./config/prometheus.dev.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=7d'
      - '--web.enable-lifecycle'
    depends_on:
      - automation-worker-dev

  # ---------------------------------------------------------------------------
  # Test Runner - Integration Tests
  # ---------------------------------------------------------------------------
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile.worker
      target: testing
    container_name: automation-test-runner
    networks:
      - automation-dev
    depends_on:
      redis-dev:
        condition: service_healthy
    environment:
      - ASPNETCORE_ENVIRONMENT=Testing
      - REDIS_CONNECTION_STRING=redis-dev:6379
      - AUTOMATION_TEST_TIMEOUT_MS=30000
    volumes:
      - ./tests:/tests:ro
      - ./test-results:/test-results
    profiles:
      - testing  # Only start when explicitly requested
