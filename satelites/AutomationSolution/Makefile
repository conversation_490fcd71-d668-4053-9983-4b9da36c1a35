# =============================================================================
# Makefile for Automation Solution
# Simplifies Docker operations and development workflows
# =============================================================================

# Configuration
PROJECT_NAME := automation-solution
IMAGE_NAME := $(PROJECT_NAME)/worker
VERSION ?= $(shell date +%Y%m%d-%H%M%S)
REGISTRY ?= 
ENVIRONMENT ?= development

# Docker Compose files
COMPOSE_DEV := docker-compose.dev.yml
COMPOSE_PROD := docker-compose.production.yml
COMPOSE_OVERRIDE := docker-compose.override.yml

# Colors for output
BLUE := \033[0;34m
GREEN := \033[0;32m
YELLOW := \033[1;33m
RED := \033[0;31m
NC := \033[0m # No Color

# Default target
.DEFAULT_GOAL := help

# =============================================================================
# Help
# =============================================================================

.PHONY: help
help: ## Show this help message
	@echo "$(BLUE)Automation Solution - Docker Management$(NC)"
	@echo ""
	@echo "$(YELLOW)Usage:$(NC)"
	@echo "  make <target> [VARIABLE=value]"
	@echo ""
	@echo "$(YELLOW)Variables:$(NC)"
	@echo "  VERSION=$(VERSION)"
	@echo "  REGISTRY=$(REGISTRY)"
	@echo "  ENVIRONMENT=$(ENVIRONMENT)"
	@echo ""
	@echo "$(YELLOW)Targets:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# =============================================================================
# Development
# =============================================================================

.PHONY: dev-up
dev-up: ## Start development environment
	@echo "$(BLUE)Starting development environment...$(NC)"
	docker-compose -f $(COMPOSE_DEV) up -d
	@echo "$(GREEN)Development environment started!$(NC)"
	@echo "Application: http://localhost:8080"
	@echo "Redis Commander: http://localhost:8081"
	@echo "Prometheus: http://localhost:9090"

.PHONY: dev-down
dev-down: ## Stop development environment
	@echo "$(BLUE)Stopping development environment...$(NC)"
	docker-compose -f $(COMPOSE_DEV) down
	@echo "$(GREEN)Development environment stopped!$(NC)"

.PHONY: dev-logs
dev-logs: ## Show development logs
	docker-compose -f $(COMPOSE_DEV) logs -f

.PHONY: dev-status
dev-status: ## Show development status
	docker-compose -f $(COMPOSE_DEV) ps

.PHONY: dev-restart
dev-restart: ## Restart development environment
	@echo "$(BLUE)Restarting development environment...$(NC)"
	docker-compose -f $(COMPOSE_DEV) restart
	@echo "$(GREEN)Development environment restarted!$(NC)"

# =============================================================================
# Production
# =============================================================================

.PHONY: prod-up
prod-up: ## Start production environment
	@echo "$(BLUE)Starting production environment...$(NC)"
	docker-compose -f $(COMPOSE_PROD) --env-file config/environments/.env.production up -d
	@echo "$(GREEN)Production environment started!$(NC)"

.PHONY: prod-down
prod-down: ## Stop production environment
	@echo "$(BLUE)Stopping production environment...$(NC)"
	docker-compose -f $(COMPOSE_PROD) down
	@echo "$(GREEN)Production environment stopped!$(NC)"

.PHONY: prod-logs
prod-logs: ## Show production logs
	docker-compose -f $(COMPOSE_PROD) logs -f

.PHONY: prod-status
prod-status: ## Show production status
	docker-compose -f $(COMPOSE_PROD) ps

.PHONY: deploy
deploy: ## Deploy to specified environment
	@echo "$(BLUE)Deploying to $(ENVIRONMENT) environment...$(NC)"
	./scripts/deployment/deploy.sh -e $(ENVIRONMENT) deploy
	@echo "$(GREEN)Deployment completed!$(NC)"

# =============================================================================
# Building
# =============================================================================

.PHONY: build
build: ## Build Docker image
	@echo "$(BLUE)Building Docker image...$(NC)"
	./scripts/docker/build.sh -v $(VERSION)
	@echo "$(GREEN)Build completed!$(NC)"

.PHONY: build-dev
build-dev: ## Build development image
	@echo "$(BLUE)Building development image...$(NC)"
	./scripts/docker/build.sh -t development -v $(VERSION)
	@echo "$(GREEN)Development build completed!$(NC)"

.PHONY: build-minimal
build-minimal: ## Build minimal image (no browsers)
	@echo "$(BLUE)Building minimal image...$(NC)"
	./scripts/docker/build.sh --minimal -v $(VERSION)
	@echo "$(GREEN)Minimal build completed!$(NC)"

.PHONY: build-clean
build-clean: ## Clean build (no cache)
	@echo "$(BLUE)Building with clean cache...$(NC)"
	./scripts/docker/build.sh -c -v $(VERSION)
	@echo "$(GREEN)Clean build completed!$(NC)"

.PHONY: build-push
build-push: ## Build and push to registry
	@echo "$(BLUE)Building and pushing to registry...$(NC)"
	./scripts/docker/build.sh -v $(VERSION) -r $(REGISTRY) -p
	@echo "$(GREEN)Build and push completed!$(NC)"

# =============================================================================
# Testing
# =============================================================================

.PHONY: test
test: ## Run all tests
	@echo "$(BLUE)Running all tests...$(NC)"
	./scripts/docker/test.sh
	@echo "$(GREEN)Tests completed!$(NC)"

.PHONY: test-integration
test-integration: ## Run integration tests
	@echo "$(BLUE)Running integration tests...$(NC)"
	./scripts/docker/test.sh -c Integration
	@echo "$(GREEN)Integration tests completed!$(NC)"

.PHONY: test-performance
test-performance: ## Run performance tests
	@echo "$(BLUE)Running performance tests...$(NC)"
	./scripts/docker/test.sh -c Performance
	@echo "$(GREEN)Performance tests completed!$(NC)"

.PHONY: test-security
test-security: ## Run security tests
	@echo "$(BLUE)Running security tests...$(NC)"
	./scripts/docker/test.sh -c Security
	@echo "$(GREEN)Security tests completed!$(NC)"

.PHONY: test-coverage
test-coverage: ## Run tests with coverage
	@echo "$(BLUE)Running tests with coverage...$(NC)"
	./scripts/docker/test.sh --coverage
	@echo "$(GREEN)Coverage tests completed!$(NC)"
	@echo "Coverage report: ./test-results/coverage-report/index.html"

# =============================================================================
# Maintenance
# =============================================================================

.PHONY: clean
clean: ## Clean Docker resources
	@echo "$(BLUE)Cleaning Docker resources...$(NC)"
	docker system prune -f
	docker volume prune -f
	@echo "$(GREEN)Cleanup completed!$(NC)"

.PHONY: clean-all
clean-all: ## Clean all Docker resources (including images)
	@echo "$(YELLOW)Warning: This will remove all unused Docker resources!$(NC)"
	@read -p "Are you sure? [y/N] " -n 1 -r; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		echo ""; \
		echo "$(BLUE)Cleaning all Docker resources...$(NC)"; \
		docker system prune -af; \
		docker volume prune -f; \
		echo "$(GREEN)Complete cleanup finished!$(NC)"; \
	else \
		echo ""; \
		echo "$(YELLOW)Cleanup cancelled.$(NC)"; \
	fi

.PHONY: backup
backup: ## Create backup
	@echo "$(BLUE)Creating backup...$(NC)"
	./scripts/deployment/deploy.sh -e $(ENVIRONMENT) backup
	@echo "$(GREEN)Backup completed!$(NC)"

.PHONY: update
update: ## Update application
	@echo "$(BLUE)Updating application...$(NC)"
	./scripts/deployment/deploy.sh -e $(ENVIRONMENT) update
	@echo "$(GREEN)Update completed!$(NC)"

# =============================================================================
# Monitoring
# =============================================================================

.PHONY: health
health: ## Check application health
	@echo "$(BLUE)Checking application health...$(NC)"
	@curl -f http://localhost:8080/health && echo "$(GREEN)Application is healthy!$(NC)" || echo "$(RED)Application is unhealthy!$(NC)"

.PHONY: metrics
metrics: ## Show application metrics
	@echo "$(BLUE)Application metrics:$(NC)"
	@curl -s http://localhost:8080/metrics | head -20

.PHONY: logs
logs: ## Show application logs
	@if [ "$(ENVIRONMENT)" = "production" ]; then \
		docker-compose -f $(COMPOSE_PROD) logs -f automation-worker; \
	else \
		docker-compose -f $(COMPOSE_DEV) logs -f automation-worker-dev; \
	fi

# =============================================================================
# Scaling
# =============================================================================

.PHONY: scale
scale: ## Scale workers (usage: make scale REPLICAS=3)
	@if [ -z "$(REPLICAS)" ]; then \
		echo "$(RED)Error: REPLICAS variable is required$(NC)"; \
		echo "Usage: make scale REPLICAS=3"; \
		exit 1; \
	fi
	@echo "$(BLUE)Scaling to $(REPLICAS) replicas...$(NC)"
	@./scripts/deployment/deploy.sh -e $(ENVIRONMENT) --scale $(REPLICAS) deploy
	@echo "$(GREEN)Scaling completed!$(NC)"

# =============================================================================
# Utilities
# =============================================================================

.PHONY: shell
shell: ## Open shell in worker container
	@if [ "$(ENVIRONMENT)" = "production" ]; then \
		docker-compose -f $(COMPOSE_PROD) exec automation-worker bash; \
	else \
		docker-compose -f $(COMPOSE_DEV) exec automation-worker-dev bash; \
	fi

.PHONY: redis-cli
redis-cli: ## Open Redis CLI
	@if [ "$(ENVIRONMENT)" = "production" ]; then \
		docker-compose -f $(COMPOSE_PROD) exec redis redis-cli; \
	else \
		docker-compose -f $(COMPOSE_DEV) exec redis-dev redis-cli; \
	fi

.PHONY: ps
ps: ## Show running containers
	@docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

.PHONY: images
images: ## Show project images
	@docker images $(PROJECT_NAME)/* --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

# =============================================================================
# Quick Commands
# =============================================================================

.PHONY: up
up: dev-up ## Alias for dev-up

.PHONY: down
down: dev-down ## Alias for dev-down

.PHONY: restart
restart: dev-restart ## Alias for dev-restart

.PHONY: status
status: dev-status ## Alias for dev-status
