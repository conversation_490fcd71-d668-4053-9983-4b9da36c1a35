# Multi-stage Dockerfile for AI Automation Solution
# Build stage
FROM mcr.microsoft.com/dotnet/sdk:10.0-preview AS build
WORKDIR /src

# Copy project files
COPY src/ ./src/
COPY *.sln ./

# Restore dependencies
RUN dotnet restore AutomationSolution.sln

# Build the solution
RUN dotnet build AutomationSolution.sln -c Release --no-restore

# Test stage
FROM build AS test
WORKDIR /src

# Run tests (if any exist)
RUN dotnet test AutomationSolution.sln -c Release --no-build --verbosity normal --logger trx --results-directory /testresults

# Publish stage
FROM build AS publish
WORKDIR /src

# Publish each project
RUN dotnet publish src/Automation.Dispatcher/Automation.Dispatcher.fsproj -c Release -o /app/dispatcher --no-build
RUN dotnet publish src/Automation.Worker/Automation.Worker.fsproj -c Release -o /app/worker --no-build

# Runtime stage - Dispatcher
FROM mcr.microsoft.com/dotnet/aspnet:10.0-preview-alpine AS dispatcher
WORKDIR /app

# Install required packages for automation
RUN apk add --no-cache \
    chromium \
    chromium-chromedriver \
    nss \
    freetype \
    harfbuzz \
    ca-certificates \
    ttf-freefont \
    nodejs \
    npm

# Set environment variables for Chromium
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser \
    CHROME_BIN=/usr/bin/chromium-browser \
    CHROME_PATH=/usr/bin/chromium-browser

# Copy published files
COPY --from=publish /app/dispatcher .

# Create non-root user for security
RUN addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Expose ports
EXPOSE 8080

# Set entrypoint
ENTRYPOINT ["dotnet", "Automation.Dispatcher.dll"]

# Runtime stage - Worker
FROM mcr.microsoft.com/dotnet/aspnet:10.0-preview-alpine AS worker
WORKDIR /app

# Install required packages for automation
RUN apk add --no-cache \
    chromium \
    chromium-chromedriver \
    nss \
    freetype \
    harfbuzz \
    ca-certificates \
    ttf-freefont \
    nodejs \
    npm \
    curl

# Set environment variables for Chromium
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser \
    CHROME_BIN=/usr/bin/chromium-browser \
    CHROME_PATH=/usr/bin/chromium-browser

# Copy published files
COPY --from=publish /app/worker .

# Create non-root user for security
RUN addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8081/health || exit 1

# Expose ports
EXPOSE 8081

# Set entrypoint
ENTRYPOINT ["dotnet", "Automation.Worker.dll"]
