using Microsoft.Playwright;
using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;

public class AppSimulationTestResult
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TestName { get; set; } = string.Empty;
    public string AppUrl { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string Status { get; set; } = "Running";
    public string? ErrorMessage { get; set; }
    public List<string> Screenshots { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public List<string> TestSteps { get; set; } = new();
}

class AppSimulationTestRunner 
{
    public static async Task<AppSimulationTestResult> RunAppSimulationTest(string appUrl, string? testName = null, string deviceType = "iPhone 12")
    {
        testName ??= "Native App Simulation Test";
        
        var testResult = new AppSimulationTestResult
        {
            TestName = testName,
            AppUrl = appUrl,
            Platform = $"App Simulation - {deviceType}",
            StartTime = DateTime.UtcNow
        };

        try 
        {
            Console.WriteLine($"📱 Starting app simulation test: {testName}");
            Console.WriteLine($"📋 Test ID: {testResult.Id}");
            Console.WriteLine($"🌐 App URL: {appUrl}");
            Console.WriteLine($"📱 Device: {deviceType}");
            
            Environment.SetEnvironmentVariable("PLAYWRIGHT_BROWSERS_PATH", "/ms-playwright");
            
            using var playwright = await Playwright.CreateAsync();
            Console.WriteLine("✅ Playwright initialized!");
            
            // Get device configuration
            var device = playwright.Devices[deviceType];
            
            await using var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions 
            { 
                Headless = true 
            });
            
            // Create context with mobile device emulation in app mode
            await using var context = await browser.NewContextAsync(new BrowserNewContextOptions
            {
                ViewportSize = device.ViewportSize,
                UserAgent = device.UserAgent,
                DeviceScaleFactor = device.DeviceScaleFactor,
                IsMobile = device.IsMobile,
                HasTouch = device.HasTouch
            });
            
            var page = await context.NewPageAsync();
            Console.WriteLine($"✅ App context created: {device.ViewportSize?.Width}x{device.ViewportSize?.Height}");
            testResult.TestSteps.Add("App context initialized successfully");
            
            // Navigate to app
            Console.WriteLine($"🚀 Launching app: {appUrl}");
            await page.GotoAsync(appUrl, new PageGotoOptions 
            { 
                WaitUntil = WaitUntilState.NetworkIdle,
                Timeout = 20000
            });
            Console.WriteLine("✅ App launched successfully!");
            testResult.TestSteps.Add("App launched and loaded");
            
            // Take initial screenshot
            var screenshotDir = "/tmp/screenshots";
            Directory.CreateDirectory(screenshotDir);
            
            var initialScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-app-sim-initial.png");
            await page.ScreenshotAsync(new PageScreenshotOptions
            {
                Path = initialScreenshot,
                FullPage = true
            });
            testResult.Screenshots.Add(initialScreenshot);
            Console.WriteLine($"📸 Initial app screenshot captured");
            
            // Test 1: Fill demo data
            Console.WriteLine("🧪 Test 1: Filling demo data...");
            try
            {
                var fillDemoButton = await page.WaitForSelectorAsync("button:has-text('Fill Demo Data')", new PageWaitForSelectorOptions { Timeout = 5000 });
                await fillDemoButton.ClickAsync();
                await Task.Delay(1000);
                
                var demoScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-app-sim-demo-filled.png");
                await page.ScreenshotAsync(new PageScreenshotOptions { Path = demoScreenshot, FullPage = true });
                testResult.Screenshots.Add(demoScreenshot);
                
                testResult.TestSteps.Add("Demo data filled successfully");
                Console.WriteLine("✅ Demo data filled");
            }
            catch (Exception ex)
            {
                testResult.TestSteps.Add($"Demo data fill failed: {ex.Message}");
                Console.WriteLine($"⚠️ Demo data fill failed: {ex.Message}");
            }
            
            // Test 2: Login form submission
            Console.WriteLine("🧪 Test 2: Testing login form...");
            try
            {
                var loginButton = await page.WaitForSelectorAsync("button[type='submit']:has-text('Login')", new PageWaitForSelectorOptions { Timeout = 5000 });
                await loginButton.ClickAsync();
                await Task.Delay(2000);
                
                var loginScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-app-sim-login.png");
                await page.ScreenshotAsync(new PageScreenshotOptions { Path = loginScreenshot, FullPage = true });
                testResult.Screenshots.Add(loginScreenshot);
                
                testResult.TestSteps.Add("Login form submitted successfully");
                Console.WriteLine("✅ Login form submitted");
            }
            catch (Exception ex)
            {
                testResult.TestSteps.Add($"Login form submission failed: {ex.Message}");
                Console.WriteLine($"⚠️ Login failed: {ex.Message}");
            }
            
            // Test 3: Navigate to Features tab
            Console.WriteLine("🧪 Test 3: Navigating to Features tab...");
            try
            {
                var featuresTab = await page.WaitForSelectorAsync(".tab:has-text('Features')", new PageWaitForSelectorOptions { Timeout = 5000 });
                await featuresTab.TapAsync();
                await Task.Delay(1000);
                
                var featuresScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-app-sim-features.png");
                await page.ScreenshotAsync(new PageScreenshotOptions { Path = featuresScreenshot, FullPage = true });
                testResult.Screenshots.Add(featuresScreenshot);
                
                testResult.TestSteps.Add("Navigated to Features tab");
                Console.WriteLine("✅ Features tab opened");
            }
            catch (Exception ex)
            {
                testResult.TestSteps.Add($"Features tab navigation failed: {ex.Message}");
                Console.WriteLine($"⚠️ Features navigation failed: {ex.Message}");
            }
            
            // Test 4: Test feature button
            Console.WriteLine("🧪 Test 4: Testing feature button...");
            try
            {
                var testFeatureButton = await page.WaitForSelectorAsync("button:has-text('Test Feature')", new PageWaitForSelectorOptions { Timeout = 5000 });
                await testFeatureButton.TapAsync();
                await Task.Delay(1000);
                
                var featureTestScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-app-sim-feature-test.png");
                await page.ScreenshotAsync(new PageScreenshotOptions { Path = featureTestScreenshot, FullPage = true });
                testResult.Screenshots.Add(featureTestScreenshot);
                
                testResult.TestSteps.Add("Feature test button clicked successfully");
                Console.WriteLine("✅ Feature test completed");
            }
            catch (Exception ex)
            {
                testResult.TestSteps.Add($"Feature test failed: {ex.Message}");
                Console.WriteLine($"⚠️ Feature test failed: {ex.Message}");
            }
            
            // Test 5: Navigate to Settings and fill form
            Console.WriteLine("🧪 Test 5: Testing Settings tab...");
            try
            {
                var settingsTab = await page.WaitForSelectorAsync(".tab:has-text('Settings')", new PageWaitForSelectorOptions { Timeout = 5000 });
                await settingsTab.TapAsync();
                await Task.Delay(1000);
                
                // Fill settings form
                var themeSelect = await page.WaitForSelectorAsync("#theme", new PageWaitForSelectorOptions { Timeout = 5000 });
                await themeSelect.SelectOptionAsync("dark");
                
                var feedbackTextarea = await page.WaitForSelectorAsync("#feedback", new PageWaitForSelectorOptions { Timeout = 5000 });
                await feedbackTextarea.FillAsync("This is an automated test feedback from App Simulation!");
                
                var saveButton = await page.WaitForSelectorAsync("button:has-text('Save Settings')", new PageWaitForSelectorOptions { Timeout = 5000 });
                await saveButton.TapAsync();
                await Task.Delay(1000);
                
                var settingsScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-app-sim-settings.png");
                await page.ScreenshotAsync(new PageScreenshotOptions { Path = settingsScreenshot, FullPage = true });
                testResult.Screenshots.Add(settingsScreenshot);
                
                testResult.TestSteps.Add("Settings form filled and saved successfully");
                Console.WriteLine("✅ Settings test completed");
            }
            catch (Exception ex)
            {
                testResult.TestSteps.Add($"Settings test failed: {ex.Message}");
                Console.WriteLine($"⚠️ Settings test failed: {ex.Message}");
            }
            
            // Test 6: Scroll test
            Console.WriteLine("🧪 Test 6: Testing scroll behavior...");
            try
            {
                await page.EvaluateAsync("window.scrollTo(0, document.body.scrollHeight)");
                await Task.Delay(1000);
                
                var scrollScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-app-sim-scroll.png");
                await page.ScreenshotAsync(new PageScreenshotOptions { Path = scrollScreenshot, FullPage = false });
                testResult.Screenshots.Add(scrollScreenshot);
                
                testResult.TestSteps.Add("Scroll test completed successfully");
                Console.WriteLine("✅ Scroll test completed");
            }
            catch (Exception ex)
            {
                testResult.TestSteps.Add($"Scroll test failed: {ex.Message}");
                Console.WriteLine($"⚠️ Scroll test failed: {ex.Message}");
            }
            
            // Get app information
            var title = await page.TitleAsync();
            var currentUrl = page.Url;
            var viewportSize = await page.EvaluateAsync<dynamic>("() => ({ width: window.innerWidth, height: window.innerHeight })");
            
            // Check for app-like features
            var hasViewportMeta = await page.Locator("meta[name='viewport']").CountAsync() > 0;
            var hasAppleMobileWebAppCapable = await page.Locator("meta[name='apple-mobile-web-app-capable']").CountAsync() > 0;
            var hasAppleMobileWebAppTitle = await page.Locator("meta[name='apple-mobile-web-app-title']").CountAsync() > 0;
            
            // Store metadata
            testResult.Metadata["title"] = title;
            testResult.Metadata["finalUrl"] = currentUrl;
            testResult.Metadata["viewportWidth"] = viewportSize.width;
            testResult.Metadata["viewportHeight"] = viewportSize.height;
            testResult.Metadata["hasViewportMeta"] = hasViewportMeta;
            testResult.Metadata["hasAppleMobileWebAppCapable"] = hasAppleMobileWebAppCapable;
            testResult.Metadata["hasAppleMobileWebAppTitle"] = hasAppleMobileWebAppTitle;
            testResult.Metadata["deviceType"] = deviceType;
            testResult.Metadata["userAgent"] = device.UserAgent;
            testResult.Metadata["totalTestSteps"] = testResult.TestSteps.Count;
            testResult.Metadata["successfulSteps"] = testResult.TestSteps.Count(s => s.Contains("successfully"));
            testResult.Metadata["failedSteps"] = testResult.TestSteps.Count(s => s.Contains("failed"));
            
            Console.WriteLine($"📄 App title: {title}");
            Console.WriteLine($"📱 Viewport: {viewportSize.width}x{viewportSize.height}");
            Console.WriteLine($"✅ Test steps completed: {testResult.TestSteps.Count}");
            Console.WriteLine($"✅ Successful steps: {testResult.Metadata["successfulSteps"]}");
            Console.WriteLine($"❌ Failed steps: {testResult.Metadata["failedSteps"]}");
            
            // Mark test as passed
            testResult.Status = "Passed";
            testResult.EndTime = DateTime.UtcNow;
            
            Console.WriteLine("🎉 App simulation test completed successfully!");
        } 
        catch (Exception ex) 
        {
            testResult.Status = "Failed";
            testResult.ErrorMessage = ex.Message;
            testResult.EndTime = DateTime.UtcNow;
            testResult.TestSteps.Add($"Test failed with error: {ex.Message}");
            
            Console.WriteLine($"❌ App simulation test failed: {ex.Message}");
        }
        
        // Save test results to JSON
        var resultsDir = "/tmp/test-results";
        Directory.CreateDirectory(resultsDir);
        
        var resultPath = Path.Combine(resultsDir, $"{testResult.Id}.json");
        var json = JsonSerializer.Serialize(testResult, new JsonSerializerOptions 
        { 
            WriteIndented = true 
        });
        
        await File.WriteAllTextAsync(resultPath, json);
        Console.WriteLine($"💾 App simulation test results saved: {resultPath}");
        
        return testResult;
    }
}

class Program 
{
    static async Task Main(string[] args) 
    {
        var appUrl = args.Length > 0 ? args[0] : "http://localhost:8083";
        var testName = args.Length > 1 ? args[1] : "Native App Simulation Test";
        var deviceType = args.Length > 2 ? args[2] : "iPhone 12";

        var result = await AppSimulationTestRunner.RunAppSimulationTest(appUrl, testName, deviceType);
        
        // Display summary
        var duration = result.EndTime - result.StartTime;
        Console.WriteLine("\n📊 APP SIMULATION TEST SUMMARY:");
        Console.WriteLine($"   Test ID: {result.Id}");
        Console.WriteLine($"   Platform: {result.Platform}");
        Console.WriteLine($"   Status: {result.Status}");
        Console.WriteLine($"   Duration: {duration.TotalSeconds:F2} seconds");
        Console.WriteLine($"   Screenshots: {result.Screenshots.Count}");
        Console.WriteLine($"   Test Steps: {result.TestSteps.Count}");
        
        if (result.Status == "Passed")
        {
            Console.WriteLine("\n✅ App simulation test passed! 📱🎉");
        }
        else
        {
            Console.WriteLine($"\n❌ App simulation test failed: {result.ErrorMessage}");
        }
    }
}
