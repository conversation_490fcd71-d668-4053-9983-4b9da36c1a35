using Microsoft.Playwright;
using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;

public class MobileTestResult
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TestName { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string Status { get; set; } = "Running";
    public string? ErrorMessage { get; set; }
    public List<string> Screenshots { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

class MultiMobileTestRunner 
{
    static async Task Main() 
    {
        var testCases = new[]
        {
            new { Url = "https://m.wikipedia.org", Device = "iPhone 12", Name = "Wikipedia Mobile" },
            new { Url = "https://mobile.twitter.com", Device = "iPhone 13", Name = "Twitter Mobile" },
            new { Url = "https://m.facebook.com", Device = "Galaxy S8", Name = "Facebook Mobile" },
            new { Url = "https://www.google.com", Device = "iPhone 14", Name = "Google Mobile" },
            new { Url = "https://stackoverflow.com", Device = "Galaxy S8", Name = "StackOverflow Mobile" }
        };

        Console.WriteLine("📱 Starting Multi-Device Mobile Test Suite");
        Console.WriteLine($"📋 Testing {testCases.Length} sites across different devices");
        
        Environment.SetEnvironmentVariable("PLAYWRIGHT_BROWSERS_PATH", "/ms-playwright");
        
        using var playwright = await Playwright.CreateAsync();
        Console.WriteLine("✅ Playwright initialized!");

        var allResults = new List<MobileTestResult>();

        foreach (var testCase in testCases)
        {
            try
            {
                var result = await RunSingleMobileTest(playwright, testCase.Url, testCase.Name, testCase.Device);
                allResults.Add(result);
                
                // Small delay between tests
                await Task.Delay(2000);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to run test for {testCase.Url}: {ex.Message}");
            }
        }

        // Generate summary
        var passed = allResults.Count(r => r.Status == "Passed");
        var failed = allResults.Count(r => r.Status == "Failed");
        var totalScreenshots = allResults.Sum(r => r.Screenshots.Count);
        
        Console.WriteLine("\n🎯 MULTI-DEVICE MOBILE TEST SUMMARY:");
        Console.WriteLine($"   Total Tests: {allResults.Count}");
        Console.WriteLine($"   ✅ Passed: {passed}");
        Console.WriteLine($"   ❌ Failed: {failed}");
        Console.WriteLine($"   📸 Screenshots: {totalScreenshots}");
        Console.WriteLine($"   📊 Success Rate: {(double)passed / allResults.Count * 100:F1}%");
        
        if (failed == 0)
        {
            Console.WriteLine("\n🎉 All mobile tests passed! 📱🎉");
        }
        else
        {
            Console.WriteLine($"\n⚠️ {failed} mobile test(s) failed. Check individual results for details.");
        }
    }

    static async Task<MobileTestResult> RunSingleMobileTest(IPlaywright playwright, string url, string testName, string deviceType)
    {
        var testResult = new MobileTestResult
        {
            TestName = testName,
            Url = url,
            Platform = $"Mobile - {deviceType}",
            StartTime = DateTime.UtcNow
        };

        try 
        {
            Console.WriteLine($"\n📱 Testing: {testName} ({url}) on {deviceType}");
            Console.WriteLine($"📋 Test ID: {testResult.Id}");
            
            // Get device configuration
            var device = playwright.Devices[deviceType];
            
            await using var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions 
            { 
                Headless = true 
            });
            
            // Create context with mobile device emulation
            await using var context = await browser.NewContextAsync(new BrowserNewContextOptions
            {
                ViewportSize = device.ViewportSize,
                UserAgent = device.UserAgent,
                DeviceScaleFactor = device.DeviceScaleFactor,
                IsMobile = device.IsMobile,
                HasTouch = device.HasTouch
            });
            
            var page = await context.NewPageAsync();
            Console.WriteLine($"✅ Mobile context created: {device.ViewportSize?.Width}x{device.ViewportSize?.Height}");
            
            // Navigate to URL
            Console.WriteLine($"🌐 Navigating to {url}...");
            await page.GotoAsync(url, new PageGotoOptions 
            { 
                WaitUntil = WaitUntilState.NetworkIdle,
                Timeout = 15000
            });
            Console.WriteLine("✅ Navigation completed!");
            
            // Take screenshot
            var screenshotDir = "/tmp/screenshots";
            Directory.CreateDirectory(screenshotDir);
            
            var screenshotPath = Path.Combine(screenshotDir, $"{testResult.Id}-{SanitizeFilename(testName)}-{SanitizeFilename(deviceType)}.png");
            await page.ScreenshotAsync(new PageScreenshotOptions
            {
                Path = screenshotPath,
                FullPage = true
            });
            testResult.Screenshots.Add(screenshotPath);
            Console.WriteLine($"📸 Screenshot saved");
            
            // Get page information
            var title = await page.TitleAsync();
            var currentUrl = page.Url;
            
            // Analyze mobile-specific elements
            var linkCount = await page.Locator("a").CountAsync();
            var imageCount = await page.Locator("img").CountAsync();
            var formCount = await page.Locator("form").CountAsync();
            var buttonCount = await page.Locator("button").CountAsync();
            var inputCount = await page.Locator("input").CountAsync();
            
            // Check for mobile-specific meta tags
            var hasViewportMeta = await page.Locator("meta[name='viewport']").CountAsync() > 0;
            var isResponsive = await page.EvaluateAsync<bool>(@"
                () => {
                    const metaViewport = document.querySelector('meta[name=""viewport""]');
                    if (!metaViewport) return false;
                    const content = metaViewport.getAttribute('content') || '';
                    return content.includes('width=device-width') || content.includes('initial-scale');
                }
            ");
            
            // Store metadata
            testResult.Metadata["title"] = title;
            testResult.Metadata["finalUrl"] = currentUrl;
            testResult.Metadata["linkCount"] = linkCount;
            testResult.Metadata["imageCount"] = imageCount;
            testResult.Metadata["formCount"] = formCount;
            testResult.Metadata["buttonCount"] = buttonCount;
            testResult.Metadata["inputCount"] = inputCount;
            testResult.Metadata["hasViewportMeta"] = hasViewportMeta;
            testResult.Metadata["isResponsive"] = isResponsive;
            testResult.Metadata["deviceType"] = deviceType;
            testResult.Metadata["userAgent"] = device.UserAgent;
            
            Console.WriteLine($"📄 Title: {title}");
            Console.WriteLine($"🔗 Links: {linkCount}, 🖼️ Images: {imageCount}, 📝 Forms: {formCount}");
            Console.WriteLine($"📐 Responsive: {isResponsive}");
            
            // Mark test as passed
            testResult.Status = "Passed";
            testResult.EndTime = DateTime.UtcNow;
            
            Console.WriteLine($"✅ Test completed successfully");
        } 
        catch (Exception ex) 
        {
            testResult.Status = "Failed";
            testResult.ErrorMessage = ex.Message;
            testResult.EndTime = DateTime.UtcNow;
            
            Console.WriteLine($"❌ Test failed: {ex.Message}");
        }
        
        // Save test results to JSON
        var resultsDir = "/tmp/test-results";
        Directory.CreateDirectory(resultsDir);
        
        var resultPath = Path.Combine(resultsDir, $"{testResult.Id}.json");
        var json = JsonSerializer.Serialize(testResult, new JsonSerializerOptions 
        { 
            WriteIndented = true 
        });
        
        await File.WriteAllTextAsync(resultPath, json);
        
        return testResult;
    }

    static string SanitizeFilename(string filename)
    {
        var invalid = Path.GetInvalidFileNameChars();
        return string.Join("_", filename.Split(invalid, StringSplitOptions.RemoveEmptyEntries));
    }
}
