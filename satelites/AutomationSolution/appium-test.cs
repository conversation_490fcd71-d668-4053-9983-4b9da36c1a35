using OpenQA.Selenium;
using OpenQA.Selenium.Appium;
using OpenQA.Selenium.Appium.Android;
using OpenQA.Selenium.Chrome;
using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;

public class MobileTestResult
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TestName { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string Status { get; set; } = "Running";
    public string? ErrorMessage { get; set; }
    public List<string> Screenshots { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

class AppiumTestRunner 
{
    public static async Task<MobileTestResult> RunMobileWebTest(string url, string? testName = null)
    {
        testName ??= $"Mobile Web Test for {url}";
        
        var testResult = new MobileTestResult
        {
            TestName = testName,
            Url = url,
            Platform = "Android Chrome",
            StartTime = DateTime.UtcNow
        };

        AppiumDriver? driver = null;

        try 
        {
            Console.WriteLine($"📱 Starting mobile test: {testName}");
            Console.WriteLine($"📋 Test ID: {testResult.Id}");
            
            // Configure Appium options for Chrome mobile
            var options = new AppiumOptions();
            options.PlatformName = "Android";
            options.AutomationName = "UiAutomator2";
            options.DeviceName = "Android Emulator";
            options.AddAdditionalAppiumOption("browserName", "Chrome");
            options.AddAdditionalAppiumOption("chromedriverAutodownload", true);
            
            // Mobile Chrome options
            var chromeOptions = new ChromeOptions();
            chromeOptions.AddArgument("--no-sandbox");
            chromeOptions.AddArgument("--disable-dev-shm-usage");
            chromeOptions.AddArgument("--disable-gpu");
            chromeOptions.AddArgument("--headless");
            chromeOptions.AddArgument("--user-agent=Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36");
            
            options.AddAdditionalAppiumOption("goog:chromeOptions", chromeOptions.ToCapabilities()["goog:chromeOptions"]);
            
            // Create driver
            var serverUri = new Uri("http://localhost:4723");
            driver = new AndroidDriver(serverUri, options, TimeSpan.FromSeconds(60));
            
            Console.WriteLine("✅ Mobile driver initialized!");
            
            // Navigate to URL
            Console.WriteLine($"🌐 Navigating to {url}...");
            driver.Navigate().GoToUrl(url);
            
            // Wait for page load
            await Task.Delay(3000);
            Console.WriteLine("✅ Navigation completed!");
            
            // Take screenshot
            var screenshotDir = "/tmp/screenshots";
            Directory.CreateDirectory(screenshotDir);
            
            var screenshotPath = Path.Combine(screenshotDir, $"{testResult.Id}-mobile-{SanitizeFilename(testName)}.png");
            var screenshot = ((ITakesScreenshot)driver).GetScreenshot();
            await File.WriteAllBytesAsync(screenshotPath, screenshot.AsByteArray);
            testResult.Screenshots.Add(screenshotPath);
            Console.WriteLine($"📸 Mobile screenshot saved: {screenshotPath}");
            
            // Get page information
            var title = driver.Title;
            var currentUrl = driver.Url;
            
            // Analyze mobile-specific elements
            var links = driver.FindElements(By.TagName("a"));
            var images = driver.FindElements(By.TagName("img"));
            var buttons = driver.FindElements(By.TagName("button"));
            var inputs = driver.FindElements(By.TagName("input"));
            
            // Check viewport size
            var windowSize = driver.Manage().Window.Size;
            
            // Check for mobile-specific elements
            var hasViewportMeta = false;
            try
            {
                var viewportMeta = driver.FindElement(By.XPath("//meta[@name='viewport']"));
                hasViewportMeta = viewportMeta != null;
            }
            catch { }
            
            // Try to detect touch elements
            var touchElements = 0;
            try
            {
                var touchableElements = driver.FindElements(By.XPath("//*[@onclick or @ontouchstart or @ontouchend]"));
                touchElements = touchableElements.Count;
            }
            catch { }
            
            // Store metadata
            testResult.Metadata["title"] = title;
            testResult.Metadata["finalUrl"] = currentUrl;
            testResult.Metadata["linkCount"] = links.Count;
            testResult.Metadata["imageCount"] = images.Count;
            testResult.Metadata["buttonCount"] = buttons.Count;
            testResult.Metadata["inputCount"] = inputs.Count;
            testResult.Metadata["windowWidth"] = windowSize.Width;
            testResult.Metadata["windowHeight"] = windowSize.Height;
            testResult.Metadata["hasViewportMeta"] = hasViewportMeta;
            testResult.Metadata["touchElements"] = touchElements;
            testResult.Metadata["userAgent"] = driver.ExecuteScript("return navigator.userAgent;").ToString();
            
            Console.WriteLine($"📄 Page title: {title}");
            Console.WriteLine($"📱 Viewport: {windowSize.Width}x{windowSize.Height}");
            Console.WriteLine($"🔗 Links: {links.Count}, 🖼️ Images: {images.Count}");
            Console.WriteLine($"🔘 Buttons: {buttons.Count}, ⌨️ Inputs: {inputs.Count}");
            Console.WriteLine($"👆 Touch elements: {touchElements}");
            Console.WriteLine($"📐 Viewport meta: {hasViewportMeta}");
            
            // Test mobile interactions if possible
            if (inputs.Count > 0)
            {
                try
                {
                    // Take screenshot before interaction
                    var beforeInteractionPath = Path.Combine(screenshotDir, $"{testResult.Id}-mobile-before-interaction.png");
                    var beforeScreenshot = ((ITakesScreenshot)driver).GetScreenshot();
                    await File.WriteAllBytesAsync(beforeInteractionPath, beforeScreenshot.AsByteArray);
                    testResult.Screenshots.Add(beforeInteractionPath);
                    
                    // Try to interact with first input
                    var firstInput = inputs[0];
                    if (firstInput.Displayed && firstInput.Enabled)
                    {
                        firstInput.Click();
                        firstInput.Clear();
                        firstInput.SendKeys("mobile test input");
                        
                        // Take screenshot after interaction
                        var afterInteractionPath = Path.Combine(screenshotDir, $"{testResult.Id}-mobile-after-interaction.png");
                        var afterScreenshot = ((ITakesScreenshot)driver).GetScreenshot();
                        await File.WriteAllBytesAsync(afterInteractionPath, afterScreenshot.AsByteArray);
                        testResult.Screenshots.Add(afterInteractionPath);
                        
                        testResult.Metadata["mobileInteraction"] = "Successfully filled input field";
                        Console.WriteLine("✅ Mobile interaction completed!");
                    }
                }
                catch (Exception ex)
                {
                    testResult.Metadata["mobileInteractionError"] = ex.Message;
                    Console.WriteLine($"⚠️ Mobile interaction failed: {ex.Message}");
                }
            }
            
            // Mark test as passed
            testResult.Status = "Passed";
            testResult.EndTime = DateTime.UtcNow;
            
            Console.WriteLine("🎉 Mobile test completed successfully!");
        } 
        catch (Exception ex) 
        {
            testResult.Status = "Failed";
            testResult.ErrorMessage = ex.Message;
            testResult.EndTime = DateTime.UtcNow;
            
            Console.WriteLine($"❌ Mobile test failed: {ex.Message}");
        }
        finally
        {
            // Clean up driver
            if (driver != null)
            {
                try
                {
                    driver.Quit();
                    driver.Dispose();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"⚠️ Error disposing driver: {ex.Message}");
                }
            }
        }
        
        // Save test results to JSON
        var resultsDir = "/tmp/test-results";
        Directory.CreateDirectory(resultsDir);
        
        var resultPath = Path.Combine(resultsDir, $"{testResult.Id}.json");
        var json = JsonSerializer.Serialize(testResult, new JsonSerializerOptions 
        { 
            WriteIndented = true 
        });
        
        await File.WriteAllTextAsync(resultPath, json);
        Console.WriteLine($"💾 Mobile test results saved: {resultPath}");
        
        return testResult;
    }

    static string SanitizeFilename(string filename)
    {
        var invalid = Path.GetInvalidFileNameChars();
        return string.Join("_", filename.Split(invalid, StringSplitOptions.RemoveEmptyEntries));
    }
}

class Program 
{
    static async Task Main(string[] args) 
    {
        if (args.Length == 0)
        {
            Console.WriteLine("Usage: dotnet run <url> [test-name]");
            Console.WriteLine("Example: dotnet run https://m.wikipedia.org \"Mobile Wikipedia Test\"");
            return;
        }

        var url = args[0];
        var testName = args.Length > 1 ? args[1] : null;

        var result = await AppiumTestRunner.RunMobileWebTest(url, testName);
        
        // Display summary
        var duration = result.EndTime - result.StartTime;
        Console.WriteLine("\n📊 MOBILE TEST SUMMARY:");
        Console.WriteLine($"   Test ID: {result.Id}");
        Console.WriteLine($"   Platform: {result.Platform}");
        Console.WriteLine($"   Status: {result.Status}");
        Console.WriteLine($"   Duration: {duration.TotalSeconds:F2} seconds");
        Console.WriteLine($"   Screenshots: {result.Screenshots.Count}");
        
        if (result.Status == "Passed")
        {
            Console.WriteLine("\n✅ Mobile test passed! 📱🎉");
        }
        else
        {
            Console.WriteLine($"\n❌ Mobile test failed: {result.ErrorMessage}");
        }
    }
}
