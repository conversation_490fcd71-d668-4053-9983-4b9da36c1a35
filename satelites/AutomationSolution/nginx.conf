events {
    worker_connections 1024;
}

http {
    upstream automation_workers {
        least_conn;
        server worker:8080 max_fails=3 fail_timeout=30s;
        # Additional worker instances will be discovered via service discovery
        keepalive 32;
    }

    upstream automation_coordinator {
        server coordinator:8080 max_fails=2 fail_timeout=30s;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=automation:10m rate=5r/s;

    # Enable gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain application/json application/xml text/css text/js text/xml application/javascript;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # Logging
    log_format automation '$remote_addr - $remote_user [$time_local] '
                         '"$request" $status $bytes_sent '
                         '"$http_referer" "$http_user_agent" '
                         '$request_time $upstream_response_time';

    # API Gateway for automation workers
    server {
        listen 80;
        server_name localhost;
        
        access_log /var/log/nginx/automation.log automation;

        # Health check endpoint
        location /health {
            return 200 "OK\n";
            add_header Content-Type text/plain;
        }

        # Automation API endpoints
        location /api/automation/ {
            limit_req zone=automation burst=10 nodelay;
            
            proxy_pass http://automation_workers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # Retry logic
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 30s;
        }

        # Coordinator management API
        location /api/coordinator/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://automation_coordinator;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Metrics endpoints (restricted access)
        location /metrics {
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;
            
            proxy_pass http://automation_workers;
            proxy_set_header Host $host;
        }

        # Static content and documentation
        location /docs/ {
            alias /usr/share/nginx/html/docs/;
            index index.html;
        }

        # Default fallback
        location / {
            return 404 "Endpoint not found\n";
            add_header Content-Type text/plain;
        }
    }

    # HTTPS server (for production)
    server {
        listen 443 ssl http2;
        server_name localhost;
        
        # SSL configuration (certificates should be mounted as volumes)
        ssl_certificate /etc/ssl/certs/nginx.crt;
        ssl_certificate_key /etc/ssl/private/nginx.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # Same location blocks as HTTP server
        include /etc/nginx/conf.d/automation-locations.conf;
    }
}
