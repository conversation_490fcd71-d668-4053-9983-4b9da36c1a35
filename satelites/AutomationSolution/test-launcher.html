<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Custom Test Launcher</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .platform-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .platform-card {
            background: rgba(255, 255, 255, 0.15);
            border: 2px solid transparent;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .platform-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .platform-card.active {
            border-color: #fff;
            background: rgba(255, 255, 255, 0.25);
        }
        
        .platform-icon {
            font-size: 3rem;
            margin-bottom: 10px;
            display: block;
        }
        
        .platform-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .platform-desc {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .test-form {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            display: none;
        }
        
        .test-form.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            font-size: 1rem;
        }
        
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            background: white;
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
        }
        
        .device-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .device-option {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid transparent;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .device-option:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .device-option.selected {
            border-color: #fff;
            background: rgba(255, 255, 255, 0.3);
        }
        
        .launch-btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .launch-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 107, 107, 0.4);
        }
        
        .launch-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            display: none;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.5);
        }
        
        .status.loading {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid rgba(33, 150, 243, 0.5);
        }
        
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .results-link {
            display: inline-block;
            margin-top: 15px;
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            text-decoration: none;
            color: white;
            transition: all 0.3s ease;
        }
        
        .results-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .file-upload {
            position: relative;
            display: inline-block;
            width: 100%;
        }
        
        .file-upload input[type=file] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        .file-upload-label {
            display: block;
            padding: 12px 15px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            color: #333;
            transition: all 0.3s ease;
        }
        
        .file-upload:hover .file-upload-label {
            background: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Custom Test Launcher</h1>
            <p>Launch automated tests for Web, Android, and iOS platforms</p>
        </div>
        
        <div class="platform-selector">
            <div class="platform-card" data-platform="web">
                <span class="platform-icon">🌐</span>
                <div class="platform-title">Web Testing</div>
                <div class="platform-desc">Test websites and web applications</div>
            </div>
            
            <div class="platform-card" data-platform="android">
                <span class="platform-icon">🤖</span>
                <div class="platform-title">Android Testing</div>
                <div class="platform-desc">Test Android apps and APKs</div>
            </div>
            
            <div class="platform-card" data-platform="ios">
                <span class="platform-icon">🍎</span>
                <div class="platform-title">iOS Testing</div>
                <div class="platform-desc">Test iOS apps and websites</div>
            </div>
        </div>
        
        <!-- Web Test Form -->
        <div id="web-form" class="test-form">
            <h3>🌐 Web Test Configuration</h3>
            <div class="form-group">
                <label for="web-url">Website URL:</label>
                <input type="url" id="web-url" placeholder="https://example.com" required>
            </div>
            <div class="form-group">
                <label for="web-name">Test Name:</label>
                <input type="text" id="web-name" placeholder="My Web Test">
            </div>
            <div class="form-group">
                <label>Browser:</label>
                <div class="device-grid">
                    <div class="device-option selected" data-browser="chromium">Chrome</div>
                    <div class="device-option" data-browser="webkit">Safari</div>
                    <div class="device-option" data-browser="firefox">Firefox</div>
                </div>
            </div>
            <button class="launch-btn" onclick="launchWebTest()">🚀 Launch Web Test</button>
        </div>
        
        <!-- Android Test Form -->
        <div id="android-form" class="test-form">
            <h3>🤖 Android Test Configuration</h3>
            <div class="form-group">
                <label>Test Type:</label>
                <select id="android-type" onchange="toggleAndroidInputs()">
                    <option value="url">Web App URL</option>
                    <option value="apk">APK File</option>
                </select>
            </div>
            <div class="form-group" id="android-url-group">
                <label for="android-url">App URL:</label>
                <input type="url" id="android-url" placeholder="https://m.example.com">
            </div>
            <div class="form-group" id="android-apk-group" style="display: none;">
                <label for="android-apk">APK File:</label>
                <div class="file-upload">
                    <input type="file" id="android-apk" accept=".apk,.xapk">
                    <label for="android-apk" class="file-upload-label">📦 Choose APK File</label>
                </div>
            </div>
            <div class="form-group">
                <label for="android-name">Test Name:</label>
                <input type="text" id="android-name" placeholder="My Android Test">
            </div>
            <div class="form-group">
                <label>Device:</label>
                <div class="device-grid">
                    <div class="device-option selected" data-device="Galaxy S8">Galaxy S8</div>
                    <div class="device-option" data-device="Pixel 5">Pixel 5</div>
                    <div class="device-option" data-device="Galaxy Note 20">Note 20</div>
                </div>
            </div>
            <button class="launch-btn" onclick="launchAndroidTest()">🚀 Launch Android Test</button>
        </div>
        
        <!-- iOS Test Form -->
        <div id="ios-form" class="test-form">
            <h3>🍎 iOS Test Configuration</h3>
            <div class="form-group">
                <label for="ios-url">App URL or App Store URL:</label>
                <input type="url" id="ios-url" placeholder="https://apps.apple.com/app/... or https://m.example.com">
            </div>
            <div class="form-group">
                <label for="ios-name">Test Name:</label>
                <input type="text" id="ios-name" placeholder="My iOS Test">
            </div>
            <div class="form-group">
                <label>Device:</label>
                <div class="device-grid">
                    <div class="device-option selected" data-device="iPhone 14">iPhone 14</div>
                    <div class="device-option" data-device="iPhone 13">iPhone 13</div>
                    <div class="device-option" data-device="iPhone 12">iPhone 12</div>
                    <div class="device-option" data-device="iPhone 14 Pro">iPhone 14 Pro</div>
                    <div class="device-option" data-device="iPad">iPad</div>
                </div>
            </div>
            <button class="launch-btn" onclick="launchiOSTest()">🚀 Launch iOS Test</button>
        </div>
        
        <div id="status" class="status"></div>
    </div>

    <script>
        let selectedPlatform = null;
        let selectedBrowser = 'chromium';
        let selectedAndroidDevice = 'Galaxy S8';
        let selectedIOSDevice = 'iPhone 14';

        // Platform selection
        document.querySelectorAll('.platform-card').forEach(card => {
            card.addEventListener('click', () => {
                // Remove active class from all cards and forms
                document.querySelectorAll('.platform-card').forEach(c => c.classList.remove('active'));
                document.querySelectorAll('.test-form').forEach(f => f.classList.remove('active'));
                
                // Add active class to selected card
                card.classList.add('active');
                selectedPlatform = card.dataset.platform;
                
                // Show corresponding form
                document.getElementById(selectedPlatform + '-form').classList.add('active');
            });
        });

        // Device/Browser selection
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('device-option')) {
                const parent = e.target.parentElement;
                parent.querySelectorAll('.device-option').forEach(opt => opt.classList.remove('selected'));
                e.target.classList.add('selected');
                
                if (e.target.dataset.browser) {
                    selectedBrowser = e.target.dataset.browser;
                } else if (e.target.dataset.device) {
                    if (selectedPlatform === 'android') {
                        selectedAndroidDevice = e.target.dataset.device;
                    } else if (selectedPlatform === 'ios') {
                        selectedIOSDevice = e.target.dataset.device;
                    }
                }
            }
        });

        // Android input toggle
        function toggleAndroidInputs() {
            const type = document.getElementById('android-type').value;
            const urlGroup = document.getElementById('android-url-group');
            const apkGroup = document.getElementById('android-apk-group');
            
            if (type === 'url') {
                urlGroup.style.display = 'block';
                apkGroup.style.display = 'none';
            } else {
                urlGroup.style.display = 'none';
                apkGroup.style.display = 'block';
            }
        }

        // File upload label update
        document.getElementById('android-apk').addEventListener('change', function(e) {
            const label = document.querySelector('.file-upload-label');
            if (e.target.files.length > 0) {
                label.textContent = `📦 ${e.target.files[0].name}`;
            } else {
                label.textContent = '📦 Choose APK File';
            }
        });

        // Test launch functions
        async function launchWebTest() {
            const url = document.getElementById('web-url').value;
            const name = document.getElementById('web-name').value || 'Custom Web Test';
            
            if (!url) {
                showStatus('error', '❌ Please enter a valid URL');
                return;
            }
            
            showStatus('loading', '🚀 Launching web test...');
            
            try {
                const response = await fetch('/api/launch-test', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        platform: 'web',
                        url: url,
                        name: name,
                        browser: selectedBrowser
                    })
                });
                
                const result = await response.json();
                if (result.success) {
                    showStatus('success', `✅ Web test launched successfully! Test ID: ${result.testId}`, result.testId);
                } else {
                    showStatus('error', `❌ Failed to launch test: ${result.error}`);
                }
            } catch (error) {
                showStatus('error', `❌ Error: ${error.message}`);
            }
        }

        async function launchAndroidTest() {
            const type = document.getElementById('android-type').value;
            const name = document.getElementById('android-name').value || 'Custom Android Test';
            let testData = {
                platform: 'android',
                name: name,
                device: selectedAndroidDevice
            };
            
            if (type === 'url') {
                const url = document.getElementById('android-url').value;
                if (!url) {
                    showStatus('error', '❌ Please enter a valid URL');
                    return;
                }
                testData.url = url;
            } else {
                const apkFile = document.getElementById('android-apk').files[0];
                if (!apkFile) {
                    showStatus('error', '❌ Please select an APK file');
                    return;
                }
                testData.apkFile = apkFile.name;
                testData.apkSize = apkFile.size;
            }
            
            showStatus('loading', '🚀 Launching Android test...');
            
            try {
                const response = await fetch('/api/launch-test', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                
                const result = await response.json();
                if (result.success) {
                    showStatus('success', `✅ Android test launched successfully! Test ID: ${result.testId}`, result.testId);
                } else {
                    showStatus('error', `❌ Failed to launch test: ${result.error}`);
                }
            } catch (error) {
                showStatus('error', `❌ Error: ${error.message}`);
            }
        }

        async function launchiOSTest() {
            const url = document.getElementById('ios-url').value;
            const name = document.getElementById('ios-name').value || 'Custom iOS Test';
            
            if (!url) {
                showStatus('error', '❌ Please enter a valid URL');
                return;
            }
            
            showStatus('loading', '🚀 Launching iOS test...');
            
            try {
                const response = await fetch('/api/launch-test', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        platform: 'ios',
                        url: url,
                        name: name,
                        device: selectedIOSDevice
                    })
                });
                
                const result = await response.json();
                if (result.success) {
                    showStatus('success', `✅ iOS test launched successfully! Test ID: ${result.testId}`, result.testId);
                } else {
                    showStatus('error', `❌ Failed to launch test: ${result.error}`);
                }
            } catch (error) {
                showStatus('error', `❌ Error: ${error.message}`);
            }
        }

        function showStatus(type, message, testId = null) {
            const status = document.getElementById('status');
            status.className = `status ${type}`;
            
            if (type === 'loading') {
                status.innerHTML = `<span class="loading-spinner"></span>${message}`;
            } else {
                status.innerHTML = message;
                if (testId && type === 'success') {
                    status.innerHTML += `<br><a href="/dashboard" class="results-link">📊 View Results Dashboard</a>`;
                }
            }
            
            status.style.display = 'block';
            
            if (type !== 'loading') {
                setTimeout(() => {
                    status.style.display = 'none';
                }, 10000);
            }
        }
    </script>
</body>
</html>
