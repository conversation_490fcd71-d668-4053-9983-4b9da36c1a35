global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Worker instances metrics
  - job_name: 'automation-workers'
    static_configs:
      - targets: ['worker:8080']
    metrics_path: '/metrics'
    scrape_interval: 5s
    
  # Coordinator metrics
  - job_name: 'automation-coordinator'
    static_configs:
      - targets: ['coordinator:8080']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Redis metrics (if Redis exporter is enabled)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
