# =============================================================================
# Multi-stage Dockerfile for Automation.Worker.CSharp
# Optimized for production deployment with security best practices
# =============================================================================

# =============================================================================
# Stage 1: Build Environment
# =============================================================================
FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS build
LABEL stage=build

# Set working directory
WORKDIR /src

# Install build dependencies
RUN apk add --no-cache \
    git \
    curl \
    && rm -rf /var/cache/apk/*

# Copy solution and project files for dependency restoration
COPY AutomationSolution.sln ./
COPY Directory.Build.props ./
COPY src/Automation.Contracts/Automation.Contracts.csproj ./src/Automation.Contracts/
COPY src/Automation.Utilities.CSharp/Automation.Utilities.CSharp.csproj ./src/Automation.Utilities.CSharp/
COPY src/Automation.Prompts.CSharp/Automation.Prompts.CSharp.csproj ./src/Automation.Prompts.CSharp/
COPY src/Automation.Web.CSharp/Automation.Web.CSharp.csproj ./src/Automation.Web.CSharp/
COPY src/Automation.Mobile.CSharp/Automation.Mobile.CSharp.csproj ./src/Automation.Mobile.CSharp/
COPY src/Automation.AI.Infrastructure.CSharp/Automation.AI.Infrastructure.CSharp.csproj ./src/Automation.AI.Infrastructure.CSharp/
COPY src/Automation.Worker.CSharp/Automation.Worker.CSharp.csproj ./src/Automation.Worker.CSharp/

# Restore dependencies
RUN dotnet restore src/Automation.Worker.CSharp/Automation.Worker.CSharp.csproj \
    --verbosity minimal

# Copy source code
COPY src/ ./src/

# Build and publish application in one step
RUN dotnet publish src/Automation.Worker.CSharp/Automation.Worker.CSharp.csproj \
    --configuration Release \
    --output /app/publish \
    --no-restore \
    --verbosity minimal

# =============================================================================
# Stage 2: Runtime Environment with Playwright and Appium
# =============================================================================
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS runtime
LABEL maintainer="Automation Solution Team"
LABEL version="1.0.0"
LABEL description="Automation Worker Service with Web and Mobile automation capabilities"

# Install Node.js and system dependencies
RUN apt-get update && apt-get install -y \
    # Basic utilities
    curl \
    wget \
    ca-certificates \
    gnupg \
    lsb-release \
    # Node.js for Playwright
    nodejs \
    npm \
    # Python for Appium
    python3 \
    python3-pip \
    # Browser dependencies
    libnss3 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libxss1 \
    libasound2 \
    # Additional dependencies for automation
    xvfb \
    dbus \
    fontconfig \
    fonts-liberation \
    # Android SDK dependencies for Appium
    default-jdk \
    unzip \
    # Cleanup
    && rm -rf /var/lib/apt/lists/*

# Install Playwright
RUN npm install -g playwright@latest && \
    npx playwright install --with-deps chromium firefox webkit

# Install Appium
RUN npm install -g appium@latest && \
    npm install -g @appium/doctor

# Install Android SDK for Appium (basic setup)
ENV ANDROID_HOME=/opt/android-sdk
ENV PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools
RUN mkdir -p $ANDROID_HOME && \
    cd $ANDROID_HOME && \
    wget -q https://dl.google.com/android/repository/commandlinetools-linux-9477386_latest.zip && \
    unzip commandlinetools-linux-9477386_latest.zip && \
    rm commandlinetools-linux-9477386_latest.zip && \
    mkdir -p cmdline-tools/latest && \
    mv cmdline-tools/* cmdline-tools/latest/ || true

# Create non-root user for security
RUN groupadd -g 1001 automation && \
    useradd -r -u 1001 -g automation -d /app -s /sbin/nologin automation

# Set working directory
WORKDIR /app

# Copy published application
COPY --from=build --chown=automation:automation /app/publish ./

# Copy initialization script
COPY scripts/docker/init-playwright.sh /usr/local/bin/init-playwright.sh
RUN chmod +x /usr/local/bin/init-playwright.sh

# Create directories for logs and data
RUN mkdir -p /app/logs /app/data /app/screenshots && \
    chown -R automation:automation /app

# Initialize Playwright and Appium
RUN /usr/local/bin/init-playwright.sh

# Set environment variables
ENV ASPNETCORE_ENVIRONMENT=Production \
    DOTNET_RUNNING_IN_CONTAINER=true \
    DOTNET_USE_POLLING_FILE_WATCHER=true \
    DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false \
    # Playwright configuration
    PLAYWRIGHT_BROWSERS_PATH=/ms-playwright \
    PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=0 \
    # Appium configuration
    APPIUM_HOME=/usr/local/lib/node_modules/appium \
    # Security
    DOTNET_EnableDiagnostics=0 \
    # Performance
    DOTNET_TieredCompilation=1 \
    DOTNET_ReadyToRun=1

# Health check configuration
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Expose ports
EXPOSE 8080

# Switch to non-root user
USER automation

# Set entry point
ENTRYPOINT ["dotnet", "Automation.Worker.CSharp.dll"]

# =============================================================================
# Stage 3: Development Environment with Playwright and Appium
# =============================================================================
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS development
LABEL stage=development

# Install Node.js and system dependencies
RUN apt-get update && apt-get install -y \
    # Basic utilities
    curl \
    wget \
    ca-certificates \
    gnupg \
    lsb-release \
    # Development tools
    bash \
    vim \
    htop \
    git \
    # Node.js for Playwright
    nodejs \
    npm \
    # Python for Appium
    python3 \
    python3-pip \
    # Browser dependencies
    libnss3 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libxss1 \
    libasound2 \
    # Additional dependencies for automation
    xvfb \
    dbus \
    fontconfig \
    fonts-liberation \
    # Android SDK dependencies for Appium
    default-jdk \
    unzip \
    # Cleanup
    && rm -rf /var/lib/apt/lists/*

# Install Playwright
RUN npm install -g playwright@latest && \
    npx playwright install --with-deps chromium firefox webkit

# Install Appium
RUN npm install -g appium@latest && \
    npm install -g @appium/doctor

# Install Android SDK for Appium (basic setup)
ENV ANDROID_HOME=/opt/android-sdk
ENV PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools
RUN mkdir -p $ANDROID_HOME && \
    cd $ANDROID_HOME && \
    wget -q https://dl.google.com/android/repository/commandlinetools-linux-9477386_latest.zip && \
    unzip commandlinetools-linux-9477386_latest.zip && \
    rm commandlinetools-linux-9477386_latest.zip && \
    mkdir -p cmdline-tools/latest && \
    mv cmdline-tools/* cmdline-tools/latest/ || true

# Set development environment
ENV ASPNETCORE_ENVIRONMENT=Development \
    DOTNET_USE_POLLING_FILE_WATCHER=true \
    DOTNET_EnableDiagnostics=1 \
    # Playwright configuration
    PLAYWRIGHT_BROWSERS_PATH=/ms-playwright \
    PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=0 \
    # Appium configuration
    APPIUM_HOME=/usr/local/lib/node_modules/appium

# Expose additional ports for debugging
EXPOSE 8080 5000 5001

# Development entry point with hot reload
WORKDIR /src
ENTRYPOINT ["dotnet", "watch", "run", "--project", "src/Automation.Worker.CSharp/Automation.Worker.CSharp.csproj"]

# =============================================================================
# Stage 4: Testing Environment with Playwright and Appium
# =============================================================================
FROM development AS testing
LABEL stage=testing

# Copy test projects
COPY tests/ ./tests/

# Restore test dependencies
RUN dotnet restore tests/Automation.Integration.Tests/Automation.Integration.Tests.csproj \
    --verbosity minimal

# Build tests
RUN dotnet build tests/Automation.Integration.Tests/Automation.Integration.Tests.csproj \
    --configuration Release \
    --no-restore \
    --verbosity minimal

# Set test environment
ENV ASPNETCORE_ENVIRONMENT=Testing \
    DOTNET_EnableDiagnostics=1 \
    # Playwright configuration for testing
    PLAYWRIGHT_BROWSERS_PATH=/ms-playwright \
    PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=0 \
    # Appium configuration for testing
    APPIUM_HOME=/usr/local/lib/node_modules/appium

# Test entry point
ENTRYPOINT ["dotnet", "test", "tests/Automation.Integration.Tests/Automation.Integration.Tests.csproj"]
