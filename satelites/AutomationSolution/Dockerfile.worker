# Multi-stage Dockerfile for Worker
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy project files
COPY src/Automation.Worker/Automation.Worker.fsproj src/Automation.Worker/
COPY src/Automation.Core/Automation.Core.fsproj src/Automation.Core/
COPY src/Automation.AI/Automation.AI.fsproj src/Automation.AI/
COPY src/Automation.Web/Automation.Web.fsproj src/Automation.Web/
COPY src/Automation.Mobile/Automation.Mobile.fsproj src/Automation.Mobile/
COPY src/Automation.Data/Automation.Data.fsproj src/Automation.Data/
COPY src/Automation.Prompts/Automation.Prompts.fsproj src/Automation.Prompts/
COPY src/Automation.Utilities/Automation.Utilities.fsproj src/Automation.Utilities/

# Restore dependencies
RUN dotnet restore src/Automation.Worker/Automation.Worker.fsproj

# Copy source code
COPY src/ src/

# Build application
RUN dotnet build src/Automation.Worker/Automation.Worker.fsproj -c Release --no-restore

# Publish application
RUN dotnet publish src/Automation.Worker/Automation.Worker.fsproj -c Release --no-build -o /app/publish

# Runtime stage
FROM mcr.microsoft.com/dotnet/runtime:8.0 AS runtime
WORKDIR /app

# Install dependencies for browser automation
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    ca-certificates \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Chrome for Playwright
RUN wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

# Copy published application
COPY --from=build /app/publish .

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:${METRICS_PORT:-8080}/health || exit 1

# Expose metrics port
EXPOSE 8080

# Set environment variables
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=1

# Run the application
ENTRYPOINT ["dotnet", "Automation.Worker.dll"]
