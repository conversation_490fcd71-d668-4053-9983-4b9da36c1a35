# =============================================================================
# Multi-stage Dockerfile for Automation.Worker.CSharp
# Optimized for production deployment with security best practices
# =============================================================================

# =============================================================================
# Stage 1: Build Environment
# =============================================================================
FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS build
LABEL stage=build

# Set working directory
WORKDIR /src

# Install build dependencies
RUN apk add --no-cache \
    git \
    curl \
    && rm -rf /var/cache/apk/*

# Copy solution and project files for dependency restoration
COPY AutomationSolution.sln ./
COPY Directory.Build.props ./
COPY src/Automation.Contracts/Automation.Contracts.csproj ./src/Automation.Contracts/
COPY src/Automation.Utilities.CSharp/Automation.Utilities.CSharp.csproj ./src/Automation.Utilities.CSharp/
COPY src/Automation.Prompts.CSharp/Automation.Prompts.CSharp.csproj ./src/Automation.Prompts.CSharp/
COPY src/Automation.Web.CSharp/Automation.Web.CSharp.csproj ./src/Automation.Web.CSharp/
COPY src/Automation.Mobile.CSharp/Automation.Mobile.CSharp.csproj ./src/Automation.Mobile.CSharp/
COPY src/Automation.AI.Infrastructure.CSharp/Automation.AI.Infrastructure.CSharp.csproj ./src/Automation.AI.Infrastructure.CSharp/
COPY src/Automation.Worker.CSharp/Automation.Worker.CSharp.csproj ./src/Automation.Worker.CSharp/

# Restore dependencies
RUN dotnet restore src/Automation.Worker.CSharp/Automation.Worker.CSharp.csproj \
    --verbosity minimal

# Copy source code
COPY src/ ./src/

# Build and publish application in one step
RUN dotnet publish src/Automation.Worker.CSharp/Automation.Worker.CSharp.csproj \
    --configuration Release \
    --output /app/publish \
    --no-restore \
    --verbosity minimal

# =============================================================================
# Stage 2: Runtime Environment with Playwright
# =============================================================================
FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine AS runtime
LABEL maintainer="Automation Solution Team"
LABEL version="1.0.0"
LABEL description="Automation Worker Service with Web and Mobile automation capabilities"

# Install runtime dependencies
RUN apk add --no-cache \
    # Basic utilities
    curl \
    wget \
    ca-certificates \
    tzdata \
    # Playwright browser dependencies
    chromium \
    chromium-chromedriver \
    firefox \
    # Additional dependencies for automation
    xvfb \
    dbus \
    fontconfig \
    ttf-freefont \
    # Security and monitoring
    procps \
    && rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup -g 1001 -S automation && \
    adduser -S -D -H -u 1001 -h /app -s /sbin/nologin -G automation automation

# Set working directory
WORKDIR /app

# Copy published application
COPY --from=build --chown=automation:automation /app/publish ./

# Create directories for logs and data
RUN mkdir -p /app/logs /app/data /app/screenshots && \
    chown -R automation:automation /app

# Set environment variables
ENV ASPNETCORE_ENVIRONMENT=Production \
    DOTNET_RUNNING_IN_CONTAINER=true \
    DOTNET_USE_POLLING_FILE_WATCHER=true \
    DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false \
    # Playwright configuration
    PLAYWRIGHT_BROWSERS_PATH=/usr/bin \
    PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1 \
    # Security
    DOTNET_EnableDiagnostics=0 \
    # Performance
    DOTNET_TieredCompilation=1 \
    DOTNET_ReadyToRun=1

# Configure Playwright to use system browsers
RUN ln -sf /usr/bin/chromium-browser /usr/bin/chromium && \
    ln -sf /usr/bin/firefox /usr/bin/firefox-bin

# Health check configuration
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Expose ports
EXPOSE 8080

# Switch to non-root user
USER automation

# Set entry point
ENTRYPOINT ["dotnet", "Automation.Worker.CSharp.dll"]

# =============================================================================
# Stage 3: Development Environment (Optional)
# =============================================================================
FROM build AS development
LABEL stage=development

# Install development tools
RUN apk add --no-cache \
    bash \
    vim \
    htop \
    && rm -rf /var/cache/apk/*

# Set development environment
ENV ASPNETCORE_ENVIRONMENT=Development \
    DOTNET_USE_POLLING_FILE_WATCHER=true \
    DOTNET_EnableDiagnostics=1

# Expose additional ports for debugging
EXPOSE 8080 5000 5001

# Development entry point with hot reload
WORKDIR /src
ENTRYPOINT ["dotnet", "watch", "run", "--project", "src/Automation.Worker.CSharp/Automation.Worker.CSharp.csproj"]

# =============================================================================
# Stage 4: Testing Environment (Optional)
# =============================================================================
FROM build AS testing
LABEL stage=testing

# Copy test projects
COPY tests/ ./tests/

# Restore test dependencies
RUN dotnet restore tests/Automation.Integration.Tests/Automation.Integration.Tests.csproj \
    --verbosity minimal

# Build tests
RUN dotnet build tests/Automation.Integration.Tests/Automation.Integration.Tests.csproj \
    --configuration Release \
    --no-restore \
    --verbosity minimal

# Set test environment
ENV ASPNETCORE_ENVIRONMENT=Testing \
    DOTNET_EnableDiagnostics=1

# Test entry point
ENTRYPOINT ["dotnet", "test", "tests/Automation.Integration.Tests/Automation.Integration.Tests.csproj"]
