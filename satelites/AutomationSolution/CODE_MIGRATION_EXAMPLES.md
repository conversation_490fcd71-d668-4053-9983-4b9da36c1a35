# 💻 Ejemplos de Código - Migración F# → C#

## 📋 Índice
1. [Automation.Web Migration](#automation-web-migration)
2. [Automation.Worker Migration](#automation-worker-migration)
3. [Resource Pool Manager](#resource-pool-manager)
4. [Performance Monitor](#performance-monitor)
5. [Interoperability Examples](#interoperability-examples)

## 🌐 Automation.Web Migration

### ANTES (F#)
```fsharp
// Automation.Web/WebTaskExecutor.fs
namespace Automation.Web

open System
open Microsoft.Playwright
open Automation.Core
open Automation.Utilities.Logging

type WebTaskExecutor() =
    let mutable browser: IBrowser option = None
    let mutable page: IPage option = None

    interface ITaskExecutor with
        member this.ExecuteAsync(actions: Action list) =
            async {
                try
                    do! this.InitializeBrowserAsync()

                    for action in actions do
                        match action with
                        | Navigate url ->
                            info $"Navigating to {url}"
                            do! page.Value.GotoAsync(url) |> Async.AwaitTask
                        | Click selector ->
                            info $"Clicking element: {selector}"
                            do! page.Value.ClickAsync(selector) |> Async.AwaitTask
                        | TypeText (selector, text) ->
                            info $"Typing text in {selector}: {text}"
                            do! page.Value.FillAsync(selector, text) |> Async.AwaitTask
                        | Screenshot path ->
                            info $"Taking screenshot: {path}"
                            do! page.Value.ScreenshotAsync(ScreenshotOptions(Path = path)) |> Async.AwaitTask
                        | _ -> ()

                    return Success "All actions completed successfully"
                with
                | ex ->
                    error $"Execution failed: {ex.Message}"
                    return Failure {
                        Message = ex.Message
                        FailedAction = None
                        ScreenshotPath = None
                        CurrentUrl = None
                        HtmlContent = None
                        AttemptedSelectors = []
                    }
            }

    member private this.InitializeBrowserAsync() =
        async {
            if browser.IsNone then
                let playwright = await Playwright.CreateAsync()
                let! newBrowser = playwright.Chromium.LaunchAsync(BrowserTypeLaunchOptions(Headless = true)) |> Async.AwaitTask
                browser <- Some newBrowser
                let! newPage = newBrowser.NewPageAsync() |> Async.AwaitTask
                page <- Some newPage
        }
```

### DESPUÉS (C#)
```csharp
// Automation.Web/WebTaskExecutor.cs
using Microsoft.Playwright;
using Microsoft.Extensions.Logging;
using Automation.Contracts;
using Automation.Core;

namespace Automation.Web;

public class WebTaskExecutor : ITaskExecutor
{
    private readonly ILogger<WebTaskExecutor> _logger;
    private readonly WebExecutorOptions _options;
    private IBrowser? _browser;
    private IPage? _page;
    private IPlaywright? _playwright;
    private bool _disposed;

    public WebTaskExecutor(ILogger<WebTaskExecutor> logger, WebExecutorOptions options)
    {
        _logger = logger;
        _options = options;
    }

    public string ExecutorType => "web";

    public async Task<TaskResult> ExecuteAsync(IEnumerable<Action> actions, CancellationToken cancellationToken = default)
    {
        try
        {
            await InitializeBrowserAsync(cancellationToken);

            foreach (var action in actions)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var result = await ExecuteActionAsync(action, cancellationToken);
                if (result.IsFailure)
                    return result;
            }

            return TaskResult.NewSuccess("All actions completed successfully");
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("Task execution was cancelled");
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Task execution failed: {Message}", ex.Message);

            var errorContext = await CaptureErrorContextAsync();
            return TaskResult.NewFailure(new ActionExecutionError
            {
                Message = ex.Message,
                FailedAction = null,
                ScreenshotPath = errorContext.ScreenshotPath,
                CurrentUrl = errorContext.CurrentUrl,
                HtmlContent = errorContext.HtmlContent,
                AttemptedSelectors = new List<string>()
            });
        }
    }

    private async Task<TaskResult> ExecuteActionAsync(Action action, CancellationToken cancellationToken)
    {
        try
        {
            switch (action)
            {
                case Action.Navigate navigate:
                    _logger.LogInformation("Navigating to {Url}", navigate.url);
                    await _page!.GotoAsync(navigate.url, new PageGotoOptions
                    {
                        WaitUntil = WaitUntilState.NetworkIdle,
                        Timeout = _options.NavigationTimeout
                    });
                    break;

                case Action.Click click:
                    _logger.LogInformation("Clicking element: {Selector}", click.selector);
                    await _page!.ClickAsync(click.selector, new PageClickOptions
                    {
                        Timeout = _options.ActionTimeout
                    });
                    break;

                case Action.TypeText typeText:
                    _logger.LogInformation("Typing text in {Selector}: {Text}", typeText.selector, typeText.text);
                    await _page!.FillAsync(typeText.selector, typeText.text, new PageFillOptions
                    {
                        Timeout = _options.ActionTimeout
                    });
                    break;

                case Action.Screenshot screenshot:
                    _logger.LogInformation("Taking screenshot: {Path}", screenshot.path);
                    await _page!.ScreenshotAsync(new PageScreenshotOptions
                    {
                        Path = screenshot.path,
                        FullPage = _options.FullPageScreenshots
                    });
                    break;

                case Action.Tap tap:
                    _logger.LogInformation("Tapping element: {Selector}", tap.selector);
                    await _page!.TapAsync(tap.selector, new PageTapOptions
                    {
                        Timeout = _options.ActionTimeout
                    });
                    break;

                case Action.GetText getText:
                    _logger.LogInformation("Getting text from element: {Selector}", getText.selector);
                    var text = await _page!.TextContentAsync(getText.selector, new PageTextContentOptions
                    {
                        Timeout = _options.ActionTimeout
                    });
                    _logger.LogDebug("Retrieved text: {Text}", text);
                    break;

                default:
                    throw new NotSupportedException($"Action type not supported: {action}");
            }

            return TaskResult.NewSuccess($"Action completed: {action}");
        }
        catch (TimeoutException ex)
        {
            _logger.LogError(ex, "Action timed out: {Action}", action);
            var errorContext = await CaptureErrorContextAsync();

            return TaskResult.NewFailure(new ActionExecutionError
            {
                Message = $"Action timed out: {ex.Message}",
                FailedAction = action,
                ScreenshotPath = errorContext.ScreenshotPath,
                CurrentUrl = errorContext.CurrentUrl,
                HtmlContent = errorContext.HtmlContent,
                AttemptedSelectors = new List<string> { GetSelectorFromAction(action) }.Where(s => s != null).ToList()!
            });
        }
    }

    private async Task InitializeBrowserAsync(CancellationToken cancellationToken)
    {
        if (_browser == null)
        {
            _playwright = await Playwright.CreateAsync();

            var launchOptions = new BrowserTypeLaunchOptions
            {
                Headless = _options.Headless,
                SlowMo = _options.SlowMotion,
                Args = _options.BrowserArgs
            };

            _browser = await _playwright.Chromium.LaunchAsync(launchOptions);

            var contextOptions = new BrowserNewContextOptions
            {
                ViewportSize = _options.ViewportSize,
                UserAgent = _options.UserAgent,
                Locale = _options.Locale
            };

            var context = await _browser.NewContextAsync(contextOptions);
            _page = await context.NewPageAsync();

            // Setup page event handlers
            _page.Console += (_, e) => _logger.LogDebug("Browser console: {Message}", e.Text);
            _page.PageError += (_, e) => _logger.LogWarning("Browser error: {Error}", e);
        }
    }

    private async Task<ErrorContext> CaptureErrorContextAsync()
    {
        try
        {
            var screenshotPath = Path.Combine(_options.ScreenshotDirectory, $"error_{DateTime.UtcNow:yyyyMMdd_HHmmss}.png");

            if (_page != null)
            {
                await _page.ScreenshotAsync(new PageScreenshotOptions { Path = screenshotPath });

                return new ErrorContext
                {
                    ScreenshotPath = screenshotPath,
                    CurrentUrl = _page.Url,
                    HtmlContent = await _page.ContentAsync()
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to capture error context");
        }

        return new ErrorContext();
    }

    private static string? GetSelectorFromAction(Action action) => action switch
    {
        Action.Click click => click.selector,
        Action.Tap tap => tap.selector,
        Action.TypeText typeText => typeText.selector,
        Action.GetText getText => getText.selector,
        _ => null
    };

    public async Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            if (_browser == null || !_browser.IsConnected)
                return false;

            if (_page == null)
                return false;

            // Simple health check - try to evaluate JavaScript
            await _page.EvaluateAsync("() => document.readyState");
            return true;
        }
        catch
        {
            return false;
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _page?.CloseAsync().GetAwaiter().GetResult();
            _browser?.CloseAsync().GetAwaiter().GetResult();
            _playwright?.Dispose();
            _disposed = true;
        }
    }
}

// Configuration options
public class WebExecutorOptions
{
    public bool Headless { get; set; } = true;
    public int SlowMotion { get; set; } = 0;
    public string[] BrowserArgs { get; set; } = Array.Empty<string>();
    public ViewportSize? ViewportSize { get; set; } = new() { Width = 1920, Height = 1080 };
    public string? UserAgent { get; set; }
    public string? Locale { get; set; } = "en-US";
    public float NavigationTimeout { get; set; } = 30000;
    public float ActionTimeout { get; set; } = 10000;
    public bool FullPageScreenshots { get; set; } = true;
    public string ScreenshotDirectory { get; set; } = "screenshots";
}

public class ErrorContext
{
    public string? ScreenshotPath { get; set; }
    public string? CurrentUrl { get; set; }
    public string? HtmlContent { get; set; }
}
```

## 🔧 Automation.Worker Migration

### ANTES (F#)
```fsharp
// Automation.Worker/Program.fs
open System
open System.Threading
open StackExchange.Redis
open Automation.Core
open Automation.AI
open Automation.Utilities.Logging

let mutable successCount = 0L
let mutable failureCount = 0L
let mutable retryCount = 0L

let handleMessage (channel: RedisChannel) (message: RedisValue) =
    let maxRetries = 3
    let baseBackoffMs = 500

    async {
        let command = message.ToString()
        info $"Processing command: {command}"

        let rec execute attempt =
            async {
                try
                    let payload = TaskDispatcher.parseMessage command
                    let actions = AIProcessor.processCommand payload.Command

                    match SecurityEngine.validateActions SecurityEngine.defaultPolicy actions with
                    | SecurityPass ->
                        let executor = WebAutomator.WebTaskExecutor() :> ITaskExecutor
                        let! result = executor.ExecuteAsync(actions)

                        match result with
                        | Success msg ->
                            info $"Task completed successfully: {msg}"
                            Interlocked.Increment(&successCount) |> ignore
                            return true
                        | Failure error ->
                            error $"Task failed: {error.Message}"
                            Interlocked.Increment(&failureCount) |> ignore
                            return false
                    | SecurityViolation violations ->
                        error $"Security violations: {String.concat "; " violations}"
                        return false
                with
                | ex ->
                    error $"Execution error: {ex.Message}"
                    if attempt < maxRetries then
                        let delay = baseBackoffMs * (pown 2 attempt)
                        do! Async.Sleep delay
                        Interlocked.Increment(&retryCount) |> ignore
                        return! execute (attempt + 1)
                    else
                        return false
            }

        let! success = execute 0
        return success
    } |> Async.Start

let main () =
    try
        use redis = ConnectionMultiplexer.Connect("localhost")
        let subscriber = redis.GetSubscriber()

        subscriber.Subscribe(RedisChannel("automation_channel", RedisChannel.PatternMode.Literal), handleMessage) |> ignore

        info "Worker is running. Press Ctrl+C to exit."
        Thread.Sleep(Timeout.Infinite)
    with
    | ex -> error $"Worker failed to start: {ex.Message}"

[<EntryPoint>]
let entryPoint _ =
    main()
    0
```

### DESPUÉS (C#)
```csharp
// Automation.Worker/WorkerService.cs
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using StackExchange.Redis;
using System.Threading.Channels;
using Automation.Contracts;
using Automation.Core;
using Automation.AI.Core;

namespace Automation.Worker;

public class WorkerService : BackgroundService
{
    private readonly ILogger<WorkerService> _logger;
    private readonly WorkerOptions _options;
    private readonly IConnectionMultiplexer _redis;
    private readonly ITaskExecutorFactory _executorFactory;
    private readonly IAIProcessor _aiProcessor;
    private readonly ISecurityEngine _securityEngine;
    private readonly SemaphoreSlim _concurrencySemaphore;
    private readonly Channel<TaskMessage> _taskChannel;
    private readonly WorkerMetrics _metrics;

    public WorkerService(
        ILogger<WorkerService> logger,
        IOptions<WorkerOptions> options,
        IConnectionMultiplexer redis,
        ITaskExecutorFactory executorFactory,
        IAIProcessor aiProcessor,
        ISecurityEngine securityEngine,
        WorkerMetrics metrics)
    {
        _logger = logger;
        _options = options.Value;
        _redis = redis;
        _executorFactory = executorFactory;
        _aiProcessor = aiProcessor;
        _securityEngine = securityEngine;
        _metrics = metrics;

        _concurrencySemaphore = new SemaphoreSlim(_options.ConcurrencyLimit, _options.ConcurrencyLimit);

        var channelOptions = new BoundedChannelOptions(_options.QueueCapacity)
        {
            FullMode = BoundedChannelFullMode.Wait,
            SingleReader = false,
            SingleWriter = true
        };
        _taskChannel = Channel.CreateBounded<TaskMessage>(channelOptions);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Worker service starting...");

        // Start message consumer
        var consumerTask = ConsumeMessagesAsync(stoppingToken);

        // Start Redis subscriber
        var subscriberTask = SubscribeToRedisAsync(stoppingToken);

        try
        {
            await Task.WhenAll(consumerTask, subscriberTask);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Worker service is stopping...");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Worker service encountered an error");
            throw;
        }
    }

    private async Task SubscribeToRedisAsync(CancellationToken stoppingToken)
    {
        var subscriber = _redis.GetSubscriber();

        await subscriber.SubscribeAsync(
            new RedisChannel(_options.TaskChannel, RedisChannel.PatternMode.Literal),
            async (channel, message) =>
            {
                if (!stoppingToken.IsCancellationRequested)
                {
                    var taskMessage = new TaskMessage
                    {
                        Id = Guid.NewGuid().ToString(),
                        Content = message.ToString(),
                        ReceivedAt = DateTimeOffset.UtcNow,
                        RetryCount = 0
                    };

                    await _taskChannel.Writer.WriteAsync(taskMessage, stoppingToken);
                }
            });

        _logger.LogInformation("Subscribed to Redis channel: {Channel}", _options.TaskChannel);

        // Keep subscription alive
        while (!stoppingToken.IsCancellationRequested)
        {
            await Task.Delay(TimeSpan.FromSeconds(1), stoppingToken);
        }
    }

    private async Task ConsumeMessagesAsync(CancellationToken stoppingToken)
    {
        await foreach (var taskMessage in _taskChannel.Reader.ReadAllAsync(stoppingToken))
        {
            // Process message with concurrency control
            _ = Task.Run(async () =>
            {
                await _concurrencySemaphore.WaitAsync(stoppingToken);
                try
                {
                    await ProcessTaskMessageAsync(taskMessage, stoppingToken);
                }
                finally
                {
                    _concurrencySemaphore.Release();
                }
            }, stoppingToken);
        }
    }

    private async Task ProcessTaskMessageAsync(TaskMessage taskMessage, CancellationToken stoppingToken)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Processing task {TaskId}: {Content}", taskMessage.Id, taskMessage.Content);

            // Parse message
            var payload = TaskDispatcher.ParseMessage(taskMessage.Content);

            // Generate actions using AI
            var actions = await _aiProcessor.ProcessCommandAsync(payload.Command, stoppingToken);

            _logger.LogDebug("Generated {ActionCount} actions for task {TaskId}", actions.Count(), taskMessage.Id);

            // Security validation
            var securityResult = await _securityEngine.ValidateActionsAsync(actions, stoppingToken);
            if (!securityResult.IsValid)
            {
                _logger.LogWarning("Security validation failed for task {TaskId}: {Violations}",
                    taskMessage.Id, string.Join("; ", securityResult.Violations));

                _metrics.RecordSecurityViolation();
                return;
            }

            // Execute actions
            using var executor = _executorFactory.CreateExecutor(payload);
            var result = await ExecuteWithRetryAsync(executor, actions, taskMessage, stoppingToken);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Task {TaskId} completed successfully in {Duration}ms",
                    taskMessage.Id, stopwatch.ElapsedMilliseconds);
                _metrics.RecordSuccess(stopwatch.Elapsed);
            }
            else
            {
                _logger.LogError("Task {TaskId} failed: {Error}", taskMessage.Id, result.Error?.Message);
                _metrics.RecordFailure(stopwatch.Elapsed);

                // Send to DLQ if max retries exceeded
                if (taskMessage.RetryCount >= _options.MaxRetries)
                {
                    await SendToDeadLetterQueueAsync(taskMessage, result.Error);
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Task {TaskId} was cancelled", taskMessage.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error processing task {TaskId}", taskMessage.Id);
            _metrics.RecordError(stopwatch.Elapsed);
        }
    }

    private async Task<TaskResult> ExecuteWithRetryAsync(
        ITaskExecutor executor,
        IEnumerable<Action> actions,
        TaskMessage taskMessage,
        CancellationToken stoppingToken)
    {
        var attempt = 0;

        while (attempt <= _options.MaxRetries)
        {
            try
            {
                var result = await executor.ExecuteAsync(actions, stoppingToken);

                if (result.IsSuccess)
                    return result;

                // Check if we should retry
                if (attempt < _options.MaxRetries && IsRetryableError(result.Error))
                {
                    var delay = CalculateBackoffDelay(attempt);
                    _logger.LogWarning("Task {TaskId} attempt {Attempt} failed, retrying in {Delay}ms: {Error}",
                        taskMessage.Id, attempt + 1, delay.TotalMilliseconds, result.Error?.Message);

                    await Task.Delay(delay, stoppingToken);
                    attempt++;
                    _metrics.RecordRetry();
                    continue;
                }

                return result;
            }
            catch (Exception ex) when (attempt < _options.MaxRetries)
            {
                var delay = CalculateBackoffDelay(attempt);
                _logger.LogWarning(ex, "Task {TaskId} attempt {Attempt} threw exception, retrying in {Delay}ms",
                    taskMessage.Id, attempt + 1, delay.TotalMilliseconds);

                await Task.Delay(delay, stoppingToken);
                attempt++;
                _metrics.RecordRetry();
            }
        }

        return TaskResult.NewFailure(new ActionExecutionError
        {
            Message = $"Task failed after {_options.MaxRetries} retries",
            FailedAction = null,
            ScreenshotPath = null,
            CurrentUrl = null,
            HtmlContent = null,
            AttemptedSelectors = new List<string>()
        });
    }

    private TimeSpan CalculateBackoffDelay(int attempt)
    {
        var delay = _options.BaseBackoffMs * Math.Pow(2, attempt);
        var jitter = Random.Shared.NextDouble() * 0.1 * delay; // 10% jitter
        return TimeSpan.FromMilliseconds(delay + jitter);
    }

    private static bool IsRetryableError(ActionExecutionError? error)
    {
        if (error == null) return false;

        // Define retryable error patterns
        var retryablePatterns = new[]
        {
            "timeout",
            "network",
            "connection",
            "temporary"
        };

        return retryablePatterns.Any(pattern =>
            error.Message.Contains(pattern, StringComparison.OrdinalIgnoreCase));
    }

    private async Task SendToDeadLetterQueueAsync(TaskMessage taskMessage, ActionExecutionError? error)
    {
        try
        {
            var database = _redis.GetDatabase();
            var dlqMessage = new
            {
                taskMessage.Id,
                taskMessage.Content,
                taskMessage.ReceivedAt,
                taskMessage.RetryCount,
                Error = error?.Message,
                FailedAt = DateTimeOffset.UtcNow
            };

            await database.ListRightPushAsync(_options.DeadLetterQueue,
                System.Text.Json.JsonSerializer.Serialize(dlqMessage));

            _metrics.RecordDeadLetter();
            _logger.LogWarning("Task {TaskId} sent to dead letter queue after {RetryCount} retries",
                taskMessage.Id, taskMessage.RetryCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send task {TaskId} to dead letter queue", taskMessage.Id);
        }
    }

    public override void Dispose()
    {
        _concurrencySemaphore?.Dispose();
        _taskChannel?.Writer.Complete();
        base.Dispose();
    }
}

// Supporting classes
public class TaskMessage
{
    public string Id { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public DateTimeOffset ReceivedAt { get; set; }
    public int RetryCount { get; set; }
}

public class WorkerOptions
{
    public string TaskChannel { get; set; } = "automation_channel";
    public string DeadLetterQueue { get; set; } = "automation_dlq";
    public int ConcurrencyLimit { get; set; } = 3;
    public int MaxRetries { get; set; } = 3;
    public int BaseBackoffMs { get; set; } = 500;
    public int QueueCapacity { get; set; } = 1000;
}

public class WorkerMetrics
{
    private long _successCount;
    private long _failureCount;
    private long _retryCount;
    private long _deadLetterCount;
    private long _securityViolationCount;
    private long _errorCount;

    public void RecordSuccess(TimeSpan duration) => Interlocked.Increment(ref _successCount);
    public void RecordFailure(TimeSpan duration) => Interlocked.Increment(ref _failureCount);
    public void RecordRetry() => Interlocked.Increment(ref _retryCount);
    public void RecordDeadLetter() => Interlocked.Increment(ref _deadLetterCount);
    public void RecordSecurityViolation() => Interlocked.Increment(ref _securityViolationCount);
    public void RecordError(TimeSpan duration) => Interlocked.Increment(ref _errorCount);

    public WorkerStats GetStats() => new()
    {
        SuccessCount = _successCount,
        FailureCount = _failureCount,
        RetryCount = _retryCount,
        DeadLetterCount = _deadLetterCount,
        SecurityViolationCount = _securityViolationCount,
        ErrorCount = _errorCount
    };
}

public class WorkerStats
{
    public long SuccessCount { get; set; }
    public long FailureCount { get; set; }
    public long RetryCount { get; set; }
    public long DeadLetterCount { get; set; }
    public long SecurityViolationCount { get; set; }
    public long ErrorCount { get; set; }
}
```