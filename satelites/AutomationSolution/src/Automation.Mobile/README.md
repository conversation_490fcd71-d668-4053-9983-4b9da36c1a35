# Automation.Mobile

This project provides capabilities for automating interactions with mobile applications and browsers. It integrates with Appium, a widely used open-source tool for mobile test automation, to enable cross-platform control over mobile devices and emulators.

## Features

*   **Cross-Platform Mobile Automation**: Supports automation for Android applications and mobile web browsers.
*   **Action Execution**: Implements the `ITaskExecutor` interface from `Automation.Core` to execute a predefined set of actions (`Click`, `Tap`, `TypeText`, `Navigate`, `Screenshot`, `GetText`) on mobile devices.
*   **Element Interaction**: Provides methods to find and interact with UI elements using various mobile-specific locator strategies (ID, XPath, Class Name, Accessibility ID, Text, Description).
*   **Screenshot Capture**: Enables capturing screenshots of the mobile device screen during automation, useful for visual verification and debugging.
*   **Error Handling & Context**: Captures detailed error information, including screenshots, current URL, and HTML content, when an action fails, aiding in debugging and potential self-healing processes.
*   **Configurable Driver Settings**: Allows customization of Appium server URL, platform details, device names, and other Appium capabilities.

## Components

*   **`MobileExecutorHelpers`**: A private module containing helper functions for finding elements and executing individual actions using an Appium driver. It abstracts away the complexities of Appium's locator strategies and action execution.
*   **`MobileExecutor`**: The main module that defines the `MobileConfig` for configuring the Appium driver and implements `MobileTaskExecutor` (and `ConfigurableMobileTaskExecutor`) which adheres to the `ITaskExecutor` interface. It manages the lifecycle of the Appium driver and orchestrates the execution of action lists.

## Usage

`Automation.Mobile` is typically used by the `Automation.Worker` service to perform automation tasks on mobile devices. It provides the low-level interaction capabilities with the mobile environment.

### Example: Initializing and Executing Mobile Actions

```fsharp
open System
open Automation.Core
open Automation.Mobile.MobileExecutor

// Assume you have an Appium server running at http://localhost:4723/wd/hub

// Configure the mobile device/emulator
let androidConfig = {
    AppiumServerUrl = "http://localhost:4723/wd/hub"
    PlatformName = "Android"
    DeviceName = "emulator-5554" // Replace with your device/emulator name
    AutomationName = "UiAutomator2"
    BrowserName = Some "Chrome" // For mobile web automation
    AppPackage = None // Set if automating a native app, e.g., "com.android.chrome"
    AppActivity = None // Set if automating a native app, e.g., "com.google.android.apps.chrome.Main"
    NoReset = true
    FullReset = false
    CommandTimeout = TimeSpan.FromSeconds(60.0)
}

// Create an instance of the mobile task executor
use mobileExecutor = new ConfigurableMobileTaskExecutor(androidConfig) :> ITaskExecutor

// Define a list of actions to perform
let actionsToExecute = [
    Navigate "https://www.google.com"
    TypeText ("id:APjFqb", "F# programming") // Google search bar by ID
    Click ("xpath://*[@name='btnK']") // Google Search button by XPath
    Screenshot "google_search_results.png"
    GetText ("id:APjFqb") // Get text from search bar
]

// Execute the actions
let! result = mobileExecutor.ExecuteActions(actionsToExecute)

match result with
| Success message ->
    printfn $"Mobile automation successful: {message}"
| Failure error ->
    printfn $"Mobile automation failed: {error.Message}"
    error.ScreenshotPath |> Option.iter (fun path -> printfn $"Error screenshot saved to: {path}")
    error.CurrentUrl |> Option.iter (fun url -> printfn $"Current URL: {url}")
    error.HtmlContent |> Option.iter (fun html -> printfn $"HTML Content (partial): {html.Substring(0, Math.Min(500, html.Length))}...")
```

## Supported Platforms

The current implementation primarily focuses on **Android** automation through Appium's `UiAutomator2` driver. While Appium supports iOS, React Native, and Flutter, specific configurations and helper functions for these platforms would need to be added or extended within this project.

## Configuration

The `MobileConfig` type allows for detailed configuration of the Appium driver. Key configurable settings include:

*   `AppiumServerUrl`: The URL of your Appium server.
*   `PlatformName`: e.g., "Android", "iOS".
*   `DeviceName`: The name of the device or emulator.
*   `AutomationName`: The automation engine to use (e.g., "UiAutomator2" for Android, "XCUITest" for iOS).
*   `BrowserName`: (Optional) For mobile web automation (e.g., "Chrome", "Safari").
*   `AppPackage`, `AppActivity`: (Optional) For automating native Android applications.
*   `NoReset`, `FullReset`: Appium capabilities for managing app state between sessions.
*   `CommandTimeout`: Timeout for Appium commands.

## Dependencies

*   `Appium.WebDriver`: The .NET client library for Appium.
*   `Selenium.WebDriver`: Underlying WebDriver library used by Appium.
*   `Automation.Core`: Provides the core `Action` types and `ITaskExecutor` interface.
*   `Automation.Utilities`: For logging.

## Development

This project is developed in F#. When extending or modifying, ensure compatibility with Appium's capabilities and mobile platform specifics.
