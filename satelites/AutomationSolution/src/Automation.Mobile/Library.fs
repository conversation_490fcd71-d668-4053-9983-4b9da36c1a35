namespace Automation.Mobile

open System
open System.IO
open System.Threading.Tasks
open Automation.Core
open OpenQA.Selenium
open OpenQA.Selenium.Appium
open OpenQA.Selenium.Appium.Enums
open OpenQA.Selenium.Appium.Android

/// Helper functions to execute individual actions with an Appium driver
module private MobileExecutorHelpers =
    
    /// Mobile-specific element selection strategies
    let private findElement (driver: AppiumDriver) (selector: string) =
        // Try different mobile locator strategies
        if selector.StartsWith("id:") then
            driver.FindElement(By.Id(selector.Substring(3)))
        elif selector.StartsWith("xpath:") then
            driver.FindElement(By.XPath(selector.Substring(6)))
        elif selector.StartsWith("class:") then
            driver.FindElement(By.ClassName(selector.Substring(6)))
        elif selector.StartsWith("text:") then
            driver.FindElement(MobileBy.AndroidUIAutomator($"new UiSelector().text(\"{selector.Substring(5)}\")"))
        elif selector.StartsWith("desc:") then
            driver.FindElement(MobileBy.AndroidUIAutomator($"new UiSelector().description(\"{selector.Substring(5)}\")"))
        else
            // Default: try CSS selector for web contexts, otherwise treat as accessibility id
            try
                driver.FindElement(By.CssSelector(selector))
            with
            | _ -> driver.FindElement(MobileBy.AccessibilityId(selector))

    let executeAction (driver: AppiumDriver) (action: Action) =
        async {
            try
                match action with
                | Navigate url ->
                    driver.Navigate().GoToUrl(url)
                    return Ok "navigate"
                | Click selector
                | Tap selector ->
                    let elem = findElement driver selector
                    elem.Click()
                    return Ok "click"
                | TypeText (selector, text) ->
                    let elem = findElement driver selector
                    elem.Clear()
                    elem.SendKeys(text)
                    return Ok "type"
                | Screenshot path ->
                    // Ensure directory exists
                    let directory = Path.GetDirectoryName(path)
                    if not (String.IsNullOrEmpty(directory)) && not (Directory.Exists(directory)) then
                        Directory.CreateDirectory(directory) |> ignore
                    
                    let ss = driver.GetScreenshot()
                    // Use SaveAsFile with just the path - format is inferred from extension
                    ss.SaveAsFile(path)
                    return Ok "screenshot"
                | GetText selector ->
                    let elem = findElement driver selector
                    let text = elem.Text
                    return Ok $"gettext: {text}"
                
            with 
            | :? NoSuchElementException as ex ->
                return Error $"Element not found: {ex.Message}"
            | :? WebDriverException as ex ->
                return Error $"WebDriver error: {ex.Message}"
            | ex ->
                return Error $"Unexpected error: {ex.Message}"
        }

/// Executor implementation
module MobileExecutor =
    
    /// Configuration for mobile driver
    type MobileConfig = {
        AppiumServerUrl: string
        PlatformName: string
        DeviceName: string
        AutomationName: string
        BrowserName: string option
        AppPackage: string option
        AppActivity: string option
        NoReset: bool
        FullReset: bool
        CommandTimeout: TimeSpan
    }
    
    /// Default Android configuration
    let defaultAndroidConfig = {
        AppiumServerUrl = "http://localhost:4723/wd/hub"
        PlatformName = "Android"
        DeviceName = "Android Emulator"
        AutomationName = "UiAutomator2"
        BrowserName = Some "Chrome"
        AppPackage = None
        AppActivity = None
        NoReset = true
        FullReset = false
        CommandTimeout = TimeSpan.FromSeconds(60.0)
    }
    
    let private createAndroidDriver (config: MobileConfig) : AppiumDriver =
        let options = AppiumOptions()
        options.PlatformName <- config.PlatformName
        options.AddAdditionalAppiumOption(MobileCapabilityType.AutomationName, config.AutomationName)
        options.AddAdditionalAppiumOption(MobileCapabilityType.DeviceName, config.DeviceName)
        options.AddAdditionalAppiumOption(MobileCapabilityType.NoReset, config.NoReset)
        options.AddAdditionalAppiumOption(MobileCapabilityType.FullReset, config.FullReset)
        
        // Optional capabilities
        config.BrowserName |> Option.iter (fun browser -> 
            options.AddAdditionalAppiumOption(MobileCapabilityType.BrowserName, browser))
        config.AppPackage |> Option.iter (fun pkg -> 
            options.AddAdditionalAppiumOption(AndroidMobileCapabilityType.AppPackage, pkg))
        config.AppActivity |> Option.iter (fun activity -> 
            options.AddAdditionalAppiumOption(AndroidMobileCapabilityType.AppActivity, activity))
        
        let uri = Uri(config.AppiumServerUrl)
        new AndroidDriver(uri, options, config.CommandTimeout)

    let executeActionsWithConfig (driver: AppiumDriver) (config: MobileConfig) (actions: Action list) =
        async {
            try
                let mutable errors : string list = []
                let mutable failedAction: Action option = None

                for action in actions do
                    if List.isEmpty errors then
                        let! result = MobileExecutorHelpers.executeAction driver action
                        match result with
                        | Ok _ -> ()
                        | Error msg -> 
                            errors <- msg :: errors
                            failedAction <- Some action

                match errors with
                | [] -> return Success(sprintf "Successfully executed %d mobile actions." (List.length actions))
                | errorList ->
                    let combinedErrors = String.concat "; " (List.rev errorList)
                    
                    let screenshotPath = 
                        try 
                            let path = $"error_screenshot_{Guid.NewGuid()}.png"
                            let ss = driver.GetScreenshot()
                            ss.SaveAsFile(path)
                            Some path
                        with _ -> None

                    let currentUrl = 
                        try 
                            Some driver.Url
                        with _ -> None

                    let htmlContent = 
                        try 
                            Some driver.PageSource
                        with _ -> None

                    return Failure({ Message = combinedErrors
                                     FailedAction = failedAction
                                     ScreenshotPath = screenshotPath
                                     CurrentUrl = currentUrl
                                     HtmlContent = htmlContent
                                     AttemptedSelectors = [] })
            with
            | :? WebDriverException as ex ->
                let screenshotPath = 
                    try 
                        let path = $"error_screenshot_{Guid.NewGuid()}.png"
                        let ss = driver.GetScreenshot()
                        ss.SaveAsFile(path)
                        Some path
                    with _ -> None

                let currentUrl = 
                    try 
                        Some driver.Url
                    with _ -> None

                let htmlContent = 
                    try 
                        Some driver.PageSource
                    with _ -> None

                return Failure({ Message = sprintf "Mobile driver error: %s" ex.Message
                                 FailedAction = None
                                 ScreenshotPath = screenshotPath
                                 CurrentUrl = currentUrl
                                 HtmlContent = htmlContent
                                 AttemptedSelectors = [] })
            | ex ->
                let screenshotPath = 
                    try 
                        let path = $"error_screenshot_{Guid.NewGuid()}.png"
                        let ss = driver.GetScreenshot()
                        ss.SaveAsFile(path)
                        Some path
                    with _ -> None

                let currentUrl = 
                    try 
                        Some driver.Url
                    with _ -> None

                let htmlContent = 
                    try 
                        Some driver.PageSource
                    with _ -> None

                return Failure({ Message = sprintf "Mobile execution error: %s" ex.Message
                                 FailedAction = None
                                 ScreenshotPath = screenshotPath
                                 CurrentUrl = currentUrl
                                 HtmlContent = htmlContent
                                 AttemptedSelectors = [] })
        }

    /// Mobile task executor implementation
    type MobileTaskExecutor() =
        let config = defaultAndroidConfig
        let mutable driver: AppiumDriver = null

        do
            driver <- createAndroidDriver config

        interface IDisposable with
            member this.Dispose() =
                if driver <> null then
                    driver.Quit()
                    driver.Dispose()

        interface ITaskExecutor with
            member this.ExecuteActions(actions) =
                executeActionsWithConfig driver config actions

            member this.TakeScreenshot(path) =
                async {
                    let ss = driver.GetScreenshot()
                    ss.SaveAsFile(path)
                }

            member this.GetCurrentUrl() =
                async {
                    return driver.Url
                }

            member this.GetCurrentHtmlContent() =
                async {
                    return driver.PageSource
                }
    
    /// Mobile task executor with custom configuration
    type ConfigurableMobileTaskExecutor(config: MobileConfig) =
        let mutable driver: AppiumDriver = null

        do
            driver <- createAndroidDriver config

        interface IDisposable with
            member this.Dispose() =
                if driver <> null then
                    driver.Quit()
                    driver.Dispose()

        interface ITaskExecutor with
            member this.ExecuteActions(actions) =
                executeActionsWithConfig driver config actions

            member this.TakeScreenshot(path) =
                async {
                    let ss = driver.GetScreenshot()
                    ss.SaveAsFile(path)
                }

            member this.GetCurrentUrl() =
                async {
                    return driver.Url
                }

            member this.GetCurrentHtmlContent() =
                async {
                    return driver.PageSource
                }