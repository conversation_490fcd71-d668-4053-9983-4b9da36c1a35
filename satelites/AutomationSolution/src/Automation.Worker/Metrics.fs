module Automation.Worker.Metrics

open System
open System.Threading.Tasks
open Microsoft.AspNetCore.Builder
open Microsoft.AspNetCore.Hosting
open Microsoft.Extensions.Hosting
open Microsoft.AspNetCore.Http

// Read port from env or default 8080
let private port =
    match Environment.GetEnvironmentVariable("METRICS_PORT") with
    | null | "" -> 8080
    | value -> match Int32.TryParse(value) with | true, n -> n | _ -> 8080

let start (metricsProvider: unit -> string) =
    task {
        let builder = WebApplication.CreateBuilder([||])
        // Listen on 0.0.0.0:<port>
        builder.WebHost.UseUrls($"http://0.0.0.0:{port}") |> ignore
        let app = builder.Build()

        app.MapGet("/health", Func<HttpContext, Task>(fun ctx ->
            ctx.Response.WriteAsync("OK")
        )) |> ignore

        app.MapGet("/metrics", Func<HttpContext, Task>(fun ctx ->
            let metrics = metricsProvider()
            ctx.Response.ContentType <- "text/plain; version=0.0.4"
            ctx.Response.WriteAsync(metrics)
        )) |> ignore

        do! app.StartAsync()
    }
    |> ignore // fire-and-forget
