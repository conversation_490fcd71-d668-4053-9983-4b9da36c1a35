module Automation.Worker.Program
open System
open System.Threading
open StackExchange.Redis
open Automation.Core
open Automation.AI
open Automation.Web
open Automation.Mobile
open Automation.Utilities.Logging
open System.Collections.Concurrent

// --------------------
// Configuration Module
// --------------------
module Config =
    // Helper to fetch integer env vars with default fallback
    let private envInt name defaultVal =
        match System.Environment.GetEnvironmentVariable(name) with
        | null | "" -> defaultVal
        | v -> match System.Int32.TryParse(v) with | true, n -> n | _ -> defaultVal

    let concurrencyLimit   = envInt "CONCURRENCY_LIMIT"   3
    let maxRetries         = envInt "MAX_RETRIES"         3
    let baseBackoffMs      = envInt "BASE_BACKOFF_MS"     500
    let failureThreshold   = envInt "FAILURE_THRESHOLD"   5
    let failureWindowSec   = envInt "FAILURE_WINDOW_SEC"  60
    let circuitOpenSec     = envInt "CIRCUIT_OPEN_SEC"    60

// Runtime parameters (loaded from env vars or defaults)
let concurrencyLimit = Config.concurrencyLimit
let semaphore = new System.Threading.SemaphoreSlim(concurrencyLimit)

// Circuit Breaker configuration (configurable)
let failureThreshold = Config.failureThreshold
let failureWindow = TimeSpan.FromSeconds(float Config.failureWindowSec)
let circuitOpenTime = TimeSpan.FromSeconds(float Config.circuitOpenSec)

let failureTimestamps = ConcurrentQueue<DateTime>()
let mutable circuitState = "Closed" // Closed | Open | HalfOpen
let circuitLock = obj()

// --------------------
// Metrics counters
// --------------------
let mutable successCount = 0L
let mutable failureCount = 0L
let mutable retryCount   = 0L
let mutable dlqCount     = 0L

let inline inc (v: byref<int64>) =
    System.Threading.Interlocked.Increment &v |> ignore

let private pruneOldFailures() =
    let now = DateTime.UtcNow
    let rec loop () =
        let mutable ts = DateTime.MinValue
        if failureTimestamps.TryPeek(&ts) && (now - ts) > failureWindow then
            let mutable dump = DateTime.MinValue
            failureTimestamps.TryDequeue(&dump) |> ignore
            loop()
    loop()

let private recordFailure () =
    inc &failureCount
    failureTimestamps.Enqueue(DateTime.UtcNow)
    pruneOldFailures()
    lock circuitLock (fun () ->
        if circuitState <> "Open" && failureTimestamps.Count >= failureThreshold then
            circuitState <- "Open"
            warn "[CircuitBreaker] OPEN – too many recent failures."
            Async.Start(async {
                do! Async.Sleep(int circuitOpenTime.TotalMilliseconds)
                lock circuitLock (fun () ->
                    circuitState <- "HalfOpen"
                    warn "[CircuitBreaker] HALF-OPEN – testing health with next message."
                )
            })
    )

let private recordSuccess () =
    inc &successCount
    // drain queue
    while not failureTimestamps.IsEmpty do
        let mutable dump = DateTime.MinValue
        failureTimestamps.TryDequeue(&dump) |> ignore
    if circuitState <> "Closed" then
        circuitState <- "Closed"
        info "[CircuitBreaker] CLOSED – system healthy again."

// Message handler
let handleMessage (channel: RedisChannel) (message: RedisValue) =
    let maxRetries = Config.maxRetries
    let baseBackoffMs = Config.baseBackoffMs
    let dlqKey = "automation_dlq"

    // Circuit breaker gate
    if circuitState = "Open" then
        warn "[CircuitBreaker] Circuit OPEN – message routed to DLQ."
        try
            use conn = ConnectionMultiplexer.Connect("localhost")
            conn.GetDatabase().ListRightPush(dlqKey, message) |> ignore
        with ex -> 
            error (sprintf "[CircuitBreaker] DLQ push failed: %s" ex.Message)
    else
        // Closed or HalfOpen: continue processing
        let work () = 
            async {
                // Acquire semaphore to limit parallelism
                do! semaphore.WaitAsync() |> Async.AwaitTask
                let command = message.ToString()

                let rec execute attempt = 
                    async {
                        try
                            info (sprintf "[Worker] Attempt %d processing command: %s" (attempt + 1) command)
                            let payload = TaskDispatcher.parseMessage command
                            let commandText = payload.Command

                            info (sprintf "[Worker] AI module processing command: %s" commandText)
                            let actions = AIProcessor.processCommand commandText
                            info (sprintf "[Worker] AI module generated %d actions." (List.length actions))

                            // Security validation before execution
                            match SecurityEngine.validateActions SecurityEngine.defaultPolicy actions with
                            | SecurityPass ->
                                info "[Worker] Security validation passed. Proceeding with execution..."
                                
                                // Executor selection based on payload metadata
                                let executor : ITaskExecutor =
                                    match payload.Metadata with
                                    | Some meta when meta.ContainsKey("target") && meta.["target"] = "mobile" ->
                                        info "[Worker] Selecting Mobile Task Executor."
                                        MobileTaskExecutor.MobileTaskExecutor() :> ITaskExecutor
                                    | _ -> 
                                        info "[Worker] Selecting Web Task Executor."
                                        WebAutomator.WebTaskExecutor() :> ITaskExecutor

                                let! taskResult = executor.ExecuteActions(actions)
                                match taskResult with
                                | Success msg ->
                                    info (sprintf "[Worker] Execution Result: SUCCESS - %s" msg)
                                    return true
                                | Failure errMsg ->
                                    error (sprintf "[Worker] Execution Result: FAILURE - %s" errMsg)
                                    return false
                            | SecurityFail (violations, errorCode) ->
                                let combinedMessage = violations |> List.map SecurityEngine.getViolationMessage |> String.concat "; "
                                error (sprintf "[Worker] SECURITY VIOLATION (Code %d): %s" errorCode combinedMessage)
                                error "[Worker] Task execution blocked for security reasons."
                                return false
                        with ex ->
                            if attempt < maxRetries then
                                let delay = baseBackoffMs * pown 2 attempt
                                warn (sprintf "[Worker] Attempt %d failed: %s. Retrying in %d ms..." (attempt + 1) ex.Message delay)
                                do! Async.Sleep delay
                                inc &retryCount
                                return! execute (attempt + 1)
                            else
                                error (sprintf "[Worker] All %d retries failed. Pushing message to DLQ." (maxRetries + 1))
                                try
                                    use conn = ConnectionMultiplexer.Connect("localhost")
                                    conn.GetDatabase().ListRightPush(dlqKey, message) |> ignore
                                with dlqEx -> 
                                    error (sprintf "[Worker] DLQ push failed: %s" dlqEx.Message)
                                return false
                    }

                // Execute with retries
                let! success = execute 0
                if success then 
                    recordSuccess() 
                else 
                    recordFailure()
                
                semaphore.Release() |> ignore
            }
        
        // Start worker task asynchronously
        Async.Start(work())

let main () =
    try
        let redisConnectionString = "localhost"
        info (sprintf "Connecting to Redis at %s..." redisConnectionString)
        use redis = ConnectionMultiplexer.Connect(redisConnectionString)
        let subscriber = redis.GetSubscriber()

        let channel = "automation_channel"
        info (sprintf "Subscribing to channel '%s'..." channel)
        subscriber.Subscribe(RedisChannel(channel, RedisChannel.PatternMode.Literal), handleMessage) |> ignore


        Automation.Worker.Metrics.start (fun () ->
            $"success_total {successCount}\n" +
            $"failure_total {failureCount}\n" +
            $"retry_total {retryCount}\n" +
            $"dlq_total {dlqCount}\n")
        info "Worker is running. Press Ctrl+C to exit."
        // Keep the application running to listen for events
        Thread.Sleep(Timeout.Infinite)

    with
    | ex -> 
        error (sprintf "An unexpected error occurred: %s" ex.Message)
        error (sprintf "Stack Trace: %s" ex.StackTrace)

main ()