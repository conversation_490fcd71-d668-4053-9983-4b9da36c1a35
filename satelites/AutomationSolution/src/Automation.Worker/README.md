# Automation.Worker

This project implements a robust, long-running background worker service responsible for processing automation tasks. It acts as the execution engine of the Automation Solution, receiving tasks from a Redis queue, processing them, and handling various operational concerns like concurrency, retries, circuit breaking, and metrics reporting.

## Features

*   **Task Processing Pipeline**: Receives automation tasks from a Redis queue, processes them using AI capabilities (via `Automation.AI`), and executes the resulting actions using appropriate executors (`Automation.Web` or `Automation.Mobile`).
*   **Concurrency Control**: Manages the number of simultaneous tasks being processed using a semaphore, preventing resource exhaustion and ensuring stable operation.
*   **Retry Mechanism**: Implements an exponential backoff retry strategy for failed task executions, increasing resilience against transient errors.
*   **Circuit Breaker Pattern**: Incorporates a circuit breaker to automatically detect and prevent repeated failures to a downstream service or operation. When a failure threshold is met, the circuit opens, routing tasks to a Dead Letter Queue (DLQ) to prevent cascading failures and allow the system to recover.
*   **Dead Letter Queue (DLQ)**: Unprocessable or persistently failing tasks are moved to a DLQ for later analysis and reprocessing, ensuring no tasks are lost.
*   **Security Validation**: Integrates with `Automation.Core.SecurityEngine` to validate automation actions before execution, preventing potentially malicious or unintended operations.
*   **Dynamic Executor Selection**: Selects the appropriate task executor (web or mobile) based on metadata within the incoming task payload.
*   **Health Monitoring & Metrics**: Exposes health endpoints and Prometheus-compatible metrics (success/failure counts, retries, DLQ additions) for real-time monitoring and observability.
*   **Graceful Shutdown**: Designed to handle shutdown signals gracefully, allowing in-progress tasks to complete before termination (though explicit graceful shutdown logic is not fully detailed in `Program.fs`, it's a common pattern for such services).

## Components

*   **`Program.fs`**: The main entry point of the worker service. It sets up the Redis connection, subscribes to the task channel, and orchestrates the task processing flow, including concurrency, retry logic, and circuit breaker implementation.
*   **`Config` Module**: Defines configurable parameters for the worker, such as concurrency limits, retry settings, and circuit breaker thresholds, typically loaded from environment variables.
*   **`Metrics.fs`**: Sets up a simple HTTP server to expose health checks (`/health`) and Prometheus-compatible metrics (`/metrics`), providing insights into the worker's operational status.

## Processing Flow

1.  **Subscription**: The worker subscribes to a Redis channel (e.g., `automation_channel`) to receive incoming automation tasks.
2.  **Concurrency Management**: Each incoming task attempts to acquire a semaphore. If the concurrency limit is reached, tasks wait.
3.  **AI Processing**: The task command is sent to the `Automation.AI` module to generate a list of executable actions.
4.  **Security Validation**: The generated actions are validated against a security policy to ensure they are safe to execute.
5.  **Executor Selection**: Based on the task's metadata (e.g., `target: "mobile"`), the worker selects either the `Automation.Web.WebTaskExecutor` or `Automation.Mobile.MobileTaskExecutor`.
6.  **Action Execution**: The selected executor executes the automation actions.
7.  **Retry & Circuit Breaking**:
    *   If an execution fails, the worker attempts retries with exponential backoff.
    *   If a predefined number of failures occur within a time window, the circuit breaker "opens," preventing further executions and routing tasks directly to the DLQ.
    *   Upon successful execution, the circuit breaker resets.
8.  **Metrics & Logging**: Successes, failures, retries, and DLQ additions are tracked as metrics and logged for observability.

## Usage

The `Automation.Worker` is designed to be deployed as a scalable background service. Multiple instances of the worker can run concurrently, processing tasks from the shared Redis queue.

### Running the Worker

To start the worker service:

```bash
dotnet run --project src/Automation.Worker
```

### Configuration

The worker's behavior is highly configurable via environment variables:

*   `REDIS_CONNECTION_STRING`: Connection string for the Redis server (default: `localhost`).
*   `CONCURRENCY_LIMIT`: Maximum number of concurrent tasks a single worker instance can process (default: `3`).
*   `MAX_RETRIES`: Maximum number of times a failed task will be retried (default: `3`).
*   `BASE_BACKOFF_MS`: Base delay in milliseconds for exponential backoff retries (default: `500`).
*   `FAILURE_THRESHOLD`: Number of failures within `FAILURE_WINDOW_SEC` to open the circuit (default: `5`).
*   `FAILURE_WINDOW_SEC`: Time window in seconds for counting failures for the circuit breaker (default: `60`).
*   `CIRCUIT_OPEN_SEC`: Duration in seconds the circuit remains open before transitioning to half-open (default: `60`).
*   `METRICS_PORT`: Port on which health and metrics endpoints are exposed (default: `8080`).
*   `WORKER_ID`: Unique identifier for the worker instance (can be set by container orchestrator, e.g., `HOSTNAME`).
*   `MAX_CAPACITY`: Maximum capacity of the worker (used by `Automation.AI.LoadBalancer`).
*   `HEARTBEAT_INTERVAL`: Interval for sending heartbeats to the coordinator.

## Monitoring

The worker exposes standard endpoints for monitoring:

*   **Health Check**: `GET /health` (e.g., `http://localhost:8080/health`) - Returns "OK" if the service is running.
*   **Prometheus Metrics**: `GET /metrics` (e.g., `http://localhost:8080/metrics`) - Provides Prometheus-compatible metrics for success/failure rates, retries, and DLQ.

Example metrics output:

```
success_total 1234
failure_total 5
retry_total 15
dlq_total 2
```

## Dependencies

*   `StackExchange.Redis`: For Redis queue interaction.
*   `Automation.Core`: Provides core types, `TaskDispatcher`, and `SecurityEngine`.
*   `Automation.AI`: Used for AI-driven action generation.
*   `Automation.Web`: Provides web automation capabilities.
*   `Automation.Mobile`: Provides mobile automation capabilities.
*   `Automation.Utilities`: For logging.
*   `Microsoft.AspNetCore.App`: For exposing HTTP endpoints for metrics and health checks.

## Development

This project is developed in F#. It is crucial for the overall stability and performance of the Automation Solution. When making changes, pay close attention to concurrency, error handling, and resource management.
