<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="Metrics.fs" />
    <Compile Include="Program.fs" />
  </ItemGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.41" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Automation.Core\Automation.Core.fsproj" />
    <ProjectReference Include="..\Automation.Web\Automation.Web.fsproj" />
    <ProjectReference Include="..\Automation.AI\Automation.AI.fsproj" />
    <ProjectReference Include="..\Automation.Utilities\Automation.Utilities.fsproj" />
    <ProjectReference Include="..\Automation.Data\Automation.Data.fsproj" />
    <ProjectReference Include="..\Automation.Mobile\Automation.Mobile.fsproj" />
  </ItemGroup>

</Project>
