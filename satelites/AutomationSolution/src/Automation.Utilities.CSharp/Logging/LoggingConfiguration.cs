using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Events;
using Serilog.Formatting.Compact;

namespace Automation.Utilities.Logging;

/// <summary>
/// Configuration options for logging
/// </summary>
public class LoggingOptions
{
    public string LogLevel { get; set; } = "Information";
    public string LogDirectory { get; set; } = "logs";
    public bool EnableConsoleLogging { get; set; } = true;
    public bool EnableFileLogging { get; set; } = true;
    public bool UseCompactFormat { get; set; } = false;
    public int RetainedFileCountLimit { get; set; } = 31;
    public long? FileSizeLimitBytes { get; set; } = 1024 * 1024 * 100; // 100MB
    public string OutputTemplate { get; set; } = "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] {Message:lj}{NewLine}{Exception}";
}

/// <summary>
/// Extension methods for configuring Serilog logging
/// </summary>
public static class LoggingConfigurationExtensions
{
    /// <summary>
    /// Adds Serilog logging with default configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddSerilogLogging(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        return services.AddSerilogLogging(configuration, options => { });
    }

    /// <summary>
    /// Adds Serilog logging with custom configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <param name="configureOptions">Action to configure logging options</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddSerilogLogging(
        this IServiceCollection services,
        IConfiguration configuration,
        Action<LoggingOptions> configureOptions)
    {
        var options = new LoggingOptions();
        configuration.GetSection("Logging").Bind(options);
        configureOptions(options);

        // Configure Serilog
        var loggerConfiguration = new LoggerConfiguration()
            .MinimumLevel.Is(ParseLogLevel(options.LogLevel))
            .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
            .MinimumLevel.Override("System", LogEventLevel.Warning)
            .Enrich.FromLogContext()
            .Enrich.WithProperty("Application", "AutomationSolution");

        // Add console sink if enabled
        if (options.EnableConsoleLogging)
        {
            if (options.UseCompactFormat)
            {
                loggerConfiguration.WriteTo.Console(new CompactJsonFormatter());
            }
            else
            {
                loggerConfiguration.WriteTo.Console(outputTemplate: options.OutputTemplate);
            }
        }

        // Add file sink if enabled
        if (options.EnableFileLogging)
        {
            var logFilePath = Path.Combine(options.LogDirectory, "automation-.log");
            
            if (options.UseCompactFormat)
            {
                loggerConfiguration.WriteTo.File(
                    new CompactJsonFormatter(),
                    logFilePath,
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: options.RetainedFileCountLimit,
                    fileSizeLimitBytes: options.FileSizeLimitBytes);
            }
            else
            {
                loggerConfiguration.WriteTo.File(
                    logFilePath,
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: options.RetainedFileCountLimit,
                    fileSizeLimitBytes: options.FileSizeLimitBytes,
                    outputTemplate: options.OutputTemplate);
            }
        }

        // Create and configure the logger
        Log.Logger = loggerConfiguration.CreateLogger();

        // Add Serilog to the service collection
        services.AddLogging(builder =>
        {
            builder.ClearProviders();
            builder.AddSerilog(dispose: true);
        });

        // Register logging options
        services.AddSingleton(options);

        return services;
    }

    /// <summary>
    /// Parses log level string to Serilog LogEventLevel
    /// </summary>
    /// <param name="logLevel">Log level string</param>
    /// <returns>Serilog LogEventLevel</returns>
    private static LogEventLevel ParseLogLevel(string logLevel)
    {
        return logLevel.ToLowerInvariant() switch
        {
            "verbose" => LogEventLevel.Verbose,
            "debug" => LogEventLevel.Debug,
            "information" => LogEventLevel.Information,
            "warning" => LogEventLevel.Warning,
            "error" => LogEventLevel.Error,
            "fatal" => LogEventLevel.Fatal,
            _ => LogEventLevel.Information
        };
    }
}

/// <summary>
/// Static logger factory for F# interop
/// </summary>
public static class LoggerFactory
{
    private static ILoggerFactory? _loggerFactory;

    /// <summary>
    /// Initializes the logger factory
    /// </summary>
    /// <param name="loggerFactory">The logger factory instance</param>
    public static void Initialize(ILoggerFactory loggerFactory)
    {
        _loggerFactory = loggerFactory;
    }

    /// <summary>
    /// Creates a logger for the specified category
    /// </summary>
    /// <param name="categoryName">The category name</param>
    /// <returns>Logger instance</returns>
    public static Microsoft.Extensions.Logging.ILogger CreateLogger(string categoryName)
    {
        if (_loggerFactory == null)
        {
            throw new InvalidOperationException("LoggerFactory has not been initialized. Call Initialize() first.");
        }

        return _loggerFactory.CreateLogger(categoryName);
    }

    /// <summary>
    /// Creates a logger for the specified type
    /// </summary>
    /// <typeparam name="T">The type to create logger for</typeparam>
    /// <returns>Logger instance</returns>
    public static ILogger<T> CreateLogger<T>()
    {
        if (_loggerFactory == null)
        {
            throw new InvalidOperationException("LoggerFactory has not been initialized. Call Initialize() first.");
        }

        return _loggerFactory.CreateLogger<T>();
    }
}
