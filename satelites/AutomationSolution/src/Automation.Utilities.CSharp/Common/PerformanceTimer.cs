using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace Automation.Utilities.Common;

/// <summary>
/// Performance timer for measuring operation execution time
/// </summary>
public class PerformanceTimer : IDisposable
{
    private readonly Stopwatch _stopwatch;
    private readonly ILogger _logger;
    private readonly string _operationName;
    private readonly LogLevel _logLevel;
    private bool _disposed = false;

    public PerformanceTimer(ILogger logger, string operationName, LogLevel logLevel = LogLevel.Debug)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _operationName = operationName ?? throw new ArgumentNullException(nameof(operationName));
        _logLevel = logLevel;
        _stopwatch = Stopwatch.StartNew();

        _logger.Log(_logLevel, "Started operation: {OperationName}", _operationName);
    }

    /// <summary>
    /// Gets the elapsed time since the timer was started
    /// </summary>
    public TimeSpan Elapsed => _stopwatch.Elapsed;

    /// <summary>
    /// Gets whether the timer is currently running
    /// </summary>
    public bool IsRunning => _stopwatch.IsRunning;

    /// <summary>
    /// Stops the timer and logs the elapsed time
    /// </summary>
    /// <returns>The elapsed time</returns>
    public TimeSpan Stop()
    {
        if (_stopwatch.IsRunning)
        {
            _stopwatch.Stop();
            _logger.Log(_logLevel, "Completed operation: {OperationName} in {ElapsedMs}ms", 
                _operationName, _stopwatch.ElapsedMilliseconds);
        }

        return _stopwatch.Elapsed;
    }

    /// <summary>
    /// Restarts the timer
    /// </summary>
    public void Restart()
    {
        _stopwatch.Restart();
        _logger.Log(_logLevel, "Restarted operation: {OperationName}", _operationName);
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            Stop();
            _disposed = true;
        }
    }
}

/// <summary>
/// Static helper for creating performance timers
/// </summary>
public static class PerformanceTimerExtensions
{
    /// <summary>
    /// Creates a performance timer for the logger
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <param name="operationName">Name of the operation being timed</param>
    /// <param name="logLevel">Log level for timing messages</param>
    /// <returns>Performance timer instance</returns>
    public static PerformanceTimer StartTimer(this ILogger logger, string operationName, LogLevel logLevel = LogLevel.Debug)
    {
        return new PerformanceTimer(logger, operationName, logLevel);
    }

    /// <summary>
    /// Executes an action and measures its performance
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <param name="operationName">Name of the operation</param>
    /// <param name="action">Action to execute</param>
    /// <param name="logLevel">Log level for timing messages</param>
    /// <returns>Elapsed time</returns>
    public static TimeSpan MeasureTime(this ILogger logger, string operationName, Action action, LogLevel logLevel = LogLevel.Debug)
    {
        using var timer = logger.StartTimer(operationName, logLevel);
        action();
        return timer.Elapsed;
    }

    /// <summary>
    /// Executes an async action and measures its performance
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <param name="operationName">Name of the operation</param>
    /// <param name="action">Async action to execute</param>
    /// <param name="logLevel">Log level for timing messages</param>
    /// <returns>Task with elapsed time</returns>
    public static async Task<TimeSpan> MeasureTimeAsync(this ILogger logger, string operationName, Func<Task> action, LogLevel logLevel = LogLevel.Debug)
    {
        using var timer = logger.StartTimer(operationName, logLevel);
        await action();
        return timer.Elapsed;
    }

    /// <summary>
    /// Executes a function and measures its performance
    /// </summary>
    /// <typeparam name="T">Return type</typeparam>
    /// <param name="logger">Logger instance</param>
    /// <param name="operationName">Name of the operation</param>
    /// <param name="func">Function to execute</param>
    /// <param name="logLevel">Log level for timing messages</param>
    /// <returns>Tuple with result and elapsed time</returns>
    public static (T Result, TimeSpan Elapsed) MeasureTime<T>(this ILogger logger, string operationName, Func<T> func, LogLevel logLevel = LogLevel.Debug)
    {
        using var timer = logger.StartTimer(operationName, logLevel);
        var result = func();
        return (result, timer.Elapsed);
    }

    /// <summary>
    /// Executes an async function and measures its performance
    /// </summary>
    /// <typeparam name="T">Return type</typeparam>
    /// <param name="logger">Logger instance</param>
    /// <param name="operationName">Name of the operation</param>
    /// <param name="func">Async function to execute</param>
    /// <param name="logLevel">Log level for timing messages</param>
    /// <returns>Task with tuple containing result and elapsed time</returns>
    public static async Task<(T Result, TimeSpan Elapsed)> MeasureTimeAsync<T>(this ILogger logger, string operationName, Func<Task<T>> func, LogLevel logLevel = LogLevel.Debug)
    {
        using var timer = logger.StartTimer(operationName, logLevel);
        var result = await func();
        return (result, timer.Elapsed);
    }
}
