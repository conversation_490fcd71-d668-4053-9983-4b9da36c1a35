using Microsoft.Extensions.Logging;

namespace Automation.Utilities.Common;

/// <summary>
/// Configuration for retry policy
/// </summary>
public class RetryPolicyOptions
{
    public int MaxAttempts { get; set; } = 3;
    public TimeSpan InitialDelay { get; set; } = TimeSpan.FromSeconds(1);
    public TimeSpan MaxDelay { get; set; } = TimeSpan.FromSeconds(30);
    public double BackoffMultiplier { get; set; } = 2.0;
    public bool UseJitter { get; set; } = true;
}

/// <summary>
/// Retry policy implementation with exponential backoff
/// </summary>
public class RetryPolicy
{
    private readonly RetryPolicyOptions _options;
    private readonly ILogger<RetryPolicy> _logger;
    private readonly Random _random = new();

    public RetryPolicy(RetryPolicyOptions options, ILogger<RetryPolicy> logger)
    {
        _options = options ?? throw new ArgumentNullException(nameof(options));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Executes an action with retry policy
    /// </summary>
    /// <param name="action">The action to execute</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the operation</returns>
    public async Task ExecuteAsync(Func<Task> action, CancellationToken cancellationToken = default)
    {
        await ExecuteAsync(async () =>
        {
            await action();
            return true;
        }, cancellationToken);
    }

    /// <summary>
    /// Executes a function with retry policy
    /// </summary>
    /// <typeparam name="T">Return type</typeparam>
    /// <param name="func">The function to execute</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the operation with result</returns>
    public async Task<T> ExecuteAsync<T>(Func<Task<T>> func, CancellationToken cancellationToken = default)
    {
        var attempt = 0;
        var delay = _options.InitialDelay;

        while (true)
        {
            attempt++;
            cancellationToken.ThrowIfCancellationRequested();

            try
            {
                _logger.LogDebug("Executing operation, attempt {Attempt}/{MaxAttempts}", attempt, _options.MaxAttempts);
                return await func();
            }
            catch (Exception ex) when (attempt < _options.MaxAttempts)
            {
                _logger.LogWarning(ex, "Operation failed on attempt {Attempt}/{MaxAttempts}, retrying in {Delay}ms", 
                    attempt, _options.MaxAttempts, delay.TotalMilliseconds);

                await Task.Delay(delay, cancellationToken);

                // Calculate next delay with exponential backoff
                delay = CalculateNextDelay(delay);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Operation failed after {MaxAttempts} attempts", _options.MaxAttempts);
                throw;
            }
        }
    }

    /// <summary>
    /// Calculates the next delay using exponential backoff with optional jitter
    /// </summary>
    /// <param name="currentDelay">Current delay</param>
    /// <returns>Next delay</returns>
    private TimeSpan CalculateNextDelay(TimeSpan currentDelay)
    {
        var nextDelay = TimeSpan.FromMilliseconds(currentDelay.TotalMilliseconds * _options.BackoffMultiplier);
        
        // Apply maximum delay limit
        if (nextDelay > _options.MaxDelay)
        {
            nextDelay = _options.MaxDelay;
        }

        // Apply jitter to avoid thundering herd
        if (_options.UseJitter)
        {
            var jitterRange = nextDelay.TotalMilliseconds * 0.1; // 10% jitter
            var jitter = (_random.NextDouble() - 0.5) * 2 * jitterRange;
            nextDelay = TimeSpan.FromMilliseconds(Math.Max(0, nextDelay.TotalMilliseconds + jitter));
        }

        return nextDelay;
    }
}

/// <summary>
/// Static helper for creating retry policies
/// </summary>
public static class Retry
{
    /// <summary>
    /// Creates a default retry policy
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <returns>Retry policy instance</returns>
    public static RetryPolicy Default(ILogger<RetryPolicy> logger)
    {
        return new RetryPolicy(new RetryPolicyOptions(), logger);
    }

    /// <summary>
    /// Creates a retry policy with custom options
    /// </summary>
    /// <param name="options">Retry policy options</param>
    /// <param name="logger">Logger instance</param>
    /// <returns>Retry policy instance</returns>
    public static RetryPolicy WithOptions(RetryPolicyOptions options, ILogger<RetryPolicy> logger)
    {
        return new RetryPolicy(options, logger);
    }

    /// <summary>
    /// Creates a retry policy with specified max attempts
    /// </summary>
    /// <param name="maxAttempts">Maximum number of attempts</param>
    /// <param name="logger">Logger instance</param>
    /// <returns>Retry policy instance</returns>
    public static RetryPolicy WithMaxAttempts(int maxAttempts, ILogger<RetryPolicy> logger)
    {
        var options = new RetryPolicyOptions { MaxAttempts = maxAttempts };
        return new RetryPolicy(options, logger);
    }
}
