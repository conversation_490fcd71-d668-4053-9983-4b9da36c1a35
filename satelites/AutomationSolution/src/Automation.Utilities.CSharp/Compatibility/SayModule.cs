using Microsoft.Extensions.Logging;

namespace Automation.Utilities.Compatibility;

/// <summary>
/// C# equivalent of the F# Say module for backward compatibility
/// </summary>
public static class Say
{
    private static ILogger? _logger;

    /// <summary>
    /// Initializes the Say module with a logger
    /// </summary>
    /// <param name="logger">Logger instance</param>
    public static void Initialize(ILogger logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Says hello to the specified name (equivalent to F# Say.hello)
    /// </summary>
    /// <param name="name">Name to greet</param>
    public static void Hello(string name)
    {
        var message = $"Hello {name}";
        
        if (_logger != null)
        {
            _logger.LogInformation(message);
        }
        else
        {
            // Fallback to console if logger not initialized
            Console.WriteLine($"[INFO] {message}");
        }
    }

    /// <summary>
    /// Says goodbye to the specified name
    /// </summary>
    /// <param name="name">Name to say goodbye to</param>
    public static void Goodbye(string name)
    {
        var message = $"Goodbye {name}";
        
        if (_logger != null)
        {
            _logger.LogInformation(message);
        }
        else
        {
            // Fallback to console if logger not initialized
            Console.WriteLine($"[INFO] {message}");
        }
    }

    /// <summary>
    /// Logs a custom message
    /// </summary>
    /// <param name="message">Message to log</param>
    public static void Message(string message)
    {
        if (_logger != null)
        {
            _logger.LogInformation(message);
        }
        else
        {
            // Fallback to console if logger not initialized
            Console.WriteLine($"[INFO] {message}");
        }
    }
}
