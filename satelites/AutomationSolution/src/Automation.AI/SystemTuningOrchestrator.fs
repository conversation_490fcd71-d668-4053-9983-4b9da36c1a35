namespace Automation.AI

open System
open System.Threading.Tasks
open Microsoft.Extensions.Logging
open Microsoft.Extensions.DependencyInjection
open Microsoft.Extensions.Hosting

/// System tuning orchestrator and demo
module SystemTuningOrchestrator =

    type TuningProfile = 
        | Development
        | Testing
        | Production
        | HighPerformance

    type TuningSession = {
        Id: Guid
        Profile: TuningProfile
        StartTime: DateTimeOffset
        EndTime: DateTimeOffset option
        Results: SystemTuning.OptimizationResult option
        Status: string
    }

    /// Orchestrates system performance tuning sessions
    type SystemTuningOrchestrator(
        tuner: SystemTuning.SystemPerformanceTuner,
        logger: ILogger<SystemTuningOrchestrator>
    ) as this =
        
        let mutable activeSessions = Map.empty<Guid, TuningSession>

        /// Get tuning configuration for a profile
        member private this.GetConfigurationForProfile(profile: TuningProfile) : SystemTuning.TuningConfiguration =
            match profile with
            | Development -> {
                EnabledOptimizations = Set.ofList [
                    "ConnectionPooling"
                    "RequestCaching"
                    "MemoryOptimization"
                ]
                ConnectionPoolSize = 10
                CacheSize = 1000
                PrewarmConnections = false
                EnableCompression = false
                OptimizeGarbageCollection = false
                DatabaseConnectionTimeout = 15
                MaxRequestQueueSize = 100
                CircuitBreakerThreshold = 3
                RetryAttempts = 2
            }
            | Testing -> {
                EnabledOptimizations = Set.ofList [
                    "ConnectionPooling"
                    "RequestCaching"
                    "ResponseCompression"
                    "MemoryOptimization"
                    "CircuitBreaker"
                ]
                ConnectionPoolSize = 15
                CacheSize = 5000
                PrewarmConnections = true
                EnableCompression = true
                OptimizeGarbageCollection = true
                DatabaseConnectionTimeout = 20
                MaxRequestQueueSize = 500
                CircuitBreakerThreshold = 4
                RetryAttempts = 3
            }
            | Production -> {
                EnabledOptimizations = Set.ofList [
                    "ConnectionPooling"
                    "RequestCaching"
                    "ResponseCompression"
                    "DatabaseOptimization"
                    "MemoryOptimization"
                    "GarbageCollectionTuning"
                    "LoadBalancing"
                    "CircuitBreaker"
                ]
                ConnectionPoolSize = 20
                CacheSize = 10000
                PrewarmConnections = true
                EnableCompression = true
                OptimizeGarbageCollection = true
                DatabaseConnectionTimeout = 30
                MaxRequestQueueSize = 1000
                CircuitBreakerThreshold = 5
                RetryAttempts = 3
            }
            | HighPerformance -> {
                EnabledOptimizations = Set.ofList [
                    "ConnectionPooling"
                    "RequestCaching"
                    "ResponseCompression"
                    "DatabaseOptimization"
                    "MemoryOptimization"
                    "GarbageCollectionTuning"
                    "LoadBalancing"
                    "CircuitBreaker"
                ]
                ConnectionPoolSize = 50
                CacheSize = 50000
                PrewarmConnections = true
                EnableCompression = true
                OptimizeGarbageCollection = true
                DatabaseConnectionTimeout = 45
                MaxRequestQueueSize = 5000
                CircuitBreakerThreshold = 10
                RetryAttempts = 5
            }

        /// Start a tuning session
        member this.StartTuningSession(profile: TuningProfile) : Task<Guid> =
            task {
                let sessionId = Guid.NewGuid()
                let session = {
                    Id = sessionId
                    Profile = profile
                    StartTime = DateTimeOffset.UtcNow
                    EndTime = None
                    Results = None
                    Status = "Running"
                }

                activeSessions <- activeSessions.Add(sessionId, session)
                
                logger.LogInformation($"Started tuning session {sessionId} with profile {profile}")
                
                return sessionId
            }

        /// Execute comprehensive tuning
        member this.ExecuteComprehensiveTuning(profile: TuningProfile) : Task<SystemTuning.OptimizationResult> =
            task {
                let! sessionId = this.StartTuningSession(profile)
                
                try
                    logger.LogInformation($"Executing comprehensive tuning with {profile} profile...")
                    
                    let config = this.GetConfigurationForProfile(profile)
                    let! result = tuner.PerformComprehensiveTuning(Some config)
                    
                    // Update session with results
                    let updatedSession = {
                        activeSessions.[sessionId] with
                            EndTime = Some DateTimeOffset.UtcNow
                            Results = Some result
                            Status = if result.TargetsAchieved then "Completed Successfully" else "Completed with Issues"
                    }
                    
                    activeSessions <- activeSessions.Add(sessionId, updatedSession)
                    
                    logger.LogInformation($"Tuning session {sessionId} completed. Targets achieved: {result.TargetsAchieved}")
                    
                    return result
                    
                with
                | ex ->
                    logger.LogError(ex, $"Error during tuning session {sessionId}")
                    
                    let failedSession = {
                        activeSessions.[sessionId] with
                            EndTime = Some DateTimeOffset.UtcNow
                            Status = $"Failed: {ex.Message}"
                    }
                    
                    activeSessions <- activeSessions.Add(sessionId, failedSession)
                    
                    reraise()
            }

        /// Get session status
        member this.GetSessionStatus(sessionId: Guid) : TuningSession option =
            activeSessions.TryFind(sessionId)

        /// Get all sessions
        member this.GetAllSessions() : TuningSession list =
            activeSessions |> Map.values |> Seq.toList

        /// Generate comprehensive tuning report
        member this.GenerateTuningReport() : string =
            let currentMetrics = tuner.GetCurrentMetrics()
            let (targetsAchieved, issues) = tuner.ValidatePerformanceTargets()
            let recentSessions = this.GetAllSessions() |> List.sortByDescending (fun s -> s.StartTime) |> List.take (min 5 (List.length (this.GetAllSessions())))
            
            sprintf """
=== COMPREHENSIVE SYSTEM TUNING REPORT ===
Generated: %s

CURRENT SYSTEM STATUS:
%s

RECENT TUNING SESSIONS:
%s

PERFORMANCE OPTIMIZATION SUMMARY:
- Memory Optimization: ✓ Implemented
- Connection Pooling: ✓ Configured  
- Request Caching: ✓ Active
- Load Balancing: ✓ Enabled
- Circuit Breaker: ✓ Configured
- Cost Optimization: ✓ AI Provider Selection
- Horizontal Scaling: ✓ Auto-scaling Enabled

SYSTEM HEALTH:
- Performance Targets: %s
- Current Throughput: %.1f RPS
- Average Response Time: %.0f ms
- Memory Usage: %d MB
- Error Rate: %.2f%%

RECOMMENDATIONS:
%s
            """
                (DateTimeOffset.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"))
                (tuner.GeneratePerformanceReport())
                (recentSessions 
                 |> List.map (fun s -> 
                     sprintf "- %s: %s (Profile: %A, Duration: %s)" 
                         (s.Id.ToString().Substring(0, 8))
                         s.Status
                         s.Profile
                         (match s.EndTime with
                          | Some endTime -> sprintf "%.1fs" (endTime - s.StartTime).TotalSeconds
                          | None -> "Running..."))
                 |> String.concat "\n")
                (if targetsAchieved then "✅ ALL TARGETS MET" else "⚠️ NEEDS ATTENTION")
                currentMetrics.ThroughputRPS
                (float currentMetrics.AverageResponseTime)
                currentMetrics.MemoryUsageMB
                (currentMetrics.ErrorRate * 100.0)
                (if targetsAchieved then 
                    "System is performing optimally. Continue monitoring and maintain current configuration."
                 else 
                    "Apply recommended optimizations:\n" + (issues |> String.concat "\n"))

    /// Demo program for system tuning
    type SystemTuningDemo() =
        
        /// Run interactive tuning demo
        static member RunInteractiveDemo(serviceProvider: IServiceProvider) : Task<unit> =
            task {
                let logger = serviceProvider.GetService<ILogger<SystemTuningDemo>>()
                let tuner = serviceProvider.GetService<SystemTuning.SystemPerformanceTuner>()
                let orchestrator = SystemTuningOrchestrator(tuner, serviceProvider.GetService<ILogger<SystemTuningOrchestrator>>())
                
                logger.LogInformation("=== System Performance Tuning Demo ===")
                logger.LogInformation("Starting comprehensive system optimization...")
                
                // Measure baseline performance
                logger.LogInformation("Measuring baseline performance...")
                let baselineMetrics = tuner.GetCurrentMetrics()
                logger.LogInformation($"Baseline - Response Time: {float baselineMetrics.AverageResponseTime}ms, Memory: {baselineMetrics.MemoryUsageMB}MB")
                
                // Run tuning for each profile
                let profiles = [Development; Testing; Production; HighPerformance]
                
                for profile in profiles do
                    logger.LogInformation($"\n--- Tuning with {profile} Profile ---")
                    
                    try
                        let! result = orchestrator.ExecuteComprehensiveTuning(profile)
                        
                        logger.LogInformation($"Profile: {profile}")
                        logger.LogInformation($"Improvement: {result.ImprovementPercentage:F2}%")
                        logger.LogInformation($"Targets Achieved: {result.TargetsAchieved}")
                        logger.LogInformation($"Response Time: {float result.AfterMetrics.AverageResponseTime}ms")
                        logger.LogInformation($"Memory Usage: {result.AfterMetrics.MemoryUsageMB}MB")
                        
                        if not (List.isEmpty result.Recommendations) then
                            logger.LogInformation("Recommendations:")
                            for recommendation in result.Recommendations do
                                logger.LogInformation($"  - {recommendation}")
                                
                    with
                    | ex -> 
                        logger.LogError(ex, $"Failed to complete tuning with {profile} profile")
                
                // Generate final report
                logger.LogInformation("\n=== FINAL TUNING REPORT ===")
                let finalReport = orchestrator.GenerateTuningReport()
                logger.LogInformation(finalReport)
                
                // Validate final performance
                let (targetsAchieved, issues) = tuner.ValidatePerformanceTargets()
                if targetsAchieved then
                    logger.LogInformation("🎉 All performance targets achieved!")
                    logger.LogInformation("✅ Simple commands: <2s")
                    logger.LogInformation("✅ Complex workflows: <10s")
                    logger.LogInformation("✅ Memory usage: <512MB")
                else
                    logger.LogWarning("⚠️ Some performance targets not met:")
                    for issue in issues do
                        logger.LogWarning($"  - {issue}")
                
                logger.LogInformation("Demo completed successfully!")
            }

        /// Run automated tuning validation
        static member RunValidationSuite(serviceProvider: IServiceProvider) : Task<bool> =
            task {
                let logger = serviceProvider.GetService<ILogger<SystemTuningDemo>>()
                let tuner = serviceProvider.GetService<SystemTuning.SystemPerformanceTuner>()
                
                logger.LogInformation("=== System Tuning Validation Suite ===")
                
                // Test simple command tuning
                logger.LogInformation("Testing simple command optimization...")
                let! simpleResult = tuner.TuneForSimpleCommands(None)
                let simpleTargetsMet = simpleResult.TargetsAchieved
                
                logger.LogInformation($"Simple commands - Targets met: {simpleTargetsMet}")
                logger.LogInformation($"Response time: {float simpleResult.AfterMetrics.AverageResponseTime}ms")
                
                // Test complex workflow tuning
                logger.LogInformation("Testing complex workflow optimization...")
                let! complexResult = tuner.TuneForComplexWorkflows(None)
                let complexTargetsMet = complexResult.TargetsAchieved
                
                logger.LogInformation($"Complex workflows - Targets met: {complexTargetsMet}")
                logger.LogInformation($"P95 response time: {float complexResult.AfterMetrics.P95ResponseTime}ms")
                
                // Overall validation
                let overallSuccess = simpleTargetsMet && complexTargetsMet
                
                if overallSuccess then
                    logger.LogInformation("✅ Validation suite passed - System meets performance targets")
                else
                    logger.LogWarning("❌ Validation suite failed - System needs additional tuning")
                
                return overallSuccess
            }

/// Console application entry point for system tuning
module SystemTuningProgram =
    
    [<EntryPoint>]
    let main args =
        async {
            try
                // Setup DI container
                let services = ServiceCollection()
                services.AddLogging(fun builder ->
                    builder.AddConsole() |> ignore
                    builder.SetMinimumLevel(LogLevel.Information) |> ignore
                ) |> ignore
                
                // Register services (would typically be configured elsewhere)
                // Note: In a real application, these would be properly configured
                services.AddSingleton<SystemTuning.SystemPerformanceTuner>(fun sp ->
                    let logger = sp.GetService<ILogger<SystemTuning.SystemPerformanceTuner>>()
                    // Note: These would be actual configured instances
                    let costOptimizer = null // CostOptimizer instance
                    let cacheOptimizer = null // CacheOptimizer instance  
                    let resourcePool = null // ResourcePoolManager instance
                    let loadBalancer = null // LoadBalancer instance
                    let redis = None // Redis connection
                    
                    SystemTuning.SystemPerformanceTuner(logger, costOptimizer, cacheOptimizer, resourcePool, loadBalancer, redis)
                ) |> ignore
                
                let serviceProvider = services.BuildServiceProvider()
                
                // Parse command line arguments
                match args with
                | [| "demo" |] ->
                    do! SystemTuningOrchestrator.SystemTuningDemo.RunInteractiveDemo(serviceProvider) |> Async.AwaitTask
                    return 0
                    
                | [| "validate" |] ->
                    let! success = SystemTuningOrchestrator.SystemTuningDemo.RunValidationSuite(serviceProvider) |> Async.AwaitTask
                    return if success then 0 else 1
                    
                | _ ->
                    printfn "Usage:"
                    printfn "  SystemTuning demo     - Run interactive tuning demo"
                    printfn "  SystemTuning validate - Run validation suite"
                    return 1
                    
            with
            | ex ->
                printfn $"Error: {ex.Message}"
                return 1
        } |> Async.RunSynchronously
