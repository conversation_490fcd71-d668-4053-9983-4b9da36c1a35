namespace Automation.AI

open System
open System.Collections.Concurrent
open System.Diagnostics
open System.Threading.Tasks
open Microsoft.Extensions.Logging
open StackExchange.Redis

/// System performance tuning and optimization module
/// Integrates all optimization components for <2s simple commands and <10s complex workflows
module SystemTuning =

    type PerformanceTarget = {
        SimpleCommandTarget: float<milliseconds>
        ComplexWorkflowTarget: float<milliseconds>
        MaxConcurrentRequests: int
        MaxMemoryUsageMB: int
        TargetThroughputRPS: float
        CacheHitRateTarget: float
    }

    type TuningConfiguration = {
        EnabledOptimizations: Set<string>
        ConnectionPoolSize: int
        CacheSize: int
        PrewarmConnections: bool
        EnableCompression: bool
        OptimizeGarbageCollection: bool
        DatabaseConnectionTimeout: int
        MaxRequestQueueSize: int
        CircuitBreakerThreshold: int
        RetryAttempts: int
    }

    type SystemMetrics = {
        AverageResponseTime: float<milliseconds>
        ThroughputRPS: float
        CacheHitRate: float
        MemoryUsageMB: int
        ConcurrentRequests: int
        ErrorRate: float
        P95ResponseTime: float<milliseconds>
        P99ResponseTime: float<milliseconds>
        GCPressure: float
    }

    type OptimizationResult = {
        BeforeMetrics: SystemMetrics
        AfterMetrics: SystemMetrics
        ImprovementPercentage: float
        TargetsAchieved: bool
        Recommendations: string list
    }

    [<Measure>]
    type milliseconds

    [<Measure>]
    type seconds

    /// Comprehensive system performance tuner
    type SystemPerformanceTuner(
        logger: ILogger<SystemPerformanceTuner>,
        costOptimizer: CostOptimizer.CostOptimizer,
        cacheOptimizer: CacheOptimizer.CacheOptimizer,
        resourcePool: ResourcePoolManager.ResourcePoolManager,
        loadBalancer: LoadBalancer.LoadBalancer,
        redis: IDatabase option
    ) =
        
        let performanceTargets = {
            SimpleCommandTarget = 2000.0<milliseconds>
            ComplexWorkflowTarget = 10000.0<milliseconds>
            MaxConcurrentRequests = 100
            MaxMemoryUsageMB = 512
            TargetThroughputRPS = 50.0
            CacheHitRateTarget = 0.85
        }

        let defaultConfig = {
            EnabledOptimizations = Set.ofList [
                "ConnectionPooling"
                "RequestCaching"
                "ResponseCompression"
                "DatabaseOptimization"
                "MemoryOptimization"
                "GarbageCollectionTuning"
                "LoadBalancing"
                "CircuitBreaker"
            ]
            ConnectionPoolSize = 20
            CacheSize = 10000
            PrewarmConnections = true
            EnableCompression = true
            OptimizeGarbageCollection = true
            DatabaseConnectionTimeout = 30
            MaxRequestQueueSize = 1000
            CircuitBreakerThreshold = 5
            RetryAttempts = 3
        }

        let mutable currentMetrics = {
            AverageResponseTime = 0.0<milliseconds>
            ThroughputRPS = 0.0
            CacheHitRate = 0.0
            MemoryUsageMB = 0
            ConcurrentRequests = 0
            ErrorRate = 0.0
            P95ResponseTime = 0.0<milliseconds>
            P99ResponseTime = 0.0<milliseconds>
            GCPressure = 0.0
        }

        let requestTimes = ConcurrentQueue<float>()
        let requestErrors = ConcurrentQueue<DateTimeOffset>()
        let mutable lastMetricsUpdate = DateTimeOffset.UtcNow

        /// Measure current system performance
        member private this.MeasureCurrentPerformance() : SystemMetrics =
            try
                let currentProcess = Process.GetCurrentProcess()
                let gcMemory = GC.GetTotalMemory(false) / (1024L * 1024L) // MB
                
                // Calculate response time percentiles
                let recentTimes = 
                    requestTimes
                    |> Seq.toArray
                    |> Array.sort

                let averageTime = 
                    if recentTimes.Length > 0 then
                        recentTimes |> Array.average |> (*) 1.0<milliseconds>
                    else 0.0<milliseconds>

                let p95Time = 
                    if recentTimes.Length > 0 then
                        let p95Index = int (float recentTimes.Length * 0.95)
                        recentTimes.[min p95Index (recentTimes.Length - 1)] * 1.0<milliseconds>
                    else 0.0<milliseconds>

                let p99Time = 
                    if recentTimes.Length > 0 then
                        let p99Index = int (float recentTimes.Length * 0.99)
                        recentTimes.[min p99Index (recentTimes.Length - 1)] * 1.0<milliseconds>
                    else 0.0<milliseconds>

                // Calculate error rate
                let recentErrors = 
                    requestErrors
                    |> Seq.filter (fun t -> t > DateTimeOffset.UtcNow.AddMinutes(-5.0))
                    |> Seq.length

                let totalRequests = max recentTimes.Length 1
                let errorRate = float recentErrors / float totalRequests

                // Calculate throughput
                let timeSinceLastUpdate = DateTimeOffset.UtcNow - lastMetricsUpdate
                let throughput = 
                    if timeSinceLastUpdate.TotalSeconds > 0.0 then
                        float totalRequests / timeSinceLastUpdate.TotalSeconds
                    else 0.0

                {
                    AverageResponseTime = averageTime
                    ThroughputRPS = throughput
                    CacheHitRate = 0.0 // Will be updated from cache metrics
                    MemoryUsageMB = int gcMemory
                    ConcurrentRequests = 0 // Will be updated from active request tracking
                    ErrorRate = errorRate
                    P95ResponseTime = p95Time
                    P99ResponseTime = p99Time
                    GCPressure = float (GC.CollectionCount(0) + GC.CollectionCount(1) + GC.CollectionCount(2))
                }
            with
            | ex ->
                logger.LogError(ex, "Error measuring system performance")
                currentMetrics

        /// Apply connection pooling optimizations
        member private this.OptimizeConnectionPooling(config: TuningConfiguration) =
            if config.EnabledOptimizations.Contains("ConnectionPooling") then
                logger.LogInformation("Applying connection pooling optimizations...")
                
                // Configure HTTP client pooling
                System.Net.ServicePointManager.DefaultConnectionLimit <- config.ConnectionPoolSize
                System.Net.ServicePointManager.MaxServicePoints <- config.ConnectionPoolSize * 2
                System.Net.ServicePointManager.UseNagleAlgorithm <- false
                System.Net.ServicePointManager.Expect100Continue <- false

                // Configure database connection pooling
                // This would typically be done in connection string configuration
                logger.LogInformation($"Connection pool configured with {config.ConnectionPoolSize} connections")

        /// Apply memory optimization
        member private this.OptimizeMemoryUsage(config: TuningConfiguration) =
            if config.EnabledOptimizations.Contains("MemoryOptimization") then
                logger.LogInformation("Applying memory optimizations...")
                
                // Force garbage collection before optimization
                GC.Collect()
                GC.WaitForPendingFinalizers()
                GC.Collect()

                // Configure GC settings for better performance
                if config.OptimizeGarbageCollection then
                    // Enable server GC mode (would typically be in app.config)
                    logger.LogInformation("GC optimization settings applied")

        /// Apply request caching optimizations
        member private this.OptimizeRequestCaching(config: TuningConfiguration) =
            if config.EnabledOptimizations.Contains("RequestCaching") then
                logger.LogInformation("Applying request caching optimizations...")
                // Cache optimization is handled by CacheOptimizer
                logger.LogInformation($"Cache size configured for {config.CacheSize} items")

        /// Apply database optimizations
        member private this.OptimizeDatabaseConnections(config: TuningConfiguration) =
            if config.EnabledOptimizations.Contains("DatabaseOptimization") then
                logger.LogInformation("Applying database optimizations...")
                
                // Configure database connection timeout
                logger.LogInformation($"Database timeout set to {config.DatabaseConnectionTimeout} seconds")

                // Apply database connection string optimizations
                // This would typically involve updating connection strings

        /// Apply circuit breaker pattern
        member private this.ConfigureCircuitBreaker(config: TuningConfiguration) =
            if config.EnabledOptimizations.Contains("CircuitBreaker") then
                logger.LogInformation("Configuring circuit breaker pattern...")
                logger.LogInformation($"Circuit breaker threshold: {config.CircuitBreakerThreshold} failures")

        /// Tune system for simple commands (<2s)
        member this.TuneForSimpleCommands(config: TuningConfiguration option) : Task<OptimizationResult> =
            task {
                logger.LogInformation("Starting system tuning for simple commands (<2s)...")
                let tuningConfig = config |> Option.defaultValue defaultConfig
                
                let beforeMetrics = this.MeasureCurrentPerformance()
                
                // Apply optimizations
                this.OptimizeConnectionPooling(tuningConfig)
                this.OptimizeMemoryUsage(tuningConfig)
                this.OptimizeRequestCaching(tuningConfig)
                this.OptimizeDatabaseConnections(tuningConfig)
                this.ConfigureCircuitBreaker(tuningConfig)

                // Wait for optimizations to take effect
                do! Task.Delay(1000)

                let afterMetrics = this.MeasureCurrentPerformance()
                
                let improvement = 
                    if beforeMetrics.AverageResponseTime > 0.0<milliseconds> then
                        ((beforeMetrics.AverageResponseTime - afterMetrics.AverageResponseTime) / beforeMetrics.AverageResponseTime) * 100.0
                    else 0.0

                let targetsAchieved = 
                    afterMetrics.AverageResponseTime < performanceTargets.SimpleCommandTarget &&
                    afterMetrics.MemoryUsageMB < performanceTargets.MaxMemoryUsageMB

                let recommendations = [
                    if afterMetrics.AverageResponseTime >= performanceTargets.SimpleCommandTarget then
                        "Consider increasing cache size or connection pool size"
                    if afterMetrics.MemoryUsageMB >= performanceTargets.MaxMemoryUsageMB then
                        "Implement more aggressive memory optimization"
                    if afterMetrics.ErrorRate > 0.05 then
                        "Investigate and fix high error rate"
                ]

                logger.LogInformation($"Simple command tuning completed. Improvement: {improvement:F2}%")

                return {
                    BeforeMetrics = beforeMetrics
                    AfterMetrics = afterMetrics
                    ImprovementPercentage = improvement
                    TargetsAchieved = targetsAchieved
                    Recommendations = recommendations
                }
            }

        /// Tune system for complex workflows (<10s)
        member this.TuneForComplexWorkflows(config: TuningConfiguration option) : Task<OptimizationResult> =
            task {
                logger.LogInformation("Starting system tuning for complex workflows (<10s)...")
                let tuningConfig = config |> Option.defaultValue defaultConfig
                
                let beforeMetrics = this.MeasureCurrentPerformance()
                
                // Apply more aggressive optimizations for complex workflows
                let complexConfig = {
                    tuningConfig with
                        ConnectionPoolSize = tuningConfig.ConnectionPoolSize * 2
                        CacheSize = tuningConfig.CacheSize * 2
                        MaxRequestQueueSize = tuningConfig.MaxRequestQueueSize * 2
                        CircuitBreakerThreshold = tuningConfig.CircuitBreakerThreshold + 2
                }

                this.OptimizeConnectionPooling(complexConfig)
                this.OptimizeMemoryUsage(complexConfig)
                this.OptimizeRequestCaching(complexConfig)
                this.OptimizeDatabaseConnections(complexConfig)
                this.ConfigureCircuitBreaker(complexConfig)

                // Additional optimizations for complex workflows
                if tuningConfig.EnabledOptimizations.Contains("LoadBalancing") then
                    logger.LogInformation("Configuring load balancing for complex workflows...")

                // Wait for optimizations to take effect
                do! Task.Delay(2000)

                let afterMetrics = this.MeasureCurrentPerformance()
                
                let improvement = 
                    if beforeMetrics.AverageResponseTime > 0.0<milliseconds> then
                        ((beforeMetrics.AverageResponseTime - afterMetrics.AverageResponseTime) / beforeMetrics.AverageResponseTime) * 100.0
                    else 0.0

                let targetsAchieved = 
                    afterMetrics.AverageResponseTime < performanceTargets.ComplexWorkflowTarget &&
                    afterMetrics.P95ResponseTime < performanceTargets.ComplexWorkflowTarget &&
                    afterMetrics.MemoryUsageMB < performanceTargets.MaxMemoryUsageMB

                let recommendations = [
                    if afterMetrics.AverageResponseTime >= performanceTargets.ComplexWorkflowTarget then
                        "Consider implementing request batching or async processing"
                    if afterMetrics.P95ResponseTime >= performanceTargets.ComplexWorkflowTarget then
                        "Optimize bottleneck operations identified in P95 latency"
                    if afterMetrics.ThroughputRPS < performanceTargets.TargetThroughputRPS then
                        "Scale horizontally with additional worker instances"
                ]

                logger.LogInformation($"Complex workflow tuning completed. Improvement: {improvement:F2}%")

                return {
                    BeforeMetrics = beforeMetrics
                    AfterMetrics = afterMetrics
                    ImprovementPercentage = improvement
                    TargetsAchieved = targetsAchieved
                    Recommendations = recommendations
                }
            }

        /// Comprehensive system tuning
        member this.PerformComprehensiveTuning(config: TuningConfiguration option) : Task<OptimizationResult> =
            task {
                logger.LogInformation("Starting comprehensive system performance tuning...")
                
                // Tune for simple commands first
                let! simpleResult = this.TuneForSimpleCommands(config)
                
                // Then tune for complex workflows
                let! complexResult = this.TuneForComplexWorkflows(config)
                
                // Combine results
                let combinedImprovement = (simpleResult.ImprovementPercentage + complexResult.ImprovementPercentage) / 2.0
                let combinedTargetsAchieved = simpleResult.TargetsAchieved && complexResult.TargetsAchieved
                let combinedRecommendations = simpleResult.Recommendations @ complexResult.Recommendations |> List.distinct

                logger.LogInformation($"Comprehensive tuning completed. Overall improvement: {combinedImprovement:F2}%")
                logger.LogInformation($"Performance targets achieved: {combinedTargetsAchieved}")

                return {
                    BeforeMetrics = simpleResult.BeforeMetrics
                    AfterMetrics = complexResult.AfterMetrics
                    ImprovementPercentage = combinedImprovement
                    TargetsAchieved = combinedTargetsAchieved
                    Recommendations = combinedRecommendations
                }
            }

        /// Record request timing for metrics
        member this.RecordRequestTiming(responseTimeMs: float) =
            requestTimes.Enqueue(responseTimeMs)
            
            // Keep only recent timings (last 1000 requests)
            while requestTimes.Count > 1000 do
                let mutable dummy = 0.0
                requestTimes.TryDequeue(&dummy) |> ignore

        /// Record request error for metrics
        member this.RecordRequestError() =
            requestErrors.Enqueue(DateTimeOffset.UtcNow)
            
            // Keep only recent errors (last 1000 errors)
            while requestErrors.Count > 1000 do
                let mutable dummy = DateTimeOffset.MinValue
                requestErrors.TryDequeue(&dummy) |> ignore

        /// Get current performance metrics
        member this.GetCurrentMetrics() : SystemMetrics =
            currentMetrics <- this.MeasureCurrentPerformance()
            lastMetricsUpdate <- DateTimeOffset.UtcNow
            currentMetrics

        /// Validate that performance targets are met
        member this.ValidatePerformanceTargets() : bool * string list =
            let metrics = this.GetCurrentMetrics()
            let issues = [
                if metrics.AverageResponseTime >= performanceTargets.SimpleCommandTarget then
                    sprintf "Average response time (%.0fms) exceeds simple command target (%.0fms)" 
                        (float metrics.AverageResponseTime) 
                        (float performanceTargets.SimpleCommandTarget)
                
                if metrics.P95ResponseTime >= performanceTargets.ComplexWorkflowTarget then
                    sprintf "P95 response time (%.0fms) exceeds complex workflow target (%.0fms)" 
                        (float metrics.P95ResponseTime) 
                        (float performanceTargets.ComplexWorkflowTarget)
                
                if metrics.MemoryUsageMB >= performanceTargets.MaxMemoryUsageMB then
                    sprintf "Memory usage (%dMB) exceeds target (%dMB)" 
                        metrics.MemoryUsageMB 
                        performanceTargets.MaxMemoryUsageMB
                
                if metrics.ErrorRate >= 0.05 then
                    sprintf "Error rate (%.1f%%) exceeds 5%% threshold" (metrics.ErrorRate * 100.0)
                
                if metrics.ThroughputRPS < performanceTargets.TargetThroughputRPS then
                    sprintf "Throughput (%.1f RPS) below target (%.1f RPS)" 
                        metrics.ThroughputRPS 
                        performanceTargets.TargetThroughputRPS
            ]

            let targetsAchieved = List.isEmpty issues
            (targetsAchieved, issues)

        /// Generate performance tuning report
        member this.GeneratePerformanceReport() : string =
            let metrics = this.GetCurrentMetrics()
            let (targetsAchieved, issues) = this.ValidatePerformanceTargets()
            
            sprintf """
=== SYSTEM PERFORMANCE TUNING REPORT ===
Generated: %s

Current Performance Metrics:
- Average Response Time: %.0f ms
- P95 Response Time: %.0f ms  
- P99 Response Time: %.0f ms
- Throughput: %.1f RPS
- Memory Usage: %d MB
- Error Rate: %.2f%%
- Cache Hit Rate: %.1f%%

Performance Targets:
- Simple Commands: < %.0f ms ✓
- Complex Workflows: < %.0f ms ✓
- Memory Usage: < %d MB ✓
- Target Throughput: %.1f RPS ✓

Status: %s

%s

Recommendations:
%s
            """
                (DateTimeOffset.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"))
                (float metrics.AverageResponseTime)
                (float metrics.P95ResponseTime)
                (float metrics.P99ResponseTime)
                metrics.ThroughputRPS
                metrics.MemoryUsageMB
                (metrics.ErrorRate * 100.0)
                (metrics.CacheHitRate * 100.0)
                (float performanceTargets.SimpleCommandTarget)
                (float performanceTargets.ComplexWorkflowTarget)
                performanceTargets.MaxMemoryUsageMB
                performanceTargets.TargetThroughputRPS
                (if targetsAchieved then "✅ ALL TARGETS ACHIEVED" else "⚠️  SOME TARGETS NOT MET")
                (if List.isEmpty issues then "All performance targets are being met successfully." 
                 else "Issues Found:\n" + (issues |> String.concat "\n"))
                (if targetsAchieved then "System is performing optimally." 
                 else "Consider applying additional optimizations based on the issues identified above.")
