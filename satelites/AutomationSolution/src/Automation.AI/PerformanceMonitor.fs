namespace Automation.AI

open System
open System.Collections.Concurrent
open System.Collections.Generic
open System.Diagnostics
open System.Threading
open Automation.Core

/// Performance monitoring and baseline establishment for AI operations
module PerformanceMonitor =
    
    /// Types of AI operations to monitor
    type AIOperationType =
        | LLMTextGeneration
        | LLMVisionProcessing
        | PromptProcessing
        | ResponseParsing
        | AutomationExecution
        | ErrorHandling
        | CacheOperation
        | ProviderSelection
        | SecurityValidation
        | AuditLogging
        | HealthCheck
    
    /// Performance metrics for a single operation
    type PerformanceMetrics = {
        OperationType: AIOperationType
        OperationId: string
        StartTime: DateTimeOffset
        EndTime: DateTimeOffset option
        Duration: TimeSpan option
        Success: bool
        ErrorMessage: string option
        InputSize: int option // Bytes or token count
        OutputSize: int option // Bytes or token count
        ProviderId: string option
        Model: string option
        CacheHit: bool
        Cost: decimal option
        MemoryUsed: int64 option // Bytes
        CpuTimeMs: int64 option
        Metadata: Map<string, obj>
    }
    
    /// Aggregated performance statistics
    type PerformanceStats = {
        OperationType: AIOperationType
        TotalOperations: int64
        SuccessfulOperations: int64
        FailedOperations: int64
        AverageResponseTime: TimeSpan
        MedianResponseTime: TimeSpan
        P95ResponseTime: TimeSpan
        P99ResponseTime: TimeSpan
        MinResponseTime: TimeSpan
        MaxResponseTime: TimeSpan
        TotalCost: decimal
        AverageCost: decimal
        CacheHitRate: float
        ErrorRate: float
        ThroughputPerSecond: float
        AverageMemoryUsage: int64
        AverageCpuTime: int64
        LastUpdated: DateTimeOffset
    }
    
    /// Performance baseline configuration
    type BaselineConfig = {
        SampleSize: int // Number of operations to use for baseline
        WindowSizeHours: int // Time window for calculating baselines
        UpdateIntervalMinutes: int // How often to recalculate baselines
        PercentileThresholds: float list // Percentiles to track (e.g., [0.5; 0.95; 0.99])
        AlertThresholds: Map<AIOperationType, float> // Max response time in seconds
        MinOperationsForBaseline: int // Minimum operations before establishing baseline
    }
    
    /// Default baseline configuration
    let defaultBaselineConfig = {
        SampleSize = 1000
        WindowSizeHours = 24
        UpdateIntervalMinutes = 15
        PercentileThresholds = [0.5; 0.95; 0.99]
        AlertThresholds = Map.ofList [
            (LLMTextGeneration, 5.0)
            (LLMVisionProcessing, 15.0)
            (PromptProcessing, 0.5)
            (ResponseParsing, 0.2)
            (AutomationExecution, 30.0)
            (ErrorHandling, 1.0)
            (CacheOperation, 0.1)
            (ProviderSelection, 0.5)
            (SecurityValidation, 1.0)
            (AuditLogging, 0.5)
            (HealthCheck, 2.0)
        ]
        MinOperationsForBaseline = 10
    }
    
    /// Performance alert
    type PerformanceAlert = {
        AlertId: Guid
        Timestamp: DateTimeOffset
        OperationType: AIOperationType
        AlertType: string // "ResponseTimeExceeded", "ErrorRateHigh", etc.
        Message: string
        CurrentValue: float
        ThresholdValue: float
        Severity: string // "Low", "Medium", "High", "Critical"
        Metadata: Map<string, obj>
    }
    
    /// Performance monitoring configuration
    type MonitorConfig = {
        EnableMetricsCollection: bool
        EnableBaselining: bool
        EnableAlerting: bool
        MetricsRetentionDays: int
        BaselineConfig: BaselineConfig
        ExportToPrometheus: bool
        PrometheusPort: int option
        ExportToFile: bool
        MetricsFilePath: string option
    }
    
    /// Default monitoring configuration
    let defaultMonitorConfig = {
        EnableMetricsCollection = true
        EnableBaselining = true
        EnableAlerting = true
        MetricsRetentionDays = 30
        BaselineConfig = defaultBaselineConfig
        ExportToPrometheus = false
        PrometheusPort = Some 9090
        ExportToFile = true
        MetricsFilePath = Some "./metrics/ai_performance_metrics.json"
    }
    
    /// Thread-safe metrics storage
    type MetricsStorage() =
        let metrics = ConcurrentDictionary<string, PerformanceMetrics>()
        let operationMetrics = ConcurrentDictionary<AIOperationType, ConcurrentQueue<PerformanceMetrics>>()
        
        member _.AddMetric(metric: PerformanceMetrics) =
            metrics.[metric.OperationId] <- metric
            
            let queue = operationMetrics.GetOrAdd(metric.OperationType, fun _ -> ConcurrentQueue<PerformanceMetrics>())
            queue.Enqueue(metric)
            
            // Limit queue size to prevent memory leaks
            while queue.Count > 10000 do
                queue.TryDequeue() |> ignore
        
        member _.GetMetric(operationId: string) =
            match metrics.TryGetValue(operationId) with
            | true, metric -> Some metric
            | false, _ -> None
        
        member _.GetMetricsByType(operationType: AIOperationType) =
            match operationMetrics.TryGetValue(operationType) with
            | true, queue -> queue.ToArray() |> Array.toList
            | false, _ -> []
        
        member _.GetAllMetrics() =
            metrics.Values |> Seq.toList
        
        member self.GetMetricsInTimeWindow(operationType: AIOperationType, windowStart: DateTimeOffset, windowEnd: DateTimeOffset) =
            self.GetMetricsByType(operationType)
            |> List.filter (fun m -> m.StartTime >= windowStart && m.StartTime <= windowEnd)
        
        member _.ClearOldMetrics(cutoffDate: DateTimeOffset) =
            let keysToRemove = 
                metrics
                |> Seq.filter (fun kvp -> kvp.Value.StartTime < cutoffDate)
                |> Seq.map (fun kvp -> kvp.Key)
                |> List.ofSeq
            
            for key in keysToRemove do
                metrics.TryRemove(key) |> ignore
    
    /// Calculate performance statistics from metrics
    let calculateStats (metrics: PerformanceMetrics list) (operationType: AIOperationType) =
        if metrics.IsEmpty then
            None
        else
            let completedMetrics = metrics |> List.filter (fun m -> m.Duration.IsSome)
            let durations = completedMetrics |> List.map (fun m -> m.Duration.Value) |> List.sort
            let successfulOps = completedMetrics |> List.filter (fun m -> m.Success)
            let costs = completedMetrics |> List.choose (fun m -> m.Cost) 
            let cacheHits = completedMetrics |> List.filter (fun m -> m.CacheHit)
            let memoryUsages = completedMetrics |> List.choose (fun m -> m.MemoryUsed)
            let cpuTimes = completedMetrics |> List.choose (fun m -> m.CpuTimeMs)
            
            let getPercentile (sorted: TimeSpan list) (percentile: float) =
                if sorted.IsEmpty then TimeSpan.Zero
                else
                    let index = int (float sorted.Length * percentile)
                    let clampedIndex = max 0 (min (sorted.Length - 1) index)
                    sorted.[clampedIndex]
            
            let totalDuration = durations |> List.fold (+) TimeSpan.Zero
            let avgDuration = if durations.Length > 0 then TimeSpan.FromTicks(totalDuration.Ticks / int64 durations.Length) else TimeSpan.Zero
            
            Some {
                OperationType = operationType
                TotalOperations = int64 metrics.Length
                SuccessfulOperations = int64 successfulOps.Length
                FailedOperations = int64 (metrics.Length - successfulOps.Length)
                AverageResponseTime = avgDuration
                MedianResponseTime = getPercentile durations 0.5
                P95ResponseTime = getPercentile durations 0.95
                P99ResponseTime = getPercentile durations 0.99
                MinResponseTime = if durations.IsEmpty then TimeSpan.Zero else List.min durations
                MaxResponseTime = if durations.IsEmpty then TimeSpan.Zero else List.max durations
                TotalCost = costs |> List.sum
                AverageCost = if costs.IsEmpty then 0m else (costs |> List.sum) / decimal costs.Length
                CacheHitRate = if completedMetrics.IsEmpty then 0.0 else float cacheHits.Length / float completedMetrics.Length
                ErrorRate = if metrics.IsEmpty then 0.0 else float (metrics.Length - successfulOps.Length) / float metrics.Length
                ThroughputPerSecond = 0.0 // Will be calculated separately based on time window
                AverageMemoryUsage = if memoryUsages.IsEmpty then 0L else memoryUsages |> List.sum |> fun total -> total / int64 memoryUsages.Length
                AverageCpuTime = if cpuTimes.IsEmpty then 0L else cpuTimes |> List.sum |> fun total -> total / int64 cpuTimes.Length
                LastUpdated = DateTimeOffset.Now
            }
    
    /// Performance monitor main class
    type PerformanceMonitor(config: MonitorConfig, auditLogger: AuditLogger.AuditLogger option) as self =
        let storage = MetricsStorage()
        let baselines = ConcurrentDictionary<AIOperationType, PerformanceStats>()
        let alerts = ConcurrentQueue<PerformanceAlert>()
        let mutable isRunning = true
        
        // Background task for baseline calculation
        let baselineCalculationTimer = new Timer(
            (fun _ -> 
                if config.EnableBaselining then
                    self.CalculateBaselines() |> ignore
            ),
            null,
            TimeSpan.FromMinutes(1.0), // Start after 1 minute
            TimeSpan.FromMinutes(float config.BaselineConfig.UpdateIntervalMinutes)
        )
        
        // Background task for cleanup
        let cleanupTimer = new Timer(
            (fun _ -> 
                let cutoffDate = DateTimeOffset.Now.AddDays(-float config.MetricsRetentionDays)
                storage.ClearOldMetrics(cutoffDate)
            ),
            null,
            TimeSpan.FromHours(1.0), // Start after 1 hour
            TimeSpan.FromHours(6.0) // Run every 6 hours
        )
        
        /// Start monitoring an operation
        member _.StartOperation(operationType: AIOperationType, operationId: string option, metadata: Map<string, obj> option) =
            if config.EnableMetricsCollection then
                let opId = defaultArg operationId (Guid.NewGuid().ToString())
                let metric = {
                    OperationType = operationType
                    OperationId = opId
                    StartTime = DateTimeOffset.Now
                    EndTime = None
                    Duration = None
                    Success = false
                    ErrorMessage = None
                    InputSize = None
                    OutputSize = None
                    ProviderId = None
                    Model = None
                    CacheHit = false
                    Cost = None
                    MemoryUsed = None
                    CpuTimeMs = None
                    Metadata = defaultArg metadata Map.empty
                }
                storage.AddMetric(metric)
                opId
            else
                Guid.NewGuid().ToString()
        
        /// Complete monitoring an operation
        member _.CompleteOperation(operationId: string, success: bool, ?errorMessage: string, ?inputSize: int, ?outputSize: int, ?providerId: string, ?model: string, ?cacheHit: bool, ?cost: decimal, ?memoryUsed: int64, ?cpuTimeMs: int64, ?metadata: Map<string, obj>) =
            if config.EnableMetricsCollection then
                match storage.GetMetric(operationId) with
                | Some existingMetric ->
                    let endTime = DateTimeOffset.Now
                    let duration = endTime - existingMetric.StartTime
                    
                    let updatedMetric = {
                        existingMetric with
                            EndTime = Some endTime
                            Duration = Some duration
                            Success = success
                            ErrorMessage = errorMessage
                            InputSize = inputSize
                            OutputSize = outputSize
                            ProviderId = providerId
                            Model = model
                            CacheHit = defaultArg cacheHit false
                            Cost = cost
                            MemoryUsed = memoryUsed
                            CpuTimeMs = cpuTimeMs
                            Metadata = 
                                match metadata with
                                | Some newMeta -> Map.fold (fun acc k v -> Map.add k v acc) existingMetric.Metadata newMeta
                                | None -> existingMetric.Metadata
                    }
                    
                    storage.AddMetric(updatedMetric)
                    
                    // Check for alerts
                    if config.EnableAlerting then
                        self.CheckForAlerts(updatedMetric)
                    
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.AuditEventType.ActionExecution,
                            AuditLogger.AuditSeverity.Info,
                            "PerformanceMonitor",
                            $"Operation completed: {existingMetric.OperationType}",
                            Map.ofList [
                                ("operationId", box operationId)
                                ("duration", box duration.TotalMilliseconds)
                                ("success", box success)
                                ("operationType", box (existingMetric.OperationType.ToString()))
                            ]
                        )
                    )
                | None ->
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.AuditEventType.ActionExecution,
                            AuditLogger.AuditSeverity.Warning,
                            "PerformanceMonitor",
                            $"Attempted to complete unknown operation: {operationId}",
                            Map.ofList [("operationId", box operationId)]
                        )
                    )
        
        /// Measure and record an operation
        member this.MeasureOperation<'T>(operationType: AIOperationType, operation: unit -> 'T, ?metadata: Map<string, obj>) : 'T =
            let operationId = this.StartOperation(operationType, None, metadata)
            let stopwatch = Stopwatch.StartNew()
            
            try
                let result = operation()
                stopwatch.Stop()
                this.CompleteOperation(operationId, true, cpuTimeMs = stopwatch.ElapsedMilliseconds)
                result
            with
            | ex ->
                stopwatch.Stop()
                this.CompleteOperation(operationId, false, errorMessage = ex.Message, cpuTimeMs = stopwatch.ElapsedMilliseconds)
                reraise()
        
        /// Measure and record an async operation
        member this.MeasureOperationAsync<'T>(operationType: AIOperationType, operation: unit -> Async<'T>, ?metadata: Map<string, obj>) : Async<'T> =
            async {
                let operationId = this.StartOperation(operationType, None, metadata)
                let stopwatch = Stopwatch.StartNew()
                
                try
                    let! result = operation()
                    stopwatch.Stop()
                    this.CompleteOperation(operationId, true, cpuTimeMs = stopwatch.ElapsedMilliseconds)
                    return result
                with
                | ex ->
                    stopwatch.Stop()
                    this.CompleteOperation(operationId, false, errorMessage = ex.Message, cpuTimeMs = stopwatch.ElapsedMilliseconds)
                    return raise ex
            }
        
        /// Calculate baselines for all operation types
        member _.CalculateBaselines() =
            let windowStart = DateTimeOffset.Now.AddHours(-float config.BaselineConfig.WindowSizeHours)
            let windowEnd = DateTimeOffset.Now
            
            let operationTypes = [
                AIOperationType.LLMTextGeneration
                AIOperationType.LLMVisionProcessing
                AIOperationType.PromptProcessing
                AIOperationType.ResponseParsing
                AIOperationType.AutomationExecution
                AIOperationType.ErrorHandling
                AIOperationType.CacheOperation
                AIOperationType.ProviderSelection
                AIOperationType.SecurityValidation
                AIOperationType.AuditLogging
                AIOperationType.HealthCheck
            ]
            
            for operationType in operationTypes do
                let metrics = storage.GetMetricsInTimeWindow(operationType, windowStart, windowEnd)
                
                if metrics.Length >= config.BaselineConfig.MinOperationsForBaseline then
                    match calculateStats metrics operationType with
                    | Some stats ->
                        // Calculate throughput
                        let timeWindowHours = float config.BaselineConfig.WindowSizeHours
                        let throughput = float stats.TotalOperations / (timeWindowHours * 3600.0)
                        let statsWithThroughput = { stats with ThroughputPerSecond = throughput }
                        
                        baselines.[operationType] <- statsWithThroughput
                        
                        auditLogger |> Option.iter (fun logger ->
                            logger.LogEvent(
                                AuditLogger.AuditEventType.HealthCheck,
                                AuditLogger.Info,
                                "PerformanceMonitor",
                                $"Baseline updated for {operationType}",
                                Map.ofList [
                                    ("operationType", box (operationType.ToString()))
                                    ("avgResponseTime", box stats.AverageResponseTime.TotalMilliseconds)
                                    ("p95ResponseTime", box stats.P95ResponseTime.TotalMilliseconds)
                                    ("errorRate", box stats.ErrorRate)
                                    ("throughput", box throughput)
                                ]
                            )
                        )
                    | None -> ()
        
        /// Check for performance alerts
        member _.CheckForAlerts(metric: PerformanceMetrics) =
            match config.BaselineConfig.AlertThresholds.TryGetValue(metric.OperationType) with
            | true, threshold when metric.Duration.IsSome ->
                let responseTimeSeconds = metric.Duration.Value.TotalSeconds
                
                if responseTimeSeconds > threshold then
                    let alert = {
                        AlertId = Guid.NewGuid()
                        Timestamp = DateTimeOffset.Now
                        OperationType = metric.OperationType
                        AlertType = "ResponseTimeExceeded"
                        Message = $"Operation {metric.OperationType} exceeded response time threshold"
                        CurrentValue = responseTimeSeconds
                        ThresholdValue = threshold
                        Severity = if responseTimeSeconds > threshold * 2.0 then "Critical" else "High"
                        Metadata = Map.ofList [
                            ("operationId", box metric.OperationId)
                            ("providerId", box metric.ProviderId)
                            ("model", box metric.Model)
                        ]
                    }
                    
                    alerts.Enqueue(alert)
                    
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.AuditEventType.Error,
                            AuditLogger.AuditSeverity.Warning,
                            "PerformanceMonitor",
                            alert.Message,
                            Map.ofList [
                                ("alertId", box alert.AlertId)
                                ("operationType", box (alert.OperationType.ToString()))
                                ("currentValue", box alert.CurrentValue)
                                ("threshold", box alert.ThresholdValue)
                                ("severity", box alert.Severity)
                            ]
                        )
                    )
            | _ -> ()
        
        /// Get baseline for operation type
        member _.GetBaseline(operationType: AIOperationType) =
            match baselines.TryGetValue(operationType) with
            | true, baseline -> Some baseline
            | false, _ -> None
        
        /// Get all baselines
        member _.GetAllBaselines() =
            baselines |> Seq.map (fun kvp -> (kvp.Key, kvp.Value)) |> Map.ofSeq
        
        /// Get recent metrics for operation type
        member _.GetRecentMetrics(operationType: AIOperationType, ?hours: int) =
            let hoursBack = defaultArg hours 1
            let windowStart = DateTimeOffset.Now.AddHours(-float hoursBack)
            storage.GetMetricsInTimeWindow(operationType, windowStart, DateTimeOffset.Now)
        
        /// Get performance statistics for operation type
        member self.GetStatistics(operationType: AIOperationType, ?hours: int) =
            let metrics = self.GetRecentMetrics(operationType, ?hours = hours)
            calculateStats metrics operationType
        
        /// Get recent alerts
        member _.GetRecentAlerts(?count: int) =
            let maxCount = defaultArg count 50
            alerts.ToArray() 
            |> Array.rev
            |> Array.take (min maxCount alerts.Count)
            |> Array.toList
        
        /// Get performance summary
        member self.GetPerformanceSummary() =
            let allBaselines = self.GetAllBaselines()
            let recentAlerts = self.GetRecentAlerts(10)
            
            {|
                Timestamp = DateTimeOffset.Now
                BaselineCount = allBaselines.Count
                Baselines = allBaselines
                RecentAlerts = recentAlerts
                AlertCount = alerts.Count
                OverallHealth = 
                    let criticalAlerts = recentAlerts |> List.filter (fun a -> a.Severity = "Critical")
                    if criticalAlerts.Length > 0 then "Critical"
                    elif recentAlerts.Length > 5 then "Degraded"
                    else "Healthy"
            |}
        
        /// Export metrics to file
        member _.ExportMetricsToFile(filePath: string) =
            if config.ExportToFile then
                try
                    let allMetrics = storage.GetAllMetrics()
                    let json = System.Text.Json.JsonSerializer.Serialize(allMetrics, System.Text.Json.JsonSerializerOptions(WriteIndented = true))
                    System.IO.File.WriteAllText(filePath, json)
                    
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.AuditEventType.Error,
                            AuditLogger.AuditSeverity.Info,
                            "PerformanceMonitor",
                            $"Metrics exported to {filePath}",
                            Map.ofList [
                                ("filePath", box filePath)
                                ("metricsCount", box allMetrics.Length)
                            ]
                        )
                    )
                with
                | ex ->
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.AuditEventType.Error,
                            AuditLogger.AuditSeverity.Error,
                            "PerformanceMonitor",
                            $"Failed to export metrics: {ex.Message}",
                            Map.ofList [
                                ("filePath", box filePath)
                                ("error", box ex.Message)
                            ]
                        )
                    )
        
        /// Dispose resources
        interface IDisposable with
            member self.Dispose() =
                isRunning <- false
                baselineCalculationTimer.Dispose()
                cleanupTimer.Dispose()
                
                // Export final metrics if configured
                config.MetricsFilePath |> Option.iter self.ExportMetricsToFile
    
    /// Create default performance monitor
    let createPerformanceMonitor (config: MonitorConfig option) (auditLogger: AuditLogger.AuditLogger option) =
        let config = defaultArg config defaultMonitorConfig
        new PerformanceMonitor(config, auditLogger)
    
    /// Helper function to measure AI operations
    let measureAIOperation<'T> (monitor: PerformanceMonitor) (operationType: AIOperationType) (operation: unit -> 'T) =
        monitor.MeasureOperation(operationType, operation)
    
    /// Helper function to measure async AI operations
    let measureAIOperationAsync<'T> (monitor: PerformanceMonitor) (operationType: AIOperationType) (operation: unit -> Async<'T>) =
        monitor.MeasureOperationAsync(operationType, operation)