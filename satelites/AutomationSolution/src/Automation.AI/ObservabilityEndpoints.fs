namespace Automation.AI

open System
open System.Text.Json
open System.Threading.Tasks
open System.Runtime.CompilerServices
open Microsoft.AspNetCore.Builder
open Microsoft.AspNetCore.Http
open Microsoft.AspNetCore.Routing
open Microsoft.Extensions.DependencyInjection
open Microsoft.Extensions.Logging
open System.Diagnostics

/// Health and metrics endpoints for observability
module ObservabilityEndpoints =

    type HealthStatus = 
        | Healthy
        | Degraded 
        | Unhealthy

    type HealthCheck = {
        Name: string
        Status: HealthStatus
        Duration: TimeSpan
        Exception: string option
        Data: Map<string, obj> option
    }

    type HealthResponse = {
        Status: HealthStatus
        TotalDuration: TimeSpan
        Checks: HealthCheck list
        Timestamp: DateTimeOffset
        Version: string
    }

    type MetricsData = {
        AutomationRunsTotal: int64
        AutomationRunsSuccess: int64
        AutomationRunsFailure: int64
        QueueDepth: int
        ActiveTasks: int
        AverageResponseTime: double
        MemoryUsageMB: long
        CpuUsagePercent: double
        UptimeSeconds: double
        RequestsPerSecond: double
        ErrorRate: double
        Timestamp: DateTimeOffset
    }

    /// Health check service
    type HealthCheckService(logger: ILogger<HealthCheckService>) =
        
        let mutable startTime = DateTimeOffset.UtcNow
        let mutable automationRunsTotal = 0L
        let mutable automationRunsSuccess = 0L
        let mutable automationRunsFailure = 0L
        let mutable queueDepth = 0
        let mutable activeTasks = 0

        member this.RecordAutomationRun(success: bool) =
            System.Threading.Interlocked.Increment(&automationRunsTotal) |> ignore
            if success then
                System.Threading.Interlocked.Increment(&automationRunsSuccess) |> ignore
            else
                System.Threading.Interlocked.Increment(&automationRunsFailure) |> ignore

        member this.UpdateQueueDepth(depth: int) =
            queueDepth <- depth

        member this.UpdateActiveTasks(tasks: int) =
            activeTasks <- tasks

        /// Check database health
        member private this.CheckDatabase() : HealthCheck =
            let stopwatch = Stopwatch.StartNew()
            try
                // Simulate database check
                System.Threading.Thread.Sleep(10)
                stopwatch.Stop()
                {
                    Name = "database"
                    Status = Healthy
                    Duration = stopwatch.Elapsed
                    Exception = None
                    Data = Some (Map.ofList [("connectionString", "***masked***" :> obj)])
                }
            with
            | ex ->
                stopwatch.Stop()
                {
                    Name = "database"
                    Status = Unhealthy
                    Duration = stopwatch.Elapsed
                    Exception = Some ex.Message
                    Data = None
                }

        /// Check Redis health
        member private this.CheckRedis() : HealthCheck =
            let stopwatch = Stopwatch.StartNew()
            try
                // Simulate Redis check
                System.Threading.Thread.Sleep(5)
                stopwatch.Stop()
                {
                    Name = "redis"
                    Status = Healthy
                    Duration = stopwatch.Elapsed
                    Exception = None
                    Data = Some (Map.ofList [("endpoint", "localhost:6379" :> obj)])
                }
            with
            | ex ->
                stopwatch.Stop()
                {
                    Name = "redis"
                    Status = Unhealthy
                    Duration = stopwatch.Elapsed
                    Exception = Some ex.Message
                    Data = None
                }

        /// Check AI providers health
        member private this.CheckAIProviders() : HealthCheck =
            let stopwatch = Stopwatch.StartNew()
            try
                // Simulate AI provider check
                System.Threading.Thread.Sleep(15)
                stopwatch.Stop()
                {
                    Name = "ai_providers"
                    Status = Healthy
                    Duration = stopwatch.Elapsed
                    Exception = None
                    Data = Some (Map.ofList [
                        ("openai", "healthy" :> obj)
                        ("anthropic", "healthy" :> obj)
                        ("google", "healthy" :> obj)
                    ])
                }
            with
            | ex ->
                stopwatch.Stop()
                {
                    Name = "ai_providers"
                    Status = Degraded
                    Duration = stopwatch.Elapsed
                    Exception = Some ex.Message
                    Data = None
                }

        /// Check memory health
        member private this.CheckMemory() : HealthCheck =
            let stopwatch = Stopwatch.StartNew()
            try
                let memoryUsed = GC.GetTotalMemory(false) / (1024L * 1024L) // MB
                let status = 
                    if memoryUsed < 400L then Healthy
                    elif memoryUsed < 500L then Degraded
                    else Unhealthy
                
                stopwatch.Stop()
                {
                    Name = "memory"
                    Status = status
                    Duration = stopwatch.Elapsed
                    Exception = None
                    Data = Some (Map.ofList [
                        ("usedMB", memoryUsed :> obj)
                        ("limitMB", 512L :> obj)
                    ])
                }
            with
            | ex ->
                stopwatch.Stop()
                {
                    Name = "memory"
                    Status = Unhealthy
                    Duration = stopwatch.Elapsed
                    Exception = Some ex.Message
                    Data = None
                }

        /// Perform comprehensive health check
        member this.CheckHealth() : HealthResponse =
            let totalStopwatch = Stopwatch.StartNew()
            
            logger.LogInformation("Starting health check...")
            
            let checks = [
                this.CheckDatabase()
                this.CheckRedis()
                this.CheckAIProviders()
                this.CheckMemory()
            ]
            
            totalStopwatch.Stop()
            
            let overallStatus = 
                if checks |> List.exists (fun c -> c.Status = Unhealthy) then Unhealthy
                elif checks |> List.exists (fun c -> c.Status = Degraded) then Degraded
                else Healthy
            
            logger.LogInformation($"Health check completed. Status: {overallStatus}")
            
            {
                Status = overallStatus
                TotalDuration = totalStopwatch.Elapsed
                Checks = checks
                Timestamp = DateTimeOffset.UtcNow
                Version = "1.0.0"
            }

        /// Get current metrics
        member this.GetMetrics() : MetricsData =
            let memoryUsed = GC.GetTotalMemory(false) / (1024L * 1024L) // MB
            let uptime = (DateTimeOffset.UtcNow - startTime).TotalSeconds
            let errorRate = 
                if automationRunsTotal > 0L then
                    double automationRunsFailure / double automationRunsTotal
                else 0.0
            
            {
                AutomationRunsTotal = automationRunsTotal
                AutomationRunsSuccess = automationRunsSuccess
                AutomationRunsFailure = automationRunsFailure
                QueueDepth = queueDepth
                ActiveTasks = activeTasks
                AverageResponseTime = 150.0 // Would be calculated from actual measurements
                MemoryUsageMB = memoryUsed
                CpuUsagePercent = 25.0 // Would be calculated from actual measurements
                UptimeSeconds = uptime
                RequestsPerSecond = if uptime > 0.0 then double automationRunsTotal / uptime else 0.0
                ErrorRate = errorRate
                Timestamp = DateTimeOffset.UtcNow
            }

    /// Configure observability endpoints
    module EndpointsConfiguration =
        
        /// Configure health endpoint
        let configureHealthEndpoint (app: IEndpointRouteBuilder) =
            app.MapGet("/health", Func<HealthCheckService, Task<IResult>>(fun healthService ->
                task {
                    let health = healthService.CheckHealth()
                    let statusCode = 
                        match health.Status with
                        | Healthy -> 200
                        | Degraded -> 200
                        | Unhealthy -> 503
                    
                    return Results.Json(health, statusCode = statusCode)
                }
            )) |> ignore

        /// Configure metrics endpoint  
        let configureMetricsEndpoint (app: IEndpointRouteBuilder) =
            app.MapGet("/metrics", Func<HealthCheckService, IResult>(fun healthService ->
                let metrics = healthService.GetMetrics()
                
                // Return Prometheus format
                let prometheusMetrics = 
                    sprintf """# HELP automation_runs_total Total number of automation runs
# TYPE automation_runs_total counter
automation_runs_total %d

# HELP automation_runs_success_total Total number of successful automation runs
# TYPE automation_runs_success_total counter
automation_runs_success_total %d

# HELP automation_runs_failure_total Total number of failed automation runs
# TYPE automation_runs_failure_total counter
automation_runs_failure_total %d

# HELP automation_queue_depth Current queue depth
# TYPE automation_queue_depth gauge
automation_queue_depth %d

# HELP automation_active_tasks Current number of active tasks
# TYPE automation_active_tasks gauge
automation_active_tasks %d

# HELP automation_response_time_seconds Average response time in seconds
# TYPE automation_response_time_seconds gauge
automation_response_time_seconds %.3f

# HELP automation_memory_usage_bytes Memory usage in bytes
# TYPE automation_memory_usage_bytes gauge
automation_memory_usage_bytes %d

# HELP automation_cpu_usage_percent CPU usage percentage
# TYPE automation_cpu_usage_percent gauge
automation_cpu_usage_percent %.2f

# HELP automation_uptime_seconds Uptime in seconds
# TYPE automation_uptime_seconds gauge
automation_uptime_seconds %.0f

# HELP automation_requests_per_second Requests per second
# TYPE automation_requests_per_second gauge
automation_requests_per_second %.2f

# HELP automation_error_rate Error rate (0.0 to 1.0)
# TYPE automation_error_rate gauge
automation_error_rate %.4f
"""
                        metrics.AutomationRunsTotal
                        metrics.AutomationRunsSuccess
                        metrics.AutomationRunsFailure
                        metrics.QueueDepth
                        metrics.ActiveTasks
                        (metrics.AverageResponseTime / 1000.0)
                        (metrics.MemoryUsageMB * 1024L * 1024L)
                        metrics.CpuUsagePercent
                        metrics.UptimeSeconds
                        metrics.RequestsPerSecond
                        metrics.ErrorRate

                Results.Text(prometheusMetrics, "text/plain; version=0.0.4")
            )) |> ignore

        /// Configure JSON metrics endpoint
        let configureMetricsJsonEndpoint (app: IEndpointRouteBuilder) =
            app.MapGet("/metrics/json", Func<HealthCheckService, IResult>(fun healthService ->
                let metrics = healthService.GetMetrics()
                Results.Json(metrics)
            )) |> ignore

        /// Configure all observability endpoints
        let configure (app: IEndpointRouteBuilder) =
            configureHealthEndpoint app
            configureMetricsEndpoint app
            configureMetricsJsonEndpoint app

    /// Extensions for easy configuration
    [<Extension>]
    type ObservabilityExtensions =
        
        [<Extension>]
        static member AddObservability(services: IServiceCollection) =
            services.AddSingleton<HealthCheckService>() |> ignore
            services

        [<Extension>]
        static member UseObservability(app: IEndpointRouteBuilder) =
            EndpointsConfiguration.configure app
            app
