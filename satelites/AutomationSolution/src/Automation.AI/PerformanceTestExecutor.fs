namespace Automation.AI

open System
open System.IO
open Automation.AI.PerformanceTesting
open Automation.AI.PerformanceReporting
open Automation.AI.IntegratedCostAwareSystem
open Automation.Utilities.Logging

/// Main performance testing orchestrator
module PerformanceTestExecutor =
    
    /// Performance test execution configuration
    type ExecutionConfig = {
        OutputDirectory: string
        GenerateReports: bool
        RunScenarios: string list option // If None, run all default scenarios
        MaxDurationMinutes: int option
        VerboseLogging: bool
        SaveRawMetrics: bool
        EnableRealTimeMonitoring: bool
        TestProfile: TestProfile
    }
    and TestProfile = 
        | Quick      // Fast tests for CI/CD
        | Standard   // Normal load testing
        | Stress     // High load stress testing
        | Endurance  // Long-running stability tests
        | Custom of TestScenario list
    
    /// Default execution configuration
    let defaultExecutionConfig = {
        OutputDirectory = "./performance-test-results"
        GenerateReports = true
        RunScenarios = None
        MaxDurationMinutes = Some 30
        VerboseLogging = true
        SaveRawMetrics = true
        EnableRealTimeMonitoring = true
        TestProfile = Standard
    }
    
    /// Create test configuration based on profile
    let createTestConfigFromProfile (profile: TestProfile) (baseConfig: PerformanceTestConfig) =
        match profile with
        | Quick ->
            let quickScenarios = [
                {
                    ScenarioId = "quick-simple-test"
                    Name = "Quick Simple Command Test"
                    Description = "Fast test for CI/CD pipeline"
                    RequestType = "simple_command"
                    ConcurrentUsers = 5
                    RequestsPerUser = 5
                    RampUpDurationSeconds = 10
                    TestDurationSeconds = 60
                    ThinkTimeSeconds = 1
                    RequestTemplates = []
                    ExpectedResponseTime = TimeSpan.FromSeconds(2.0)
                    ExpectedThroughput = 2.0
                    ExpectedSuccessRate = 0.95
                }
                {
                    ScenarioId = "quick-complex-test"
                    Name = "Quick Complex Workflow Test"
                    Description = "Fast complex workflow test"
                    RequestType = "complex_workflow"
                    ConcurrentUsers = 2
                    RequestsPerUser = 3
                    RampUpDurationSeconds = 15
                    TestDurationSeconds = 90
                    ThinkTimeSeconds = 2
                    RequestTemplates = []
                    ExpectedResponseTime = TimeSpan.FromSeconds(8.0)
                    ExpectedThroughput = 0.5
                    ExpectedSuccessRate = 0.90
                }
            ]
            { baseConfig with Scenarios = quickScenarios; TestName = "Quick Performance Test" }
            
        | Standard ->
            baseConfig
            
        | Stress ->
            let stressScenarios = [
                {
                    ScenarioId = "stress-high-concurrency"
                    Name = "High Concurrency Stress Test"
                    Description = "Test system under extreme concurrent load"
                    RequestType = "stress_test"
                    ConcurrentUsers = 100
                    RequestsPerUser = 20
                    RampUpDurationSeconds = 180
                    TestDurationSeconds = 600
                    ThinkTimeSeconds = 1
                    RequestTemplates = []
                    ExpectedResponseTime = TimeSpan.FromSeconds(10.0)
                    ExpectedThroughput = 15.0
                    ExpectedSuccessRate = 0.80
                }
                {
                    ScenarioId = "stress-burst-load"
                    Name = "Burst Load Test"
                    Description = "Sudden burst of requests"
                    RequestType = "burst_test"
                    ConcurrentUsers = 50
                    RequestsPerUser = 50
                    RampUpDurationSeconds = 30
                    TestDurationSeconds = 300
                    ThinkTimeSeconds = 0
                    RequestTemplates = []
                    ExpectedResponseTime = TimeSpan.FromSeconds(15.0)
                    ExpectedThroughput = 20.0
                    ExpectedSuccessRate = 0.75
                }
            ]
            { baseConfig with Scenarios = stressScenarios; TestName = "Stress Performance Test" }
            
        | Endurance ->
            let enduranceScenarios = [
                {
                    ScenarioId = "endurance-sustained-load"
                    Name = "Sustained Load Endurance Test"
                    Description = "Long-running test to detect memory leaks and performance degradation"
                    RequestType = "endurance"
                    ConcurrentUsers = 25
                    RequestsPerUser = 200
                    RampUpDurationSeconds = 600
                    TestDurationSeconds = 7200 // 2 hours
                    ThinkTimeSeconds = 15
                    RequestTemplates = []
                    ExpectedResponseTime = TimeSpan.FromSeconds(5.0)
                    ExpectedThroughput = 3.0
                    ExpectedSuccessRate = 0.95
                }
            ]
            { baseConfig with Scenarios = enduranceScenarios; TestName = "Endurance Performance Test" }
            
        | Custom scenarios ->
            { baseConfig with Scenarios = scenarios; TestName = "Custom Performance Test" }
    
    /// Performance test execution context
    type ExecutionContext = {
        Config: ExecutionConfig
        Logger: Logger option
        AuditLogger: AuditLogger.AuditLogger option
        OutputDirectory: string
        StartTime: DateTimeOffset
    }
    
    /// Create execution context
    let createExecutionContext (config: ExecutionConfig) =
        let timestamp = DateTime.UtcNow.ToString("yyyyMMdd-HHmmss")
        let outputDir = Path.Combine(config.OutputDirectory, sprintf "perf-test-%s" timestamp)
        
        // Create output directory
        if not (Directory.Exists(outputDir)) then
            Directory.CreateDirectory(outputDir) |> ignore
        
        // Setup logging
        let logger = 
            if config.VerboseLogging then
                Some (createLogger "PerformanceTest" (Some (Path.Combine(outputDir, "test-execution.log"))))
            else None
        
        let auditLogger = 
            logger |> Option.map (fun log -> 
                AuditLogger.createAuditLogger (Some (Path.Combine(outputDir, "audit.log"))))
        
        {
            Config = config
            Logger = logger
            AuditLogger = auditLogger
            OutputDirectory = outputDir
            StartTime = DateTimeOffset.UtcNow
        }
    
    /// Execute performance tests
    let executePerformanceTests (context: ExecutionContext) =
        async {
            context.Logger |> Option.iter (fun logger ->
                logger.LogInfo(sprintf "Starting performance test execution at %s" (context.StartTime.ToString()))
                logger.LogInfo(sprintf "Output directory: %s" context.OutputDirectory)
                logger.LogInfo(sprintf "Test profile: %A" context.Config.TestProfile)
            )
            
            try
                // Initialize cost-aware system
                let! costAwareSystem = IntegratedCostAwareSystem.createIntegratedSystem()
                
                // Create test configuration
                let baseTestConfig = {
                    defaultPerfTestConfig with
                        TestSuiteId = sprintf "perf-test-%s" (DateTime.UtcNow.ToString("yyyyMMdd-HHmmss"))
                        OutputDirectory = context.OutputDirectory
                        EnableRealTimeReporting = context.Config.EnableRealTimeMonitoring
                        CollectDetailedMetrics = context.Config.SaveRawMetrics
                }
                
                let testConfig = createTestConfigFromProfile context.Config.TestProfile baseTestConfig
                
                // Create performance test runner
                use testRunner = createPerformanceTestRunner (Some testConfig) context.AuditLogger
                
                // Set sample request templates if not provided
                let finalTestConfig = 
                    if testConfig.Scenarios |> List.exists (fun s -> s.RequestTemplates.IsEmpty) then
                        let sampleTemplates = testRunner.CreateSampleRequestTemplates()
                        { testConfig with 
                            Scenarios = testConfig.Scenarios |> List.map (fun scenario ->
                                if scenario.RequestTemplates.IsEmpty then
                                    let templatesForComplexity = 
                                        match scenario.RequestType with
                                        | "simple_command" -> sampleTemplates |> List.filter (fun t -> t.Complexity = Simple)
                                        | "complex_workflow" -> sampleTemplates |> List.filter (fun t -> t.Complexity = Complex || t.Complexity = VeryComplex)
                                        | _ -> sampleTemplates
                                    { scenario with RequestTemplates = templatesForComplexity }
                                else scenario)
                        }
                    else testConfig
                
                context.Logger |> Option.iter (fun logger ->
                    logger.LogInfo(sprintf "Configured %d test scenarios" finalTestConfig.Scenarios.Length)
                    for scenario in finalTestConfig.Scenarios do
                        logger.LogInfo(sprintf "  - %s: %d users, %d requests each" 
                            scenario.Name scenario.ConcurrentUsers scenario.RequestsPerUser)
                )
                
                // Run the performance tests
                context.Logger |> Option.iter (fun logger ->
                    logger.LogInfo("Starting performance test execution...")
                )
                
                let! testResult = testRunner.RunPerformanceTest(costAwareSystem)
                
                match testResult with
                | Ok results ->
                    context.Logger |> Option.iter (fun logger ->
                        logger.LogInfo("Performance tests completed successfully")
                        logger.LogInfo(sprintf "Total requests: %d" results.OverallMetrics.TotalRequests)
                        logger.LogInfo(sprintf "Success rate: %.1f%%" (results.OverallMetrics.OverallSuccessRate * 100.0))
                        logger.LogInfo(sprintf "Average response time: %.2fs" results.OverallMetrics.OverallAverageResponseTime.TotalSeconds)
                        logger.LogInfo(sprintf "Total cost: $%.2f" (float results.OverallMetrics.TotalTestCost))
                    )
                    
                    // Generate reports if requested
                    if context.Config.GenerateReports then
                        context.Logger |> Option.iter (fun logger ->
                            logger.LogInfo("Generating performance test reports...")
                        )
                        
                        let reportsResult = PerformanceReporting.saveTestReportPackage results context.OutputDirectory
                        
                        match reportsResult with
                        | Ok reportPackage ->
                            context.Logger |> Option.iter (fun logger ->
                                logger.LogInfo("Reports generated successfully:")
                                for (reportType, filePath) in reportPackage.Files do
                                    logger.LogInfo(sprintf "  - %s: %s" reportType filePath)
                            )
                            
                            // Print executive summary to console
                            let summary = PerformanceReporting.generateExecutiveSummary results
                            printfn "%s" summary
                            
                            return Ok {|
                                TestResults = results
                                ReportPackage = reportPackage
                                ExecutionContext = context
                            |}
                            
                        | Error reportError ->
                            context.Logger |> Option.iter (fun logger ->
                                logger.LogError(sprintf "Failed to generate reports: %s" reportError)
                            )
                            
                            return Ok {|
                                TestResults = results
                                ReportPackage = {| 
                                    OutputDirectory = context.OutputDirectory
                                    Files = Map.empty
                                    Summary = sprintf "Report generation failed: %s" reportError
                                |}
                                ExecutionContext = context
                            |}
                    else
                        return Ok {|
                            TestResults = results
                            ReportPackage = {| 
                                OutputDirectory = context.OutputDirectory
                                Files = Map.empty
                                Summary = "Report generation skipped"
                            |}
                            ExecutionContext = context
                        |}
                
                | Error testError ->
                    context.Logger |> Option.iter (fun logger ->
                        logger.LogError(sprintf "Performance tests failed: %s" testError)
                    )
                    
                    return Error (sprintf "Performance test execution failed: %s" testError)
                    
            with
            | ex ->
                context.Logger |> Option.iter (fun logger ->
                    logger.LogError(sprintf "Unexpected error during performance test execution: %s" ex.Message)
                    logger.LogError(sprintf "Stack trace: %s" ex.StackTrace)
                )
                
                return Error (sprintf "Unexpected error: %s" ex.Message)
        }
    
    /// Run quick performance test (for CI/CD)
    let runQuickTest (outputDir: string option) =
        async {
            let config = { 
                defaultExecutionConfig with 
                    TestProfile = Quick
                    OutputDirectory = defaultArg outputDir "./quick-perf-test-results"
                    VerboseLogging = false
            }
            
            let context = createExecutionContext config
            return! executePerformanceTests context
        }
    
    /// Run standard performance test
    let runStandardTest (outputDir: string option) =
        async {
            let config = { 
                defaultExecutionConfig with 
                    TestProfile = Standard
                    OutputDirectory = defaultArg outputDir "./standard-perf-test-results"
            }
            
            let context = createExecutionContext config
            return! executePerformanceTests context
        }
    
    /// Run stress test
    let runStressTest (outputDir: string option) =
        async {
            let config = { 
                defaultExecutionConfig with 
                    TestProfile = Stress
                    OutputDirectory = defaultArg outputDir "./stress-perf-test-results"
                    MaxDurationMinutes = Some 60
            }
            
            let context = createExecutionContext config
            return! executePerformanceTests context
        }
    
    /// Run endurance test
    let runEnduranceTest (outputDir: string option) =
        async {
            let config = { 
                defaultExecutionConfig with 
                    TestProfile = Endurance
                    OutputDirectory = defaultArg outputDir "./endurance-perf-test-results"
                    MaxDurationMinutes = Some 180
            }
            
            let context = createExecutionContext config
            return! executePerformanceTests context
        }
    
    /// Validate performance test results against targets
    let validatePerformanceTargets (results: TestResults) =
        let validationResults = ResizeArray<string * bool * string>()
        
        // Overall performance targets
        let overallSuccessRate = results.OverallMetrics.OverallSuccessRate
        validationResults.Add(("Overall Success Rate", overallSuccessRate >= 0.95, sprintf "%.1f%% (target: ≥95%%)" (overallSuccessRate * 100.0)))
        
        let avgResponseTime = results.OverallMetrics.OverallAverageResponseTime.TotalSeconds
        validationResults.Add(("Average Response Time", avgResponseTime <= 5.0, sprintf "%.2fs (target: ≤5s)" avgResponseTime))
        
        let costPerRequest = float results.OverallMetrics.CostPerRequest
        validationResults.Add(("Cost per Request", costPerRequest <= 0.02, sprintf "$%.4f (target: ≤$0.02)" costPerRequest))
        
        // Scenario-specific targets
        for scenario in results.Scenarios do
            let scenarioName = sprintf "Scenario '%s'" scenario.ScenarioName
            validationResults.Add((sprintf "%s Success Rate" scenarioName, scenario.SuccessRate >= 0.90, sprintf "%.1f%%" (scenario.SuccessRate * 100.0)))
            validationResults.Add((sprintf "%s Response Time" scenarioName, scenario.AverageResponseTime <= scenario.ExpectedResponseTime, sprintf "%.2fs" scenario.AverageResponseTime.TotalSeconds))
        
        // Resource utilization targets
        let memoryUsageMB = results.ResourceUtilization.PeakMemoryUsage / 1024L / 1024L
        validationResults.Add(("Peak Memory Usage", memoryUsageMB <= 1024L, sprintf "%d MB (target: ≤1024 MB)" memoryUsageMB))
        
        let cpuUsage = results.ResourceUtilization.PeakCpuUsage
        validationResults.Add(("Peak CPU Usage", cpuUsage <= 85.0, sprintf "%.1f%% (target: ≤85%%)" cpuUsage))
        
        let allTargetsMet = validationResults |> Seq.forall (fun (_, passed, _) -> passed)
        let passedCount = validationResults |> Seq.filter (fun (_, passed, _) -> passed) |> Seq.length
        let totalCount = validationResults.Count
        
        {|
            AllTargetsMet = allTargetsMet
            PassedTargets = passedCount
            TotalTargets = totalCount
            PassRate = float passedCount / float totalCount * 100.0
            ValidationResults = validationResults.ToArray() |> Array.toList
            Summary = sprintf "%d/%d targets met (%.1f%%)" passedCount totalCount (float passedCount / float totalCount * 100.0)
        |}
