namespace Automation.AI

open System
open System.Collections.Generic
open System.Security.Cryptography
open System.Text
open System.Text.Json
open System.Threading.Tasks
open Automation.Core

/// Comprehensive audit logging for AI decisions and actions
module AuditLogger =
    
    /// Audit event types
    type AuditEventType =
        | AIRequest
        | AIResponse
        | SecurityViolation
        | PIIDetection
        | PromptInjectionAttempt
        | ValidationFailure
        | RateLimitExceeded
        | ActionExecution
        | HealthCheck
        | ConfigurationChange
        | Error

    /// Audit log severity levels
    type AuditSeverity =
        | Info
        | Warning
        | Error
        | Critical

    /// Audit event data
    type AuditEvent = {
        EventId: Guid
        Timestamp: DateTimeOffset
        EventType: AuditEventType
        Severity: AuditSeverity
        UserId: string option
        SessionId: string option
        RequestId: string option
        Source: string
        Message: string
        Details: Map<string, obj>
        Hash: string option // For integrity verification
    }

    /// AI-specific audit data
    type AIAuditData = {
        ProviderId: string
        Model: string
        InputTokens: int option
        OutputTokens: int option
        ResponseTime: TimeSpan
        Success: bool
        ErrorMessage: string option
        Cost: decimal option
        ConfidenceScore: float option
    }

    /// Security-specific audit data
    type SecurityAuditData = {
        ViolationType: string
        BlockedContent: string option
        DetectedPatterns: string list
        SanitizationApplied: bool
        OriginalInput: string option
        SanitizedInput: string option
    }

    /// Action execution audit data
    type ActionAuditData = {
        ActionType: string
        Target: string option
        Parameters: Map<string, string>
        Success: bool
        ExecutionTime: TimeSpan
        ErrorMessage: string option
    }

    /// Audit storage interface
    type IAuditStorage =
        abstract member WriteEvent: AuditEvent -> Async<Result<unit, string>>
        abstract member WriteEvents: AuditEvent list -> Async<Result<unit, string>>
        abstract member QueryEvents: startTime: DateTimeOffset option * endTime: DateTimeOffset option * eventTypes: AuditEventType list * userId: string option -> Async<Result<AuditEvent list, string>>
        abstract member VerifyIntegrity: AuditEvent -> Async<Result<bool, string>>

    /// Calculate hash for audit event integrity
    let calculateEventHash (event: AuditEvent) =
        let eventWithoutHash = { event with Hash = None }
        let json = JsonSerializer.Serialize(eventWithoutHash, JsonSerializerOptions(WriteIndented = false))
        use sha256 = SHA256.Create()
        let hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(json))
        Convert.ToBase64String(hashBytes)

    /// File-based audit storage implementation
    type FileAuditStorage(logDirectory: string) =
        let getLogFilePath (timestamp: DateTimeOffset) =
            let fileName = timestamp.ToString("yyyy-MM-dd") + "-audit.jsonl"
            System.IO.Path.Combine(logDirectory, fileName)
        
        let ensureDirectoryExists () =
            if not (System.IO.Directory.Exists(logDirectory)) then
                System.IO.Directory.CreateDirectory(logDirectory) |> ignore
        
        interface IAuditStorage with
            member _.WriteEvent(event: AuditEvent) =
                async {
                    try
                        ensureDirectoryExists()
                        let filePath = getLogFilePath event.Timestamp
                        let json = JsonSerializer.Serialize(event, JsonSerializerOptions(WriteIndented = false))
                        do! System.IO.File.AppendAllTextAsync(filePath, json + "\n") |> Async.AwaitTask
                        return Ok ()
                    with
                    | ex -> return Result.Error $"Failed to write audit event: {ex.Message}"
                }
            
            member _.WriteEvents(events: AuditEvent list) =
                async {
                    try
                        ensureDirectoryExists()
                        let groupedByDate = 
                            events 
                            |> List.groupBy (fun e -> e.Timestamp.Date)
                        
                        for (date, dayEvents) in groupedByDate do
                            let filePath = getLogFilePath (DateTimeOffset(date))
                            let jsonLines = 
                                dayEvents
                                |> List.map (fun e -> JsonSerializer.Serialize(e, JsonSerializerOptions(WriteIndented = false)))
                                |> String.concat "\n"
                            do! System.IO.File.AppendAllTextAsync(filePath, jsonLines + "\n") |> Async.AwaitTask
                        
                        return Ok ()
                    with
                    | ex -> return Result.Error $"Failed to write audit events: {ex.Message}"
                }
            
            member _.QueryEvents(startTime: DateTimeOffset option, endTime: DateTimeOffset option, eventTypes: AuditEventType list, userId: string option) =
                async {
                    try
                        let events = List<AuditEvent>()
                        let startDate = defaultArg startTime (DateTimeOffset.Now.AddDays(-30.0))
                        let endDate = defaultArg endTime DateTimeOffset.Now
                        
                        let mutable currentDate = startDate.Date
                        while currentDate <= endDate.Date do
                            let filePath = getLogFilePath (DateTimeOffset(currentDate))
                            if System.IO.File.Exists(filePath) then
                                let! lines = System.IO.File.ReadAllLinesAsync(filePath) |> Async.AwaitTask
                                for line in lines do
                                    if not (String.IsNullOrWhiteSpace(line)) then
                                        try
                                            let event = JsonSerializer.Deserialize<AuditEvent>(line)
                                            let matchesTimeRange = event.Timestamp >= startDate && event.Timestamp <= endDate
                                            let matchesEventType = eventTypes.IsEmpty || eventTypes |> List.contains event.EventType
                                            let matchesUser = 
                                                match userId with
                                                | None -> true
                                                | Some uid -> event.UserId = Some uid
                                            
                                            if matchesTimeRange && matchesEventType && matchesUser then
                                                events.Add(event)
                                        with
                                        | ex -> () // Skip malformed lines
                            currentDate <- currentDate.AddDays(1.0)
                        
                        return Ok(List.ofSeq events)
                    with
                    | ex -> return Result.Error ($"Failed to query audit events: {ex.Message}")
                }
            
            member _.VerifyIntegrity(event: AuditEvent) =
                async {
                    try
                        let calculatedHash = calculateEventHash event
                        let isValid = 
                            match event.Hash with
                            | Some hash -> hash = calculatedHash
                            | None -> false
                        return Ok(isValid)
                    with
                    | ex -> return Result.Error ($"Failed to verify integrity: {ex.Message}")
                }


    /// Audit logger configuration
    type AuditConfig = {
        Storage: IAuditStorage
        EnableIntegrityChecking: bool
        BatchSize: int
        FlushIntervalMs: int
        MaxRetries: int
    }

    /// Main audit logger
    type AuditLogger(config: AuditConfig) =
        let eventQueue = Queue<AuditEvent>()
        let queueLock = obj()
        
        let flushEvents () =
            async {
                let eventsToFlush = 
                    lock queueLock (fun () ->
                        let events = List.ofSeq eventQueue
                        eventQueue.Clear()
                        events
                    )
                
                if not eventsToFlush.IsEmpty then
                    let! result = config.Storage.WriteEvents(eventsToFlush)
                    match result with
                    | Ok () -> ()
                    | Result.Error err -> 
                        // Re-queue events for retry (simplified implementation)
                        lock queueLock (fun () ->
                            for event in eventsToFlush do
                                eventQueue.Enqueue(event)
                        )
            }
        
        // Background task to flush events periodically
        let flushTimer = new System.Threading.Timer(
            (fun _ -> flushEvents() |> Async.Start),
            null,
            config.FlushIntervalMs,
            config.FlushIntervalMs
        )
        
        member _.LogEvent(eventType: AuditEventType, severity: AuditSeverity, source: string, message: string, ?details: Map<string, obj>, ?userId: string, ?sessionId: string, ?requestId: string) =
            let event = {
                EventId = Guid.NewGuid()
                Timestamp = DateTimeOffset.Now
                EventType = eventType
                Severity = severity
                UserId = userId
                SessionId = sessionId
                RequestId = requestId
                Source = source
                Message = message
                Details = defaultArg details Map.empty
                Hash = None
            }
            
            let eventWithHash = 
                if config.EnableIntegrityChecking then
                    { event with Hash = Some(calculateEventHash event) }
                else
                    event
            
            lock queueLock (fun () ->
                eventQueue.Enqueue(eventWithHash)
                if eventQueue.Count >= config.BatchSize then
                    flushEvents() |> Async.Start
            )
        
        member self.LogAIRequest(request: AIFramework.AIRequest, auditData: AIAuditData, ?userId: string, ?sessionId: string, ?requestId: string) =
            let details = 
                Map.ofList [
                    ("providerId", box auditData.ProviderId)
                    ("model", box auditData.Model)
                    ("promptLength", box request.Prompt.Length)
                    ("hasSystemMessage", box request.SystemMessage.IsSome)
                    ("hasImages", box request.Images.IsSome)
                    ("inputTokens", box auditData.InputTokens)
                    ("responseTime", box auditData.ResponseTime.TotalMilliseconds)
                    ("success", box auditData.Success)
                    ("cost", box auditData.Cost)
                ]
            
            let message = $"AI request to {auditData.ProviderId} using model {auditData.Model}"
            let severity = if auditData.Success then Info else Error
            
            self.LogEvent(AIRequest, severity, "AIFramework", message, details, ?userId = userId, ?sessionId = sessionId, ?requestId = requestId)
        
        member self.LogAIResponse(response: AIFramework.AIResponse, auditData: AIAuditData, ?userId: string, ?sessionId: string, ?requestId: string) =
            let details = 
                Map.ofList [
                    ("providerId", box (response.ProviderId.ToString()))
                    ("model", box response.Model)
                    ("responseLength", box response.Content.Length)
                    ("outputTokens", box auditData.OutputTokens)
                    ("responseTime", box response.ResponseTime.TotalMilliseconds)
                    ("success", box auditData.Success)
                    ("cost", box auditData.Cost)
                    ("confidenceScore", box auditData.ConfidenceScore)
                ]
            
            let message = $"AI response from {response.ProviderId} with {response.Content.Length} characters"
            
            self.LogEvent(AIResponse, Info, "AIFramework", message, details, ?userId = userId, ?sessionId = sessionId, ?requestId = requestId)
        
        member self.LogSecurityViolation(violationType: string, securityData: SecurityAuditData, ?userId: string, ?sessionId: string, ?requestId: string) =
            let details = 
                Map.ofList [
                    ("violationType", box securityData.ViolationType)
                    ("detectedPatterns", box securityData.DetectedPatterns)
                    ("sanitizationApplied", box securityData.SanitizationApplied)
                    ("hasBlockedContent", box securityData.BlockedContent.IsSome)
                ]
            
            let message = $"Security violation detected: {violationType}"
            
            self.LogEvent(SecurityViolation, Warning, "SecurityValidator", message, details, ?userId = userId, ?sessionId = sessionId, ?requestId = requestId)
        
        member self.LogPIIDetection(piiType: string, occurrences: int, ?userId: string, ?sessionId: string, ?requestId: string) =
            let details = 
                Map.ofList [
                    ("piiType", box piiType)
                    ("occurrences", box occurrences)
                ]
            
            let message = $"PII detected and masked: {piiType} ({occurrences} occurrences)"
            
            self.LogEvent(PIIDetection, Warning, "SecurityValidator", message, details, ?userId = userId, ?sessionId = sessionId, ?requestId = requestId)
        
        member self.LogActionExecution(actionType: string, actionData: ActionAuditData, ?userId: string, ?sessionId: string, ?requestId: string) =
            let details = 
                Map.ofList [
                    ("actionType", box actionData.ActionType)
                    ("target", box actionData.Target)
                    ("parametersCount", box actionData.Parameters.Count)
                    ("success", box actionData.Success)
                    ("executionTime", box actionData.ExecutionTime.TotalMilliseconds)
                    ("hasError", box actionData.ErrorMessage.IsSome)
                ]
            
            let message = $"Action executed: {actionType}"
            let severity = if actionData.Success then Info else Error
            
            self.LogEvent(ActionExecution, severity, "ActionExecutor", message, details, ?userId = userId, ?sessionId = sessionId, ?requestId = requestId)
        
        member _.FlushAsync() =
            flushEvents()
        
        interface IDisposable with
            member _.Dispose() =
                flushTimer.Dispose()
                flushEvents() |> Async.RunSynchronously

    /// Create default audit logger with file storage
    let createFileAuditLogger (logDirectory: string) =
        let storage = FileAuditStorage(logDirectory) :> IAuditStorage
        let config = {
            Storage = storage
            EnableIntegrityChecking = true
            BatchSize = 100
            FlushIntervalMs = 5000
            MaxRetries = 3
        }
        new AuditLogger(config)