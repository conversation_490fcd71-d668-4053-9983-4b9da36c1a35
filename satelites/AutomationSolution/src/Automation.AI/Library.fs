namespace Automation.AI

open System
open System.Collections.Generic
open System.Threading.Tasks
open Automation.Core
open Automation.Data

/// AI Provider types and interfaces for multi-provider framework
module AIFramework =
    
    /// Supported AI model types
    type ModelType =
        | TextCompletion
        | TextChat
        | Vision
        | Embedding
    
    /// AI Provider identification
    type ProviderId =
        | OpenAI
        | Anthropic
        | GoogleGemini
        | AzureOpenAI
        | Ollama
        | OpenRouter
        | Custom of string
    
    /// Provider health status
    type ProviderHealth =
        | Healthy
        | Degraded of reason: string
        | Unhealthy of reason: string
        | Unknown
    
    /// AI request configuration
    type AIRequest = {
        Prompt: string
        MaxTokens: int option
        Temperature: float option
        Model: string option
        SystemMessage: string option
        Images: byte[] list option // For vision models
        Metadata: Map<string, string>
    }
    
    /// AI response from provider
    type AIResponse = {
        Content: string
        TokensUsed: int option
        Model: string
        ProviderId: ProviderId
        ResponseTime: TimeSpan
        Metadata: Map<string, string>
    }
    
    /// Provider configuration
    type ProviderConfig = {
        ProviderId: ProviderId
        ApiKey: string
        BaseUrl: string option
        DefaultModel: string
        MaxRetries: int
        TimeoutMs: int
        RateLimitRpm: int option
        Priority: int // Lower number = higher priority
        Enabled: bool
        HealthCheckInterval: TimeSpan
    }
    
    /// Provider statistics
    type ProviderStats = {
        RequestCount: int64
        SuccessCount: int64
        FailureCount: int64
        AverageResponseTime: TimeSpan
        LastUsed: DateTime option
        LastHealthCheck: DateTime option
        CurrentHealth: ProviderHealth
    }
    
    /// Interface for AI providers
    type IAIProvider =
        abstract member ProviderId: ProviderId
        abstract member Config: ProviderConfig
        abstract member Stats: ProviderStats
        abstract member IsHealthy: unit -> bool
        abstract member CheckHealth: unit -> Async<ProviderHealth>
        abstract member GenerateResponse: AIRequest -> Async<Result<AIResponse, string>>
        abstract member SupportedModels: ModelType list
    
    /// Provider selection strategy
    type ProviderSelectionStrategy =
        | ByPriority
        | RoundRobin
        | LeastUsed
        | HealthBased
        | CostOptimized
    
    /// Provider manager interface
    type IProviderManager =
        abstract member RegisterProvider: IAIProvider -> unit
        abstract member GetProvider: ProviderId -> IAIProvider option
        abstract member GetAvailableProviders: unit -> IAIProvider list
        abstract member SelectProvider: ProviderSelectionStrategy -> ModelType -> IAIProvider option
        abstract member GetProviderStats: ProviderId -> ProviderStats option
        abstract member UpdateProviderHealth: ProviderId -> ProviderHealth -> unit
        abstract member ProcessRequest: AIRequest -> Async<Result<AIResponse, string>>
    
    /// OpenAI Provider Implementation
    type OpenAIProvider(config: ProviderConfig) =
        let mutable stats = {
            RequestCount = 0L
            SuccessCount = 0L
            FailureCount = 0L
            AverageResponseTime = TimeSpan.Zero
            LastUsed = None
            LastHealthCheck = None
            CurrentHealth = Unknown
        }
        
        interface IAIProvider with
            member _.ProviderId = config.ProviderId
            member _.Config = config
            member _.Stats = stats
            member _.IsHealthy() = stats.CurrentHealth = Healthy
            member _.CheckHealth() = 
                async {
                    // Simple health check - in real implementation would ping API
                    stats <- { stats with LastHealthCheck = Some DateTime.UtcNow; CurrentHealth = Healthy }
                    return Healthy
                }
            member _.GenerateResponse(request: AIRequest) = 
                async {
                    try
                        let startTime = DateTime.UtcNow
                        
                        // Mock response - in real implementation would call OpenAI API
                        let response = {
                            Content = "Mock response from OpenAI"
                            TokensUsed = Some 150
                            Model = request.Model |> Option.defaultValue config.DefaultModel
                            ProviderId = config.ProviderId
                            ResponseTime = DateTime.UtcNow - startTime
                            Metadata = Map.empty
                        }
                        
                        stats <- { stats with 
                                    RequestCount = stats.RequestCount + 1L
                                    SuccessCount = stats.SuccessCount + 1L
                                    LastUsed = Some DateTime.UtcNow }
                        
                        return Ok response
                    with
                    | ex ->
                        stats <- { stats with 
                                    RequestCount = stats.RequestCount + 1L
                                    FailureCount = stats.FailureCount + 1L }
                        return Error $"OpenAI API error: {ex.Message}"
                }
            member _.SupportedModels = [TextCompletion; TextChat; Vision]
    
    /// Anthropic Provider Implementation
    type AnthropicProvider(config: ProviderConfig) =
        let mutable stats = {
            RequestCount = 0L
            SuccessCount = 0L
            FailureCount = 0L
            AverageResponseTime = TimeSpan.Zero
            LastUsed = None
            LastHealthCheck = None
            CurrentHealth = Unknown
        }
        
        interface IAIProvider with
            member _.ProviderId = config.ProviderId
            member _.Config = config
            member _.Stats = stats
            member _.IsHealthy() = stats.CurrentHealth = Healthy
            member _.CheckHealth() = 
                async {
                    stats <- { stats with LastHealthCheck = Some DateTime.UtcNow; CurrentHealth = Healthy }
                    return Healthy
                }
            member _.GenerateResponse(request: AIRequest) = 
                async {
                    try
                        let startTime = DateTime.UtcNow
                        
                        let response = {
                            Content = "Mock response from Anthropic Claude"
                            TokensUsed = Some 120
                            Model = request.Model |> Option.defaultValue config.DefaultModel
                            ProviderId = config.ProviderId
                            ResponseTime = DateTime.UtcNow - startTime
                            Metadata = Map.empty
                        }
                        
                        stats <- { stats with 
                                    RequestCount = stats.RequestCount + 1L
                                    SuccessCount = stats.SuccessCount + 1L
                                    LastUsed = Some DateTime.UtcNow }
                        
                        return Ok response
                    with
                    | ex ->
                        stats <- { stats with 
                                    RequestCount = stats.RequestCount + 1L
                                    FailureCount = stats.FailureCount + 1L }
                        return Error $"Anthropic API error: {ex.Message}"
                }
            member _.SupportedModels = [TextCompletion; TextChat]
    
    /// Google Gemini Provider Implementation
    type GoogleGeminiProvider(config: ProviderConfig) =
        let mutable stats = {
            RequestCount = 0L
            SuccessCount = 0L
            FailureCount = 0L
            AverageResponseTime = TimeSpan.Zero
            LastUsed = None
            LastHealthCheck = None
            CurrentHealth = Unknown
        }
        
        interface IAIProvider with
            member _.ProviderId = config.ProviderId
            member _.Config = config
            member _.Stats = stats
            member _.IsHealthy() = stats.CurrentHealth = Healthy
            member _.CheckHealth() = 
                async {
                    stats <- { stats with LastHealthCheck = Some DateTime.UtcNow; CurrentHealth = Healthy }
                    return Healthy
                }
            member _.GenerateResponse(request: AIRequest) = 
                async {
                    try
                        let startTime = DateTime.UtcNow
                        
                        let response = {
                            Content = "Mock response from Google Gemini"
                            TokensUsed = Some 100
                            Model = request.Model |> Option.defaultValue config.DefaultModel
                            ProviderId = config.ProviderId
                            ResponseTime = DateTime.UtcNow - startTime
                            Metadata = Map.empty
                        }
                        
                        stats <- { stats with 
                                    RequestCount = stats.RequestCount + 1L
                                    SuccessCount = stats.SuccessCount + 1L
                                    LastUsed = Some DateTime.UtcNow }
                        
                        return Ok response
                    with
                    | ex ->
                        stats <- { stats with 
                                    RequestCount = stats.RequestCount + 1L
                                    FailureCount = stats.FailureCount + 1L }
                        return Error $"Google Gemini API error: {ex.Message}"
                }
            member _.SupportedModels = [TextCompletion; TextChat; Vision]
    
    /// Azure OpenAI Provider Implementation
    type AzureOpenAIProvider(config: ProviderConfig) =
        let mutable stats = {
            RequestCount = 0L
            SuccessCount = 0L
            FailureCount = 0L
            AverageResponseTime = TimeSpan.Zero
            LastUsed = None
            LastHealthCheck = None
            CurrentHealth = Unknown
        }
        
        interface IAIProvider with
            member _.ProviderId = config.ProviderId
            member _.Config = config
            member _.Stats = stats
            member _.IsHealthy() = stats.CurrentHealth = Healthy
            member _.CheckHealth() = 
                async {
                    stats <- { stats with LastHealthCheck = Some DateTime.UtcNow; CurrentHealth = Healthy }
                    return Healthy
                }
            member _.GenerateResponse(request: AIRequest) = 
                async {
                    try
                        let startTime = DateTime.UtcNow
                        
                        let response = {
                            Content = "Mock response from Azure OpenAI"
                            TokensUsed = Some 140
                            Model = request.Model |> Option.defaultValue config.DefaultModel
                            ProviderId = config.ProviderId
                            ResponseTime = DateTime.UtcNow - startTime
                            Metadata = Map.empty
                        }
                        
                        stats <- { stats with 
                                    RequestCount = stats.RequestCount + 1L
                                    SuccessCount = stats.SuccessCount + 1L
                                    LastUsed = Some DateTime.UtcNow }
                        
                        return Ok response
                    with
                    | ex ->
                        stats <- { stats with 
                                    RequestCount = stats.RequestCount + 1L
                                    FailureCount = stats.FailureCount + 1L }
                        return Error $"Azure OpenAI API error: {ex.Message}"
                }
            member _.SupportedModels = [TextCompletion; TextChat; Vision]
    
    /// Ollama Provider Implementation
    type OllamaProvider(config: ProviderConfig) =
        let mutable stats = {
            RequestCount = 0L
            SuccessCount = 0L
            FailureCount = 0L
            AverageResponseTime = TimeSpan.Zero
            LastUsed = None
            LastHealthCheck = None
            CurrentHealth = Unknown
        }
        
        interface IAIProvider with
            member _.ProviderId = config.ProviderId
            member _.Config = config
            member _.Stats = stats
            member _.IsHealthy() = stats.CurrentHealth = Healthy
            member _.CheckHealth() = 
                async {
                    stats <- { stats with LastHealthCheck = Some DateTime.UtcNow; CurrentHealth = Healthy }
                    return Healthy
                }
            member _.GenerateResponse(request: AIRequest) = 
                async {
                    try
                        let startTime = DateTime.UtcNow
                        
                        let response = {
                            Content = "Mock response from Ollama local LLM"
                            TokensUsed = Some 80
                            Model = request.Model |> Option.defaultValue config.DefaultModel
                            ProviderId = config.ProviderId
                            ResponseTime = DateTime.UtcNow - startTime
                            Metadata = Map.empty
                        }
                        
                        stats <- { stats with 
                                    RequestCount = stats.RequestCount + 1L
                                    SuccessCount = stats.SuccessCount + 1L
                                    LastUsed = Some DateTime.UtcNow }
                        
                        return Ok response
                    with
                    | ex ->
                        stats <- { stats with 
                                    RequestCount = stats.RequestCount + 1L
                                    FailureCount = stats.FailureCount + 1L }
                        return Error $"Ollama API error: {ex.Message}"
                }
            member _.SupportedModels = [TextCompletion; TextChat]
    
    /// OpenRouter Provider Implementation
    type OpenRouterProvider(config: ProviderConfig) =
        let mutable stats = {
            RequestCount = 0L
            SuccessCount = 0L
            FailureCount = 0L
            AverageResponseTime = TimeSpan.Zero
            LastUsed = None
            LastHealthCheck = None
            CurrentHealth = Unknown
        }
        
        interface IAIProvider with
            member _.ProviderId = config.ProviderId
            member _.Config = config
            member _.Stats = stats
            member _.IsHealthy() = stats.CurrentHealth = Healthy
            member _.CheckHealth() = 
                async {
                    stats <- { stats with LastHealthCheck = Some DateTime.UtcNow; CurrentHealth = Healthy }
                    return Healthy
                }
            member _.GenerateResponse(request: AIRequest) = 
                async {
                    try
                        let startTime = DateTime.UtcNow
                        
                        let response = {
                            Content = "Mock response from OpenRouter (multi-model gateway)"
                            TokensUsed = Some 110
                            Model = request.Model |> Option.defaultValue config.DefaultModel
                            ProviderId = config.ProviderId
                            ResponseTime = DateTime.UtcNow - startTime
                            Metadata = Map.empty
                        }
                        
                        stats <- { stats with 
                                    RequestCount = stats.RequestCount + 1L
                                    SuccessCount = stats.SuccessCount + 1L
                                    LastUsed = Some DateTime.UtcNow }
                        
                        return Ok response
                    with
                    | ex ->
                        stats <- { stats with 
                                    RequestCount = stats.RequestCount + 1L
                                    FailureCount = stats.FailureCount + 1L }
                        return Error $"OpenRouter API error: {ex.Message}"
                }
            member _.SupportedModels = [TextCompletion; TextChat; Vision; Embedding]
    
    /// Rate limiting configuration
    type RateLimitConfig = {
        RequestsPerMinute: int
        RequestsPerHour: int
        ConcurrentRequests: int
        BackoffStrategy: BackoffStrategy
    }
    
    /// Backoff strategies for rate limiting
    and BackoffStrategy =
        | Linear of intervalMs: int
        | Exponential of baseMs: int * multiplier: float
        | Fixed of delayMs: int
    
    /// Rate limiter for provider requests
    type RateLimiter(config: RateLimitConfig) =
        let mutable lastRequest = DateTime.MinValue
        let mutable requestCount = 0
        
        member _.CanMakeRequest() : bool =
            let now = DateTime.UtcNow
            let timeSinceLastRequest = now - lastRequest
            
            // Simple rate limiting logic
            if timeSinceLastRequest.TotalMinutes >= 1.0 then
                requestCount <- 0
                true
            else
                requestCount < config.RequestsPerMinute
        
        member _.RecordRequest() =
            lastRequest <- DateTime.UtcNow
            requestCount <- requestCount + 1
        
        member _.GetBackoffDelay(attemptCount: int) : TimeSpan =
            match config.BackoffStrategy with
            | Linear intervalMs -> TimeSpan.FromMilliseconds(float (intervalMs * attemptCount))
            | Exponential (baseMs, multiplier) -> 
                let delay = float baseMs * Math.Pow(multiplier, float attemptCount)
                TimeSpan.FromMilliseconds(delay)
            | Fixed delayMs -> TimeSpan.FromMilliseconds(float delayMs)
    
    /// Provider manager implementation
    type ProviderManager() =
        let providers = new Dictionary<ProviderId, IAIProvider>()
        let rateLimiters = new Dictionary<ProviderId, RateLimiter>()
        
        interface IProviderManager with
            member _.RegisterProvider(provider: IAIProvider) =
                providers.[provider.ProviderId] <- provider
                
                // Create rate limiter for provider
                let rateLimitConfig = {
                    RequestsPerMinute = provider.Config.RateLimitRpm |> Option.defaultValue 60
                    RequestsPerHour = provider.Config.RateLimitRpm |> Option.defaultValue 60 |> (*) 60
                    ConcurrentRequests = 5
                    BackoffStrategy = Exponential (1000, 2.0)
                }
                rateLimiters.[provider.ProviderId] <- RateLimiter(rateLimitConfig)
            
            member _.GetProvider(providerId: ProviderId) =
                providers.TryGetValue(providerId) |> function
                | (true, provider) -> Some provider
                | _ -> None
            
            member _.GetAvailableProviders() =
                providers.Values |> Seq.toList
            
            member _.SelectProvider(strategy: ProviderSelectionStrategy) (modelType: ModelType) =
                let availableProviders = 
                    providers.Values 
                    |> Seq.filter (fun p -> p.IsHealthy() && p.SupportedModels |> List.contains modelType)
                    |> Seq.toList
                
                match strategy with
                | ByPriority -> 
                    availableProviders 
                    |> List.sortBy (fun p -> p.Config.Priority)
                    |> List.tryHead
                | RoundRobin -> 
                    // Simple round-robin (in real implementation would track state)
                    availableProviders |> List.tryHead
                | LeastUsed -> 
                    availableProviders 
                    |> List.sortBy (fun p -> p.Stats.RequestCount)
                    |> List.tryHead
                | HealthBased -> 
                    availableProviders 
                    |> List.filter (fun p -> p.Stats.CurrentHealth = Healthy)
                    |> List.tryHead
                | CostOptimized -> 
                    // Simple cost optimization (prefer local models, then OpenRouter for variety)
                    availableProviders 
                    |> List.tryFind (fun p -> p.ProviderId = Ollama)
                    |> Option.orElse (availableProviders |> List.tryFind (fun p -> p.ProviderId = OpenRouter))
                    |> Option.orElse (List.tryHead availableProviders)
            
            member _.GetProviderStats(providerId: ProviderId) =
                match providers.TryGetValue(providerId) with
                | (true, provider) -> Some provider.Stats
                | _ -> None
            
            member _.UpdateProviderHealth(providerId: ProviderId) (health: ProviderHealth) =
                match providers.TryGetValue(providerId) with
                | (true, provider) -> 
                    // Update health in provider stats (would need mutable stats)
                    ()
                | _ -> ()
            
            member _.ProcessRequest(request: AIRequest) =
                async {
                    let modelType = 
                        match request.Images with
                        | Some _ -> Vision
                        | None -> TextChat
                    
                    let providerOption = 
                        (providers.Values |> Seq.filter (fun p -> p.IsHealthy() && p.SupportedModels |> List.contains modelType) |> Seq.tryHead)
                    
                    match providerOption with
                    | Some provider ->
                        // Check rate limiting
                        match rateLimiters.TryGetValue(provider.ProviderId) with
                        | (true, rateLimiter) when rateLimiter.CanMakeRequest() ->
                            rateLimiter.RecordRequest()
                            return! provider.GenerateResponse(request)
                        | (true, rateLimiter) ->
                            return Error "Rate limit exceeded"
                        | _ ->
                            return! provider.GenerateResponse(request)
                    | None ->
                        return Error "No available provider for request"
                }
    
    /// AI orchestrator for managing providers and requests
    type AIOrchestrator(providerManager: IProviderManager) =
        
        member _.ProcessRequest(request: AIRequest) : Async<Result<AIResponse, string>> =
            providerManager.ProcessRequest(request)
        
        member _.ProcessRequestWithFailover(request: AIRequest) : Async<Result<AIResponse, string>> =
            async {
                let providers = providerManager.GetAvailableProviders()
                
                let rec tryProviders remainingProviders =
                    async {
                        match remainingProviders with
                        | [] -> return Error "All providers failed"
                        | provider :: rest ->
                            let! result = (provider :> IAIProvider).GenerateResponse(request)
                            match result with
                            | Ok response -> return Ok response
                            | Error _ -> return! tryProviders rest
                    }
                
                return! tryProviders providers
            }
        
        member _.GetSystemStatus() : Map<ProviderId, ProviderHealth> =
            providerManager.GetAvailableProviders()
            |> List.map (fun p -> (p.ProviderId, p.Stats.CurrentHealth))
            |> Map.ofList
        
        member _.GetProviderStats() : Map<ProviderId, ProviderStats> =
            providerManager.GetAvailableProviders()
            |> List.map (fun p -> (p.ProviderId, p.Stats))
            |> Map.ofList
        
        member _.RegisterProvider(provider: IAIProvider) : unit =
            providerManager.RegisterProvider(provider)
        
        member _.GetProvider(providerId: ProviderId) : IAIProvider option =
            providerManager.GetProvider(providerId)
        
        member _.GetAvailableProviders() : IAIProvider list =
            providerManager.GetAvailableProviders()
        
        member _.SelectProvider(strategy: ProviderSelectionStrategy) (modelType: ModelType) : IAIProvider option =
            providerManager.SelectProvider(strategy) modelType
        
        member _.GetProviderStats(providerId: ProviderId) : ProviderStats option =
            providerManager.GetProviderStats(providerId)
        
        member _.CheckAllProvidersHealth() : Async<Map<ProviderId, ProviderHealth>> =
            async {
                let providers = providerManager.GetAvailableProviders()
                let! healthResults = 
                    providers
                    |> List.map (fun p -> 
                        async {
                            let! health = p.CheckHealth()
                            return (p.ProviderId, health)
                        })
                    |> Async.Parallel
                
                return healthResults |> Map.ofArray
            }
    
    /// AI orchestrator interface  
    type IAIOrchestrator =
        abstract member ProcessRequest: AIRequest -> Async<Result<AIResponse, string>>
        abstract member ProcessWithFallback: AIRequest -> ProviderId list -> Async<Result<AIResponse, string>>
        abstract member GetSystemHealth: unit -> Async<Map<ProviderId, ProviderHealth>>

/// AI Framework Configuration Module
module AIConfig =
    
    open AIFramework
    
    /// Configuration for the AI framework
    type AIFrameworkConfig = {
        CacheConfig: CacheConfig
        ProviderConfigs: ProviderConfig list
        DefaultStrategy: ProviderSelectionStrategy
        EnableHealthMonitoring: bool
        HealthCheckInterval: TimeSpan
    }
    
    /// Configuration for cache behavior
    and CacheConfig = {
        RedisConnectionString: string
        EnableCaching: bool
        DefaultOrganization: string
        DefaultTarget: string
    }
    
    /// Helper to get environment variable with default
    let private getEnvVar name defaultValue =
        match System.Environment.GetEnvironmentVariable(name) with
        | null | "" -> defaultValue
        | value -> value
    
    /// Helper to get environment variable as int with default
    let private getEnvVarInt name defaultValue =
        match System.Environment.GetEnvironmentVariable(name) with
        | null | "" -> defaultValue
        | value -> 
            match System.Int32.TryParse(value) with
            | (true, parsed) -> parsed
            | _ -> defaultValue
    
    /// Create OpenAI provider configuration from environment variables
    let createOpenAIConfig () = {
        ProviderId = OpenAI
        ApiKey = getEnvVar "OPENAI_API_KEY" ""
        BaseUrl = Some (getEnvVar "OPENAI_BASE_URL" "https://api.openai.com")
        DefaultModel = getEnvVar "OPENAI_DEFAULT_MODEL" "gpt-3.5-turbo"
        MaxRetries = getEnvVarInt "OPENAI_MAX_RETRIES" 3
        TimeoutMs = getEnvVarInt "OPENAI_TIMEOUT_MS" 30000
        RateLimitRpm = Some (getEnvVarInt "OPENAI_RATE_LIMIT_RPM" 3000)
        Priority = getEnvVarInt "OPENAI_PRIORITY" 1
        Enabled = getEnvVar "OPENAI_ENABLED" "true" = "true"
        HealthCheckInterval = TimeSpan.FromMinutes(5.0)
    }
    
    /// Create Anthropic provider configuration from environment variables
    let createAnthropicConfig () = {
        ProviderId = Anthropic
        ApiKey = getEnvVar "ANTHROPIC_API_KEY" ""
        BaseUrl = Some (getEnvVar "ANTHROPIC_BASE_URL" "https://api.anthropic.com")
        DefaultModel = getEnvVar "ANTHROPIC_DEFAULT_MODEL" "claude-3-sonnet-20240229"
        MaxRetries = getEnvVarInt "ANTHROPIC_MAX_RETRIES" 3
        TimeoutMs = getEnvVarInt "ANTHROPIC_TIMEOUT_MS" 30000
        RateLimitRpm = Some (getEnvVarInt "ANTHROPIC_RATE_LIMIT_RPM" 1000)
        Priority = getEnvVarInt "ANTHROPIC_PRIORITY" 2
        Enabled = getEnvVar "ANTHROPIC_ENABLED" "true" = "true"
        HealthCheckInterval = TimeSpan.FromMinutes(5.0)
    }
    
    /// Create Google Gemini provider configuration from environment variables
    let createGoogleGeminiConfig () = {
        ProviderId = GoogleGemini
        ApiKey = getEnvVar "GOOGLE_API_KEY" ""
        BaseUrl = Some (getEnvVar "GOOGLE_BASE_URL" "https://generativelanguage.googleapis.com")
        DefaultModel = getEnvVar "GOOGLE_DEFAULT_MODEL" "gemini-pro"
        MaxRetries = getEnvVarInt "GOOGLE_MAX_RETRIES" 3
        TimeoutMs = getEnvVarInt "GOOGLE_TIMEOUT_MS" 30000
        RateLimitRpm = Some (getEnvVarInt "GOOGLE_RATE_LIMIT_RPM" 1500)
        Priority = getEnvVarInt "GOOGLE_PRIORITY" 3
        Enabled = getEnvVar "GOOGLE_ENABLED" "true" = "true"
        HealthCheckInterval = TimeSpan.FromMinutes(5.0)
    }
    
    /// Create Azure OpenAI provider configuration from environment variables
    let createAzureOpenAIConfig () = {
        ProviderId = AzureOpenAI
        ApiKey = getEnvVar "AZURE_OPENAI_API_KEY" ""
        BaseUrl = Some (getEnvVar "AZURE_OPENAI_BASE_URL" "https://your-resource.openai.azure.com")
        DefaultModel = getEnvVar "AZURE_OPENAI_DEFAULT_MODEL" "gpt-35-turbo"
        MaxRetries = getEnvVarInt "AZURE_OPENAI_MAX_RETRIES" 3
        TimeoutMs = getEnvVarInt "AZURE_OPENAI_TIMEOUT_MS" 30000
        RateLimitRpm = Some (getEnvVarInt "AZURE_OPENAI_RATE_LIMIT_RPM" 2000)
        Priority = getEnvVarInt "AZURE_OPENAI_PRIORITY" 4
        Enabled = getEnvVar "AZURE_OPENAI_ENABLED" "true" = "true"
        HealthCheckInterval = TimeSpan.FromMinutes(5.0)
    }
    
    /// Create Ollama provider configuration from environment variables
    let createOllamaConfig () = {
        ProviderId = Ollama
        ApiKey = getEnvVar "OLLAMA_API_KEY" "" // Ollama doesn't require API key by default
        BaseUrl = Some (getEnvVar "OLLAMA_BASE_URL" "http://localhost:11434")
        DefaultModel = getEnvVar "OLLAMA_DEFAULT_MODEL" "llama2"
        MaxRetries = getEnvVarInt "OLLAMA_MAX_RETRIES" 3
        TimeoutMs = getEnvVarInt "OLLAMA_TIMEOUT_MS" 60000 // Local models can be slower
        RateLimitRpm = Some (getEnvVarInt "OLLAMA_RATE_LIMIT_RPM" 100) // Lower for local processing
        Priority = getEnvVarInt "OLLAMA_PRIORITY" 5
        Enabled = getEnvVar "OLLAMA_ENABLED" "true" = "true"
        HealthCheckInterval = TimeSpan.FromMinutes(2.0)
    }
    
    /// Create OpenRouter provider configuration from environment variables
    let createOpenRouterConfig () = {
        ProviderId = OpenRouter
        ApiKey = getEnvVar "OPENROUTER_API_KEY" ""
        BaseUrl = Some (getEnvVar "OPENROUTER_BASE_URL" "https://openrouter.ai/api/v1")
        DefaultModel = getEnvVar "OPENROUTER_DEFAULT_MODEL" "openai/gpt-3.5-turbo"
        MaxRetries = getEnvVarInt "OPENROUTER_MAX_RETRIES" 3
        TimeoutMs = getEnvVarInt "OPENROUTER_TIMEOUT_MS" 30000
        RateLimitRpm = Some (getEnvVarInt "OPENROUTER_RATE_LIMIT_RPM" 2000)
        Priority = getEnvVarInt "OPENROUTER_PRIORITY" 6
        Enabled = getEnvVar "OPENROUTER_ENABLED" "true" = "true"
        HealthCheckInterval = TimeSpan.FromMinutes(5.0)
    }
    
    /// Default cache configuration
    let defaultCacheConfig = {
        RedisConnectionString = getEnvVar "REDIS_CONNECTION_STRING" "localhost"
        EnableCaching = getEnvVar "AI_CACHE_ENABLED" "true" = "true"
        DefaultOrganization = getEnvVar "AI_DEFAULT_ORG" "default"
        DefaultTarget = getEnvVar "AI_DEFAULT_TARGET" "web"
    }
    
    /// Default AI framework configuration with all providers
    let defaultAIConfig = {
        CacheConfig = defaultCacheConfig
        ProviderConfigs = [
            createOpenAIConfig()
            createAnthropicConfig()
            createGoogleGeminiConfig()
            createAzureOpenAIConfig()
            createOllamaConfig()
            createOpenRouterConfig()
        ]
        DefaultStrategy = ByPriority
        EnableHealthMonitoring = true
        HealthCheckInterval = TimeSpan.FromMinutes(5.0)
    }
    
    /// Create provider instance from configuration
    let createProvider (config: ProviderConfig) : IAIProvider =
        match config.ProviderId with
        | OpenAI -> OpenAIProvider(config) :> IAIProvider
        | Anthropic -> AnthropicProvider(config) :> IAIProvider
        | GoogleGemini -> GoogleGeminiProvider(config) :> IAIProvider
        | AzureOpenAI -> AzureOpenAIProvider(config) :> IAIProvider
        | Ollama -> OllamaProvider(config) :> IAIProvider
        | OpenRouter -> OpenRouterProvider(config) :> IAIProvider
        | Custom _ -> failwith "Custom providers not implemented"
    
    /// Initialize all configured providers
    let initializeProviders (configs: ProviderConfig list) : IAIProvider list =
        configs
        |> List.filter (fun config -> config.Enabled && not (String.IsNullOrEmpty(config.ApiKey)))
        |> List.map createProvider

/// Multi-Agent System Framework
module AgentFramework =
    
    open AIFramework
    open AIConfig
    open System.Collections.Generic
    
    /// Agent types in the system
    type AgentType =
        | PlanningAgent
        | ExecutionAgent
        | ValidationAgent
        | LearningAgent
    
    /// Agent message types for communication
    type AgentMessage = {
        Id: string
        From: AgentType
        To: AgentType
        MessageType: MessageType
        Content: string
        Metadata: Map<string, string>
        Timestamp: DateTime
    }
    
    /// Message types for agent communication
    and MessageType =
        | PlanRequest of command: string
        | PlanResponse of actions: Action list * confidence: float
        | ExecutionRequest of actions: Action list
        | ExecutionResponse of result: TaskResult * metrics: ExecutionMetrics
        | ValidationRequest of plan: Action list
        | ValidationResponse of isValid: bool * issues: string list
        | LearningUpdate of feedback: LearningFeedback
        | StatusUpdate of status: string
    
    /// Execution metrics for tracking performance
    and ExecutionMetrics = {
        Duration: TimeSpan
        SuccessRate: float
        ErrorCount: int
        StepsCompleted: int
        TotalSteps: int
    }
    
    /// Learning feedback for continuous improvement
    and LearningFeedback = {
        Command: string
        Actions: Action list
        Success: bool
        ErrorDetails: string option
        UserFeedback: string option
        Improvements: string list
    }
    
    /// Agent configuration
    type AgentConfig = {
        AgentType: AgentType
        Name: string
        SystemPrompt: string
        MaxRetries: int
        TimeoutMs: int
        EnableLogging: bool
    }
    
    /// Base agent interface
    type IAgent =
        abstract member AgentType: AgentType
        abstract member Config: AgentConfig
        abstract member ProcessMessage: AgentMessage -> Async<AgentMessage option>
        abstract member CanHandle: MessageType -> bool
        abstract member GetStatus: unit -> string
    
    /// Agent coordinator interface
    type IAgentCoordinator =
        abstract member RegisterAgent: IAgent -> unit
        abstract member SendMessage: AgentMessage -> Async<AgentMessage option>
        abstract member ProcessCommand: string -> Async<Action list>
        abstract member GetSystemStatus: unit -> Map<AgentType, string>

/// Vision Processing Module for visual element detection and understanding
module VisionProcessor =
    
    open AIFramework
    open System.IO
    open System.Drawing
    
    /// Vision analysis result types
    type VisionElement = {
        Type: ElementType
        Description: string
        BoundingBox: Rectangle
        Confidence: float
        Text: string option
        Selector: string option
        ClickCoordinates: Point option
    }
    
    /// Types of visual elements that can be detected
    and ElementType =
        | Button
        | Link
        | Input
        | Image
        | Text
        | Icon
        | Menu
        | Form
        | Table
        | Unknown
    
    /// Vision analysis request
    type VisionRequest = {
        ImagePath: string
        Task: VisionTask
        Prompt: string option
        Context: string option
    }
    
    /// Vision analysis tasks
    and VisionTask =
        | ElementDetection
        | OCR
        | SelectorGeneration of targetDescription: string
        | LayoutAnalysis
        | ContentExtraction
    
    /// Vision analysis response
    type VisionResponse = {
        Elements: VisionElement list
        OverallDescription: string
        SuggestedActions: string list
        Confidence: float
        ProcessingTime: TimeSpan
    }
    
    /// Configuration for vision processing
    type VisionConfig = {
        EnableVision: bool
        MaxImageSize: int
        SupportedFormats: string list
        DefaultConfidenceThreshold: float
        EnableOCR: bool
        EnableElementDetection: bool
    }
    
    /// Default vision configuration
    let defaultVisionConfig = {
        EnableVision = true
        MaxImageSize = 1920 * 1080
        SupportedFormats = [".png"; ".jpg"; ".jpeg"; ".bmp"]
        DefaultConfidenceThreshold = 0.7
        EnableOCR = true
        EnableElementDetection = true
    }
    
    /// Screenshot capture utilities
    module ScreenCapture =
        
        /// Generate unique screenshot filename
        let generateScreenshotPath (baseDir: string) : string =
            let timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss_fff")
            let filename = $"screenshot_{timestamp}.png"
            Path.Combine(baseDir, filename)
        
        /// Validate image file
        let validateImageFile (imagePath: string) (config: VisionConfig) : Result<unit, string> =
            if not (File.Exists(imagePath)) then
                Error "Image file not found"
            else
                let extension = Path.GetExtension(imagePath).ToLower()
                if not (config.SupportedFormats |> List.contains extension) then
                    Error $"Unsupported image format: {extension}"
                else
                    let fileInfo = FileInfo(imagePath)
                    if fileInfo.Length > int64 (config.MaxImageSize * 4) then // Rough estimate
                        Error "Image file too large"
                    else
                        Ok ()

/// Vision AI Integration Module
module VisionAI =
    
    open AIFramework
    open VisionProcessor
    open System.IO
    open System.Text.Json
    open System.Net.Http
    
    /// OpenAI Vision API integration
    module OpenAIVision =
        open Automation.Prompts
        open System.Threading.Tasks
        
        /// Convert image to base64 string
        let private imageToBase64 (imagePath: string) : string =
            let imageBytes = File.ReadAllBytes(imagePath)
            Convert.ToBase64String(imageBytes)
        
        /// Create vision request payload for OpenAI API (loads system prompt from PromptStore)
        let private createVisionPayloadAsync (request: VisionRequest) : Async<string> =
            async {
                // Map tasks to prompt IDs and default templates
                let getTemplate task =
                    match task with
                    | ElementDetection ->
                        "vision.element_detection",
                        "You are a vision AI that detects UI elements in screenshots. Analyze the image and identify clickable elements like buttons, links, inputs, and other interactive components. Return a JSON response with element details including type, description, bounding box coordinates, and confidence score."
                    | OCR ->
                        "vision.ocr",
                        "You are a vision AI that extracts text from images. Analyze the image and extract all visible text, preserving layout and structure. Return extracted text with positional information."
                    | SelectorGeneration _ ->
                        "vision.selector_generation",
                        "You are a vision AI that helps generate CSS selectors for UI elements. Find the element described as '{target}' in the screenshot and suggest the best CSS selector or XPath to locate it. Also provide click coordinates if the element is clickable."
                    | LayoutAnalysis ->
                        "vision.layout_analysis",
                        "You are a vision AI that analyzes UI layout. Examine the screenshot and describe the overall layout structure, identify different sections, and understand the hierarchy of elements."
                    | ContentExtraction ->
                        "vision.content_extraction",
                        "You are a vision AI that extracts structured content from screenshots. Analyze the image and extract meaningful content like forms, tables, lists, and other structured data."

                let (promptId, defaultTemplate) = getTemplate request.Task

                // Fetch template from DB (fallback to default)
                let! template = PromptStore.getOrDefault promptId defaultTemplate |> Async.AwaitTask

                // Replace placeholder if needed
                let systemPrompt =
                    match request.Task with
                    | SelectorGeneration targetDesc -> template.Replace("{target}", targetDesc)
                    | _ -> template

                // Build payload
                let base64Image =
                    let bytes = File.ReadAllBytes(request.ImagePath)
                    Convert.ToBase64String(bytes)
                let mimeType =
                    match Path.GetExtension(request.ImagePath).ToLower() with
                    | ".png" -> "image/png"
                    | ".jpg" | ".jpeg" -> "image/jpeg"
                    | _ -> "image/png"
                let userPrompt = request.Prompt |> Option.defaultValue "Analyze this screenshot and provide detailed information about the UI elements."

                let payloadObj =
                    {|
                        model = "gpt-4-vision-preview"
                        messages = [|
                            box {| role = "system"; content = systemPrompt |};
                            box {| role = "user"; content = $"[{{\"type\": \"text\", \"text\": \"{userPrompt}\"}}, {{\"type\": \"image_url\", \"image_url\": {{\"url\": \"data:{mimeType};base64,{base64Image}\"}}}}]" |}
                        |]
                        max_tokens = 2000
                        temperature = 0.1
                    |}
                return JsonSerializer.Serialize(payloadObj)
            }

        
        /// Parse elements from content string
        let private parseElementsFromContent (content: string) : VisionElement list =
            // Simple element detection logic
            // In a real implementation, this would be more sophisticated
            let parseElementType (description: string) =
                match description.ToLower() with
                | s when s.Contains("button") -> VisionProcessor.ElementType.Button
                | s when s.Contains("link") -> VisionProcessor.ElementType.Link
                | s when s.Contains("input") -> VisionProcessor.ElementType.Input
                | s when s.Contains("text") -> VisionProcessor.ElementType.Text
                | s when s.Contains("image") -> VisionProcessor.ElementType.Image
                | s when s.Contains("icon") -> VisionProcessor.ElementType.Icon
                | s when s.Contains("menu") -> VisionProcessor.ElementType.Menu
                | s when s.Contains("form") -> VisionProcessor.ElementType.Form
                | s when s.Contains("table") -> VisionProcessor.ElementType.Table
                | _ -> VisionProcessor.ElementType.Unknown
            
            // Mock elements for demonstration
            // In a real implementation, this would parse actual JSON structure
            [
                {
                    Type = parseElementType content
                    Description = "Detected UI element"
                    BoundingBox = System.Drawing.Rectangle(100, 100, 200, 50)
                    Confidence = 0.85
                    Text = Some "Sample text"
                    Selector = Some "button[data-testid='sample']"
                    ClickCoordinates = Some (System.Drawing.Point(200, 125))
                }
            ]
        
        /// Parse vision response from OpenAI API
        let private parseVisionResponse (responseContent: string) : VisionResponse =
            try
                let jsonDoc = JsonDocument.Parse(responseContent)
                let root = jsonDoc.RootElement
                let choicesArray = root.GetProperty("choices")
                let firstChoice = choicesArray.EnumerateArray() |> Seq.head
                let message = firstChoice.GetProperty("message")
                let content = message.GetProperty("content").GetString()
                
                // Try to parse structured JSON response if available
                let elements = 
                    try
                        // Look for JSON structure in the response
                        if content.Contains("{") && content.Contains("[") then
                            // Try to extract elements from JSON response
                            parseElementsFromContent content
                        else
                            []
                    with
                    | _ -> []
                
                let suggestedActions = 
                    if content.Contains("click") then ["click"]
                    elif content.Contains("type") then ["type"]
                    elif content.Contains("scroll") then ["scroll"]
                    else []
                
                {
                    Elements = elements
                    OverallDescription = content
                    SuggestedActions = suggestedActions
                    Confidence = 0.8
                    ProcessingTime = TimeSpan.FromSeconds(2.0)
                }
            with
            | ex ->
                {
                    Elements = []
                    OverallDescription = $"Error parsing vision response: {ex.Message}"
                    SuggestedActions = []
                    Confidence = 0.0
                    ProcessingTime = TimeSpan.FromSeconds(0.0)
                }
        
        /// Make vision API request to OpenAI
        let processVisionRequest (request: VisionRequest) : Async<Result<VisionResponse, string>> =
            async {
                try
                    use client = new HttpClient()
                    let apiKey = Environment.GetEnvironmentVariable("OPENAI_API_KEY")
                    
                    if String.IsNullOrEmpty(apiKey) then
                        return Error "OpenAI API key not configured"
                    else
                        client.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}")
                        
                        let! payload = createVisionPayloadAsync request
                        use content = new StringContent(payload, System.Text.Encoding.UTF8, "application/json")
                        
                        let! response = client.PostAsync("https://api.openai.com/v1/chat/completions", content) |> Async.AwaitTask
                        let! responseContent = response.Content.ReadAsStringAsync() |> Async.AwaitTask
                        
                        if response.IsSuccessStatusCode then
                            let visionResponse = parseVisionResponse responseContent
                            return Ok visionResponse
                        else
                            return Error $"OpenAI API error: {response.StatusCode} - {responseContent}"
                with
                | ex ->
                    return Error $"Vision processing error: {ex.Message}"
            }
    
    /// Main vision processor interface
    type VisionProcessor() =
        
        /// Process vision request with validation
        member _.ProcessVisionRequest(request: VisionRequest) : Async<Result<VisionResponse, string>> =
            async {
                // Validate image file
                let validationResult = ScreenCapture.validateImageFile request.ImagePath defaultVisionConfig
                
                match validationResult with
                | Ok _ ->
                    // Process with OpenAI Vision
                    return! OpenAIVision.processVisionRequest request
                | Error errorMsg ->
                    return Error errorMsg
            }
        
        /// Generate screenshot and analyze with vision
        member this.CaptureAndAnalyze(screenshotDir: string, task: VisionTask, ?prompt: string) : Async<Result<VisionResponse, string>> =
            async {
                try
                    // Generate screenshot path
                    let screenshotPath = ScreenCapture.generateScreenshotPath screenshotDir
                    
                    // Create vision request
                    let visionRequest = {
                        ImagePath = screenshotPath
                        Task = task
                        Prompt = prompt
                        Context = None
                    }
                    
                    // Note: Screenshot capture would be implemented by the Web/Mobile automator
                    // For now, assume the screenshot already exists
                    if File.Exists(screenshotPath) then
                        return! this.ProcessVisionRequest(visionRequest)
                    else
                        return Error "Screenshot not found. Ensure screenshot capture is implemented in the automator."
                with
                | ex ->
                    return Error $"Vision capture and analysis error: {ex.Message}"
            }
    
    /// OCR and text extraction utilities
    module OCRProcessor =
        
        /// Extract text from image using vision API
        let extractTextFromImage (imagePath: string) : Async<Result<string list, string>> =
            async {
                let visionRequest = {
                    ImagePath = imagePath
                    Task = OCR
                    Prompt = Some "Extract all text from this image. Return the text in order from top to bottom, left to right."
                    Context = None
                }
                
                let! result = OpenAIVision.processVisionRequest visionRequest
                
                match result with
                | Ok response ->
                    // Parse text from description
                    let textLines = 
                        response.OverallDescription.Split([|'\n'; '\r'|], StringSplitOptions.RemoveEmptyEntries)
                        |> Array.map (fun line -> line.Trim())
                        |> Array.filter (fun line -> not (String.IsNullOrWhiteSpace(line)))
                        |> Array.toList
                    
                    return Ok textLines
                | Error error ->
                    return Error error
            }
        
        /// Find text elements with position information
        let findTextElements (imagePath: string) (searchText: string) : Async<Result<VisionElement list, string>> =
            async {
                let visionRequest = {
                    ImagePath = imagePath
                    Task = SelectorGeneration searchText
                    Prompt = Some $"Find all instances of text '{searchText}' in this image and provide their locations and bounding boxes."
                    Context = None
                }
                
                let! result = OpenAIVision.processVisionRequest visionRequest
                
                match result with
                | Ok response ->
                    // Filter elements that contain the search text
                    let textElements = 
                        response.Elements
                        |> List.filter (fun element -> 
                            match element.Text with
                            | Some text -> text.Contains(searchText, StringComparison.OrdinalIgnoreCase)
                            | None -> false)
                    
                    return Ok textElements
                | Error error ->
                    return Error error
            }
    
    /// Selector generation utilities
    module SelectorGenerator =
        
        /// Generate CSS selector for a described element
        let generateSelector (imagePath: string) (elementDescription: string) : Async<Result<string, string>> =
            async {
                let visionRequest = {
                    ImagePath = imagePath
                    Task = SelectorGeneration elementDescription
                    Prompt = Some $"Generate the best CSS selector for the element described as '{elementDescription}'. Prefer data-testid, aria-label, or other semantic attributes over generic selectors."
                    Context = None
                }
                
                let! result = OpenAIVision.processVisionRequest visionRequest
                
                match result with
                | Ok response ->
                    // Extract selector from elements or description
                    let selector = 
                        match response.Elements with
                        | element :: _ when element.Selector.IsSome -> element.Selector.Value
                        | _ ->
                            // Parse selector from description
                            let lines = response.OverallDescription.Split('\n')
                            let selectorLine = 
                                lines 
                                |> Array.tryFind (fun line -> line.Contains("selector") || line.Contains("css"))
                            match selectorLine with
                            | Some line -> line.Trim()
                            | None -> $"[aria-label*='{elementDescription}']"
                    
                    return Ok selector
                | Error error ->
                    return Error error
            }
        
        /// Generate click coordinates for an element
        let generateClickCoordinates (imagePath: string) (elementDescription: string) : Async<Result<System.Drawing.Point, string>> =
            async {
                let visionRequest = {
                    ImagePath = imagePath
                    Task = SelectorGeneration elementDescription
                    Prompt = Some $"Find the element described as '{elementDescription}' and provide its center coordinates for clicking."
                    Context = None
                }
                
                let! result = OpenAIVision.processVisionRequest visionRequest
                
                match result with
                | Ok response ->
                    let coordinates = 
                        match response.Elements with
                        | element :: _ when element.ClickCoordinates.IsSome -> element.ClickCoordinates.Value
                        | _ -> System.Drawing.Point(300, 300) // Default center position
                    
                    return Ok coordinates
                | Error error ->
                    return Error error
            }
    
    /// Layout analysis utilities
    module LayoutAnalyzer =
        
        /// Analyze the overall layout of a screen
        let analyzeLayout (imagePath: string) : Async<Result<string, string>> =
            async {
                let visionRequest = {
                    ImagePath = imagePath
                    Task = LayoutAnalysis
                    Prompt = Some "Analyze the layout of this screen. Identify main sections, navigation areas, content areas, and interactive elements."
                    Context = None
                }
                
                let! result = OpenAIVision.processVisionRequest visionRequest
                
                match result with
                | Ok response ->
                    return Ok response.OverallDescription
                | Error error ->
                    return Error error
            }
        
        /// Identify all interactive elements on the screen
        let identifyInteractiveElements (imagePath: string) : Async<Result<VisionElement list, string>> =
            async {
                let visionRequest = {
                    ImagePath = imagePath
                    Task = ElementDetection
                    Prompt = Some "Identify all interactive elements on this screen including buttons, links, input fields, dropdown menus, and other clickable elements."
                    Context = None
                }
                
                let! result = OpenAIVision.processVisionRequest visionRequest
                
                match result with
                | Ok response ->
                    // Filter for interactive elements
                    let interactiveElements = 
                        response.Elements
                        |> List.filter (fun element -> 
                            match element.Type with
                            | VisionProcessor.ElementType.Button | VisionProcessor.ElementType.Link | VisionProcessor.ElementType.Input | VisionProcessor.ElementType.Menu -> true
                            | _ -> false)
                    
                    return Ok interactiveElements
                | Error error ->
                    return Error error
            }

module AIProcessor =
    
    open AIFramework
    open AIConfig

    /// Extract cache key components from command
    let private extractCacheComponents (command: string) (config: CacheConfig) : (string * string * string) =
        // For now, use default values. In the future, this could parse the command to extract org/target/task info
        let taskHash = command.GetHashCode().ToString()
        (config.DefaultOrganization, config.DefaultTarget, taskHash)

    /// Try to retrieve cached test plan
    let private tryGetCachedPlan (config: CacheConfig) (org: string) (target: string) (task: string) : Async<TestPlan option> =
        async {
            if config.EnableCaching then
                let! latestVersion = TestPlanCache.getLatestVersion config.RedisConnectionString org target task
                if latestVersion > 0 then
                    let key = { Organization = org; Target = target; Task = task; Version = latestVersion }
                    return! TestPlanCache.retrieveTestPlan config.RedisConnectionString key
                else
                    return None
            else
                return None
        }

    /// Store generated test plan in cache
    let private storePlanInCache (config: CacheConfig) (org: string) (target: string) (task: string) (actions: Action list) : Async<unit> =
        async {
            if config.EnableCaching then
                // Extract selectors from actions for caching
                let selectors = 
                    actions
                    |> List.choose (function
                        | Click selector -> Some ("click", [selector])
                        | TypeText (selector, _) -> Some ("type", [selector])
                        | Tap selector -> Some ("tap", [selector])
                        | GetText selector -> Some ("getText", [selector])
                        | _ -> None)
                    |> Map.ofList

                // For now, empty locators history - this could be enhanced to track actual locator evolution
                let locatorsHistory = []

                let! _ = TestPlanCache.storeNewVersion config.RedisConnectionString org target task actions selectors locatorsHistory
                ()
            else
                ()
        }

    /// Processes a natural language command and converts it into a list of actions.
    /// Uses caching to store and retrieve generated test plans.
    let processCommandWithCache (config: CacheConfig) (command: string) : Async<Action list> =
        async {
            let (org, target, task) = extractCacheComponents command config
            
            // Try to get cached plan first
            let! cachedPlan = tryGetCachedPlan config org target task
            
            match cachedPlan with
            | Some plan ->
                printfn $"[AIProcessor] Retrieved cached plan for task '{task}' (version {plan.Version}) - {plan.Steps.Length} actions"
                return plan.Steps
            | None ->
                printfn $"[AIProcessor] No cached plan found for '{command}'. Generating new plan..."
                
                // Generate new actions (mock implementation for now)
                let actions = [
                    Navigate "https://www.google.com"
                    TypeText ("textarea[name=q]", "F# programming language")
                    Click ("input[name=btnK]")
                ]
                
                // Store in cache for future use
                do! storePlanInCache config org target task actions
                printfn $"[AIProcessor] Generated and cached new plan with {actions.Length} actions"
                
                return actions
        }

    /// Processes a natural language command and converts it into a list of actions.
    /// This version uses the default cache configuration.
    let processCommand (command: string) : Action list =
        processCommandWithCache defaultCacheConfig command |> Async.RunSynchronously

/// Advanced Natural Language Processing & Intent Recognition Module
module NLPProcessor =
    
    open AIFramework
    open AIConfig
    open System.Text.Json
    open System.Net.Http
    open System.Text.RegularExpressions
    
    /// Intent types that can be recognized from natural language
    type Intent =
        | NavigationIntent of url: string
        | ClickIntent of target: string
        | TypeIntent of target: string * text: string
        | SearchIntent of query: string
        | FormFillIntent of fields: Map<string, string>
        | WorkflowIntent of steps: string list
        | ConditionalIntent of condition: string * thenAction: string * elseAction: string option
        | LoopIntent of action: string * condition: string
        | WaitIntent of target: string option * duration: int option
        | ValidationIntent of target: string * expectedValue: string
        | UnknownIntent of rawCommand: string
    
    /// Context information for intent recognition
    type IntentContext = {
        PreviousActions: Action list
        CurrentPage: string option
        UserPreferences: Map<string, string>
        SessionHistory: string list
    }
    
    /// Intent recognition result
    type IntentResult = {
        Intent: Intent
        Confidence: float
        AlternativeIntents: Intent list
        RequiredContext: string list
        SuggestedActions: Action list
    }
    
    /// Action planning result
    type ActionPlan = {
        Actions: Action list
        EstimatedDuration: TimeSpan
        RiskLevel: RiskLevel
        ErrorRecoveryPlan: Action list
        ConditionalBranches: Map<string, Action list>
    }
    
    /// Risk assessment levels
    and RiskLevel =
        | Low
        | Medium
        | High
        | Critical
    
    /// Error recovery strategies
    type ErrorRecoveryStrategy =
        | Retry of maxAttempts: int
        | Alternative of actions: Action list
        | Fallback of action: Action
        | UserIntervention of message: string
        | Abort
    
    /// Workflow step with error handling
    type WorkflowStep = {
        StepId: string
        Action: Action
        Description: string
        ErrorRecovery: ErrorRecoveryStrategy
        Condition: string option
        Timeout: TimeSpan option
    }
    
    /// Multi-step workflow
    type Workflow = {
        WorkflowId: string
        Steps: WorkflowStep list
        GlobalErrorRecovery: ErrorRecoveryStrategy
        MaxExecutionTime: TimeSpan
        RequiredPermissions: string list
    }
    
    /// Intent recognition patterns
    module IntentPatterns =
        
        /// Navigation patterns
        let navigationPatterns = [
            (@"(?i)(?:go to|navigate to|visit|open)\s+(.+)", fun (m: Match) -> NavigationIntent (m.Groups.[1].Value.Trim()))
            (@"(?i)(?:load|browse)\s+(.+)", fun (m: Match) -> NavigationIntent (m.Groups.[1].Value.Trim()))
        ]
        
        /// Click patterns
        let clickPatterns = [
            (@"(?i)(?:click|tap|press)\s+(?:on\s+)?(.+)", fun (m: Match) -> ClickIntent (m.Groups.[1].Value.Trim()))
            (@"(?i)(?:select|choose)\s+(.+)", fun (m: Match) -> ClickIntent (m.Groups.[1].Value.Trim()))
        ]
        
        /// Type patterns
        let typePatterns = [
            (@"(?i)(?:type|enter|input)\s+[""'](.+?)[""']\s+(?:in|into)\s+(.+)", fun (m: Match) -> TypeIntent (m.Groups.[2].Value.Trim(), m.Groups.[1].Value.Trim()))
            (@"(?i)(?:fill|populate)\s+(.+?)\s+with\s+[""'](.+?)[""']", fun (m: Match) -> TypeIntent (m.Groups.[1].Value.Trim(), m.Groups.[2].Value.Trim()))
        ]
        
        /// Search patterns
        let searchPatterns = [
            (@"(?i)(?:search for|find|look for)\s+(.+)", fun (m: Match) -> SearchIntent (m.Groups.[1].Value.Trim()))
        ]
        
        /// Conditional patterns
        let conditionalPatterns = [
            (@"(?i)if\s+(.+?)\s+then\s+(.+?)(?:\s+else\s+(.+))?", fun (m: Match) -> 
                let condition = m.Groups.[1].Value.Trim()
                let thenAction = m.Groups.[2].Value.Trim()
                let elseAction = if m.Groups.[3].Success then Some (m.Groups.[3].Value.Trim()) else None
                ConditionalIntent (condition, thenAction, elseAction))
        ]
        
        /// All patterns combined
        let allPatterns = 
            [
                navigationPatterns
                clickPatterns
                typePatterns
                searchPatterns
                conditionalPatterns
            ] |> List.concat
    
    /// Intent recognition engine
    module IntentRecognizer =
        
        /// Recognize intent from natural language using pattern matching
        let recognizeIntentFromPatterns (command: string) : Intent option =
            let tryMatch (patterns: (string * (Match -> Intent)) list) =
                patterns
                |> List.tryPick (fun (pattern, converter) ->
                    let regex = Regex(pattern)
                    let match' = regex.Match(command)
                    if match'.Success then Some (converter match') else None)
            
            tryMatch IntentPatterns.allPatterns
        
        /// Recognize intent using AI-powered analysis
        let recognizeIntentWithAI (command: string) (context: IntentContext) : Async<IntentResult> =
            async {
                try
                    let systemPrompt = """
You are an advanced intent recognition system for web automation. Analyze the user's natural language command and:

1. Identify the primary intent (navigation, click, type, search, form fill, workflow, conditional, loop, wait, validation)
2. Extract relevant parameters and targets
3. Assess confidence level (0.0 to 1.0)
4. Suggest alternative interpretations
5. Identify required context or missing information
6. Generate specific automation actions

Return a JSON response with the following structure:
{
    "intent": "primary_intent_type",
    "parameters": {"key": "value"},
    "confidence": 0.95,
    "alternatives": ["alternative1", "alternative2"],
    "required_context": ["context1", "context2"],
    "suggested_actions": ["action1", "action2"]
}
"""
                    
                    let userPrompt = $"""
Command: "{command}"

Context:
- Previous actions: {context.PreviousActions.Length} actions
- Current page: {context.CurrentPage |> Option.defaultValue "unknown"}
- Session history: {context.SessionHistory.Length} commands

Analyze this command and provide structured intent recognition.
"""
                    
                    let request = {
                        Prompt = userPrompt
                        MaxTokens = Some 1000
                        Temperature = Some 0.1
                        Model = Some "gpt-4"
                        SystemMessage = Some systemPrompt
                        Images = None
                        Metadata = Map.empty
                    }
                    
                    // Mock AI response for now - in real implementation, use AI provider
                    let patternIntent = recognizeIntentFromPatterns command
                    
                    match patternIntent with
                    | Some intent ->
                        return {
                            Intent = intent
                            Confidence = 0.85
                            AlternativeIntents = []
                            RequiredContext = []
                            SuggestedActions = []
                        }
                    | None ->
                        return {
                            Intent = UnknownIntent command
                            Confidence = 0.5
                            AlternativeIntents = []
                            RequiredContext = ["More specific instructions needed"]
                            SuggestedActions = []
                        }
                with
                | ex ->
                    return {
                        Intent = UnknownIntent command
                        Confidence = 0.0
                        AlternativeIntents = []
                        RequiredContext = [$"Error: {ex.Message}"]
                        SuggestedActions = []
                    }
            }
    
    /// Context-aware action planner
    module ActionPlanner =
        
        /// Convert intent to actions with context awareness
        let planActionsFromIntent (intent: Intent) (context: IntentContext) : ActionPlan =
            let estimateDuration actions =
                let baseTime = TimeSpan.FromSeconds(2.0) // Base time per action
                TimeSpan.FromMilliseconds(float (List.length actions) * baseTime.TotalMilliseconds)
            
            let assessRisk actions =
                let hasNavigation = actions |> List.exists (function Navigate _ -> true | _ -> false)
                let hasFormInput = actions |> List.exists (function TypeText _ -> true | _ -> false)
                
                match actions.Length with
                | n when n > 10 -> High
                | n when n > 5 -> Medium
                | _ when hasNavigation && hasFormInput -> Medium
                | _ -> Low
            
            let createErrorRecovery actions =
                [
                    Screenshot "error_recovery_screenshot.png"
                    // Add more sophisticated error recovery actions
                ]
            
            match intent with
            | NavigationIntent url ->
                let actions = [Navigate url]
                {
                    Actions = actions
                    EstimatedDuration = estimateDuration actions
                    RiskLevel = assessRisk actions
                    ErrorRecoveryPlan = createErrorRecovery actions
                    ConditionalBranches = Map.empty
                }
            
            | ClickIntent target ->
                let actions = [Click target]
                {
                    Actions = actions
                    EstimatedDuration = estimateDuration actions
                    RiskLevel = assessRisk actions
                    ErrorRecoveryPlan = createErrorRecovery actions
                    ConditionalBranches = Map.empty
                }
            
            | TypeIntent (target, text) ->
                let actions = [TypeText (target, text)]
                {
                    Actions = actions
                    EstimatedDuration = estimateDuration actions
                    RiskLevel = assessRisk actions
                    ErrorRecoveryPlan = createErrorRecovery actions
                    ConditionalBranches = Map.empty
                }
            
            | SearchIntent query ->
                let actions = [
                    Click "input[type='search'], input[name='q'], input[placeholder*='search']"
                    TypeText ("input[type='search'], input[name='q'], input[placeholder*='search']", query)
                    Click "button[type='submit'], input[type='submit'], button[aria-label*='search']"
                ]
                {
                    Actions = actions
                    EstimatedDuration = estimateDuration actions
                    RiskLevel = assessRisk actions
                    ErrorRecoveryPlan = createErrorRecovery actions
                    ConditionalBranches = Map.empty
                }
            
            | FormFillIntent fields ->
                let actions = 
                    fields
                    |> Map.toList
                    |> List.map (fun (field, value) -> TypeText (field, value))
                {
                    Actions = actions
                    EstimatedDuration = estimateDuration actions
                    RiskLevel = assessRisk actions
                    ErrorRecoveryPlan = createErrorRecovery actions
                    ConditionalBranches = Map.empty
                }
            
            | WorkflowIntent steps ->
                let actions = 
                    steps
                    |> List.mapi (fun i step -> Screenshot $"workflow_step_{i}.png")
                {
                    Actions = actions
                    EstimatedDuration = estimateDuration actions
                    RiskLevel = High
                    ErrorRecoveryPlan = createErrorRecovery actions
                    ConditionalBranches = Map.empty
                }
            
            | ConditionalIntent (condition, thenAction, elseAction) ->
                let thenActions = [Click thenAction] // Simplified
                let elseActions = 
                    match elseAction with
                    | Some action -> [Click action]
                    | None -> []
                
                let conditionalBranches = 
                    Map.ofList [
                        ("then", thenActions)
                        ("else", elseActions)
                    ]
                
                {
                    Actions = [] // Main actions determined at runtime
                    EstimatedDuration = TimeSpan.FromSeconds(5.0)
                    RiskLevel = Medium
                    ErrorRecoveryPlan = createErrorRecovery []
                    ConditionalBranches = conditionalBranches
                }
            
            | UnknownIntent command ->
                {
                    Actions = []
                    EstimatedDuration = TimeSpan.Zero
                    RiskLevel = Critical
                    ErrorRecoveryPlan = []
                    ConditionalBranches = Map.empty
                }
            
            | _ ->
                {
                    Actions = []
                    EstimatedDuration = TimeSpan.FromSeconds(1.0)
                    RiskLevel = Low
                    ErrorRecoveryPlan = []
                    ConditionalBranches = Map.empty
                }
    
    /// Workflow generator for multi-step processes
    module WorkflowGenerator =
        
        /// Generate workflow from high-level description
        let generateWorkflow (description: string) (context: IntentContext) : Async<Workflow> =
            async {
                let workflowId = Guid.NewGuid().ToString()
                
                // Parse workflow steps from description
                let steps = 
                    description.Split([|';'; '\n'|], StringSplitOptions.RemoveEmptyEntries)
                    |> Array.mapi (fun i step ->
                        {
                            StepId = $"step_{i + 1}"
                            Action = Click (step.Trim()) // Simplified
                            Description = step.Trim()
                            ErrorRecovery = Retry 3
                            Condition = None
                            Timeout = Some (TimeSpan.FromSeconds(30.0))
                        })
                    |> Array.toList
                
                return {
                    WorkflowId = workflowId
                    Steps = steps
                    GlobalErrorRecovery = UserIntervention "Workflow failed. Please review and retry."
                    MaxExecutionTime = TimeSpan.FromMinutes(10.0)
                    RequiredPermissions = ["web_automation"]
                }
            }
        
        /// Execute workflow with error recovery
        let executeWorkflow (workflow: Workflow) (executor: ITaskExecutor) : Async<TaskResult> =
            async {
                try
                    let mutable allActions = []
                    
                    for step in workflow.Steps do
                        // Add condition checking logic here
                        allActions <- step.Action :: allActions
                    
                    let! result = executor.ExecuteActions (List.rev allActions)
                    return result
                with
                | ex ->
                    let error = {
                        Message = $"Workflow execution failed: {ex.Message}"
                        FailedAction = None
                        ScreenshotPath = None
                        CurrentUrl = None
                        HtmlContent = None
                        AttemptedSelectors = []
                    }
                    return Failure error
            }
    
    /// Error recovery and conditional logic
    module ErrorRecovery =
        
        /// Analyze error and determine recovery strategy
        let analyzeError (error: string) (context: IntentContext) : ErrorRecoveryStrategy =
            match error.ToLower() with
            | e when e.Contains("element not found") -> 
                Alternative [Screenshot "error_element_not_found.png"]
            | e when e.Contains("timeout") -> 
                Retry 2
            | e when e.Contains("navigation") -> 
                Fallback (Navigate "about:blank")
            | _ -> 
                UserIntervention $"Unexpected error: {error}"
        
        /// Execute error recovery strategy
        let executeRecovery (strategy: ErrorRecoveryStrategy) (executor: ITaskExecutor) : Async<TaskResult> =
            async {
                try
                    match strategy with
                    | Retry maxAttempts ->
                        return Success $"Retry strategy initiated with {maxAttempts} attempts"
                    | Alternative actions ->
                        return! executor.ExecuteActions actions
                    | Fallback action ->
                        return! executor.ExecuteActions [action]
                    | UserIntervention message ->
                        let error = {
                            Message = $"Recovery requires user intervention: {message}"
                            FailedAction = None
                            ScreenshotPath = None
                            CurrentUrl = None
                            HtmlContent = None
                            AttemptedSelectors = []
                        }
                        return Failure error
                    | Abort ->
                        let error = {
                            Message = "Recovery strategy aborted"
                            FailedAction = None
                            ScreenshotPath = None
                            CurrentUrl = None
                            HtmlContent = None
                            AttemptedSelectors = []
                        }
                        return Failure error
                with ex ->
                    let error = {
                        Message = $"Error during recovery: {ex.Message}"
                        FailedAction = None
                        ScreenshotPath = None
                        CurrentUrl = None
                        HtmlContent = None
                        AttemptedSelectors = []
                    }
                    return Failure error
            }
    
    /// Main NLP processor interface
    type NLPProcessor(config: AIFrameworkConfig) =
        
        /// Process natural language command with full NLP capabilities
        member _.ProcessCommand(command: string, context: IntentContext) : Async<ActionPlan> =
            async {
                // Step 1: Recognize intent
                let! intentResult = IntentRecognizer.recognizeIntentWithAI command context
                
                // Step 2: Plan actions based on intent
                let actionPlan = ActionPlanner.planActionsFromIntent intentResult.Intent context
                
                return actionPlan
            }
        
        /// Process simple command (backward compatibility)
        member this.ProcessSimpleCommand(command: string) : Async<Action list> =
            async {
                let context = {
                    PreviousActions = []
                    CurrentPage = None
                    UserPreferences = Map.empty
                    SessionHistory = []
                }
                
                let! actionPlan = this.ProcessCommand(command, context)
                return actionPlan.Actions
            }
        
        /// Generate workflow from description
        member _.GenerateWorkflow(description: string, context: IntentContext) : Async<Workflow> =
            WorkflowGenerator.generateWorkflow description context
        
        /// Execute workflow with error handling
        member _.ExecuteWorkflow(workflow: Workflow, executor: ITaskExecutor) : Async<TaskResult> =
            WorkflowGenerator.executeWorkflow workflow executor

/// Multi-Agent System Implementation Module
module MultiAgentSystem =
    
    open AgentFramework
    open AIFramework
    open AIConfig
    open NLPProcessor
    open System.Collections.Concurrent
    
    /// Validation result for plans
    type ValidationResult = {
        IsValid: bool
        Issues: string list
        Suggestions: string list
        Confidence: float
        RiskAssessment: RiskLevel
    }
    
    /// Learning metrics for performance tracking
    type LearningMetrics = {
        SuccessRate: float
        AverageExecutionTime: TimeSpan
        CommonErrors: Map<string, int>
        ImprovementSuggestions: string list
        PerformanceTrend: float // Positive = improving, negative = degrading
    }
    
    /// Learning feedback data
    type LearningData = {
        Command: string
        GeneratedPlan: Action list
        ActualResult: TaskResult
        ExecutionTime: TimeSpan
        UserFeedback: string option
        Context: IntentContext
        Timestamp: DateTime
    }
    
    /// Agent coordination context
    type CoordinationContext = {
        SessionId: string
        CurrentTask: string option
        AgentStates: Map<AgentType, string>
        MessageHistory: AgentMessage list
        ExecutionMetrics: ExecutionMetrics option
    }
    
    /// Planning Agent Implementation
    type PlanningAgent(config: AgentConfig, aiConfig: AIFrameworkConfig) =
        let nlpProcessor = NLPProcessor(aiConfig)
        
        interface IAgent with
            member _.AgentType = AgentFramework.AgentType.PlanningAgent
            member _.Config = config
            member _.GetStatus() = "Planning Agent - Ready"
            member _.CanHandle(messageType) = 
                match messageType with
                | PlanRequest _ -> true
                | _ -> false
            
            member _.ProcessMessage(message: AgentMessage) : Async<AgentMessage option> =
                async {
                    match message.MessageType with
                    | PlanRequest command ->
                        try
                            let context = {
                                PreviousActions = []
                                CurrentPage = None
                                UserPreferences = Map.empty
                                SessionHistory = []
                            }
                            
                            let! actionPlan = nlpProcessor.ProcessCommand(command, context)
                            
                            let response = {
                                Id = Guid.NewGuid().ToString()
                                From = AgentFramework.AgentType.PlanningAgent
                                To = AgentFramework.AgentType.ValidationAgent
                                MessageType = PlanResponse (actionPlan.Actions, 0.85)
                                Content = $"Generated plan with {actionPlan.Actions.Length} actions"
                                Metadata = Map.ofList [("risk_level", actionPlan.RiskLevel.ToString())]
                                Timestamp = DateTime.UtcNow
                            }
                            
                            return Some response
                        with
                        | ex ->
                            let errorResponse = {
                                Id = Guid.NewGuid().ToString()
                                From = AgentFramework.AgentType.PlanningAgent
                                To = message.From
                                MessageType = StatusUpdate $"Planning failed: {ex.Message}"
                                Content = "Planning agent encountered an error"
                                Metadata = Map.empty
                                Timestamp = DateTime.UtcNow
                            }
                            return Some errorResponse
                    | _ -> 
                        return None
                }
    
    /// Validation Agent Implementation
    type ValidationAgent(config: AgentConfig, aiConfig: AIFrameworkConfig) =
        
        /// Validate action plan for safety and feasibility
        let validatePlan (actions: Action list) : ValidationResult =
            let issues = ResizeArray<string>()
            let suggestions = ResizeArray<string>()
            
            // Check for basic safety issues
            if actions.Length > 20 then
                issues.Add("Plan too complex - consider breaking into smaller workflows")
            
            // Check for unsafe navigation
            let hasUnsafeNavigation = 
                actions 
                |> List.exists (function 
                    | Navigate url when url.Contains("javascript:") || url.Contains("data:") -> true
                    | _ -> false)
            
            if hasUnsafeNavigation then
                issues.Add("Unsafe navigation detected - contains potentially dangerous URLs")
            
            // Check for missing context
            let hasClickWithoutNavigation = 
                actions 
                |> List.exists (function Click _ -> true | _ -> false) &&
                not (actions |> List.exists (function Navigate _ -> true | _ -> false))
            
            if hasClickWithoutNavigation then
                suggestions.Add("Consider adding navigation action before clicking elements")
            
            // Assess risk level
            let riskLevel = 
                match issues.Count with
                | 0 -> Low
                | 1 -> Medium
                | _ -> High
            
            {
                IsValid = issues.Count = 0
                Issues = issues |> List.ofSeq
                Suggestions = suggestions |> List.ofSeq
                Confidence = if issues.Count = 0 then 0.9 else 0.5
                RiskAssessment = riskLevel
            }
        
        interface IAgent with
            member _.AgentType = AgentFramework.AgentType.ValidationAgent
            member _.Config = config
            member _.GetStatus() = "Validation Agent - Ready"
            member _.CanHandle(messageType) = 
                match messageType with
                | PlanResponse _ | ValidationRequest _ -> true
                | _ -> false
            
            member _.ProcessMessage(message: AgentMessage) : Async<AgentMessage option> =
                async {
                    match message.MessageType with
                    | PlanResponse (actions, confidence) ->
                        let validation = validatePlan actions
                        
                        let validationStatus = if validation.IsValid then "PASSED" else "FAILED"
                        let response = {
                            Id = Guid.NewGuid().ToString()
                            From = AgentFramework.AgentType.ValidationAgent
                            To = if validation.IsValid then AgentFramework.AgentType.ExecutionAgent else AgentFramework.AgentType.LearningAgent
                            MessageType = ValidationResponse (validation.IsValid, validation.Issues)
                            Content = $"Validation result: {validationStatus}"
                            Metadata = Map.ofList [
                                ("confidence", validation.Confidence.ToString())
                                ("risk_level", validation.RiskAssessment.ToString())
                                ("suggestions", String.Join("; ", validation.Suggestions))
                            ]
                            Timestamp = DateTime.UtcNow
                        }
                        
                        return Some response
                    
                    | ValidationRequest plan ->
                        let validation = validatePlan plan
                        
                        let issueCount = validation.Issues.Length
                        let response = {
                            Id = Guid.NewGuid().ToString()
                            From = AgentFramework.AgentType.ValidationAgent
                            To = message.From
                            MessageType = ValidationResponse (validation.IsValid, validation.Issues)
                            Content = $"Validation completed: {issueCount} issues found"
                            Metadata = Map.ofList [("confidence", validation.Confidence.ToString())]
                            Timestamp = DateTime.UtcNow
                        }
                        
                        return Some response
                    | _ -> 
                        return None
                }
    
    /// Execution Agent Implementation
    type ExecutionAgent(config: AgentConfig, taskExecutor: ITaskExecutor) =
        
        interface IAgent with
            member _.AgentType = AgentFramework.AgentType.ExecutionAgent
            member _.Config = config
            member _.GetStatus() = "Execution Agent - Ready"
            member _.CanHandle(messageType) = 
                match messageType with
                | ExecutionRequest _ -> true
                | _ -> false
            
            member _.ProcessMessage(message: AgentMessage) : Async<AgentMessage option> =
                async {
                    match message.MessageType with
                    | ExecutionRequest actions ->
                        let startTime = DateTime.UtcNow
                        
                        try
                            let! result = taskExecutor.ExecuteActions actions
                            let endTime = DateTime.UtcNow
                            let duration = endTime - startTime
                            
                            let metrics = {
                                Duration = duration
                                SuccessRate = match result with Success _ -> 1.0 | Failure _ -> 0.0
                                ErrorCount = match result with Success _ -> 0 | Failure _ -> 1
                                StepsCompleted = actions.Length
                                TotalSteps = actions.Length
                            }
                            
                            let contentMessage = 
                                match result with 
                                | Success msg -> $"Execution completed successfully: {msg}"
                                | Failure err -> $"Execution failed: {err}"
                            
                            let response = {
                                Id = Guid.NewGuid().ToString()
                                From = AgentFramework.AgentType.ExecutionAgent
                                To = AgentFramework.AgentType.LearningAgent
                                MessageType = ExecutionResponse (result, metrics)
                                Content = contentMessage
                                Metadata = Map.ofList [
                                    ("duration", duration.TotalSeconds.ToString())
                                    ("steps_completed", actions.Length.ToString())
                                ]
                                Timestamp = DateTime.UtcNow
                            }
                            
                            return Some response
                        with
                        | ex ->
                            let errorResponse = {
                                Id = Guid.NewGuid().ToString()
                                From = AgentFramework.AgentType.ExecutionAgent
                                To = AgentFramework.AgentType.LearningAgent
                                MessageType = StatusUpdate $"Execution error: {ex.Message}"
                                Content = "Execution agent encountered an error"
                                Metadata = Map.empty
                                Timestamp = DateTime.UtcNow
                            }
                            return Some errorResponse
                    | _ -> 
                        return None
                }
    
    /// Learning Agent Implementation
    type LearningAgent(config: AgentConfig) =
        let learningData = ConcurrentQueue<LearningData>()
        let mutable metrics = {
            SuccessRate = 0.0
            AverageExecutionTime = TimeSpan.Zero
            CommonErrors = Map.empty
            ImprovementSuggestions = []
            PerformanceTrend = 0.0
        }
        
        /// Update learning metrics based on execution results
        let updateMetrics (data: LearningData) =
            learningData.Enqueue(data)
            
            // Keep only last 100 entries for performance
            if learningData.Count > 100 then
                learningData.TryDequeue() |> ignore
            
            // Calculate new metrics
            let allData = learningData.ToArray()
            let successCount = allData |> Array.filter (fun d -> match d.ActualResult with Success _ -> true | _ -> false) |> Array.length
            let newSuccessRate = float successCount / float allData.Length
            
            let avgTime = 
                if allData.Length > 0 then
                    let totalTime = allData |> Array.sumBy (fun d -> d.ExecutionTime.TotalMilliseconds)
                    TimeSpan.FromMilliseconds(totalTime / float allData.Length)
                else
                    TimeSpan.Zero
            
            // Update common errors
            let errors = 
                allData 
                |> Array.choose (fun d -> match d.ActualResult with Failure err -> Some err.Message | _ -> None)
                |> Array.groupBy id
                |> Array.map (fun (error, instances) -> (error, instances.Length))
                |> Map.ofArray
            
            // Generate improvement suggestions
            let suggestions = 
                if newSuccessRate < 0.7 then
                    ["Consider simplifying action plans"; "Add more validation checks"]
                else if avgTime.TotalSeconds > 30.0 then
                    ["Optimize action sequences"; "Add timeouts for long operations"]
                else
                    ["Continue current approach"; "Monitor for edge cases"]
            
            metrics <- {
                SuccessRate = newSuccessRate
                AverageExecutionTime = avgTime
                CommonErrors = errors
                ImprovementSuggestions = suggestions
                PerformanceTrend = newSuccessRate - metrics.SuccessRate
            }
        
        interface IAgent with
            member _.AgentType = AgentFramework.AgentType.LearningAgent
            member _.Config = config
            member _.GetStatus() = $"Learning Agent - Success Rate: {metrics.SuccessRate:P2}, Avg Time: {metrics.AverageExecutionTime.TotalSeconds:F1}s"
            member _.CanHandle(messageType) = 
                match messageType with
                | ExecutionResponse _ | LearningUpdate _ -> true
                | _ -> false
            
            member _.ProcessMessage(message: AgentMessage) : Async<AgentMessage option> =
                async {
                    match message.MessageType with
                    | ExecutionResponse (result, executionMetrics) ->
                        let learningData = {
                            Command = message.Content
                            GeneratedPlan = [] // Would need to be passed from context
                            ActualResult = result
                            ExecutionTime = executionMetrics.Duration
                            UserFeedback = None
                            Context = {
                                PreviousActions = []
                                CurrentPage = None
                                UserPreferences = Map.empty
                                SessionHistory = []
                            }
                            Timestamp = DateTime.UtcNow
                        }
                        
                        updateMetrics learningData
                        
                        let response = {
                            Id = Guid.NewGuid().ToString()
                            From = AgentFramework.AgentType.LearningAgent
                            To = AgentFramework.AgentType.PlanningAgent
                            MessageType = StatusUpdate "Learning updated"
                            Content = $"Updated learning metrics: Success Rate {metrics.SuccessRate:P2}"
                            Metadata = Map.ofList [
                                ("success_rate", metrics.SuccessRate.ToString())
                                ("avg_time", metrics.AverageExecutionTime.TotalSeconds.ToString())
                                ("performance_trend", metrics.PerformanceTrend.ToString())
                            ]
                            Timestamp = DateTime.UtcNow
                        }
                        
                        return Some response
                    
                    | LearningUpdate feedback ->
                        let learningData = {
                            Command = feedback.Command
                            GeneratedPlan = feedback.Actions
                            ActualResult = if feedback.Success then Success "User feedback positive" else Failure { Message = "User feedback negative"; FailedAction = None; ScreenshotPath = None; CurrentUrl = None; HtmlContent = None; AttemptedSelectors = [] }
                            ExecutionTime = TimeSpan.FromSeconds(5.0) // Estimated
                            UserFeedback = feedback.UserFeedback
                            Context = {
                                PreviousActions = []
                                CurrentPage = None
                                UserPreferences = Map.empty
                                SessionHistory = []
                            }
                            Timestamp = DateTime.UtcNow
                        }
                        
                        updateMetrics learningData
                        
                        return None // No response needed for learning updates
                    | _ -> 
                        return None
                }
        
        /// Get current learning metrics
        member _.GetMetrics() = metrics
        
        /// Get learning suggestions
        member _.GetSuggestions() = metrics.ImprovementSuggestions
    
    /// Enhanced Agent Coordinator Implementation
    type AgentCoordinator(config: AIFrameworkConfig, taskExecutor: ITaskExecutor) as this =
        let agents = ConcurrentDictionary<AgentType, IAgent>()
        let messageQueue = ConcurrentQueue<AgentMessage>()
        let coordinationContext = {
            SessionId = Guid.NewGuid().ToString()
            CurrentTask = None
            AgentStates = Map.empty
            MessageHistory = []
            ExecutionMetrics = None
        }
        
        // Initialize default agents
        do
            let agentConfigs = [
                { AgentType = AgentFramework.AgentType.PlanningAgent; Name = "Planning Agent"; SystemPrompt = "Plan generation"; MaxRetries = 3; TimeoutMs = 30000; EnableLogging = true }
                { AgentType = AgentFramework.AgentType.ValidationAgent; Name = "Validation Agent"; SystemPrompt = "Plan validation"; MaxRetries = 2; TimeoutMs = 15000; EnableLogging = true }
                { AgentType = AgentFramework.AgentType.ExecutionAgent; Name = "Execution Agent"; SystemPrompt = "Plan execution"; MaxRetries = 3; TimeoutMs = 60000; EnableLogging = true }
                { AgentType = AgentFramework.AgentType.LearningAgent; Name = "Learning Agent"; SystemPrompt = "Learning and improvement"; MaxRetries = 1; TimeoutMs = 10000; EnableLogging = true }
            ]
            
            for agentConfig in agentConfigs do
                let agent = 
                    match agentConfig.AgentType with
                    | AgentFramework.AgentType.PlanningAgent -> PlanningAgent(agentConfig, config) :> IAgent
                    | AgentFramework.AgentType.ValidationAgent -> ValidationAgent(agentConfig, config) :> IAgent
                    | AgentFramework.AgentType.ExecutionAgent -> ExecutionAgent(agentConfig, taskExecutor) :> IAgent
                    | AgentFramework.AgentType.LearningAgent -> LearningAgent(agentConfig) :> IAgent
                
                agents.TryAdd(agentConfig.AgentType, agent) |> ignore
        
        /// Process message queue
        let processMessageQueue() =
            async {
                while not messageQueue.IsEmpty do
                    match messageQueue.TryDequeue() with
                    | (true, message) ->
                        let targetAgent = agents.TryGetValue(message.To)
                        match targetAgent with
                        | (true, agent) when agent.CanHandle(message.MessageType) ->
                            try
                                let! response = agent.ProcessMessage(message)
                                match response with
                                | Some responseMessage ->
                                    messageQueue.Enqueue(responseMessage)
                                | None -> ()
                            with
                            | ex ->
                                printfn $"Error processing message in {message.To}: {ex.Message}"
                        | _ ->
                            printfn $"No suitable agent found for message type {message.MessageType}"
                    | (false, _) -> ()
            }
        
        interface IAgentCoordinator with
            member _.RegisterAgent(agent: IAgent) =
                agents.TryAdd(agent.AgentType, agent) |> ignore
            
            member _.SendMessage(message: AgentMessage) : Async<AgentMessage option> =
                async {
                    messageQueue.Enqueue(message)
                    do! processMessageQueue()
                    return None // Return would be handled by message processing
                }
            
            member _.ProcessCommand(command: string) : Async<Action list> =
                async {
                    // Create initial planning request
                    let planRequest = {
                        Id = Guid.NewGuid().ToString()
                        From = AgentFramework.AgentType.PlanningAgent // Self-reference for response routing
                        To = AgentFramework.AgentType.PlanningAgent
                        MessageType = PlanRequest command
                        Content = command
                        Metadata = Map.empty
                        Timestamp = DateTime.UtcNow
                    }
                    
                    // Send to planning agent
                    let! _ = (this :> IAgentCoordinator).SendMessage(planRequest)
                    
                    // Wait for processing and return empty list for now
                    // In a real implementation, this would wait for the full workflow
                    return []
                }
            
            member _.GetSystemStatus() : Map<AgentType, string> =
                agents.Values
                |> Seq.map (fun agent -> (agent.AgentType, agent.GetStatus()))
                |> Map.ofSeq
        
        /// Get learning metrics from learning agent
        member _.GetLearningMetrics() : LearningMetrics option =
            match agents.TryGetValue(AgentType.LearningAgent) with
            | (true, (:? LearningAgent as learningAgent)) -> Some (learningAgent.GetMetrics())
            | _ -> None
        
        /// Get coordination context
        member _.GetCoordinationContext() = coordinationContext
        
        /// Send learning feedback
        member _.SendLearningFeedback(feedback: LearningFeedback) : Async<unit> =
            async {
                let learningMessage = {
                    Id = Guid.NewGuid().ToString()
                    From = AgentType.LearningAgent
                    To = AgentType.LearningAgent
                    MessageType = LearningUpdate feedback
                    Content = $"Learning feedback for command: {feedback.Command}"
                    Metadata = Map.empty
                    Timestamp = DateTime.UtcNow
                }
                
                let! _ = (this :> IAgentCoordinator).SendMessage(learningMessage)
                return ()
            }

/// AI-Specific Caching and Optimization Module
module AICacheOptimization =
    
    open AIFramework
    open AIConfig
    open System.Collections.Generic
    open System.Security.Cryptography
    open System.Text
    open StackExchange.Redis
    open System.Text.Json
    
    /// Cache entry types
    type CacheEntryType =
        | AIResponse
        | VisionAnalysis
        | TemplateResponse
        | ProviderResponse
        | IntentAnalysis
        | WorkflowPlan
    
    /// Cache metadata
    type CacheMetadata = {
        EntryType: CacheEntryType
        CreatedAt: DateTime
        ExpiresAt: DateTime
        TokenCost: int option
        ApiCost: decimal option
        ProviderId: ProviderId option
        HitCount: int
        Size: int
    }
    
    /// Cache entry
    type CacheEntry<'T> = {
        Key: string
        Value: 'T
        Metadata: CacheMetadata
    }
    
    /// Cache statistics
    type CacheStats = {
        HitCount: int64
        MissCount: int64
        EvictionCount: int64
        TotalCost: decimal
        TotalTokens: int64
        AverageResponseTime: TimeSpan
        CacheSize: int64
    }
    
    /// Cache configuration
    type CacheConfiguration = {
        RedisConnectionString: string
        DefaultTTL: TimeSpan
        MaxCacheSize: int64
        CostThreshold: decimal
        TokenThreshold: int
        EnableCompression: bool
        EvictionPolicy: EvictionPolicy
    }
    
    /// Cache eviction policies
    and EvictionPolicy =
        | LRU // Least Recently Used
        | LFU // Least Frequently Used
        | TTL // Time To Live
        | Cost // Cost-based eviction
    
    /// Cache manager interface
    type ICacheManager =
        abstract member Get<'T> : string -> Async<'T option>
        abstract member Set<'T> : string -> 'T -> TimeSpan -> CacheMetadata -> Async<unit>
        abstract member Delete : string -> Async<unit>
        abstract member Clear : unit -> Async<unit>
        abstract member GetStats : unit -> Async<CacheStats>
        abstract member OptimizeCache : unit -> Async<unit>
    
    /// AI-specific cache manager
    type AICacheManager(config: CacheConfiguration) =
        let mutable stats = {
            HitCount = 0L
            MissCount = 0L
            EvictionCount = 0L
            TotalCost = 0.0m
            TotalTokens = 0L
            AverageResponseTime = TimeSpan.Zero
            CacheSize = 0L
        }
        
        /// Generate cache key from input
        let generateCacheKey (input: string) (entryType: CacheEntryType) (providerId: ProviderId option) : string =
            let hasher = SHA256.Create()
            let providerSuffix = providerId |> Option.map (fun p -> $":{p}") |> Option.defaultValue ""
            let combined = $"{entryType}:{input}{providerSuffix}"
            let hash = hasher.ComputeHash(Encoding.UTF8.GetBytes(combined))
            let hashString = BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant()
            $"ai_cache:{entryType}:{hashString}"
        
        /// Serialize cache entry
        let serializeCacheEntry (entry: CacheEntry<'T>) : string =
            JsonSerializer.Serialize(entry)
        
        /// Deserialize cache entry
        let deserializeCacheEntry (json: string) : CacheEntry<'T> option =
            try
                Some (JsonSerializer.Deserialize<CacheEntry<'T>>(json))
            with
            | _ -> None
        
        /// Check if entry is expired
        let isExpired (metadata: CacheMetadata) : bool =
            DateTime.UtcNow > metadata.ExpiresAt
        
        /// Calculate cache key priority for eviction
        let calculatePriority (metadata: CacheMetadata) : float =
            let age = (DateTime.UtcNow - metadata.CreatedAt).TotalHours
            let hits = float metadata.HitCount
            let cost = metadata.ApiCost |> Option.defaultValue 0.0m |> float
            
            match config.EvictionPolicy with
            | LRU -> -age // Older entries have lower priority
            | LFU -> hits // Higher hit count = higher priority
            | TTL -> -(metadata.ExpiresAt - DateTime.UtcNow).TotalHours // Closer to expiry = lower priority
            | Cost -> cost // Higher cost = higher priority
        
        interface ICacheManager with
            member _.Get<'T>(key: string) : Async<'T option> =
                async {
                    try
                        use connection = ConnectionMultiplexer.Connect(config.RedisConnectionString)
                        let db = connection.GetDatabase()
                        
                        let! result = db.StringGetAsync(key) |> Async.AwaitTask
                        
                        if result.HasValue then
                            match deserializeCacheEntry (result.ToString()) with
                            | Some entry when not (isExpired entry.Metadata) ->
                                stats <- { stats with HitCount = stats.HitCount + 1L }
                                
                                // Update hit count
                                let updatedMetadata = { entry.Metadata with HitCount = entry.Metadata.HitCount + 1 }
                                let updatedEntry = { entry with Metadata = updatedMetadata }
                                let! _ = db.StringSetAsync(key, serializeCacheEntry updatedEntry) |> Async.AwaitTask
                                
                                return Some entry.Value
                            | _ ->
                                stats <- { stats with MissCount = stats.MissCount + 1L }
                                return None
                        else
                            stats <- { stats with MissCount = stats.MissCount + 1L }
                            return None
                    with
                    | _ -> 
                        stats <- { stats with MissCount = stats.MissCount + 1L }
                        return None
                }
            
            member _.Set<'T>(key: string) (value: 'T) (ttl: TimeSpan) (metadata: CacheMetadata) : Async<unit> =
                async {
                    try
                        use connection = ConnectionMultiplexer.Connect(config.RedisConnectionString)
                        let db = connection.GetDatabase()
                        
                        let entry = {
                            Key = key
                            Value = value
                            Metadata = metadata
                        }
                        
                        let serialized = serializeCacheEntry entry
                        let! _ = db.StringSetAsync(key, serialized, ttl) |> Async.AwaitTask
                        
                        stats <- { 
                            stats with 
                                TotalCost = stats.TotalCost + (metadata.ApiCost |> Option.defaultValue 0.0m)
                                TotalTokens = stats.TotalTokens + (metadata.TokenCost |> Option.defaultValue 0 |> int64)
                                CacheSize = stats.CacheSize + (int64 serialized.Length)
                        }
                        
                        return ()
                    with
                    | _ -> return ()
                }
            
            member _.Delete(key: string) : Async<unit> =
                async {
                    try
                        use connection = ConnectionMultiplexer.Connect(config.RedisConnectionString)
                        let db = connection.GetDatabase()
                        let! _ = db.KeyDeleteAsync(key) |> Async.AwaitTask
                        return ()
                    with
                    | _ -> return ()
                }
            
            member _.Clear() : Async<unit> =
                async {
                    try
                        use connection = ConnectionMultiplexer.Connect(config.RedisConnectionString)
                        let server = connection.GetServer(connection.GetEndPoints().[0])
                        let keys = server.Keys(pattern = RedisValue("ai_cache:*"))
                        
                        for key in keys do
                            let db = connection.GetDatabase()
                            let! _ = db.KeyDeleteAsync(key) |> Async.AwaitTask
                            ()
                        
                        stats <- {
                            HitCount = 0L
                            MissCount = 0L
                            EvictionCount = 0L
                            TotalCost = 0.0m
                            TotalTokens = 0L
                            AverageResponseTime = TimeSpan.Zero
                            CacheSize = 0L
                        }
                        
                        return ()
                    with
                    | _ -> return ()
                }
            
            member _.GetStats() : Async<CacheStats> =
                async {
                    return stats
                }
            
            member _.OptimizeCache() : Async<unit> =
                async {
                    try
                        use connection = ConnectionMultiplexer.Connect(config.RedisConnectionString)
                        let server = connection.GetServer(connection.GetEndPoints().[0])
                        let keys = server.Keys(pattern = RedisValue("ai_cache:*"))
                        
                        let mutable evictedCount = 0L
                        
                        let db = connection.GetDatabase()
                        for key in keys do
                            let! result = db.StringGetAsync(key) |> Async.AwaitTask
                            
                            if result.HasValue then
                                try
                                    let json = result.ToString()
                                    let entry = JsonSerializer.Deserialize<CacheEntry<obj>>(json)
                                    
                                    if isExpired entry.Metadata then
                                        let! _ = db.KeyDeleteAsync(key) |> Async.AwaitTask
                                        evictedCount <- evictedCount + 1L
                                with
                                | _ -> ()
                        
                        stats <- { stats with EvictionCount = stats.EvictionCount + evictedCount }
                        return ()
                    with
                    | _ -> return ()
                }
        
        /// Get cache instance
        member this.GetCacheInstance() : ICacheManager = 
            this :> ICacheManager
    
    /// Cache strategies for different AI operations
    module CacheStrategies =
        
        /// Vision analysis cache strategy
        let visionAnalysisCacheStrategy = {
            RedisConnectionString = "localhost"
            DefaultTTL = TimeSpan.FromHours(24.0) // Vision analysis is stable for 24 hours
            MaxCacheSize = 100_000_000L // 100MB
            CostThreshold = 0.10m // Cache expensive vision calls
            TokenThreshold = 1000
            EnableCompression = true
            EvictionPolicy = Cost
        }
        
        /// Template response cache strategy
        let templateResponseCacheStrategy = {
            RedisConnectionString = "localhost"
            DefaultTTL = TimeSpan.FromDays(7.0) // Template responses are very stable
            MaxCacheSize = 50_000_000L // 50MB
            CostThreshold = 0.01m // Cache all template responses
            TokenThreshold = 100
            EnableCompression = true
            EvictionPolicy = LFU
        }
        
        /// Provider response cache strategy
        let providerResponseCacheStrategy = {
            RedisConnectionString = "localhost"
            DefaultTTL = TimeSpan.FromHours(4.0) // Provider responses can change
            MaxCacheSize = 200_000_000L // 200MB
            CostThreshold = 0.05m // Cache moderately expensive calls
            TokenThreshold = 500
            EnableCompression = true
            EvictionPolicy = LRU
        }
        
        /// Intent analysis cache strategy
        let intentAnalysisCacheStrategy = {
            RedisConnectionString = "localhost"
            DefaultTTL = TimeSpan.FromHours(12.0) // Intent analysis is moderately stable
            MaxCacheSize = 75_000_000L // 75MB
            CostThreshold = 0.02m // Cache most intent analysis
            TokenThreshold = 250
            EnableCompression = true
            EvictionPolicy = LFU
        }
        
        /// Workflow plan cache strategy
        let workflowPlanCacheStrategy = {
            RedisConnectionString = "localhost"
            DefaultTTL = TimeSpan.FromHours(8.0) // Workflow plans can evolve
            MaxCacheSize = 150_000_000L // 150MB
            CostThreshold = 0.03m // Cache workflow planning
            TokenThreshold = 750
            EnableCompression = true
            EvictionPolicy = TTL
        }
    
    /// Cost-aware cache optimizer
    module CostAwareCaching =
        
        /// Cost analysis for caching decision
        type CostAnalysis = {
            ComputeCost: decimal
            StorageCost: decimal
            NetworkCost: decimal
            TotalCost: decimal
            Savings: decimal
            ROI: float
        }
        
        /// Analyze cost benefits of caching
        let analyzeCachingCosts (requestFrequency: float) (avgResponseTime: TimeSpan) (tokenCost: decimal) (storageCost: decimal) : CostAnalysis =
            let computeCost = tokenCost * (decimal requestFrequency)
            let networkCost = 0.001m * (decimal requestFrequency) // Estimate network cost
            let totalCost = computeCost + storageCost + networkCost
            let savings = max 0.0m (computeCost - storageCost)
            let roi = if totalCost > 0.0m then float (savings / totalCost) else 0.0
            
            {
                ComputeCost = computeCost
                StorageCost = storageCost
                NetworkCost = networkCost
                TotalCost = totalCost
                Savings = savings
                ROI = roi
            }
        
        /// Determine if item should be cached based on cost analysis
        let shouldCache (analysis: CostAnalysis) (threshold: decimal) : bool =
            analysis.Savings > threshold && analysis.ROI > 0.2
        
        /// Optimize cache based on cost analysis
        let optimizeCacheForCost (cacheManager: ICacheManager) (costThreshold: decimal) : Async<unit> =
            async {
                let! stats = cacheManager.GetStats()
                
                // If total cost is above threshold, trigger optimization
                if stats.TotalCost > costThreshold then
                    do! cacheManager.OptimizeCache()
                
                return ()
            }
    
    /// Cache warming strategies
    module CacheWarming =
        
        /// Warm cache with common patterns
        let warmCacheWithCommonPatterns (cacheManager: ICacheManager) (commonPatterns: string list) : Async<unit> =
            async {
                for pattern in commonPatterns do
                    // Pre-compute common patterns and store in cache
                    let metadata = {
                        EntryType = TemplateResponse
                        CreatedAt = DateTime.UtcNow
                        ExpiresAt = DateTime.UtcNow.AddHours(24.0)
                        TokenCost = Some 50
                        ApiCost = Some 0.01m
                        ProviderId = None
                        HitCount = 0
                        Size = pattern.Length
                    }
                    
                    let key = $"warm_cache:{pattern.GetHashCode()}"
                    do! cacheManager.Set key pattern (TimeSpan.FromHours(24.0)) metadata
                
                return ()
            }
        
        /// Warm cache with provider-specific patterns
        let warmCacheWithProviderPatterns (cacheManager: ICacheManager) (providerId: ProviderId) (patterns: string list) : Async<unit> =
            async {
                for pattern in patterns do
                    let metadata = {
                        EntryType = ProviderResponse
                        CreatedAt = DateTime.UtcNow
                        ExpiresAt = DateTime.UtcNow.AddHours(8.0)
                        TokenCost = Some 100
                        ApiCost = Some 0.05m
                        ProviderId = Some providerId
                        HitCount = 0
                        Size = pattern.Length
                    }
                    
                    let key = $"warm_cache:{providerId}:{pattern.GetHashCode()}"
                    do! cacheManager.Set key pattern (TimeSpan.FromHours(8.0)) metadata
                
                return ()
            }
    
    /// Cache performance monitoring
    module CacheMonitoring =
        
        /// Cache performance metrics
        type CachePerformanceMetrics = {
            HitRate: float
            MissRate: float
            AverageResponseTime: TimeSpan
            CostSavings: decimal
            TokenSavings: int64
            EvictionRate: float
            CacheEfficiency: float
        }
        
        /// Calculate cache performance metrics
        let calculateCacheMetrics (stats: CacheStats) : CachePerformanceMetrics =
            let totalRequests = stats.HitCount + stats.MissCount
            let hitRate = if totalRequests > 0L then float stats.HitCount / float totalRequests else 0.0
            let missRate = if totalRequests > 0L then float stats.MissCount / float totalRequests else 0.0
            let evictionRate = if totalRequests > 0L then float stats.EvictionCount / float totalRequests else 0.0
            let cacheEfficiency = if stats.TotalCost > 0.0m then float (stats.TotalCost / (decimal totalRequests)) else 0.0
            
            {
                HitRate = hitRate
                MissRate = missRate
                AverageResponseTime = stats.AverageResponseTime
                CostSavings = stats.TotalCost
                TokenSavings = stats.TotalTokens
                EvictionRate = evictionRate
                CacheEfficiency = cacheEfficiency
            }
        
        /// Monitor cache performance and trigger optimization
        let monitorAndOptimize (cacheManager: ICacheManager) (targetHitRate: float) : Async<unit> =
            async {
                let! stats = cacheManager.GetStats()
                let metrics = calculateCacheMetrics stats
                
                if metrics.HitRate < targetHitRate then
                    do! cacheManager.OptimizeCache()
                
                return ()
            }
    
    /// Default cache manager factory
    let createDefaultCacheManager () : ICacheManager =
        let config = CacheStrategies.providerResponseCacheStrategy
        let manager = AICacheManager(config)
        manager.GetCacheInstance()
    
    /// Create specialized cache manager for specific use case
    let createSpecializedCacheManager (entryType: CacheEntryType) : ICacheManager =
        let config = 
            match entryType with
            | VisionAnalysis -> CacheStrategies.visionAnalysisCacheStrategy
            | TemplateResponse -> CacheStrategies.templateResponseCacheStrategy
            | ProviderResponse -> CacheStrategies.providerResponseCacheStrategy
            | IntentAnalysis -> CacheStrategies.intentAnalysisCacheStrategy
            | WorkflowPlan -> CacheStrategies.workflowPlanCacheStrategy
            | _ -> CacheStrategies.providerResponseCacheStrategy
        
        let manager = AICacheManager(config)
        manager.GetCacheInstance()