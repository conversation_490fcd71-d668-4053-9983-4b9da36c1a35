namespace Automation.AI

open System
open System.Collections.Concurrent
open System.Diagnostics
open System.Threading
open System.Threading.Tasks
open Automation.Core
open Automation.AI.IntegratedCostAwareSystem
open Automation.Utilities.Logging

/// Performance testing framework for AI workloads
module PerformanceTesting =
    
    /// Test scenario configuration
    type TestScenario = {
        ScenarioId: string
        Name: string
        Description: string
        RequestType: string // "simple_command", "complex_workflow", "batch_processing"
        ConcurrentUsers: int
        RequestsPerUser: int
        RampUpDurationSeconds: int
        TestDurationSeconds: int
        ThinkTimeSeconds: int // Time between requests
        RequestTemplates: AIRequestTemplate list
        ExpectedResponseTime: TimeSpan
        ExpectedThroughput: float // requests per second
        ExpectedSuccessRate: float // 0.0-1.0
    }
    and AIRequestTemplate = {
        TemplateId: string
        Model: string
        TokenCount: int option
        ImageCount: int option
        Priority: RequestPriority
        Complexity: TestComplexity
        Payload: string
    }
    and TestComplexity = Simple | Medium | Complex | VeryComplex
    
    /// Performance test configuration
    type PerformanceTestConfig = {
        TestSuiteId: string
        TestName: string
        BaseUrl: string option // If testing HTTP endpoints
        Scenarios: TestScenario list
        WarmupRequests: int
        CooldownTimeSeconds: int
        CollectDetailedMetrics: bool
        EnableRealTimeReporting: bool
        OutputDirectory: string
        MaxConcurrentConnections: int
        TimeoutSeconds: int
    }
    
    /// Default performance test configuration
    let defaultPerfTestConfig = {
        TestSuiteId = "default-ai-perf-test"
        TestName = "AI Automation Performance Test"
        BaseUrl = None
        Scenarios = []
        WarmupRequests = 10
        CooldownTimeSeconds = 30
        CollectDetailedMetrics = true
        EnableRealTimeReporting = true
        OutputDirectory = "./perf-test-results"
        MaxConcurrentConnections = 100
        TimeoutSeconds = 120
    }
    
    /// Performance metrics collected during testing
    type PerformanceMetrics = {
        TestRunId: string
        ScenarioId: string
        RequestId: string
        Timestamp: DateTimeOffset
        ResponseTime: TimeSpan
        Success: bool
        ErrorMessage: string option
        ProviderUsed: AIFramework.ProviderId option
        ActualCost: decimal option
        TokensProcessed: int option
        ImagesProcessed: int option
        MemoryUsage: long option // bytes
        CpuUsage: float option // percentage
        NetworkLatency: TimeSpan option
        QueueWaitTime: TimeSpan option
    }
    
    /// Aggregated test results
    type TestResults = {
        TestRunId: string
        TestName: string
        StartTime: DateTimeOffset
        EndTime: DateTimeOffset
        TotalDuration: TimeSpan
        Scenarios: ScenarioResults list
        OverallMetrics: OverallMetrics
        ResourceUtilization: ResourceUtilization
        PerformanceTargetsMet: bool
        Issues: string list
    }
    and ScenarioResults = {
        ScenarioId: string
        ScenarioName: string
        TotalRequests: int
        SuccessfulRequests: int
        FailedRequests: int
        SuccessRate: float
        AverageResponseTime: TimeSpan
        MedianResponseTime: TimeSpan
        P95ResponseTime: TimeSpan
        P99ResponseTime: TimeSpan
        MinResponseTime: TimeSpan
        MaxResponseTime: TimeSpan
        Throughput: float // requests per second
        TotalCost: decimal
        AverageCost: decimal
        CostEfficiency: float // throughput per dollar
        MetPerformanceTarget: bool
        PercentileBreakdown: (int * TimeSpan) list // percentile -> response time
    }
    and OverallMetrics = {
        TotalRequests: int
        OverallSuccessRate: float
        OverallThroughput: float
        OverallAverageResponseTime: TimeSpan
        TotalTestCost: decimal
        CostPerRequest: decimal
        ErrorDistribution: Map<string, int>
        ProviderDistribution: Map<AIFramework.ProviderId, int>
    }
    and ResourceUtilization = {
        PeakMemoryUsage: long // bytes
        AverageMemoryUsage: long
        PeakCpuUsage: float // percentage
        AverageCpuUsage: float
        NetworkUtilization: float
        StorageIOPS: float option
        ThreadPoolMetrics: ThreadPoolMetrics option
    }
    and ThreadPoolMetrics = {
        PeakActiveThreads: int
        AverageActiveThreads: float
        ThreadPoolUtilization: float
        QueueLength: int
    }
    
    /// Performance test runner
    type PerformanceTestRunner(config: PerformanceTestConfig, auditLogger: AuditLogger.AuditLogger option) =
        let metricsCollector = ConcurrentQueue<PerformanceMetrics>()
        let cancellationTokenSource = new CancellationTokenSource()
        let performanceCounters = System.Collections.Generic.Dictionary<string, PerformanceCounter>()
        let mutable isRunning = false
        
        do
            // Initialize performance counters
            try
                performanceCounters.["CPU"] <- new PerformanceCounter("Processor", "% Processor Time", "_Total")
                performanceCounters.["Memory"] <- new PerformanceCounter("Memory", "Available MBytes")
                performanceCounters.["Network"] <- new PerformanceCounter("Network Interface", "Bytes Total/sec", "_Total")
            with
            | ex -> 
                auditLogger |> Option.iter (fun logger ->
                    logger.LogEvent(
                        AuditLogger.Warning,
                        AuditLogger.Warning,
                        "PerformanceTestRunner",
                        sprintf "Failed to initialize performance counters: %s" ex.Message,
                        Map.ofList [("exception", box ex.Message)]
                    )
                )
        
        /// Create sample AI request templates
        member _.CreateSampleRequestTemplates() = [
            {
                TemplateId = "simple-text-command"
                Model = "gpt-3.5-turbo"
                TokenCount = Some 300
                ImageCount = None
                Priority = Low
                Complexity = Simple
                Payload = "Navigate to google.com and search for 'F# programming'"
            }
            {
                TemplateId = "medium-workflow"
                Model = "gpt-4"
                TokenCount = Some 800
                ImageCount = Some 1
                Priority = Medium
                Complexity = Medium
                Payload = "Analyze this webpage screenshot and extract all form fields, then fill them with test data"
            }
            {
                TemplateId = "complex-automation"
                Model = "gpt-4"
                TokenCount = Some 1500
                ImageCount = Some 3
                Priority = High
                Complexity = Complex
                Payload = "Perform a complete e-commerce checkout flow: search product, add to cart, enter shipping info, and complete payment"
            }
            {
                TemplateId = "very-complex-workflow"
                Model = "claude-3-sonnet"
                TokenCount = Some 2500
                ImageCount = Some 5
                Priority = High
                Complexity = VeryComplex
                Payload = "Automate a multi-step business process: login, navigate to reports section, generate quarterly report, download as PDF, and email to stakeholders"
            }
        ]
        
        /// Create default test scenarios
        member this.CreateDefaultScenarios() = [
            {
                ScenarioId = "simple-load-test"
                Name = "Simple Command Load Test"
                Description = "Test simple AI commands under moderate load"
                RequestType = "simple_command"
                ConcurrentUsers = 10
                RequestsPerUser = 20
                RampUpDurationSeconds = 30
                TestDurationSeconds = 300
                ThinkTimeSeconds = 2
                RequestTemplates = this.CreateSampleRequestTemplates() |> List.filter (fun t -> t.Complexity = Simple)
                ExpectedResponseTime = TimeSpan.FromSeconds(2.0)
                ExpectedThroughput = 5.0
                ExpectedSuccessRate = 0.95
            }
            {
                ScenarioId = "complex-workflow-test"
                Name = "Complex Workflow Test"
                Description = "Test complex AI workflows under realistic load"
                RequestType = "complex_workflow"
                ConcurrentUsers = 5
                RequestsPerUser = 10
                RampUpDurationSeconds = 60
                TestDurationSeconds = 600
                ThinkTimeSeconds = 5
                RequestTemplates = this.CreateSampleRequestTemplates() |> List.filter (fun t -> t.Complexity = Complex || t.Complexity = VeryComplex)
                ExpectedResponseTime = TimeSpan.FromSeconds(10.0)
                ExpectedThroughput = 1.0
                ExpectedSuccessRate = 0.90
            }
            {
                ScenarioId = "stress-test"
                Name = "Stress Test"
                Description = "Push system to limits with high concurrent load"
                RequestType = "stress_test"
                ConcurrentUsers = 50
                RequestsPerUser = 10
                RampUpDurationSeconds = 120
                TestDurationSeconds = 300
                ThinkTimeSeconds = 1
                RequestTemplates = this.CreateSampleRequestTemplates()
                ExpectedResponseTime = TimeSpan.FromSeconds(5.0)
                ExpectedThroughput = 10.0
                ExpectedSuccessRate = 0.85
            }
            {
                ScenarioId = "endurance-test"
                Name = "Endurance Test"
                Description = "Long-running test to check for memory leaks and degradation"
                RequestType = "endurance"
                ConcurrentUsers = 20
                RequestsPerUser = 50
                RampUpDurationSeconds = 300
                TestDurationSeconds = 3600 // 1 hour
                ThinkTimeSeconds = 10
                RequestTemplates = this.CreateSampleRequestTemplates() |> List.filter (fun t -> t.Complexity = Simple || t.Complexity = Medium)
                ExpectedResponseTime = TimeSpan.FromSeconds(3.0)
                ExpectedThroughput = 2.0
                ExpectedSuccessRate = 0.92
            }
        ]
        
        /// Collect system metrics
        member _.CollectSystemMetrics() =
            try
                let cpuUsage = 
                    match performanceCounters.TryGetValue("CPU") with
                    | true, counter -> Some (counter.NextValue() |> float)
                    | false, _ -> None
                
                let availableMemory = 
                    match performanceCounters.TryGetValue("Memory") with
                    | true, counter -> Some (counter.NextValue() |> int64 |> (*) 1024L |> (*) 1024L) // Convert MB to bytes
                    | false, _ -> None
                
                let networkUsage = 
                    match performanceCounters.TryGetValue("Network") with
                    | true, counter -> Some (counter.NextValue() |> float)
                    | false, _ -> None
                
                let currentProcess = Process.GetCurrentProcess()
                let memoryUsage = currentProcess.WorkingSet64
                let threadCount = currentProcess.Threads.Count
                
                {|
                    CpuUsage = cpuUsage
                    MemoryUsage = memoryUsage
                    AvailableMemory = availableMemory
                    NetworkUsage = networkUsage
                    ThreadCount = threadCount
                    Timestamp = DateTimeOffset.UtcNow
                |}
            with
            | ex ->
                auditLogger |> Option.iter (fun logger ->
                    logger.LogEvent(
                        AuditLogger.Warning,
                        AuditLogger.Warning,
                        "PerformanceTestRunner",
                        sprintf "Failed to collect system metrics: %s" ex.Message,
                        Map.ofList [("exception", box ex.Message)]
                    )
                )
                {|
                    CpuUsage = None
                    MemoryUsage = 0L
                    AvailableMemory = None
                    NetworkUsage = None
                    ThreadCount = 0
                    Timestamp = DateTimeOffset.UtcNow
                |}
        
        /// Execute a single AI request
        member _.ExecuteAIRequest(system: IntegratedCostAwareSystem, template: AIRequestTemplate, scenarioId: string, requestId: string) =
            async {
                let stopwatch = Stopwatch.StartNew()
                let systemMetricsBefore = this.CollectSystemMetrics()
                
                try
                    // Create request context
                    let context = {
                        RequestId = requestId
                        UserId = Some "perf-test-user"
                        RequestType = "automation"
                        Priority = template.Priority
                        BudgetConstraint = Some 5.0m
                        LatencyRequirement = Some (TimeSpan.FromSeconds(30.0))
                        QualityRequirement = Some 0.8
                        PreferredProviders = None
                        Model = template.Model
                        TokenCount = template.TokenCount
                        ImageCount = template.ImageCount
                        Metadata = Map.ofList [
                            ("test_scenario", scenarioId)
                            ("complexity", template.Complexity.ToString())
                            ("template_id", template.TemplateId)
                        ]
                    }
                    
                    // Execute request
                    let! selectionResult = system.SelectProvider(context)
                    
                    stopwatch.Stop()
                    let systemMetricsAfter = this.CollectSystemMetrics()
                    
                    match selectionResult with
                    | Ok selection ->
                        // Simulate actual AI processing time based on complexity
                        let simulatedProcessingTime = 
                            match template.Complexity with
                            | Simple -> Random().Next(500, 2000)
                            | Medium -> Random().Next(1500, 5000)
                            | Complex -> Random().Next(4000, 12000)
                            | VeryComplex -> Random().Next(8000, 25000)
                        
                        do! Async.Sleep(simulatedProcessingTime)
                        
                        // Record performance
                        let actualCost = selection.EstimatedCost * decimal (0.8 + Random().NextDouble() * 0.4)
                        let actualLatency = stopwatch.Elapsed
                        let success = Random().NextDouble() > 0.05 // 95% success rate baseline
                        
                        system.RecordPerformance(
                            requestId, selection.SelectedProvider, selection.Model,
                            actualCost, actualLatency, success, Some 0.85, Some 0.9,
                            template.TokenCount, template.ImageCount)
                        
                        let metrics = {
                            TestRunId = config.TestSuiteId
                            ScenarioId = scenarioId
                            RequestId = requestId
                            Timestamp = DateTimeOffset.UtcNow
                            ResponseTime = stopwatch.Elapsed
                            Success = success
                            ErrorMessage = if success then None else Some "Simulated failure"
                            ProviderUsed = Some selection.SelectedProvider
                            ActualCost = Some actualCost
                            TokensProcessed = template.TokenCount
                            ImagesProcessed = template.ImageCount
                            MemoryUsage = Some systemMetricsAfter.MemoryUsage
                            CpuUsage = systemMetricsAfter.CpuUsage
                            NetworkLatency = None
                            QueueWaitTime = None
                        }
                        
                        metricsCollector.Enqueue(metrics)
                        return Ok metrics
                        
                    | Error error ->
                        stopwatch.Stop()
                        
                        let metrics = {
                            TestRunId = config.TestSuiteId
                            ScenarioId = scenarioId
                            RequestId = requestId
                            Timestamp = DateTimeOffset.UtcNow
                            ResponseTime = stopwatch.Elapsed
                            Success = false
                            ErrorMessage = Some error
                            ProviderUsed = None
                            ActualCost = None
                            TokensProcessed = template.TokenCount
                            ImagesProcessed = template.ImageCount
                            MemoryUsage = Some systemMetricsAfter.MemoryUsage
                            CpuUsage = systemMetricsAfter.CpuUsage
                            NetworkLatency = None
                            QueueWaitTime = None
                        }
                        
                        metricsCollector.Enqueue(metrics)
                        return Error error
                
                with
                | ex ->
                    stopwatch.Stop()
                    
                    let metrics = {
                        TestRunId = config.TestSuiteId
                        ScenarioId = scenarioId
                        RequestId = requestId
                        Timestamp = DateTimeOffset.UtcNow
                        ResponseTime = stopwatch.Elapsed
                        Success = false
                        ErrorMessage = Some ex.Message
                        ProviderUsed = None
                        ActualCost = None
                        TokensProcessed = template.TokenCount
                        ImagesProcessed = template.ImageCount
                        MemoryUsage = None
                        CpuUsage = None
                        NetworkLatency = None
                        QueueWaitTime = None
                    }
                    
                    metricsCollector.Enqueue(metrics)
                    return Error ex.Message
            }
        
        /// Execute a test scenario
        member this.ExecuteScenario(system: IntegratedCostAwareSystem, scenario: TestScenario) =
            async {
                auditLogger |> Option.iter (fun logger ->
                    logger.LogEvent(
                        AuditLogger.Info,
                        AuditLogger.Info,
                        "PerformanceTestRunner",
                        sprintf "Starting scenario: %s with %d concurrent users" scenario.Name scenario.ConcurrentUsers,
                        Map.ofList [
                            ("scenarioId", box scenario.ScenarioId)
                            ("concurrentUsers", box scenario.ConcurrentUsers)
                            ("requestsPerUser", box scenario.RequestsPerUser)
                        ]
                    )
                )
                
                let semaphore = new SemaphoreSlim(scenario.ConcurrentUsers, scenario.ConcurrentUsers)
                let userTasks = ResizeArray<Task<unit>>()
                
                // Create user simulation tasks
                for userId in 1..scenario.ConcurrentUsers do
                    let userTask = Task.Run(fun () ->
                        async {
                            // Ramp-up delay
                            let rampUpDelay = (scenario.RampUpDurationSeconds * 1000 * userId) / scenario.ConcurrentUsers
                            do! Async.Sleep(rampUpDelay)
                            
                            do! semaphore.WaitAsync() |> Async.AwaitTask
                            
                            try
                                // Execute requests for this user
                                for requestNum in 1..scenario.RequestsPerUser do
                                    if not cancellationTokenSource.Token.IsCancellationRequested then
                                        let template = scenario.RequestTemplates.[Random().Next(scenario.RequestTemplates.Length)]
                                        let requestId = sprintf "%s-user%d-req%d" scenario.ScenarioId userId requestNum
                                        
                                        let! result = this.ExecuteAIRequest(system, template, scenario.ScenarioId, requestId)
                                        
                                        match result with
                                        | Ok _ -> ()
                                        | Error error ->
                                            auditLogger |> Option.iter (fun logger ->
                                                logger.LogEvent(
                                                    AuditLogger.Warning,
                                                    AuditLogger.Warning,
                                                    "PerformanceTestRunner",
                                                    sprintf "Request failed: %s" error,
                                                    Map.ofList [("requestId", box requestId); ("error", box error)]
                                                )
                                            )
                                        
                                        // Think time between requests
                                        if requestNum < scenario.RequestsPerUser then
                                            do! Async.Sleep(scenario.ThinkTimeSeconds * 1000)
                            finally
                                semaphore.Release() |> ignore
                        } |> Async.StartAsTask)
                    
                    userTasks.Add(userTask)
                
                // Wait for all user tasks to complete or timeout
                let timeoutTask = Task.Delay(scenario.TestDurationSeconds * 1000)
                let allUserTasks = Task.WhenAll(userTasks)
                
                let! completedTask = Task.WhenAny(allUserTasks, timeoutTask) |> Async.AwaitTask
                
                if completedTask = timeoutTask then
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.Warning,
                            AuditLogger.Warning,
                            "PerformanceTestRunner",
                            sprintf "Scenario %s timed out after %d seconds" scenario.Name scenario.TestDurationSeconds,
                            Map.ofList [("scenarioId", box scenario.ScenarioId)]
                        )
                    )
                    cancellationTokenSource.Cancel()
                
                auditLogger |> Option.iter (fun logger ->
                    logger.LogEvent(
                        AuditLogger.Info,
                        AuditLogger.Info,
                        "PerformanceTestRunner",
                        sprintf "Completed scenario: %s" scenario.Name,
                        Map.ofList [("scenarioId", box scenario.ScenarioId)]
                    )
                )
            }
        
        /// Analyze collected metrics and generate results
        member _.AnalyzeResults(scenarios: TestScenario list, startTime: DateTimeOffset, endTime: DateTimeOffset) =
            let allMetrics = metricsCollector.ToArray()
            
            let scenarioResults = 
                scenarios
                |> List.map (fun scenario ->
                    let scenarioMetrics = allMetrics |> Array.filter (fun m -> m.ScenarioId = scenario.ScenarioId)
                    
                    if scenarioMetrics.Length = 0 then
                        {
                            ScenarioId = scenario.ScenarioId
                            ScenarioName = scenario.Name
                            TotalRequests = 0
                            SuccessfulRequests = 0
                            FailedRequests = 0
                            SuccessRate = 0.0
                            AverageResponseTime = TimeSpan.Zero
                            MedianResponseTime = TimeSpan.Zero
                            P95ResponseTime = TimeSpan.Zero
                            P99ResponseTime = TimeSpan.Zero
                            MinResponseTime = TimeSpan.Zero
                            MaxResponseTime = TimeSpan.Zero
                            Throughput = 0.0
                            TotalCost = 0.0m
                            AverageCost = 0.0m
                            CostEfficiency = 0.0
                            MetPerformanceTarget = false
                            PercentileBreakdown = []
                        }
                    else
                        let successfulRequests = scenarioMetrics |> Array.filter (fun m -> m.Success) |> Array.length
                        let failedRequests = scenarioMetrics.Length - successfulRequests
                        let successRate = float successfulRequests / float scenarioMetrics.Length
                        
                        let responseTimes = scenarioMetrics |> Array.map (fun m -> m.ResponseTime.TotalMilliseconds) |> Array.sort
                        let avgResponseTime = responseTimes |> Array.average |> TimeSpan.FromMilliseconds
                        let medianResponseTime = 
                            let median = responseTimes.[responseTimes.Length / 2]
                            TimeSpan.FromMilliseconds(median)
                        
                        let p95Index = int (float responseTimes.Length * 0.95)
                        let p99Index = int (float responseTimes.Length * 0.99)
                        let p95ResponseTime = TimeSpan.FromMilliseconds(responseTimes.[min p95Index (responseTimes.Length - 1)])
                        let p99ResponseTime = TimeSpan.FromMilliseconds(responseTimes.[min p99Index (responseTimes.Length - 1)])
                        
                        let minResponseTime = TimeSpan.FromMilliseconds(responseTimes |> Array.min)
                        let maxResponseTime = TimeSpan.FromMilliseconds(responseTimes |> Array.max)
                        
                        let totalDuration = (endTime - startTime).TotalSeconds
                        let throughput = float scenarioMetrics.Length / totalDuration
                        
                        let totalCost = scenarioMetrics |> Array.sumBy (fun m -> defaultArg m.ActualCost 0.0m)
                        let avgCost = if scenarioMetrics.Length > 0 then totalCost / decimal scenarioMetrics.Length else 0.0m
                        let costEfficiency = if totalCost > 0.0m then float throughput / float totalCost else 0.0
                        
                        let metTarget = avgResponseTime <= scenario.ExpectedResponseTime && successRate >= scenario.ExpectedSuccessRate
                        
                        let percentiles = [50; 75; 90; 95; 99]
                        let percentileBreakdown = 
                            percentiles
                            |> List.map (fun p ->
                                let index = int (float responseTimes.Length * float p / 100.0)
                                let safeIndex = min index (responseTimes.Length - 1)
                                (p, TimeSpan.FromMilliseconds(responseTimes.[safeIndex])))
                        
                        {
                            ScenarioId = scenario.ScenarioId
                            ScenarioName = scenario.Name
                            TotalRequests = scenarioMetrics.Length
                            SuccessfulRequests = successfulRequests
                            FailedRequests = failedRequests
                            SuccessRate = successRate
                            AverageResponseTime = avgResponseTime
                            MedianResponseTime = medianResponseTime
                            P95ResponseTime = p95ResponseTime
                            P99ResponseTime = p99ResponseTime
                            MinResponseTime = minResponseTime
                            MaxResponseTime = maxResponseTime
                            Throughput = throughput
                            TotalCost = totalCost
                            AverageCost = avgCost
                            CostEfficiency = costEfficiency
                            MetPerformanceTarget = metTarget
                            PercentileBreakdown = percentileBreakdown
                        })
            
            let overallMetrics = 
                let totalRequests = allMetrics.Length
                let successfulRequests = allMetrics |> Array.filter (fun m -> m.Success) |> Array.length
                let overallSuccessRate = if totalRequests > 0 then float successfulRequests / float totalRequests else 0.0
                
                let totalDuration = (endTime - startTime).TotalSeconds
                let overallThroughput = float totalRequests / totalDuration
                
                let avgResponseTime = 
                    if totalRequests > 0 then
                        allMetrics |> Array.averageBy (fun m -> m.ResponseTime.TotalMilliseconds) |> TimeSpan.FromMilliseconds
                    else TimeSpan.Zero
                
                let totalCost = allMetrics |> Array.sumBy (fun m -> defaultArg m.ActualCost 0.0m)
                let costPerRequest = if totalRequests > 0 then totalCost / decimal totalRequests else 0.0m
                
                let errorDistribution = 
                    allMetrics 
                    |> Array.filter (fun m -> not m.Success)
                    |> Array.groupBy (fun m -> defaultArg m.ErrorMessage "Unknown error")
                    |> Array.map (fun (error, metrics) -> (error, metrics.Length))
                    |> Map.ofArray
                
                let providerDistribution = 
                    allMetrics 
                    |> Array.choose (fun m -> m.ProviderUsed)
                    |> Array.groupBy id
                    |> Array.map (fun (provider, metrics) -> (provider, metrics.Length))
                    |> Map.ofArray
                
                {
                    TotalRequests = totalRequests
                    OverallSuccessRate = overallSuccessRate
                    OverallThroughput = overallThroughput
                    OverallAverageResponseTime = avgResponseTime
                    TotalTestCost = totalCost
                    CostPerRequest = costPerRequest
                    ErrorDistribution = errorDistribution
                    ProviderDistribution = providerDistribution
                }
            
            let resourceUtilization = 
                let memoryMetrics = allMetrics |> Array.choose (fun m -> m.MemoryUsage)
                let cpuMetrics = allMetrics |> Array.choose (fun m -> m.CpuUsage)
                
                {
                    PeakMemoryUsage = if memoryMetrics.Length > 0 then Array.max memoryMetrics else 0L
                    AverageMemoryUsage = if memoryMetrics.Length > 0 then Array.average memoryMetrics |> int64 else 0L
                    PeakCpuUsage = if cpuMetrics.Length > 0 then Array.max cpuMetrics else 0.0
                    AverageCpuUsage = if cpuMetrics.Length > 0 then Array.average cpuMetrics else 0.0
                    NetworkUtilization = 0.0 // Placeholder
                    StorageIOPS = None
                    ThreadPoolMetrics = None
                }
            
            let performanceTargetsMet = scenarioResults |> List.forall (fun s -> s.MetPerformanceTarget)
            
            let issues = ResizeArray<string>()
            
            // Check for performance issues
            if overallMetrics.OverallSuccessRate < 0.9 then
                issues.Add(sprintf "Low success rate: %.1f%%" (overallMetrics.OverallSuccessRate * 100.0))
            
            if resourceUtilization.PeakMemoryUsage > 512L * 1024L * 1024L then // 512MB
                issues.Add(sprintf "High memory usage: %d MB" (resourceUtilization.PeakMemoryUsage / 1024L / 1024L))
            
            if resourceUtilization.PeakCpuUsage > 90.0 then
                issues.Add(sprintf "High CPU usage: %.1f%%" resourceUtilization.PeakCpuUsage)
            
            for scenario in scenarioResults do
                if not scenario.MetPerformanceTarget then
                    issues.Add(sprintf "Scenario '%s' did not meet performance targets" scenario.ScenarioName)
            
            {
                TestRunId = config.TestSuiteId
                TestName = config.TestName
                StartTime = startTime
                EndTime = endTime
                TotalDuration = endTime - startTime
                Scenarios = scenarioResults
                OverallMetrics = overallMetrics
                ResourceUtilization = resourceUtilization
                PerformanceTargetsMet = performanceTargetsMet
                Issues = issues.ToArray() |> Array.toList
            }
        
        /// Run complete performance test suite
        member this.RunPerformanceTest(system: IntegratedCostAwareSystem) =
            async {
                isRunning <- true
                let startTime = DateTimeOffset.UtcNow
                
                auditLogger |> Option.iter (fun logger ->
                    logger.LogEvent(
                        AuditLogger.Info,
                        AuditLogger.Info,
                        "PerformanceTestRunner",
                        sprintf "Starting performance test suite: %s" config.TestName,
                        Map.ofList [
                            ("testSuiteId", box config.TestSuiteId)
                            ("scenarioCount", box config.Scenarios.Length)
                        ]
                    )
                )
                
                try
                    // Warmup phase
                    if config.WarmupRequests > 0 then
                        auditLogger |> Option.iter (fun logger ->
                            logger.LogEvent(
                                AuditLogger.Info,
                                AuditLogger.Info,
                                "PerformanceTestRunner",
                                sprintf "Running warmup with %d requests" config.WarmupRequests,
                                Map.ofList [("warmupRequests", box config.WarmupRequests)]
                            )
                        )
                        
                        let warmupTemplates = this.CreateSampleRequestTemplates()
                        for i in 1..config.WarmupRequests do
                            let template = warmupTemplates.[Random().Next(warmupTemplates.Length)]
                            let! _ = this.ExecuteAIRequest(system, template, "warmup", sprintf "warmup-req-%d" i)
                            ()
                    
                    // Clear warmup metrics
                    while not metricsCollector.IsEmpty do
                        let mutable result = Unchecked.defaultof<PerformanceMetrics>
                        metricsCollector.TryDequeue(&result) |> ignore
                    
                    // Execute test scenarios
                    for scenario in config.Scenarios do
                        if not cancellationTokenSource.Token.IsCancellationRequested then
                            do! this.ExecuteScenario(system, scenario)
                            
                            // Cooldown between scenarios
                            if config.CooldownTimeSeconds > 0 then
                                do! Async.Sleep(config.CooldownTimeSeconds * 1000)
                    
                    let endTime = DateTimeOffset.UtcNow
                    
                    // Analyze results
                    let results = this.AnalyzeResults(config.Scenarios, startTime, endTime)
                    
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.Info,
                            AuditLogger.Info,
                            "PerformanceTestRunner",
                            (sprintf "Performance test completed. Success rate: %.1f%%, Avg response time: %.2fs" 
                                (results.OverallMetrics.OverallSuccessRate * 100.0)
                                results.OverallMetrics.OverallAverageResponseTime.TotalSeconds),
                            Map.ofList [
                                ("totalRequests", box results.OverallMetrics.TotalRequests)
                                ("successRate", box results.OverallMetrics.OverallSuccessRate)
                                ("avgResponseTime", box results.OverallMetrics.OverallAverageResponseTime.TotalSeconds)
                                ("totalCost", box results.OverallMetrics.TotalTestCost)
                                ("performanceTargetsMet", box results.PerformanceTargetsMet)
                            ]
                        )
                    )
                    
                    Ok results
                    
                with
                | ex ->
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.Error,
                            AuditLogger.Error,
                            "PerformanceTestRunner",
                            sprintf "Performance test failed: %s" ex.Message,
                            Map.ofList [("exception", box ex.Message)]
                        )
                    )
                    
                    Error ex.Message
                |> fun result ->
                    isRunning <- false
                    result
            }
        
        interface IDisposable with
            member _.Dispose() =
                if isRunning then
                    cancellationTokenSource.Cancel()
                
                cancellationTokenSource.Dispose()
                
                for kvp in performanceCounters do
                    try
                        kvp.Value.Dispose()
                    with _ -> ()
    
    /// Create performance test runner with default configuration
    let createPerformanceTestRunner (config: PerformanceTestConfig option) (auditLogger: AuditLogger.AuditLogger option) =
        let config = defaultArg config defaultPerfTestConfig
        new PerformanceTestRunner(config, auditLogger)
