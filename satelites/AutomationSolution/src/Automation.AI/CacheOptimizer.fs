namespace Automation.AI

open System
open System.Collections.Concurrent
open System.Security.Cryptography
open System.Text
open System.Text.Json
open Automation.Core
open Automation.Utilities.Logging

/// AI response caching and optimization for reducing latency and costs
module CacheOptimizer =
    
    /// Cached AI response structure
    type CachedAIResponse = {
        CacheKey: string
        Response: string
        Model: string
        ProviderId: AIFramework.ProviderId
        TokensUsed: int option
        ImageCount: int option
        Cost: decimal option
        QualityScore: float option
        Timestamp: DateTimeOffset
        ExpiryTime: DateTimeOffset
        AccessCount: int
        LastAccessed: DateTimeOffset
        Confidence: float option
        ResponseType: string // "text", "vision", "action", "healing"
        ValidationHash: string // Hash of input that generated this response
    }
    
    /// Cache strategy configuration
    type CacheStrategy =
        | NoCache // Don't cache this type of response
        | ShortTerm of minutes: int // Cache for specified minutes
        | LongTerm of hours: int // Cache for specified hours
        | Persistent of days: int // Cache for specified days
        | Smart of baseMinutes: int * qualityThreshold: float // Smart caching based on quality
        | Custom of ttlFunc: (CachedAIResponse -> TimeSpan)
    
    /// Cache optimization configuration
    type CacheOptimizerConfig = {
        EnableCaching: bool
        DefaultStrategy: CacheStrategy
        MaxCacheSize: int // Maximum number of cached responses
        CleanupIntervalMinutes: int
        StrategyByResponseType: Map<string, CacheStrategy>
        MinQualityForCaching: float option
        CostThresholdForCaching: decimal option // Cache expensive responses longer
        PreferentialCaching: bool // Prefer caching high-cost, high-quality responses
        CompressionEnabled: bool
        EncryptSensitiveResponses: bool
        AuditLogger: AuditLogger.AuditLogger option
    }
    
    /// Default cache optimizer configuration
    let defaultCacheOptimizerConfig = {
        EnableCaching = true
        DefaultStrategy = Smart(baseMinutes = 30, qualityThreshold = 0.8)
        MaxCacheSize = 10000
        CleanupIntervalMinutes = 60
        StrategyByResponseType = Map.ofList [
            ("text", LongTerm 6) // 6 hours for text responses
            ("vision", ShortTerm 120) // 2 hours for vision analysis
            ("action", ShortTerm 30) // 30 minutes for action generation
            ("healing", Persistent 1) // 1 day for healing solutions
        ]
        MinQualityForCaching = Some 0.7
        CostThresholdForCaching = Some 0.01m
        PreferentialCaching = true
        CompressionEnabled = true
        EncryptSensitiveResponses = false
        AuditLogger = None
    }
    
    /// Cache hit/miss statistics
    type CacheStatistics = {
        TotalRequests: int64
        CacheHits: int64
        CacheMisses: int64
        CacheHitRate: float
        TotalCostSaved: decimal
        AverageResponseTime: TimeSpan
        CacheSize: int
        TopCachedTypes: (string * int) list
        ExpirationRate: float
    }
    
    /// Create cache key from request parameters
    let createCacheKey (prompt: string) (model: string) (providerId: AIFramework.ProviderId) (imageData: byte[] option) (responseType: string) =
        let input = $"{prompt}|{model}|{providerId}|{responseType}"
        let combinedInput = 
            match imageData with
            | Some data -> input + "|" + Convert.ToBase64String(data)
            | None -> input
        
        use sha256 = SHA256.Create()
        let hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(combinedInput))
        Convert.ToHexString(hashBytes).ToLowerInvariant()
    
    /// Validate cache entry integrity
    let validateCacheEntry (entry: CachedAIResponse) (inputHash: string) =
        entry.ValidationHash = inputHash && entry.ExpiryTime > DateTimeOffset.Now
    
    /// Calculate TTL based on strategy
    let calculateTtl (strategy: CacheStrategy) (response: CachedAIResponse) =
        match strategy with
        | NoCache -> TimeSpan.Zero
        | ShortTerm minutes -> TimeSpan.FromMinutes(float minutes)
        | LongTerm hours -> TimeSpan.FromHours(float hours)
        | Persistent days -> TimeSpan.FromDays(float days)
        | Smart (baseMinutes, qualityThreshold) ->
            let baseTime = TimeSpan.FromMinutes(float baseMinutes)
            let qualityMultiplier = 
                match response.QualityScore with
                | Some score when score >= qualityThreshold -> 2.0 // Double TTL for high quality
                | Some score when score >= 0.5 -> 1.5 // 1.5x TTL for decent quality
                | _ -> 1.0 // Normal TTL for low quality
            
            let costMultiplier = 
                match response.Cost with
                | Some cost when cost > 0.05m -> 1.5 // Longer TTL for expensive responses
                | Some cost when cost > 0.01m -> 1.2 // Slightly longer TTL
                | _ -> 1.0
            
            TimeSpan.FromTicks(int64 (float baseTime.Ticks * qualityMultiplier * costMultiplier))
        | Custom ttlFunc -> ttlFunc response
    
    /// Cache optimizer main class
    type CacheOptimizer(config: CacheOptimizerConfig) =
        let cache = ConcurrentDictionary<string, CachedAIResponse>()
        let mutable totalRequests = 0L
        let mutable cacheHits = 0L
        let mutable cacheMisses = 0L
        let mutable totalCostSaved = 0L
        let responseTimes = ConcurrentQueue<TimeSpan>()
        let maxResponseTimesSample = 1000
        
        // Cleanup timer
        let cleanupTimer = new System.Timers.Timer(float (config.CleanupIntervalMinutes * 60 * 1000))
        
        let evictOldestEntries(count: int) =
            let sortedEntries = 
                cache.ToArray()
                |> Array.sortBy (fun kvp -> kvp.Value.LastAccessed)
                |> Array.take (min count cache.Count)
            
            let evictedCount = ref 0
            for kvp in sortedEntries do
                let mutable removedValue = Unchecked.defaultof<CachedAIResponse>
                if cache.TryRemove(kvp.Key, &removedValue) then
                    incr evictedCount
            
            config.AuditLogger |> Option.iter (fun logger ->
                logger.LogEvent(
                    AuditLogger.AuditEventType.ActionExecution,
                    AuditLogger.AuditSeverity.Info,
                    "CacheOptimizer",
                    $"Evicted {!evictedCount} oldest entries",
                    Map.ofList [("evictedCount", box !evictedCount)]
                )
            )
        
        let performCleanup() =
            // Remove expired entries
            let expiredKeys = 
                cache.Keys
                |> Seq.filter (fun key -> 
                    match cache.TryGetValue(key) with
                    | true, response -> response.ExpiryTime <= DateTimeOffset.Now
                    | _ -> false)
                |> List.ofSeq
            
            for key in expiredKeys do
                cache.TryRemove(key) |> ignore
            
            if expiredKeys.Length > 0 then
                config.AuditLogger |> Option.iter (fun logger ->
                    logger.LogEvent(
                        AuditLogger.AuditEventType.ActionExecution,
                        AuditLogger.AuditSeverity.Info,
                        "CacheOptimizer",
                        $"Expired entries cleaned: {expiredKeys.Length}",
                        Map.ofList [("expiredCount", box expiredKeys.Length)]
                    )
                )
        
        do
            cleanupTimer.Elapsed.Add(fun _ -> performCleanup())
            cleanupTimer.Start()
            
            config.AuditLogger |> Option.iter (fun logger ->
                logger.LogEvent(
                    AuditLogger.AuditEventType.ActionExecution,
                    AuditLogger.AuditSeverity.Info,
                    "CacheOptimizer",
                    "Cache optimizer initialized with config",
                    Map.ofList [
                        ("enableCaching", box config.EnableCaching)
                        ("maxCacheSize", box config.MaxCacheSize)
                        ("defaultStrategy", box (config.DefaultStrategy.ToString()))
                    ]
                )
            )
        
        /// Get cached response if available and valid
        member _.TryGetCachedResponse(prompt: string, model: string, providerId: AIFramework.ProviderId, responseType: string, ?imageData: byte[]) =
            if not config.EnableCaching then
                System.Threading.Interlocked.Increment(&totalRequests) |> ignore
                System.Threading.Interlocked.Increment(&cacheMisses) |> ignore
                None
            else
                let cacheKey = createCacheKey prompt model providerId imageData responseType
                let inputHash = cacheKey // Using the same hash for simplicity
                
                System.Threading.Interlocked.Increment(&totalRequests) |> ignore
                
                match cache.TryGetValue(cacheKey) with
                | true, entry when validateCacheEntry entry inputHash ->
                    // Update access statistics
                    let updatedEntry = {
                        entry with
                            AccessCount = entry.AccessCount + 1
                            LastAccessed = DateTimeOffset.Now
                    }
                    cache.[cacheKey] <- updatedEntry
                    
                    System.Threading.Interlocked.Increment(&cacheHits) |> ignore
                    
                    // Add cost savings
                    let costSaved = defaultArg entry.Cost 0m
                    System.Threading.Interlocked.Add(&totalCostSaved, int64 (costSaved * 100m)) |> ignore // Convert to cents for atomic operation
                    
                    config.AuditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.AuditEventType.HealthCheck,
                            AuditLogger.AuditSeverity.Info,
                            "CacheOptimizer",
                            $"Cache HIT for {responseType} response",
                            Map.ofList [
                                ("cacheKey", box cacheKey)
                                ("model", box model)
                                ("providerId", box (providerId.ToString()))
                                ("costSaved", box costSaved)
                                ("accessCount", box updatedEntry.AccessCount)
                            ]
                        )
                    )
                    
                    Some entry.Response
                | _ ->
                    System.Threading.Interlocked.Increment(&cacheMisses) |> ignore
                    
                    config.AuditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.AuditEventType.HealthCheck,
                            AuditLogger.AuditSeverity.Info,
                            "CacheOptimizer",
                            $"Cache MISS for {responseType} response",
                            Map.ofList [
                                ("cacheKey", box cacheKey)
                                ("model", box model)
                                ("providerId", box (providerId.ToString()))
                            ]
                        )
                    )
                    
                    None
        
        /// Cache AI response
        member _.CacheResponse(prompt: string, model: string, providerId: AIFramework.ProviderId, response: string, responseType: string, 
                              ?imageData: byte[], ?tokensUsed: int, ?cost: decimal, ?qualityScore: float, ?confidence: float) =
            if not config.EnableCaching then
                ()
            else
                // Check quality threshold
                let shouldCache = 
                    match config.MinQualityForCaching, qualityScore with
                    | Some minQuality, Some quality when quality < minQuality -> false
                    | _ -> true
                
                if not shouldCache then
                    config.AuditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.AuditEventType.HealthCheck,
                            AuditLogger.AuditSeverity.Info,
                            "CacheOptimizer",
                            $"Response not cached due to low quality score: {qualityScore}",
                            Map.ofList [
                                ("responseType", box responseType)
                                ("qualityScore", box qualityScore)
                                ("minQuality", box config.MinQualityForCaching)
                            ]
                        )
                    )
                else
                    let cacheKey = createCacheKey prompt model providerId imageData responseType
                    let strategy = 
                        match config.StrategyByResponseType.TryFind responseType with
                        | Some s -> s
                        | None -> config.DefaultStrategy
                    
                    let cachedResponse = {
                        CacheKey = cacheKey
                        Response = response
                        Model = model
                        ProviderId = providerId
                        TokensUsed = tokensUsed
                        ImageCount = if imageData.IsSome then Some 1 else None
                        Cost = cost
                        QualityScore = qualityScore
                        Timestamp = DateTimeOffset.Now
                        ExpiryTime = DateTimeOffset.Now // Will be set below
                        AccessCount = 0
                        LastAccessed = DateTimeOffset.Now
                        Confidence = confidence
                        ResponseType = responseType
                        ValidationHash = cacheKey
                    }
                    
                    let ttl = calculateTtl strategy cachedResponse
                    let finalResponse = { cachedResponse with ExpiryTime = DateTimeOffset.Now.Add(ttl) }
                    
                    // Check cache size limit
                    if cache.Count >= config.MaxCacheSize then
                        evictOldestEntries(config.MaxCacheSize / 10) // Evict 10% when full
                    
                    cache.[cacheKey] <- finalResponse
                    
                    config.AuditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.AuditEventType.ActionExecution,
                            AuditLogger.AuditSeverity.Info,
                            "CacheOptimizer",
                            $"Cached {responseType} response for {ttl.TotalMinutes:F1} minutes",
                            Map.ofList [
                                ("cacheKey", box cacheKey)
                                ("model", box model)
                                ("providerId", box (providerId.ToString()))
                                ("ttlMinutes", box ttl.TotalMinutes)
                                ("tokensUsed", box tokensUsed)
                                ("cost", box cost)
                            ]
                        )
                    )
        
        /// Evict oldest entries to free space
        member private _.EvictOldestEntries(count: int) =
            let sortedEntries = 
                cache.ToArray()
                |> Array.sortBy (fun kvp -> kvp.Value.LastAccessed)
                |> Array.take (min count cache.Count)
            
            for kvp in sortedEntries do
                cache.TryRemove(kvp.Key) |> ignore
            
            config.AuditLogger |> Option.iter (fun logger ->
                logger.LogEvent(
                    AuditLogger.AuditEventType.ActionExecution,
                    AuditLogger.AuditSeverity.Info,
                    "CacheOptimizer",
                    $"Evicted {sortedEntries.Length} oldest cache entries",
                    Map.ofList [
                        ("evictedCount", box sortedEntries.Length)
                        ("remainingSize", box cache.Count)
                    ]
                )
            )
        
        /// Perform periodic cleanup of expired entries
        member private _.PerformCleanup() =
            let now = DateTimeOffset.Now
            let expiredKeys = 
                cache.ToArray()
                |> Array.filter (fun kvp -> kvp.Value.ExpiryTime <= now)
                |> Array.map (fun kvp -> kvp.Key)
            
            for key in expiredKeys do
                cache.TryRemove(key) |> ignore
            
            if expiredKeys.Length > 0 then
                config.AuditLogger |> Option.iter (fun logger ->
                    logger.LogEvent(
                        AuditLogger.AuditEventType.ActionExecution,
                        AuditLogger.AuditSeverity.Info,
                        "CacheOptimizer",
                        $"Cleaned up {expiredKeys.Length} expired cache entries",
                        Map.ofList [
                            ("expiredCount", box expiredKeys.Length)
                            ("remainingSize", box cache.Count)
                        ]
                    )
                )
        
        /// Record response time for statistics
        member _.RecordResponseTime(responseTime: TimeSpan) =
            responseTimes.Enqueue(responseTime)
            
            // Keep only recent samples
            while responseTimes.Count > maxResponseTimesSample do
                responseTimes.TryDequeue() |> ignore
        
        /// Get cache statistics
        member _.GetCacheStatistics() =
            let totalReq = totalRequests
            let hits = cacheHits
            let misses = cacheMisses
            
            let hitRate = 
                if totalReq > 0L then float hits / float totalReq
                else 0.0
            
            let costSaved = decimal totalCostSaved / 100m // Convert back from cents
            
            let avgResponseTime = 
                let times = responseTimes.ToArray()
                if times.Length > 0 then
                    TimeSpan.FromTicks(times |> Array.averageBy (fun t -> float t.Ticks) |> int64)
                else TimeSpan.Zero
            
            let topTypes = 
                cache.Values
                |> Seq.groupBy (fun r -> r.ResponseType)
                |> Seq.map (fun (responseType, entries) -> (responseType, Seq.length entries))
                |> Seq.sortByDescending snd
                |> Seq.take 5
                |> List.ofSeq
            
            let totalEntries = cache.Count
            let expiredEntries = 
                cache.Values
                |> Seq.filter (fun r -> r.ExpiryTime <= DateTimeOffset.Now)
                |> Seq.length
            
            let expirationRate = 
                if totalEntries > 0 then float expiredEntries / float totalEntries
                else 0.0
            
            {
                TotalRequests = totalReq
                CacheHits = hits
                CacheMisses = misses
                CacheHitRate = hitRate
                TotalCostSaved = costSaved
                AverageResponseTime = avgResponseTime
                CacheSize = cache.Count
                TopCachedTypes = topTypes
                ExpirationRate = expirationRate
            }
        
        /// Clear all cache entries
        member _.ClearCache() =
            let clearedCount = cache.Count
            cache.Clear()
            
            config.AuditLogger |> Option.iter (fun logger ->
                logger.LogEvent(
                    AuditLogger.AuditEventType.ActionExecution,
                    AuditLogger.AuditSeverity.Info,
                    "CacheOptimizer",
                    $"Cache cleared: {clearedCount} entries removed",
                    Map.ofList [
                        ("clearedCount", box clearedCount)
                    ]
                )
            )
        
        /// Dispose resources
        interface IDisposable with
            member _.Dispose() =
                cleanupTimer.Stop()
                cleanupTimer.Dispose()
    
    /// Factory function for creating cache optimizer
    let createCacheOptimizer (config: CacheOptimizerConfig option) =
        let config = defaultArg config defaultCacheOptimizerConfig
        new CacheOptimizer(config)
    
    /// Integration helper for AI processors
    let tryGetCachedResponseAsync (cacheOptimizer: CacheOptimizer option) (prompt: string) (model: string) (providerId: AIFramework.ProviderId) (responseType: string) (imageData: byte[] option) =
        async {
            match cacheOptimizer with
            | None -> return None
            | Some cache -> 
                return cache.TryGetCachedResponse(prompt, model, providerId, responseType, ?imageData = imageData)
        }
    
    /// Integration helper for caching responses
    let cacheResponseAsync (cacheOptimizer: CacheOptimizer option) (prompt: string) (model: string) (providerId: AIFramework.ProviderId) (response: string) (responseType: string) 
                          (imageData: byte[] option) (tokensUsed: int option) (cost: decimal option) (qualityScore: float option) (confidence: float option) =
        async {
            match cacheOptimizer with
            | None -> return ()
            | Some cache -> 
                cache.CacheResponse(prompt, model, providerId, response, responseType, 
                                  ?imageData = imageData, ?tokensUsed = tokensUsed, 
                                  ?cost = cost, ?qualityScore = qualityScore, ?confidence = confidence)
        }
    
    /// Integration for recording response times
    let recordResponseTimeAsync (cacheOptimizer: CacheOptimizer option) (responseTime: TimeSpan) =
        async {
            match cacheOptimizer with
            | None -> return ()
            | Some cache -> cache.RecordResponseTime(responseTime)
        }