namespace Automation.AI

open System
open System.Collections.Concurrent
open System.Threading
open System.Threading.Tasks

/// Rate limiting and abuse prevention for AI operations
module RateLimiter =
    
    /// Rate limit result
    type RateLimitResult =
        | Allowed of remainingRequests: int * resetTime: DateTimeOffset
        | Exceeded of retryAfter: TimeSpan * resetTime: DateTimeOffset
        | Blocked of reason: string

    /// Rate limit configuration
    type RateLimitConfig = {
        RequestsPerMinute: int
        RequestsPerHour: int
        RequestsPerDay: int
        BurstLimit: int
        WindowSizeMinutes: int
        EnableDynamicLimits: bool
        SuspiciousActivityThreshold: int
        BlockDurationMinutes: int
    }

    /// Default rate limit configuration
    let defaultRateLimitConfig = {
        RequestsPerMinute = 10
        RequestsPerHour = 300
        RequestsPerDay = 2000
        BurstLimit = 5
        WindowSizeMinutes = 1
        EnableDynamicLimits = true
        SuspiciousActivityThreshold = 50
        BlockDurationMinutes = 15
    }

    /// Rate limit window
    type RateLimitWindow = {
        StartTime: DateTimeOffset
        RequestCount: int
        LastRequestTime: DateTimeOffset
        BurstCount: int
        SuspiciousActivityCount: int
        IsBlocked: bool
        BlockedUntil: DateTimeOffset option
    }

    /// Rate limit key (identifier for who is making requests)
    type RateLimitKey =
        | UserId of string
        | IPAddress of string
        | APIKey of string
        | SessionId of string
        | Combined of userId: string * ipAddress: string

    /// Rate limiter storage interface
    type IRateLimitStorage =
        abstract member GetWindow: RateLimitKey -> Async<RateLimitWindow option>
        abstract member SetWindow: RateLimitKey * RateLimitWindow -> Async<unit>
        abstract member RemoveWindow: RateLimitKey -> Async<unit>
        abstract member CleanupExpiredWindows: unit -> Async<unit>

    /// In-memory rate limit storage
    type InMemoryRateLimitStorage() =
        let storage = ConcurrentDictionary<RateLimitKey, RateLimitWindow>()
        
        interface IRateLimitStorage with
            member _.GetWindow(key: RateLimitKey) =
                async {
                    match storage.TryGetValue(key) with
                    | true, window -> return Some window
                    | false, _ -> return None
                }
            
            member _.SetWindow(key: RateLimitKey, window: RateLimitWindow) =
                async {
                    storage.AddOrUpdate(key, window, fun _ _ -> window) |> ignore
                }
            
            member _.RemoveWindow(key: RateLimitKey) =
                async {
                    storage.TryRemove(key) |> ignore
                }
            
            member _.CleanupExpiredWindows() =
                async {
                    let now = DateTimeOffset.Now
                    let expiredKeys = 
                        storage
                        |> Seq.filter (fun kvp -> 
                            kvp.Value.StartTime.AddMinutes(60.0) < now || // 1 hour expiry
                            (kvp.Value.IsBlocked && kvp.Value.BlockedUntil.IsSome && kvp.Value.BlockedUntil.Value < now)
                        )
                        |> Seq.map (fun kvp -> kvp.Key)
                        |> List.ofSeq
                    
                    for key in expiredKeys do
                        storage.TryRemove(key) |> ignore
                }

    /// Rate limiter implementation
    type RateLimiter(config: RateLimitConfig, storage: IRateLimitStorage) =
        let lockObj = obj()
        
        // Background cleanup task
        let cleanupTimer = new Timer(
            (fun _ -> storage.CleanupExpiredWindows() |> Async.Start),
            null,
            TimeSpan.FromMinutes(5.0),
            TimeSpan.FromMinutes(5.0)
        )
        
        let createNewWindow (now: DateTimeOffset) = {
            StartTime = now
            RequestCount = 0
            LastRequestTime = now
            BurstCount = 0
            SuspiciousActivityCount = 0
            IsBlocked = false
            BlockedUntil = None
        }
        
        let isWithinTimeWindow (window: RateLimitWindow) (now: DateTimeOffset) (minutes: int) =
            window.StartTime.AddMinutes(float minutes) >= now
        
        let calculateResetTime (window: RateLimitWindow) (minutes: int) =
            window.StartTime.AddMinutes(float minutes)
        
        let detectSuspiciousActivity (window: RateLimitWindow) (now: DateTimeOffset) =
            // Check for rapid successive requests (potential bot behavior)
            let timeSinceLastRequest = now - window.LastRequestTime
            let isRapidRequest = timeSinceLastRequest < TimeSpan.FromSeconds(1.0)
            
            // Check for burst behavior
            let isBurstBehavior = window.BurstCount >= config.BurstLimit
            
            isRapidRequest || isBurstBehavior
        
        member _.CheckRateLimit(key: RateLimitKey, ?requestType: string) =
            async {
                let now = DateTimeOffset.Now
                
                // Get current window
                let! existingWindow = storage.GetWindow(key)
                let window = 
                    match existingWindow with
                    | Some w when isWithinTimeWindow w now config.WindowSizeMinutes -> w
                    | _ -> createNewWindow now
                
                // Check if currently blocked
                if window.IsBlocked then
                    match window.BlockedUntil with
                    | Some blockedUntil when now < blockedUntil ->
                        let retryAfter = blockedUntil - now
                        return Blocked($"Rate limit exceeded. Blocked until {blockedUntil}")
                    | _ ->
                        // Block has expired, reset window
                        let newWindow = createNewWindow now
                        do! storage.SetWindow(key, newWindow)
                        return Allowed(config.RequestsPerMinute - 1, calculateResetTime newWindow config.WindowSizeMinutes)
                else
                    // Check minute-based rate limit
                    let minuteWindow = 
                        if isWithinTimeWindow window now 1 then window
                        else createNewWindow now
                    
                    if minuteWindow.RequestCount >= config.RequestsPerMinute then
                        let resetTime = calculateResetTime minuteWindow 1
                        let retryAfter = resetTime - now
                        return Exceeded(retryAfter, resetTime)
                    else
                        // Check hourly rate limit
                        let hourlyCount = 
                            if isWithinTimeWindow window now 60 then window.RequestCount
                            else 0
                        
                        if hourlyCount >= config.RequestsPerHour then
                            let resetTime = calculateResetTime window 60
                            let retryAfter = resetTime - now
                            return Exceeded(retryAfter, resetTime)
                        else
                            // Check daily rate limit
                            let dailyCount = 
                                if isWithinTimeWindow window now (24 * 60) then window.RequestCount
                                else 0
                            
                            if dailyCount >= config.RequestsPerDay then
                                let resetTime = calculateResetTime window (24 * 60)
                                let retryAfter = resetTime - now
                                return Exceeded(retryAfter, resetTime)
                            else
                                // Check for suspicious activity
                                let isSuspicious = config.EnableDynamicLimits && detectSuspiciousActivity window now
                                let newSuspiciousCount = 
                                    if isSuspicious then window.SuspiciousActivityCount + 1
                                    else window.SuspiciousActivityCount
                                
                                // Determine if we should block this key
                                let shouldBlock = newSuspiciousCount >= config.SuspiciousActivityThreshold
                                
                                // Update burst count  
                                let timeSinceLastRequest = now - window.LastRequestTime
                                let newBurstCount = 
                                    if timeSinceLastRequest < TimeSpan.FromSeconds(10.0) then
                                        window.BurstCount + 1
                                    else
                                        1 // Reset burst count if sufficient time has passed
                                
                                // Create updated window
                                let updatedWindow = {
                                    window with
                                        RequestCount = window.RequestCount + 1
                                        LastRequestTime = now
                                        BurstCount = newBurstCount
                                        SuspiciousActivityCount = newSuspiciousCount
                                        IsBlocked = shouldBlock
                                        BlockedUntil = 
                                            if shouldBlock then 
                                                Some(now.AddMinutes(float config.BlockDurationMinutes))
                                            else None
                                }
                                
                                // Save updated window
                                do! storage.SetWindow(key, updatedWindow)
                                
                                if shouldBlock then
                                    let retryAfter = TimeSpan.FromMinutes(float config.BlockDurationMinutes)
                                    return Blocked($"Suspicious activity detected. Blocked for {config.BlockDurationMinutes} minutes")
                                else
                                    let remainingRequests = config.RequestsPerMinute - updatedWindow.RequestCount
                                    let resetTime = calculateResetTime updatedWindow config.WindowSizeMinutes
                                    return Allowed(remainingRequests, resetTime)
            }
        
        member _.GetRateLimitStatus(key: RateLimitKey) =
            async {
                let! window = storage.GetWindow(key)
                match window with
                | None -> 
                    return {|
                        RequestCount = 0
                        RemainingRequests = config.RequestsPerMinute
                        ResetTime = DateTimeOffset.Now.AddMinutes(float config.WindowSizeMinutes)
                        IsBlocked = false
                        BlockedUntil = None
                    |}
                | Some w ->
                    let now = DateTimeOffset.Now
                    let remainingRequests = max 0 (config.RequestsPerMinute - w.RequestCount)
                    return {|
                        RequestCount = w.RequestCount
                        RemainingRequests = remainingRequests
                        ResetTime = calculateResetTime w config.WindowSizeMinutes
                        IsBlocked = w.IsBlocked
                        BlockedUntil = w.BlockedUntil
                    |}
            }
        
        member _.ResetRateLimit(key: RateLimitKey) =
            async {
                do! storage.RemoveWindow(key)
            }
        
        member _.GetAllActiveLimits() =
            async {
                // This would need to be implemented based on the storage type
                // For in-memory storage, we could expose the internal dictionary
                return []
            }
        
        interface IDisposable with
            member _.Dispose() =
                cleanupTimer.Dispose()

    /// Rate limit middleware for different contexts
    type RateLimitMiddleware(rateLimiter: RateLimiter, auditLogger: AuditLogger.AuditLogger option) =
        
        member _.CheckAIRequestLimit(key: RateLimitKey, request: AIFramework.AIRequest) =
            async {
                let! result = rateLimiter.CheckRateLimit(key, "AI_REQUEST")
                
                match result with
                | Exceeded(retryAfter, resetTime) ->
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.AuditEventType.RateLimitExceeded,
                            AuditLogger.AuditSeverity.Warning,
                            "RateLimiter",
                            $"Rate limit exceeded for key: {key}",
                            Map.ofList [
                                ("retryAfterSeconds", box retryAfter.TotalSeconds)
                                ("resetTime", box resetTime)
                                ("requestType", box "AI_REQUEST")
                            ]
                        )
                    )
                    return result
                | Blocked(reason) ->
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.AuditEventType.SecurityViolation,
                            AuditLogger.AuditSeverity.Error,
                            "RateLimiter",
                            $"Request blocked: {reason}",
                            Map.ofList [
                                ("key", box (key.ToString()))
                                ("reason", box reason)
                                ("requestType", box "AI_REQUEST")
                            ]
                        )
                    )
                    return result
                | Allowed(remaining, resetTime) ->
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.AuditEventType.AIRequest,
                            AuditLogger.AuditSeverity.Info,
                            "RateLimiter",
                            $"Request allowed for key: {key}",
                            Map.ofList [
                                ("remainingRequests", box remaining)
                                ("resetTime", box resetTime)
                                ("requestType", box "AI_REQUEST")
                            ]
                        )
                    )
                    return result
            }
        
        member _.CheckActionExecutionLimit(key: RateLimitKey, actionType: string) =
            async {
                let! result = rateLimiter.CheckRateLimit(key, $"ACTION_{actionType}")
                
                match result with
                | Exceeded(_, _) | Blocked(_) ->
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.AuditEventType.RateLimitExceeded,
                            AuditLogger.AuditSeverity.Warning,
                            "RateLimiter",
                            $"Action execution rate limit exceeded for {actionType}",
                            Map.ofList [
                                ("actionType", box actionType)
                                ("key", box (key.ToString()))
                            ]
                        )
                    )
                | Allowed(_, _) -> ()
                
                return ()
            }

    /// Create default rate limiter with in-memory storage
    let createInMemoryRateLimiter (config: RateLimitConfig option) =
        let config = defaultArg config defaultRateLimitConfig
        let storage = InMemoryRateLimitStorage() :> IRateLimitStorage
        new RateLimiter(config, storage)

    /// Create rate limit middleware
    let createRateLimitMiddleware (rateLimiter: RateLimiter) (auditLogger: AuditLogger.AuditLogger option) =
        new RateLimitMiddleware(rateLimiter, auditLogger)