namespace Automation.AI

open System
open System.Text.RegularExpressions

/// Security validation results
type ValidationResult =
    | Valid
    | Invalid of string list
    | Sanitized of string * string * string list // original, sanitized, warnings

/// Security configuration
type SecurityConfig = {
    /// Maximum input length
    MaxInputLength: int
    /// Disallowed patterns
    DisallowedPatterns: string list
    /// Allowed domains for automation
    AllowedDomains: string list
    /// Maximum file size for uploads
    MaxFileSize: int64
}

module SecurityValidator =
    
    /// Default security configuration
    let defaultConfig = {
        MaxInputLength = 10000
        DisallowedPatterns = [
            "(?i)<script.*?>.*?</script>"  // Script tags
            "(?i)javascript:"              // JavaScript URLs
            "(?i)on\\w+\\s*="             // Event handlers
            "(?i)eval\\s*\\("             // eval() calls
            "(?i)document\\."             // DOM access
            "(?i)window\\."               // Window access
        ]
        AllowedDomains = [
            "localhost"
            "127.0.0.1"
            "example.com"
        ]
        MaxFileSize = 10L * 1024L * 1024L // 10MB
    }
    
    /// Validate input string
    let validateInput (config: SecurityConfig) (input: string) =
        if String.IsNullOrEmpty(input) then
            Valid
        elif input.Length > config.MaxInputLength then
            Invalid [$"Input too long: {input.Length} > {config.MaxInputLength}"]
        else
            let violations = 
                config.DisallowedPatterns
                |> List.choose (fun pattern ->
                    if Regex.IsMatch(input, pattern) then
                        Some $"Pattern violation: {pattern}"
                    else
                        None)
            
            if violations.IsEmpty then
                Valid
            else
                Invalid violations
    
    /// Sanitize input by removing dangerous patterns
    let sanitizeInput (input: string) =
        if String.IsNullOrEmpty(input) then
            ""
        else
            input
                .Replace("<script", "&lt;script")
                .Replace("</script>", "&lt;/script&gt;")
                .Replace("javascript:", "")
                .Replace("eval(", "")
    
    /// Validate prompt for AI requests
    let validatePrompt (config: SecurityConfig) (prompt: string) =
        validateInput config prompt
    
    /// Validate domain for automation requests
    let validateDomain (config: SecurityConfig) (domain: string) =
        if config.AllowedDomains |> List.contains domain then
            Valid
        else
            Invalid [$"Domain not allowed: {domain}"]
    
    /// Validate AI request
    let validateAIRequest (config: SecurityConfig) (request: AIFramework.AIRequest) =
        let promptValidation = validatePrompt config request.Prompt
        
        let systemMessageValidation = 
            match request.SystemMessage with
            | Some msg -> validateInput config msg
            | None -> Valid
        
        match promptValidation, systemMessageValidation with
        | Valid, Valid -> Valid
        | Invalid errs1, Valid -> Invalid errs1
        | Valid, Invalid errs2 -> Invalid errs2
        | Invalid errs1, Invalid errs2 -> Invalid (errs1 @ errs2)
        | Sanitized(orig1, san1, warn1), Valid -> Sanitized(orig1, san1, warn1)
        | Valid, Sanitized(orig2, san2, warn2) -> Sanitized(orig2, san2, warn2)
        | Sanitized(orig1, san1, warn1), Sanitized(orig2, san2, warn2) -> 
            Sanitized(orig1 + "|" + orig2, san1 + "|" + san2, warn1 @ warn2)
        | Invalid errs, Sanitized(_, _, _) -> Invalid errs
        | Sanitized(_, _, _), Invalid errs -> Invalid errs

    /// Create a sanitized version of an AI request
    let sanitizeAIRequest (config: SecurityConfig) (request: AIFramework.AIRequest) =
        let sanitizedPrompt = sanitizeInput request.Prompt
        let sanitizedSystemMessage = 
            request.SystemMessage 
            |> Option.map sanitizeInput
        
        { request with 
            Prompt = sanitizedPrompt
            SystemMessage = sanitizedSystemMessage }
