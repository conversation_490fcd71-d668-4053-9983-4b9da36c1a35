// Performance Testing Demo Program
// This demonstrates how to use the AI automation performance testing framework

namespace Automation.AI

open System
open System.IO
open Automation.AI.PerformanceTestExecutor
open Automation.Utilities.Logging

/// Demo program for performance testing
module PerformanceTestDemo =
    
    [<EntryPoint>]
    let main args =
        async {
            printfn "🚀 AI Automation Performance Testing Framework Demo"
            printfn "======================================================"
            printfn ""
            
            let testType = 
                if args.Length > 0 then args.[0].ToLower()
                else "standard"
            
            let outputDir = 
                if args.Length > 1 then Some args.[1]
                else None
            
            printfn "Starting %s performance test..." testType
            printfn "Output directory: %s" (defaultArg outputDir "default")
            printfn ""
            
            let! result = 
                match testType with
                | "quick" -> runQuickTest outputDir
                | "standard" -> runStandardTest outputDir
                | "stress" -> runStressTest outputDir
                | "endurance" -> runEnduranceTest outputDir
                | _ ->
                    printfn "Unknown test type: %s. Using standard test." testType
                    runStandardTest outputDir
            
            match result with
            | Ok executionResult ->
                printfn "✅ Performance tests completed successfully!"
                printfn ""
                
                // Validate performance targets
                let validation = validatePerformanceTargets executionResult.TestResults
                printfn "📊 Performance Target Validation:"
                printfn "=================================="
                printfn "Overall: %s" validation.Summary
                printfn ""
                
                if validation.AllTargetsMet then
                    printfn "🎉 ALL PERFORMANCE TARGETS MET!"
                    printfn "   System is ready for production deployment."
                else
                    printfn "⚠️  Some performance targets were not met:"
                    for (targetName, passed, value) in validation.ValidationResults do
                        let status = if passed then "✅" else "❌"
                        printfn "   %s %s: %s" status targetName value
                
                printfn ""
                printfn "📁 Reports generated in: %s" executionResult.ReportPackage.OutputDirectory
                for (reportType, filePath) in executionResult.ReportPackage.Files do
                    printfn "   - %s: %s" reportType (Path.GetFileName(filePath))
                
                // Return exit code based on performance targets
                if validation.AllTargetsMet then 0 else 1
                
            | Error errorMessage ->
                printfn "❌ Performance tests failed:"
                printfn "   %s" errorMessage
                printfn ""
                printfn "Check the logs for more details."
                1
        } |> Async.RunSynchronously
    
    /// Print usage information
    let printUsage() =
        printfn "Usage: PerformanceTestDemo [test-type] [output-directory]"
        printfn ""
        printfn "Test Types:"
        printfn "  quick      - Fast tests suitable for CI/CD (5-10 minutes)"
        printfn "  standard   - Standard load testing (15-30 minutes)"
        printfn "  stress     - High load stress testing (30-60 minutes)"
        printfn "  endurance  - Long-running stability tests (2-3 hours)"
        printfn ""
        printfn "Examples:"
        printfn "  PerformanceTestDemo quick"
        printfn "  PerformanceTestDemo standard ./my-test-results"
        printfn "  PerformanceTestDemo stress"
