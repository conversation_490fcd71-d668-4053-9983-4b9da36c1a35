namespace Automation.AI

open System
open System.IO
open System.Text.Json
open System.Text.Json.Serialization
open Automation.AI.PerformanceTesting

/// Performance test reporting and analysis tools
module PerformanceReporting =
    
    /// JSON serialization options for test results
    let jsonOptions = 
        let options = JsonSerializerOptions()
        options.WriteIndented <- true
        options.PropertyNamingPolicy <- JsonNamingPolicy.CamelCase
        options.Converters.Add(JsonFSharpConverter())
        options
    
    /// Export test results to JSON
    let exportToJson (results: TestResults) (filePath: string) =
        try
            let json = JsonSerializer.Serialize(results, jsonOptions)
            File.WriteAllText(filePath, json)
            Ok filePath
        with
        | ex -> Error (sprintf "Failed to export to JSON: %s" ex.Message)
    
    /// Generate HTML report from test results
    let generateHtmlReport (results: TestResults) (outputPath: string) =
        try
            let issuesHtml = 
                if results.Issues.IsEmpty then ""
                else 
                    let issueItems = results.Issues |> List.map (fun issue -> sprintf "<div>• %s</div>" issue) |> String.concat ""
                    sprintf "<div class=\"issues-section\"><h3>⚠️ Issues Detected</h3>%s</div>" issueItems
            
            let recommendationsHtml = generateRecommendations results
            let scenarioReportsHtml = generateScenarioReportsHtml results.Scenarios
            
            let html = sprintf """<!DOCTYPE html>
<html><head><title>Performance Test Report - %s</title></head>
<body>
<h1>Performance Test Report: %s</h1>
<p>Duration: %s</p>
<p>Completed: %s</p>
<h2>Summary</h2>
<p>Total Requests: %d</p>
<p>Success Rate: %.1f%%</p>
<p>Avg Response Time: %.2f seconds</p>
<p>Throughput: %.1f req/sec</p>
<p>Total Cost: $%.2f</p>
<p>Cost per Request: $%.4f</p>
%s
%s
<h2>Scenario Results</h2>
%s
<p>Generated on %s</p>
</body></html>"""
                            results.TestName
                            results.TestName
                            (results.TotalDuration.ToString(@"hh\:mm\:ss"))
                            (results.EndTime.ToString("yyyy-MM-dd HH:mm:ss UTC"))
                            results.OverallMetrics.TotalRequests
                            (results.OverallMetrics.OverallSuccessRate * 100.0)
                            results.OverallMetrics.OverallAverageResponseTime.TotalSeconds
                            results.OverallMetrics.OverallThroughput
                            (float results.OverallMetrics.TotalTestCost)
                            (float results.OverallMetrics.CostPerRequest)
                            issuesHtml
                            recommendationsHtml
                            scenarioReportsHtml
                            (DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss UTC"))
            
            File.WriteAllText(outputPath, html)
            Ok outputPath
        with
        | ex -> Error (sprintf "Failed to generate HTML report: %s" ex.Message)
    
    let generateRecommendations (results: TestResults) =
        let recommendations = ResizeArray<string>()
        
        // Performance recommendations
        if results.OverallMetrics.OverallSuccessRate < 0.95 then
            recommendations.Add("Consider implementing retry logic and circuit breakers to improve reliability")
        
        if results.OverallMetrics.OverallAverageResponseTime.TotalSeconds > 5.0 then
            recommendations.Add("Optimize response times by implementing request batching or caching strategies")
        
        if results.ResourceUtilization.PeakCpuUsage > 80.0 then
            recommendations.Add("Scale horizontally or optimize CPU-intensive operations")
        
        if results.ResourceUtilization.PeakMemoryUsage > 1024L * 1024L * 1024L then // 1GB
            recommendations.Add("Investigate memory usage patterns and implement garbage collection optimizations")
        
        // Cost recommendations
        if results.OverallMetrics.CostPerRequest > 0.01m then
            recommendations.Add("Consider optimizing provider selection algorithms to reduce costs")
        
        let hasHighVarianceScenarios = 
            results.Scenarios 
            |> List.exists (fun s -> s.P99ResponseTime.TotalSeconds > s.AverageResponseTime.TotalSeconds * 3.0)
        
        if hasHighVarianceScenarios then
            recommendations.Add("High response time variance detected - implement adaptive timeout strategies")
        
        if recommendations.Count = 0 then
            recommendations.Add("✅ System performance is within acceptable parameters")
            recommendations.Add("💡 Consider running endurance tests to validate long-term stability")
        
        if recommendations.Count > 0 then
            let recItems = recommendations |> Seq.map (fun recommendation -> sprintf "<div>• %s</div>" recommendation) |> String.concat ""
            sprintf "<div><h3>💡 Recommendations</h3>%s</div>" recItems
        else ""
    
    let generateScenarioReportsHtml (scenarios: ScenarioResults list) =
        scenarios
        |> List.map (fun scenario ->
            let statusText = if scenario.MetPerformanceTarget then "PASS" else "FAIL"
            let successIcon = if scenario.SuccessRate >= 0.95 then "✅" else "❌"
            
            sprintf """
            <div>
                <h3>%s - %s</h3>
                <table>
                    <tr><th>Metric</th><th>Value</th></tr>
                    <tr><td>Total Requests</td><td>%d</td></tr>
                    <tr><td>Success Rate</td><td>%.1f%% %s</td></tr>
                    <tr><td>Average Response Time</td><td>%.2fs</td></tr>
                    <tr><td>95th Percentile</td><td>%.2fs</td></tr>
                    <tr><td>99th Percentile</td><td>%.2fs</td></tr>
                    <tr><td>Throughput</td><td>%.1f req/s</td></tr>
                    <tr><td>Total Cost</td><td>$%.2f</td></tr>
                    <tr><td>Cost Efficiency</td><td>%.2f req/$</td></tr>
                </table>
            </div>"""
            scenario.ScenarioName statusText
            scenario.TotalRequests
            (scenario.SuccessRate * 100.0) successIcon
            scenario.AverageResponseTime.TotalSeconds
            scenario.P95ResponseTime.TotalSeconds
            scenario.P99ResponseTime.TotalSeconds
            scenario.Throughput
            (float scenario.TotalCost)
            scenario.CostEfficiency)
        |> String.concat ""
    
    /// Generate CSV report for analysis
    let generateCsvReport (results: TestResults) (outputPath: string) =
        try
            let csvContent =
                [
                    "TestRunId,ScenarioName,TotalRequests,SuccessfulRequests,FailedRequests,SuccessRate,AvgResponseTime,P95ResponseTime,P99ResponseTime,Throughput,TotalCost,CostEfficiency,MetTarget"
                    for scenario in results.Scenarios do
                        yield sprintf "%s,%s,%d,%d,%d,%.3f,%.3f,%.3f,%.3f,%.3f,%.2f,%.3f,%b"
                                results.TestRunId
                                scenario.ScenarioName
                                scenario.TotalRequests
                                scenario.SuccessfulRequests
                                scenario.FailedRequests
                                scenario.SuccessRate
                                scenario.AverageResponseTime.TotalSeconds
                                scenario.P95ResponseTime.TotalSeconds
                                scenario.P99ResponseTime.TotalSeconds
                                scenario.Throughput
                                (float scenario.TotalCost)
                                scenario.CostEfficiency
                                scenario.MetPerformanceTarget
                ] |> String.concat Environment.NewLine
            
            File.WriteAllText(outputPath, csvContent)
            Ok outputPath
        with
        | ex -> Error (sprintf "Failed to generate CSV report: %s" ex.Message)
    
    /// Generate executive summary
    let generateExecutiveSummary (results: TestResults) =
        let passRate = 
            let passedScenarios = results.Scenarios |> List.filter (fun s -> s.MetPerformanceTarget) |> List.length
            float passedScenarios / float results.Scenarios.Length * 100.0
        
        let avgCostPerRequest = float results.OverallMetrics.CostPerRequest
        let avgResponseTime = results.OverallMetrics.OverallAverageResponseTime.TotalSeconds
        
        let performanceGrade = 
            match passRate with
            | p when p >= 90.0 -> "A"
            | p when p >= 80.0 -> "B"
            | p when p >= 70.0 -> "C"
            | p when p >= 60.0 -> "D"
            | _ -> "F"
        
        let costGrade = 
            match avgCostPerRequest with
            | c when c <= 0.005 -> "A"
            | c when c <= 0.01 -> "B"
            | c when c <= 0.02 -> "C"
            | c when c <= 0.05 -> "D"
            | _ -> "F"
        
        let latencyGrade = 
            match avgResponseTime with
            | t when t <= 1.0 -> "A"
            | t when t <= 3.0 -> "B"
            | t when t <= 5.0 -> "C"
            | t when t <= 10.0 -> "D"
            | _ -> "F"
        
        sprintf """
=== EXECUTIVE SUMMARY ===

📊 OVERALL PERFORMANCE GRADE: %s
   • Performance Targets: %.0f%% (%s)
   • Cost Efficiency: $%.4f per request (%s)
   • Response Time: %.2fs average (%s)
   • Success Rate: %.1f%%

🎯 KEY METRICS:
   • Total Requests Processed: %s
   • Overall Throughput: %.1f requests/second
   • Total Test Cost: $%.2f
   • Test Duration: %s

🚀 SCENARIO PERFORMANCE:
%s

%s

🔧 SYSTEM RESOURCE USAGE:
   • Peak Memory: %d MB
   • Peak CPU: %.1f%%
   • Average Memory: %d MB
   • Average CPU: %.1f%%

⚖️ COST ANALYSIS:
   • Most Cost-Effective Scenario: %s
   • Least Cost-Effective Scenario: %s
   • Provider Distribution: %s

%s
"""
            performanceGrade
            passRate performanceGrade
            avgCostPerRequest costGrade
            avgResponseTime latencyGrade
            (results.OverallMetrics.OverallSuccessRate * 100.0)
            (results.OverallMetrics.TotalRequests.ToString("N0"))
            results.OverallMetrics.OverallThroughput
            (float results.OverallMetrics.TotalTestCost)
            (results.TotalDuration.ToString(@"hh\:mm\:ss"))
            (results.Scenarios 
             |> List.map (fun s -> 
                 sprintf "   • %s: %s (%.1f%% success, %.2fs avg)"
                     s.ScenarioName
                     (if s.MetPerformanceTarget then "✅ PASS" else "❌ FAIL")
                     (s.SuccessRate * 100.0)
                     s.AverageResponseTime.TotalSeconds)
             |> String.concat Environment.NewLine)
            (if results.Issues.IsEmpty then "✅ No critical issues detected" 
             else sprintf "⚠️ ISSUES DETECTED:%s%s" Environment.NewLine 
                     (results.Issues |> List.map (fun i -> sprintf "   • %s" i) |> String.concat Environment.NewLine))
            (results.ResourceUtilization.PeakMemoryUsage / 1024L / 1024L)
            results.ResourceUtilization.PeakCpuUsage
            (results.ResourceUtilization.AverageMemoryUsage / 1024L / 1024L)
            results.ResourceUtilization.AverageCpuUsage
            (results.Scenarios |> List.maxBy (fun s -> s.CostEfficiency) |> fun s -> s.ScenarioName)
            (results.Scenarios |> List.minBy (fun s -> s.CostEfficiency) |> fun s -> s.ScenarioName)
            (results.OverallMetrics.ProviderDistribution 
             |> Map.toList 
             |> List.map (fun (provider, count) -> sprintf "%A: %d" provider count)
             |> String.concat ", ")
            (if results.PerformanceTargetsMet then "🎉 ALL PERFORMANCE TARGETS MET! System is ready for production."
             else "⚠️ Some performance targets not met. Review recommendations before production deployment.")
    
    /// Save complete test report package
    let saveTestReportPackage (results: TestResults) (outputDirectory: string) =
        try
            // Create output directory
            if not (Directory.Exists(outputDirectory)) then
                Directory.CreateDirectory(outputDirectory) |> ignore
            
            let timestamp = DateTime.UtcNow.ToString("yyyyMMdd-HHmmss")
            let baseFileName = sprintf "%s-%s" results.TestRunId timestamp
            
            // Save JSON report
            let jsonPath = Path.Combine(outputDirectory, sprintf "%s-results.json" baseFileName)
            let jsonResult = exportToJson results jsonPath
            
            // Save HTML report
            let htmlPath = Path.Combine(outputDirectory, sprintf "%s-report.html" baseFileName)
            let htmlResult = generateHtmlReport results htmlPath
            
            // Save CSV report
            let csvPath = Path.Combine(outputDirectory, sprintf "%s-data.csv" baseFileName)
            let csvResult = generateCsvReport results csvPath
            
            // Save executive summary
            let summaryPath = Path.Combine(outputDirectory, sprintf "%s-summary.txt" baseFileName)
            let summary = generateExecutiveSummary results
            File.WriteAllText(summaryPath, summary)
            
            let results = [
                ("JSON", jsonResult)
                ("HTML", htmlResult)
                ("CSV", csvResult)
                ("Summary", Ok summaryPath)
            ]
            
            let successful = results |> List.choose (fun (name, result) -> 
                match result with 
                | Ok path -> Some (name, path) 
                | Error _ -> None)
                
            let failed = results |> List.choose (fun (name, result) -> 
                match result with 
                | Error error -> Some (name, error) 
                | Ok _ -> None)
            
            if failed.IsEmpty then
                Ok {| 
                    OutputDirectory = outputDirectory
                    Files = successful |> Map.ofList
                    Summary = summary
                |}
            else
                Error (sprintf "Some reports failed to generate: %s" 
                    (failed |> List.map (fun (name, error) -> sprintf "%s: %s" name error) |> String.concat "; "))
                
        with
        | ex -> Error (sprintf "Failed to save test report package: %s" ex.Message)
