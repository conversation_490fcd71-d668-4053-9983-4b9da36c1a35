namespace Automation.AI

open System
open System.Collections.Concurrent
open System.Threading
open System.Threading.Tasks
open StackExchange.Redis
open Automation.Core
open Automation.AI.LoadBalancer
open Automation.Utilities.Logging

/// Worker coordination service for horizontal scaling
module WorkerCoordination =
    
    /// Worker coordinator configuration
    type WorkerCoordinatorConfig = {
        RedisConnectionString: string
        WorkerRegistrationChannel: string
        TaskDistributionChannel: string
        HeartbeatIntervalSeconds: int
        WorkerInstancePrefix: string
        LoadBalancerConfig: LoadBalancerConfig
        EnableDistributedLocking: bool
    }
    
    /// Default worker coordinator configuration
    let defaultCoordinatorConfig = {
        RedisConnectionString = "localhost"
        WorkerRegistrationChannel = "worker_registration"
        TaskDistributionChannel = "automation_channel"
        HeartbeatIntervalSeconds = 15
        WorkerInstancePrefix = "worker"
        LoadBalancerConfig = defaultLoadBalancerConfig
        EnableDistributedLocking = true
    }
    
    /// Worker registration message
    type WorkerRegistrationMessage = {
        WorkerId: string
        Action: string // "register", "unregister", "heartbeat"
        MaxCapacity: int
        CurrentLoad: int
        Health: string
        ProcessingRate: float
        AvgResponseTimeMs: float
        Timestamp: DateTimeOffset
    }
    
    /// Worker coordinator for managing distributed workers
    type WorkerCoordinator(config: WorkerCoordinatorConfig) =
        let loadBalancer = new LoadBalancer(config.LoadBalancerConfig)
        let mutable connection: IConnectionMultiplexer option = None
        let mutable isRunning = false
        let autoScalingTimer = new Timer((fun _ -> ()), null, Timeout.Infinite, Timeout.Infinite)
        
        /// Start the coordinator
        member _.Start() =
            async {
                try
                    info "[WorkerCoordinator] Starting worker coordinator..."
                    
                    let conn = ConnectionMultiplexer.Connect(config.RedisConnectionString)
                    connection <- Some conn
                    
                    let subscriber = conn.GetSubscriber()
                    
                    // Subscribe to worker registration messages
                    subscriber.Subscribe(RedisChannel.Literal(config.WorkerRegistrationChannel), 
                        fun channel message -> _.HandleWorkerRegistration(message.ToString())) |> ignore
                    
                    isRunning <- true
                    
                    // Start auto-scaling timer
                    autoScalingTimer.Change(TimeSpan.FromMinutes(1.0), TimeSpan.FromMinutes(1.0)) |> ignore
                    
                    info (sprintf "[WorkerCoordinator] Coordinator started, listening on channel '%s'" config.WorkerRegistrationChannel)
                    
                with
                | ex -> 
                    error (sprintf "[WorkerCoordinator] Failed to start coordinator: %s" ex.Message)
                    reraise()
            }
        
        /// Stop the coordinator
        member _.Stop() =
            isRunning <- false
            autoScalingTimer.Change(Timeout.Infinite, Timeout.Infinite) |> ignore
            connection |> Option.iter (fun conn -> conn.Dispose())
            connection <- None
            info "[WorkerCoordinator] Worker coordinator stopped"
        
        /// Handle worker registration messages
        member private _.HandleWorkerRegistration(messageStr: string) =
            try
                let message = System.Text.Json.JsonSerializer.Deserialize<WorkerRegistrationMessage>(messageStr)
                
                match message.Action with
                | "register" ->
                    loadBalancer.RegisterWorker(message.WorkerId, message.MaxCapacity)
                    info (sprintf "[WorkerCoordinator] Worker %s registered with capacity %d" message.WorkerId message.MaxCapacity)
                
                | "unregister" ->
                    loadBalancer.UnregisterWorker(message.WorkerId)
                    info (sprintf "[WorkerCoordinator] Worker %s unregistered" message.WorkerId)
                
                | "heartbeat" ->
                    let health = match message.Health with
                                 | "healthy" -> Healthy
                                 | "degraded" -> Degraded
                                 | _ -> Unhealthy
                    let avgResponseTime = TimeSpan.FromMilliseconds(message.AvgResponseTimeMs)
                    loadBalancer.UpdateWorkerStatus(message.WorkerId, health, message.CurrentLoad, message.ProcessingRate, avgResponseTime)
                
                | _ -> warn (sprintf "[WorkerCoordinator] Unknown registration action: %s" message.Action)
            
            with
            | ex -> error (sprintf "[WorkerCoordinator] Error handling worker registration: %s" ex.Message)
        
        /// Route task to best available worker
        member _.RouteTask(taskMessage: string) =
            async {
                match loadBalancer.RouteTask() with
                | Some workerId ->
                    match connection with
                    | Some conn ->
                        try
                            let subscriber = conn.GetSubscriber()
                            let workerChannel = sprintf "%s_%s" config.TaskDistributionChannel workerId
                            let! result = subscriber.PublishAsync(RedisChannel.Literal(workerChannel), taskMessage) |> Async.AwaitTask
                            return result > 0L
                        with
                        | ex ->
                            error (sprintf "[WorkerCoordinator] Failed to route task to worker %s: %s" workerId ex.Message)
                            return false
                    | None ->
                        error "[WorkerCoordinator] No Redis connection available"
                        return false
                | None ->
                    warn "[WorkerCoordinator] No workers available for task routing"
                    return false
            }
        
        /// Get coordinator metrics
        member _.GetMetrics() =
            let lbMetrics = loadBalancer.GetMetrics()
            Map.ofList [
                ("coordinator_running", box isRunning)
                ("redis_connected", box (connection.IsSome))
            ] |> Map.fold (fun acc k v -> Map.add k v acc) lbMetrics
        
        /// Perform auto-scaling check
        member private _.CheckAutoScaling() =
            try
                let scalingActions = loadBalancer.CheckAutoScaling()
                for action in scalingActions do
                    match action.Split(':') with
                    | [| "scale_up"; countStr |] ->
                        match Int32.TryParse(countStr) with
                        | true, count -> _.TriggerScaleUp(count)
                        | false, _ -> warn (sprintf "[WorkerCoordinator] Invalid scale up count: %s" countStr)
                    | [| "scale_down"; countStr |] ->
                        match Int32.TryParse(countStr) with
                        | true, count -> _.TriggerScaleDown(count)
                        | false, _ -> warn (sprintf "[WorkerCoordinator] Invalid scale down count: %s" countStr)
                    | _ -> warn (sprintf "[WorkerCoordinator] Unknown scaling action: %s" action)
            with
            | ex -> error (sprintf "[WorkerCoordinator] Auto-scaling check failed: %s" ex.Message)
        
        /// Trigger scale up (placeholder - in real implementation would interact with container orchestrator)
        member private _.TriggerScaleUp(count: int) =
            info (sprintf "[WorkerCoordinator] Scaling up: requesting %d new worker instances" count)
            // In a real implementation, this would:
            // 1. Call container orchestrator API (Docker Swarm, Kubernetes, etc.)
            // 2. Or trigger cloud auto-scaling group
            // 3. Or start new worker processes
            
            // For now, log the action
            config.LoadBalancerConfig.AuditLogger |> Option.iter (fun logger ->
                logger.LogEvent(
                    AuditLogger.Info,
                    AuditLogger.Info,
                    "WorkerCoordinator",
                    $"Scale up triggered: {count} instances",
                    Map.ofList [("instances", box count); ("action", box "scale_up")]
                )
            )
        
        /// Trigger scale down (placeholder)
        member private _.TriggerScaleDown(count: int) =
            info (sprintf "[WorkerCoordinator] Scaling down: requesting removal of %d worker instances" count)
            // In a real implementation, this would gracefully shut down workers
            
            config.LoadBalancerConfig.AuditLogger |> Option.iter (fun logger ->
                logger.LogEvent(
                    AuditLogger.Info,
                    AuditLogger.Info,
                    "WorkerCoordinator",
                    $"Scale down triggered: {count} instances",
                    Map.ofList [("instances", box count); ("action", box "scale_down")]
                )
            )
        
        interface IDisposable with
            member _.Dispose() =
                _.Stop()
                loadBalancer.Dispose()
                autoScalingTimer.Dispose()
    
    /// Worker instance helper for registering with coordinator
    type WorkerInstance(workerId: string, maxCapacity: int, config: WorkerCoordinatorConfig) =
        let mutable connection: IConnectionMultiplexer option = None
        let mutable currentLoad = 0
        let mutable processingRate = 0.0
        let mutable avgResponseTime = TimeSpan.Zero
        let mutable health = Healthy
        let heartbeatTimer = new Timer((fun _ -> ()), null, Timeout.Infinite, Timeout.Infinite)
        
        /// Start the worker instance
        member _.Start() =
            async {
                try
                    let conn = ConnectionMultiplexer.Connect(config.RedisConnectionString)
                    connection <- Some conn
                    
                    // Register with coordinator
                    do! _.SendRegistrationMessage("register")
                    
                    // Start heartbeat timer
                    let interval = TimeSpan.FromSeconds(float config.HeartbeatIntervalSeconds)
                    heartbeatTimer.Change(interval, interval) |> ignore
                    
                    info (sprintf "[WorkerInstance] Worker %s started and registered" workerId)
                    
                with
                | ex ->
                    error (sprintf "[WorkerInstance] Failed to start worker %s: %s" workerId ex.Message)
                    reraise()
            }
        
        /// Stop the worker instance
        member _.Stop() =
            async {
                heartbeatTimer.Change(Timeout.Infinite, Timeout.Infinite) |> ignore
                do! _.SendRegistrationMessage("unregister")
                connection |> Option.iter (fun conn -> conn.Dispose())
                connection <- None
                info (sprintf "[WorkerInstance] Worker %s stopped and unregistered" workerId)
            }
        
        /// Send registration message to coordinator
        member private _.SendRegistrationMessage(action: string) =
            async {
                match connection with
                | Some conn ->
                    try
                        let message = {
                            WorkerId = workerId
                            Action = action
                            MaxCapacity = maxCapacity
                            CurrentLoad = currentLoad
                            Health = match health with Healthy -> "healthy" | Degraded -> "degraded" | Unhealthy -> "unhealthy"
                            ProcessingRate = processingRate
                            AvgResponseTimeMs = avgResponseTime.TotalMilliseconds
                            Timestamp = DateTimeOffset.Now
                        }
                        
                        let messageStr = System.Text.Json.JsonSerializer.Serialize(message)
                        let subscriber = conn.GetSubscriber()
                        do! subscriber.PublishAsync(RedisChannel.Literal(config.WorkerRegistrationChannel), messageStr) |> Async.AwaitTask |> Async.Ignore
                        
                    with
                    | ex -> error (sprintf "[WorkerInstance] Failed to send registration message: %s" ex.Message)
                | None -> 
                    warn "[WorkerInstance] No Redis connection available for registration"
            }
        
        /// Update worker metrics
        member _.UpdateMetrics(newLoad: int, newProcessingRate: float, newAvgResponseTime: TimeSpan, newHealth: HealthStatus) =
            currentLoad <- newLoad
            processingRate <- newProcessingRate
            avgResponseTime <- newAvgResponseTime
            health <- newHealth
        
        /// Send heartbeat
        member private _.SendHeartbeat() =
            _.SendRegistrationMessage("heartbeat") |> Async.Start
        
        interface IDisposable with
            member _.Dispose() =
                (_.Stop() |> Async.RunSynchronously)
                heartbeatTimer.Dispose()
