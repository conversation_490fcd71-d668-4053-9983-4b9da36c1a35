namespace Automation.AI

open System
open System.Threading
open System.Threading.Tasks
open System.Collections.Concurrent
open System.Net.Http

/// Health checks and graceful shutdown mechanisms for production readiness
module HealthChecks =
    
    /// Health check status
    type HealthStatus =
        | Healthy
        | Unhealthy of reason: string
        | Degraded of reason: string
        | Unknown

    /// Individual health check result
    type HealthCheckResult = {
        Name: string
        Status: HealthStatus
        ResponseTime: TimeSpan
        CheckedAt: DateTimeOffset
        Details: Map<string, obj>
        Tags: string list
    }

    /// Overall system health
    type SystemHealth = {
        Status: HealthStatus
        TotalChecks: int
        HealthyChecks: int
        UnhealthyChecks: int
        DegradedChecks: int
        CheckedAt: DateTimeOffset
        Results: HealthCheckResult list
        Uptime: TimeSpan
    }

    /// Health check interface
    type IHealthCheck =
        abstract member Name: string
        abstract member Tags: string list
        abstract member ExecuteAsync: CancellationToken -> Async<HealthCheckResult>

    /// Database health check
    type DatabaseHealthCheck(connectionString: string, name: string) =
        interface IHealthCheck with
            member _.Name = name
            member _.Tags = ["database"; "infrastructure"]
            member _.ExecuteAsync(cancellationToken: CancellationToken) =
                async {
                    let startTime = DateTimeOffset.Now
                    try
                        // Simple database ping - this would be implemented based on your database provider
                        // For now, we'll simulate a database check
                        do! Async.Sleep(100) // Simulate database query time
                        
                        let responseTime = DateTimeOffset.Now - startTime
                        return {
                            Name = name
                            Status = Healthy
                            ResponseTime = responseTime
                            CheckedAt = DateTimeOffset.Now
                            Details = Map.ofList [("connectionString", box (connectionString.Substring(0, min 20 connectionString.Length) + "..."))]
                            Tags = ["database"; "infrastructure"]
                        }
                    with
                    | ex ->
                        let responseTime = DateTimeOffset.Now - startTime
                        return {
                            Name = name
                            Status = Unhealthy(ex.Message)
                            ResponseTime = responseTime
                            CheckedAt = DateTimeOffset.Now
                            Details = Map.ofList [("error", box ex.Message)]
                            Tags = ["database"; "infrastructure"]
                        }
                }

    /// AI Provider health check
    type AIProviderHealthCheck(providerId: string, healthEndpoint: string option, httpClient: HttpClient) =
        interface IHealthCheck with
            member _.Name = $"AI_Provider_{providerId}"
            member _.Tags = ["ai"; "external"; "provider"]
            member _.ExecuteAsync(cancellationToken: CancellationToken) =
                async {
                    let startTime = DateTimeOffset.Now
                    try
                        match healthEndpoint with
                        | Some endpoint ->
                            let! response = httpClient.GetAsync(endpoint, cancellationToken) |> Async.AwaitTask
                            let responseTime = DateTimeOffset.Now - startTime
                            
                            if response.IsSuccessStatusCode then
                                return {
                                    Name = $"AI_Provider_{providerId}"
                                    Status = Healthy
                                    ResponseTime = responseTime
                                    CheckedAt = DateTimeOffset.Now
                                    Details = Map.ofList [
                                        ("providerId", box providerId)
                                        ("statusCode", box (int response.StatusCode))
                                        ("endpoint", box endpoint)
                                    ]
                                    Tags = ["ai"; "external"; "provider"]
                                }
                            else
                                return {
                                    Name = $"AI_Provider_{providerId}"
                                    Status = Unhealthy($"HTTP {response.StatusCode}")
                                    ResponseTime = responseTime
                                    CheckedAt = DateTimeOffset.Now
                                    Details = Map.ofList [
                                        ("providerId", box providerId)
                                        ("statusCode", box (int response.StatusCode))
                                        ("endpoint", box endpoint)
                                    ]
                                    Tags = ["ai"; "external"; "provider"]
                                }
                        | None ->
                            // Fallback to a simple connectivity test or provider-specific check
                            do! Async.Sleep(50) // Simulate check
                            let responseTime = DateTimeOffset.Now - startTime
                            
                            return {
                                Name = $"AI_Provider_{providerId}"
                                Status = Healthy
                                ResponseTime = responseTime
                                CheckedAt = DateTimeOffset.Now
                                Details = Map.ofList [("providerId", box providerId); ("checkType", box "basic")]
                                Tags = ["ai"; "external"; "provider"]
                            }
                    with
                    | ex ->
                        let responseTime = DateTimeOffset.Now - startTime
                        return {
                            Name = $"AI_Provider_{providerId}"
                            Status = Unhealthy(ex.Message)
                            ResponseTime = responseTime
                            CheckedAt = DateTimeOffset.Now
                            Details = Map.ofList [("providerId", box providerId); ("error", box ex.Message)]
                            Tags = ["ai"; "external"; "provider"]
                        }
                }

    /// Memory usage health check
    type MemoryHealthCheck(maxMemoryMB: int64) =
        interface IHealthCheck with
            member _.Name = "Memory_Usage"
            member _.Tags = ["system"; "resources"]
            member _.ExecuteAsync(cancellationToken: CancellationToken) =
                async {
                    let startTime = DateTimeOffset.Now
                    try
                        let totalMemory = GC.GetTotalMemory(false)
                        let totalMemoryMB = totalMemory / (1024L * 1024L)
                        let gen0Collections = GC.CollectionCount(0)
                        let gen1Collections = GC.CollectionCount(1)
                        let gen2Collections = GC.CollectionCount(2)
                        
                        let responseTime = DateTimeOffset.Now - startTime
                        let usagePercentage = float totalMemoryMB / float maxMemoryMB * 100.0
                        
                        let status =
                            if totalMemoryMB > maxMemoryMB then
                                Unhealthy($"Memory usage {totalMemoryMB}MB exceeds limit {maxMemoryMB}MB")
                            elif usagePercentage > 80.0 then
                                let usageStr = usagePercentage.ToString("F1")
                                Degraded($"Memory usage {totalMemoryMB}MB is {usageStr}%% of limit")
                            else
                                Healthy
                        
                        return {
                            Name = "Memory_Usage"
                            Status = status
                            ResponseTime = responseTime
                            CheckedAt = DateTimeOffset.Now
                            Details = Map.ofList [
                                ("totalMemoryMB", box totalMemoryMB)
                                ("maxMemoryMB", box maxMemoryMB)
                                ("usagePercentage", box usagePercentage)
                                ("gen0Collections", box gen0Collections)
                                ("gen1Collections", box gen1Collections)
                                ("gen2Collections", box gen2Collections)
                            ]
                            Tags = ["system"; "resources"]
                        }
                    with
                    | ex ->
                        let responseTime = DateTimeOffset.Now - startTime
                        return {
                            Name = "Memory_Usage"
                            Status = Unhealthy(ex.Message)
                            ResponseTime = responseTime
                            CheckedAt = DateTimeOffset.Now
                            Details = Map.ofList [("error", box ex.Message)]
                            Tags = ["system"; "resources"]
                        }
                }

    /// Disk space health check
    type DiskSpaceHealthCheck(path: string, minFreeSpaceGB: int64) =
        interface IHealthCheck with
            member _.Name = "Disk_Space"
            member _.Tags = ["system"; "storage"]
            member _.ExecuteAsync(cancellationToken: CancellationToken) =
                async {
                    let startTime = DateTimeOffset.Now
                    try
                        let driveInfo = System.IO.DriveInfo(path)
                        let freeSpaceGB = driveInfo.AvailableFreeSpace / (1024L * 1024L * 1024L)
                        let totalSpaceGB = driveInfo.TotalSize / (1024L * 1024L * 1024L)
                        let usedSpaceGB = totalSpaceGB - freeSpaceGB
                        let usagePercentage = float usedSpaceGB / float totalSpaceGB * 100.0
                        
                        let responseTime = DateTimeOffset.Now - startTime
                        
                        let status =
                            if freeSpaceGB < minFreeSpaceGB then
                                Unhealthy($"Free disk space {freeSpaceGB}GB is below minimum {minFreeSpaceGB}GB")
                            elif usagePercentage > 90.0 then
                                let usageStr = usagePercentage.ToString("F1")
                                Degraded("Disk usage is " + usageStr + "%")
                            else
                                Healthy
                        
                        return {
                            Name = "Disk_Space"
                            Status = status
                            ResponseTime = responseTime
                            CheckedAt = DateTimeOffset.Now
                            Details = Map.ofList [
                                ("path", box path)
                                ("freeSpaceGB", box freeSpaceGB)
                                ("totalSpaceGB", box totalSpaceGB)
                                ("usedSpaceGB", box usedSpaceGB)
                                ("usagePercentage", box usagePercentage)
                                ("minFreeSpaceGB", box minFreeSpaceGB)
                            ]
                            Tags = ["system"; "storage"]
                        }
                    with
                    | ex ->
                        let responseTime = DateTimeOffset.Now - startTime
                        return {
                            Name = "Disk_Space"
                            Status = Unhealthy(ex.Message)
                            ResponseTime = responseTime
                            CheckedAt = DateTimeOffset.Now
                            Details = Map.ofList [("error", box ex.Message); ("path", box path)]
                            Tags = ["system"; "storage"]
                        }
                }

    /// Health check service
    type HealthCheckService(auditLogger: AuditLogger.AuditLogger option) =
        let checks = ConcurrentDictionary<string, IHealthCheck>()
        let startTime = DateTimeOffset.Now
        
        member _.RegisterCheck(healthCheck: IHealthCheck) =
            checks.AddOrUpdate(healthCheck.Name, healthCheck, fun _ _ -> healthCheck) |> ignore
            
            auditLogger |> Option.iter (fun logger ->
                logger.LogEvent(
                    AuditLogger.AuditEventType.ConfigurationChange,
                    AuditLogger.AuditSeverity.Info,
                    "HealthCheckService",
                    $"Health check registered: {healthCheck.Name}",
                    Map.ofList [
                        ("checkName", box healthCheck.Name)
                        ("tags", box (String.Join(",", healthCheck.Tags)))
                    ]
                )
            )
        
        member _.UnregisterCheck(checkName: string) =
            checks.TryRemove(checkName) |> ignore
            
            auditLogger |> Option.iter (fun logger ->
                logger.LogEvent(
                    AuditLogger.AuditEventType.ConfigurationChange,
                    AuditLogger.AuditSeverity.Info,
                    "HealthCheckService",
                    $"Health check unregistered: {checkName}",
                    Map.ofList [("checkName", box checkName)]
                )
            )
        
        member _.ExecuteHealthChecks(?timeout: TimeSpan, ?cancellationToken: CancellationToken) =
            async {
                let timeout = defaultArg timeout (TimeSpan.FromSeconds(30.0))
                let cancellationToken = defaultArg cancellationToken CancellationToken.None
                
                let executeCheck (check: IHealthCheck) =
                    async {
                        try
                            use cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken)
                            cts.CancelAfter(timeout)
                            return! check.ExecuteAsync(cts.Token)
                        with
                        | :? OperationCanceledException ->
                            return {
                                Name = check.Name
                                Status = Unhealthy("Health check timed out")
                                ResponseTime = timeout
                                CheckedAt = DateTimeOffset.Now
                                Details = Map.ofList [("timeout", box timeout.TotalSeconds)]
                                Tags = check.Tags
                            }
                        | ex ->
                            return {
                                Name = check.Name
                                Status = Unhealthy(ex.Message)
                                ResponseTime = TimeSpan.Zero
                                CheckedAt = DateTimeOffset.Now
                                Details = Map.ofList [("error", box ex.Message)]
                                Tags = check.Tags
                            }
                    }
                
                let checkTasks = 
                    checks.Values
                    |> Seq.map executeCheck
                    |> Async.Parallel
                
                let! results = checkTasks
                
                let healthyCount = results |> Array.filter (fun r -> r.Status = Healthy) |> Array.length
                let unhealthyCount = results |> Array.filter (fun r -> match r.Status with Unhealthy(_) -> true | _ -> false) |> Array.length
                let degradedCount = results |> Array.filter (fun r -> match r.Status with Degraded(_) -> true | _ -> false) |> Array.length
                
                let overallStatus = 
                    if unhealthyCount > 0 then
                        Unhealthy($"{unhealthyCount} unhealthy checks")
                    elif degradedCount > 0 then
                        Degraded($"{degradedCount} degraded checks")
                    else
                        Healthy
                
                let systemHealth = {
                    Status = overallStatus
                    TotalChecks = results.Length
                    HealthyChecks = healthyCount
                    UnhealthyChecks = unhealthyCount
                    DegradedChecks = degradedCount
                    CheckedAt = DateTimeOffset.Now
                    Results = List.ofArray results
                    Uptime = DateTimeOffset.Now - startTime
                }
                
                auditLogger |> Option.iter (fun logger ->
                    let severity =
                        match overallStatus with
                        | Healthy -> AuditLogger.AuditSeverity.Info
                        | Degraded(_) -> AuditLogger.AuditSeverity.Warning
                        | Unhealthy(_) -> AuditLogger.AuditSeverity.Error
                        | Unknown -> AuditLogger.AuditSeverity.Warning

                    logger.LogEvent(
                        AuditLogger.AuditEventType.HealthCheck,
                        severity,
                        "HealthCheckService",
                        $"Health check completed: {overallStatus}",
                        Map.ofList [
                            ("totalChecks", box systemHealth.TotalChecks)
                            ("healthyChecks", box systemHealth.HealthyChecks)
                            ("unhealthyChecks", box systemHealth.UnhealthyChecks)
                            ("degradedChecks", box systemHealth.DegradedChecks)
                            ("uptime", box systemHealth.Uptime.TotalSeconds)
                        ]
                    )
                )
                
                return systemHealth
            }
        
        member this.GetReadinessCheck() =
            async {
                let! health = this.ExecuteHealthChecks(TimeSpan.FromSeconds(10.0))
                
                // Readiness means all critical checks are healthy or degraded (no unhealthy)
                let isReady = health.UnhealthyChecks = 0
                
                return {|
                    Status = if isReady then "Ready" else "NotReady"
                    Timestamp = DateTimeOffset.Now
                    Checks = health.Results |> List.length
                    UnhealthyChecks = health.UnhealthyChecks
                |}
            }
        
        member _.GetLivenessCheck() =
            async {
                // Liveness is a simple check that the service is running
                // We can check basic functionality like memory allocation
                try
                    let testArray = Array.zeroCreate<byte> 1024
                    Array.fill testArray 0 1024 42uy
                    
                    return {|
                        Status = "Alive"
                        Timestamp = DateTimeOffset.Now
                        Uptime = DateTimeOffset.Now - startTime
                        ProcessId = System.Diagnostics.Process.GetCurrentProcess().Id
                    |}
                with
                | ex ->
                    return {|
                        Status = "NotAlive"
                        Timestamp = DateTimeOffset.Now
                        Uptime = DateTimeOffset.Now - startTime
                        ProcessId = System.Diagnostics.Process.GetCurrentProcess().Id
                    |}
            }

    /// Graceful shutdown manager
    type GracefulShutdownManager(auditLogger: AuditLogger.AuditLogger option) as this =
        let mutable shutdownRequested = false
        let mutable activeOperations = 0
        let shutdownEvent = new ManualResetEventSlim(false)
        let lockObj = obj()

        // Register for shutdown signals
        let cancellationTokenSource = new CancellationTokenSource()

        do
            // Handle SIGTERM gracefully
            System.AppDomain.CurrentDomain.ProcessExit.Add(fun _ ->
                this.InitiateShutdown("ProcessExit")
            )

            // Handle console cancel events (Ctrl+C)
            System.Console.CancelKeyPress.Add(fun args ->
                args.Cancel <- true
                this.InitiateShutdown("CancelKeyPress")
            )
        
        member _.RegisterOperation() =
            lock lockObj (fun () ->
                if not shutdownRequested then
                    activeOperations <- activeOperations + 1
                    true
                else
                    false
            )
        
        member _.UnregisterOperation() =
            lock lockObj (fun () ->
                activeOperations <- max 0 (activeOperations - 1)
                if activeOperations = 0 && shutdownRequested then
                    shutdownEvent.Set()
            )
        
        member _.InitiateShutdown(reason: string) =
            lock lockObj (fun () ->
                if not shutdownRequested then
                    shutdownRequested <- true
                    cancellationTokenSource.Cancel()
                    
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.AuditEventType.ConfigurationChange,
                            AuditLogger.AuditSeverity.Warning,
                            "GracefulShutdownManager",
                            $"Shutdown initiated: {reason}",
                            Map.ofList [
                                ("reason", box reason)
                                ("activeOperations", box activeOperations)
                            ]
                        )
                    )
                    
                    if activeOperations = 0 then
                        shutdownEvent.Set()
            )
        
        member _.WaitForShutdown(?timeoutMs: int) =
            let timeoutMs = defaultArg timeoutMs 30000 // 30 seconds default
            
            if shutdownRequested then
                let completed = shutdownEvent.Wait(timeoutMs)
                
                if not completed then
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.AuditEventType.Error,
                            AuditLogger.AuditSeverity.Warning,
                            "GracefulShutdownManager",
                            $"Graceful shutdown timed out after {timeoutMs}ms with {activeOperations} active operations",
                            Map.ofList [
                                ("timeoutMs", box timeoutMs)
                                ("activeOperations", box activeOperations)
                            ]
                        )
                    )
                else
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.AuditEventType.ConfigurationChange,
                            AuditLogger.AuditSeverity.Info,
                            "GracefulShutdownManager",
                            "Graceful shutdown completed successfully",
                            Map.empty
                        )
                    )
                
                completed
            else
                true
        
        member _.IsShutdownRequested = shutdownRequested
        member _.ActiveOperations = activeOperations
        member _.CancellationToken = cancellationTokenSource.Token
        
        interface IDisposable with
            member _.Dispose() =
                cancellationTokenSource.Dispose()
                shutdownEvent.Dispose()

    /// Helper for wrapping operations with graceful shutdown support
    module GracefulOperations =
        
        /// Execute operation with graceful shutdown support
        let executeWithShutdownSupport (shutdownManager: GracefulShutdownManager) (operation: CancellationToken -> Async<'T>) =
            async {
                if shutdownManager.RegisterOperation() then
                    try
                        return! operation shutdownManager.CancellationToken
                    finally
                        shutdownManager.UnregisterOperation()
                else
                    return failwith "Cannot start operation: shutdown in progress"
            }
        
        /// Execute long-running operation with periodic shutdown checks
        let executeLongRunningWithShutdownChecks (shutdownManager: GracefulShutdownManager) (operation: CancellationToken -> Async<'T>) (checkIntervalMs: int) =
            async {
                if shutdownManager.RegisterOperation() then
                    try
                        use cts = CancellationTokenSource.CreateLinkedTokenSource(shutdownManager.CancellationToken)
                        
                        // Start a background task to periodically check for shutdown
                        let checkTask = 
                            async {
                                while not cts.Token.IsCancellationRequested do
                                    do! Async.Sleep(checkIntervalMs)
                                    if shutdownManager.IsShutdownRequested then
                                        cts.Cancel()
                            }
                        
                        Async.Start(checkTask, cts.Token)
                        
                        return! operation cts.Token
                    finally
                        shutdownManager.UnregisterOperation()
                else
                    return failwith "Cannot start operation: shutdown in progress"
            }

    /// Create default health check service
    let createHealthCheckService (auditLogger: AuditLogger.AuditLogger option) =
        let service = new HealthCheckService(auditLogger)
        
        // Register default system health checks
        service.RegisterCheck(MemoryHealthCheck(512L)) // 512MB limit
        service.RegisterCheck(DiskSpaceHealthCheck(".", 1L)) // 1GB minimum free space
        
        service

    /// Create graceful shutdown manager
    let createGracefulShutdownManager (auditLogger: AuditLogger.AuditLogger option) =
        new GracefulShutdownManager(auditLogger)