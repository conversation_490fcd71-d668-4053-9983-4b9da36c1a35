namespace Automation.AI

open System
open System.Threading.Tasks
open Automation.Core
open Automation.AI.CostOptimizer
open Automation.AI.AdvancedCostAlgorithms
open Automation.Utilities.Logging

/// Integrated cost-aware AI provider system with advanced algorithms
module IntegratedCostAwareSystem =
    
    /// Configuration for the integrated system
    type IntegratedSystemConfig = {
        BasicOptimizerConfig: CostOptimizerConfig
        AdvancedAlgorithmConfig: AdvancedAlgorithmConfig
        EnableAdvancedFeatures: bool
        FallbackToBasic: bool
        PerformanceTrackingEnabled: bool
        ABTestingEnabled: bool
        ABTestPercentage: float // 0.0-1.0, percentage of requests to use for A/B testing
        CostBudgetLimits: CostBudgetLimits option
        AlertingConfig: AlertingConfig option
    }
    and CostBudgetLimits = {
        DailyLimit: decimal
        MonthlyLimit: decimal
        PerRequestLimit: decimal
        EnableAutomaticThrottling: bool
    }
    and AlertingConfig = {
        EnableSlackAlerts: bool
        EnableEmailAlerts: bool
        SlackWebhookUrl: string option
        EmailRecipients: string list
        CostThresholds: decimal list
        LatencyThresholds: TimeSpan list
    }
    
    /// Default integrated system configuration
    let defaultIntegratedConfig = {
        BasicOptimizerConfig = defaultCostOptimizerConfig
        AdvancedAlgorithmConfig = defaultAdvancedConfig
        EnableAdvancedFeatures = true
        FallbackToBasic = true
        PerformanceTrackingEnabled = true
        ABTestingEnabled = false
        ABTestPercentage = 0.1
        CostBudgetLimits = Some {
            DailyLimit = 100.0m
            MonthlyLimit = 2000.0m
            PerRequestLimit = 5.0m
            EnableAutomaticThrottling = true
        }
        AlertingConfig = Some {
            EnableSlackAlerts = false
            EnableEmailAlerts = false
            SlackWebhookUrl = None
            EmailRecipients = []
            CostThresholds = [50.0m; 100.0m; 200.0m]
            LatencyThresholds = [TimeSpan.FromSeconds(5.0); TimeSpan.FromSeconds(10.0); TimeSpan.FromSeconds(30.0)]
        }
    }
    
    /// Request context for cost-aware decisions
    type AIRequestContext = {
        RequestId: string
        UserId: string option
        RequestType: string // "simple_command", "complex_workflow", "batch_processing"
        Priority: RequestPriority
        BudgetConstraint: decimal option
        LatencyRequirement: TimeSpan option
        QualityRequirement: float option // 0.0-1.0
        PreferredProviders: AIFramework.ProviderId list option
        Model: string
        TokenCount: int option
        ImageCount: int option
        Metadata: Map<string, string>
    }
    and RequestPriority = Low | Medium | High | Critical
    
    /// Result of cost-aware provider selection
    type ProviderSelectionResult = {
        SelectedProvider: AIFramework.ProviderId
        Model: string
        EstimatedCost: decimal
        EstimatedLatency: TimeSpan
        ConfidenceScore: float
        SelectionReason: string
        AlternativeProviders: (AIFramework.ProviderId * decimal * string) list
        UsedAdvancedAlgorithm: bool
        ABTestGroup: string option
        BudgetImpact: BudgetImpact
    }
    and BudgetImpact = {
        RemainingDailyBudget: decimal
        RemainingMonthlyBudget: decimal
        ProjectedDailyCost: decimal
        ProjectedMonthlyCost: decimal
        IsWithinLimits: bool
    }
    
    /// Integrated cost-aware AI system
    type IntegratedCostAwareSystem(config: IntegratedSystemConfig, auditLogger: AuditLogger.AuditLogger option) =
        let basicOptimizer = createCostOptimizer (Some config.BasicOptimizerConfig) auditLogger
        let advancedOptimizer = 
            if config.EnableAdvancedFeatures then
                Some (createAdvancedCostOptimizer (Some config.AdvancedAlgorithmConfig) basicOptimizer auditLogger)
            else None
        
        let mutable requestCounter = 0L
        let abTestGroups = System.Collections.Concurrent.ConcurrentDictionary<string, string>()
        
        /// Check budget constraints
        member _.CheckBudgetConstraints(estimatedCost: decimal) =
            match config.CostBudgetLimits with
            | None -> 
                { RemainingDailyBudget = 999999.0m
                  RemainingMonthlyBudget = 999999.0m
                  ProjectedDailyCost = estimatedCost
                  ProjectedMonthlyCost = estimatedCost * 30.0m
                  IsWithinLimits = true }
            | Some limits ->
                let todaysCost = basicOptimizer.GetDailyCost(DateOnly.FromDateTime(DateTime.Today))
                let monthsCost = basicOptimizer.GetMonthlyCost(DateTime.Today.Year, DateTime.Today.Month)
                
                let remainingDaily = limits.DailyLimit - todaysCost
                let remainingMonthly = limits.MonthlyLimit - monthsCost
                let projectedDaily = todaysCost + estimatedCost
                let projectedMonthly = monthsCost + estimatedCost
                
                let isWithinLimits = 
                    estimatedCost <= limits.PerRequestLimit &&
                    projectedDaily <= limits.DailyLimit &&
                    projectedMonthly <= limits.MonthlyLimit
                
                { RemainingDailyBudget = remainingDaily
                  RemainingMonthlyBudget = remainingMonthly
                  ProjectedDailyCost = projectedDaily
                  ProjectedMonthlyCost = projectedMonthly
                  IsWithinLimits = isWithinLimits }
        
        /// Determine if request should use A/B testing
        member _.ShouldUseABTesting(requestId: string) =
            if not config.ABTestingEnabled then false
            else
                let hash = requestId.GetHashCode() |> abs
                let normalizedHash = float hash / float Int32.MaxValue
                normalizedHash < config.ABTestPercentage
        
        /// Assign A/B test group
        member this.AssignABTestGroup(requestId: string) =
            if not (this.ShouldUseABTesting(requestId)) then None
            else
                let hash = requestId.GetHashCode() |> abs
                let group = if hash % 2 = 0 then "advanced" else "basic"
                abTestGroups.[requestId] <- group
                Some group
        
        /// Select provider using appropriate algorithm
        member this.SelectProvider(context: AIRequestContext) : Async<Result<ProviderSelectionResult, string>> =
            async {
                try
                    let requestId = context.RequestId
                    let abTestGroup = this.AssignABTestGroup(requestId)
                    
                    // Determine which algorithm to use
                    let useAdvanced = 
                        match abTestGroup with
                        | Some "advanced" -> true
                        | Some "basic" -> false
                        | None -> config.EnableAdvancedFeatures && advancedOptimizer.IsSome
                        | _ -> config.EnableAdvancedFeatures && advancedOptimizer.IsSome
                    
                    let! selectedEstimate = 
                        if useAdvanced && advancedOptimizer.IsSome then
                            async {
                                let advanced = advancedOptimizer.Value
                                let goals = this.DetermineOptimizationGoals(context)
                                match advanced.SelectProviderAdvanced(requestId, context.Model, context.TokenCount, context.ImageCount, goals) with
                                | Some estimate -> return Some (estimate, true)
                                | None when config.FallbackToBasic ->
                                    let strategy = this.DetermineBasicStrategy(context)
                                    match basicOptimizer.SelectProvider(strategy, context.Model, context.TokenCount, context.ImageCount) with
                                    | Some estimate -> return Some (estimate, false)
                                    | None -> return None
                                | None -> return None
                            }
                        else
                            async {
                                let strategy = this.DetermineBasicStrategy(context)
                                match basicOptimizer.SelectProvider(strategy, context.Model, context.TokenCount, context.ImageCount) with
                                | Some estimate -> return Some (estimate, false)
                                | None -> return None
                            }
                    
                    match selectedEstimate with
                    | Some (estimate, usedAdvanced) ->
                        // Check budget constraints
                        let budgetImpact = this.CheckBudgetConstraints(estimate.EstimatedCost)
                        
                        if not budgetImpact.IsWithinLimits && 
                           config.CostBudgetLimits.IsSome && 
                           config.CostBudgetLimits.Value.EnableAutomaticThrottling then
                            
                            auditLogger |> Option.iter (fun logger ->
                                logger.LogEvent(
                                    AuditLogger.AuditEventType.ValidationFailure,
                                    AuditLogger.AuditSeverity.Warning,
                                    "IntegratedCostAwareSystem",
                                    $"Request {requestId} blocked due to budget constraints",
                                    Map.ofList [
                                        ("requestId", box requestId)
                                        ("estimatedCost", box estimate.EstimatedCost)
                                        ("remainingDailyBudget", box budgetImpact.RemainingDailyBudget)
                                    ]
                                )
                            )
                            
                            return Result.Error "Request blocked due to budget constraints"
                        else
                            // Get alternative providers for comparison
                            let alternatives = this.GetAlternativeProviders(context, estimate.ProviderId)
                            
                            let result = {
                                SelectedProvider = estimate.ProviderId
                                Model = estimate.Model
                                EstimatedCost = estimate.EstimatedCost
                                EstimatedLatency = estimate.ExpectedLatency
                                ConfidenceScore = estimate.Confidence
                                SelectionReason = String.concat "; " estimate.Reasoning
                                AlternativeProviders = alternatives
                                UsedAdvancedAlgorithm = usedAdvanced
                                ABTestGroup = abTestGroup
                                BudgetImpact = budgetImpact
                            }
                            
                            auditLogger |> Option.iter (fun logger ->
                                logger.LogEvent(
                                    AuditLogger.AuditEventType.ActionExecution,
                                    AuditLogger.AuditSeverity.Info,
                                    "IntegratedCostAwareSystem",
                                    $"Selected {estimate.ProviderId} for request {requestId} - Cost: ${estimate.EstimatedCost:F4}",
                                    Map.ofList [
                                        ("requestId", box requestId)
                                        ("providerId", box (estimate.ProviderId.ToString()))
                                        ("estimatedCost", box estimate.EstimatedCost)
                                        ("usedAdvanced", box usedAdvanced)
                                        ("abTestGroup", box abTestGroup)
                                    ]
                                )
                            )
                            
                            return Result.Ok result
                    
                    | None ->
                        auditLogger |> Option.iter (fun logger ->
                            logger.LogEvent(
                                AuditLogger.AuditEventType.Error,
                                AuditLogger.AuditSeverity.Error,
                                "IntegratedCostAwareSystem",
                                $"No suitable provider found for request {requestId}",
                                Map.ofList [("requestId", box requestId)]
                            )
                        )
                        
                        return Result.Error "No suitable provider found"
                
                with
                | ex ->
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.AuditEventType.Error,
                            AuditLogger.AuditSeverity.Error,
                            "IntegratedCostAwareSystem",
                            $"Error selecting provider for request {context.RequestId}: {ex.Message}",
                            Map.ofList [
                                ("requestId", box context.RequestId)
                                ("exception", box ex.Message)
                            ]
                        )
                    )
                    
                    return Result.Error $"Error selecting provider: {ex.Message}"
            }
        
        /// Determine optimization goals based on request context
        member this.DetermineOptimizationGoals(context: AIRequestContext) =
            match context.Priority with
            | Critical -> [MinimizeLatency; MaximizeReliability]
            | High -> [BalanceMultipleGoals(Map.ofList [(MinimizeLatency, 0.4); (MaximizeQuality, 0.3); (MaximizeReliability, 0.3)])]
            | Medium -> [BalanceMultipleGoals(Map.ofList [(MinimizeCost, 0.3); (MaximizeQuality, 0.3); (MinimizeLatency, 0.2); (MaximizeReliability, 0.2)])]
            | Low -> [MinimizeCost; MaximizeReliability]
        
        /// Determine basic selection strategy based on context
        member _.DetermineBasicStrategy(context: AIRequestContext) =
            match context.Priority with
            | Critical -> LatencyOptimized
            | High -> QualityMaximization
            | Medium -> BalancedCostQuality
            | Low -> CostMinimization
        
        /// Get alternative providers for comparison
        member this.GetAlternativeProviders(context: AIRequestContext, selectedProviderId: AIFramework.ProviderId) =
            let allEstimates = basicOptimizer.GetAllCostEstimates(context.Model, context.TokenCount, context.ImageCount)
            
            allEstimates
            |> List.filter (fun est -> est.ProviderId <> selectedProviderId)
            |> List.map (fun est -> (est.ProviderId, est.EstimatedCost, $"Quality: {est.QualityScore:F2}, Latency: {est.ExpectedLatency.TotalSeconds:F1}s"))
            |> List.sortBy (fun (_, cost, _) -> cost)
        
        /// Record actual performance for learning
        member this.RecordPerformance(requestId: string, providerId: AIFramework.ProviderId, model: string, 
                                     actualCost: decimal, actualLatency: TimeSpan, success: bool, 
                                     ?qualityScore: float, ?userSatisfaction: float, ?tokensUsed: int, ?imagesProcessed: int) =
            
            // Record in basic optimizer
            basicOptimizer.RecordActualCost(providerId, model, actualCost, tokensUsed, imagesProcessed)
            
            // Record in advanced optimizer if available
            if config.PerformanceTrackingEnabled && advancedOptimizer.IsSome then
                let advanced = advancedOptimizer.Value
                
                // Get estimated values for comparison (if available from original selection)
                let estimatedCost = actualCost // Placeholder - should store original estimates
                let estimatedLatency = actualLatency // Placeholder - should store original estimates
                let successRate = if success then 1.0 else 0.0
                
                advanced.RecordPerformance(
                    providerId, model, actualCost, estimatedCost, actualLatency, estimatedLatency, successRate,
                    ?qualityScore=qualityScore, ?tokensUsed=tokensUsed, ?imagesProcessed=imagesProcessed,
                    ?userSatisfaction=userSatisfaction)
            
            auditLogger |> Option.iter (fun logger ->
                logger.LogEvent(
                    AuditLogger.AuditEventType.ActionExecution,
                    AuditLogger.AuditSeverity.Info,
                    "IntegratedCostAwareSystem",
                    $"Recorded performance for request {requestId} - Cost: ${actualCost:F4}, Latency: {actualLatency.TotalSeconds:F2}s, Success: {success}",
                    Map.ofList [
                        ("requestId", box requestId)
                        ("providerId", box (providerId.ToString()))
                        ("actualCost", box actualCost)
                        ("actualLatency", box actualLatency.TotalSeconds)
                        ("success", box success)
                    ]
                )
            )
        
        /// Get comprehensive system analytics
        member this.GetSystemAnalytics() =
            let basicStats = basicOptimizer.GetCostStatistics()
            let providerComparison = basicOptimizer.GetProviderComparison()
            
            let advancedAnalytics = 
                match advancedOptimizer with
                | Some advanced -> Some (advanced.GetPerformanceAnalytics())
                | None -> None
            
            let abTestAnalytics = 
                if config.ABTestingEnabled then
                    let groups = abTestGroups.ToArray() |> Array.map (fun kvp -> kvp.Value) |> Array.groupBy id
                    Some (groups |> Array.map (fun (group, requests) -> {| Group = group; RequestCount = requests.Length |}))
                else None
            
            {|
                BasicStatistics = basicStats
                ProviderComparison = providerComparison
                AdvancedAnalytics = advancedAnalytics
                ABTestAnalytics = abTestAnalytics
                SystemConfig = {|
                    EnableAdvancedFeatures = config.EnableAdvancedFeatures
                    ABTestingEnabled = config.ABTestingEnabled
                    PerformanceTrackingEnabled = config.PerformanceTrackingEnabled
                    BudgetLimitsEnabled = config.CostBudgetLimits.IsSome
                |}
                AnalysisTimestamp = DateTimeOffset.UtcNow
            |}
        
        /// Send alerts based on configuration
        member _.SendAlert(alertType: string, message: string, data: Map<string, obj>) =
            match config.AlertingConfig with
            | None -> ()
            | Some alertConfig ->
                if alertConfig.EnableSlackAlerts && alertConfig.SlackWebhookUrl.IsSome then
                    // Placeholder for Slack integration
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.AuditEventType.ActionExecution,
                            AuditLogger.AuditSeverity.Info,
                            "IntegratedCostAwareSystem",
                            $"Would send Slack alert: {alertType} - {message}",
                            data
                        )
                    )
                
                if alertConfig.EnableEmailAlerts && not alertConfig.EmailRecipients.IsEmpty then
                    // Placeholder for email integration
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.AuditEventType.ActionExecution,
                            AuditLogger.AuditSeverity.Info,
                            "IntegratedCostAwareSystem",
                            $"Would send email alert to {alertConfig.EmailRecipients.Length} recipients: {alertType} - {message}",
                            data
                        )
                    )
        
        interface IDisposable with
            member _.Dispose() =
                // Cleanup resources
                // basicOptimizer and advancedOptimizer don't implement IDisposable
                ()
    
    /// Create integrated cost-aware system
    let createIntegratedSystem (config: IntegratedSystemConfig option) (auditLogger: AuditLogger.AuditLogger option) =
        let config = defaultArg config defaultIntegratedConfig
        new IntegratedCostAwareSystem(config, auditLogger)
