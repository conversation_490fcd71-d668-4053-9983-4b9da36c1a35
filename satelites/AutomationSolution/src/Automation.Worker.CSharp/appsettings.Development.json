{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Information", "Automation": "Debug"}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information"}}}, "Worker": {"Concurrency": {"MaxConcurrentTasks": 1}, "CircuitBreaker": {"FailureThreshold": 3, "FailureWindow": "00:00:30"}, "Metrics": {"IncludeDetailedMetrics": true}}, "WebAutomation": {"Headless": false, "LaunchOptions": {"Devtools": true, "SlowMo": 100}}, "AIInfrastructure": {"Observability": {"EnableDetailedLogging": true}}}