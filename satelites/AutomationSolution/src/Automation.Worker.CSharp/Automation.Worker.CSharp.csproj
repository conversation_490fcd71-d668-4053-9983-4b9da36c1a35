<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <RootNamespace>Automation.Worker</RootNamespace>
    <AssemblyName>Automation.Worker.CSharp</AssemblyName>
    <Description>C# worker service for processing automation tasks with Redis integration</Description>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <Nullable>enable</Nullable>
    <UserSecretsId>automation-worker-secrets</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Automation.Contracts\Automation.Contracts.csproj" />
    <ProjectReference Include="..\Automation.Web.CSharp\Automation.Web.CSharp.csproj" />
    <ProjectReference Include="..\Automation.Mobile.CSharp\Automation.Mobile.CSharp.csproj" />
    <ProjectReference Include="..\Automation.AI.Infrastructure.CSharp\Automation.AI.Infrastructure.CSharp.csproj" />
    <ProjectReference Include="..\Automation.Utilities.CSharp\Automation.Utilities.CSharp.csproj">
      <Aliases>CSharpUtilities</Aliases>
    </ProjectReference>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="8.0.0" />
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
    <PackageReference Include="StackExchange.Redis" Version="2.7.10" />
    <PackageReference Include="Polly" Version="8.2.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
  </ItemGroup>

</Project>
