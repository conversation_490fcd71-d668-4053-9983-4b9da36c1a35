namespace Automation.Worker.Configuration;

/// <summary>
/// Configuration for the automation worker service
/// </summary>
public class WorkerConfiguration
{
    /// <summary>
    /// Redis connection configuration
    /// </summary>
    public RedisConfiguration Redis { get; set; } = new();

    /// <summary>
    /// Concurrency and performance settings
    /// </summary>
    public ConcurrencyConfiguration Concurrency { get; set; } = new();

    /// <summary>
    /// Circuit breaker configuration
    /// </summary>
    public CircuitBreakerConfiguration CircuitBreaker { get; set; } = new();

    /// <summary>
    /// Retry policy configuration
    /// </summary>
    public RetryConfiguration Retry { get; set; } = new();

    /// <summary>
    /// Health check configuration
    /// </summary>
    public HealthCheckConfiguration HealthCheck { get; set; } = new();

    /// <summary>
    /// Metrics configuration
    /// </summary>
    public MetricsConfiguration Metrics { get; set; } = new();

    /// <summary>
    /// Creates default configuration
    /// </summary>
    public static WorkerConfiguration CreateDefault()
    {
        return new WorkerConfiguration();
    }
}

/// <summary>
/// Redis connection configuration
/// </summary>
public class RedisConfiguration
{
    /// <summary>
    /// Redis connection string
    /// </summary>
    public string ConnectionString { get; set; } = "localhost:6379";

    /// <summary>
    /// Channel to subscribe to for incoming tasks
    /// </summary>
    public string TaskChannel { get; set; } = "automation_channel";

    /// <summary>
    /// Dead letter queue key for failed messages
    /// </summary>
    public string DeadLetterQueue { get; set; } = "automation_dlq";

    /// <summary>
    /// Connection timeout in milliseconds
    /// </summary>
    public int ConnectionTimeoutMs { get; set; } = 5000;

    /// <summary>
    /// Command timeout in milliseconds
    /// </summary>
    public int CommandTimeoutMs { get; set; } = 30000;

    /// <summary>
    /// Whether to abort connection on connect failure
    /// </summary>
    public bool AbortOnConnectFail { get; set; } = false;
}

/// <summary>
/// Concurrency and performance configuration
/// </summary>
public class ConcurrencyConfiguration
{
    /// <summary>
    /// Maximum number of concurrent tasks
    /// </summary>
    public int MaxConcurrentTasks { get; set; } = 3;

    /// <summary>
    /// Task processing timeout in milliseconds
    /// </summary>
    public int TaskTimeoutMs { get; set; } = 300000; // 5 minutes

    /// <summary>
    /// Whether to enable task cancellation
    /// </summary>
    public bool EnableTaskCancellation { get; set; } = true;

    /// <summary>
    /// Graceful shutdown timeout in milliseconds
    /// </summary>
    public int ShutdownTimeoutMs { get; set; } = 30000;
}

/// <summary>
/// Circuit breaker configuration
/// </summary>
public class CircuitBreakerConfiguration
{
    /// <summary>
    /// Whether circuit breaker is enabled
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Number of failures before opening circuit
    /// </summary>
    public int FailureThreshold { get; set; } = 5;

    /// <summary>
    /// Time window for counting failures
    /// </summary>
    public TimeSpan FailureWindow { get; set; } = TimeSpan.FromMinutes(1);

    /// <summary>
    /// How long to keep circuit open
    /// </summary>
    public TimeSpan OpenDuration { get; set; } = TimeSpan.FromMinutes(1);

    /// <summary>
    /// Number of test requests in half-open state
    /// </summary>
    public int HalfOpenTestRequests { get; set; } = 3;
}

/// <summary>
/// Retry policy configuration
/// </summary>
public class RetryConfiguration
{
    /// <summary>
    /// Maximum number of retry attempts
    /// </summary>
    public int MaxAttempts { get; set; } = 3;

    /// <summary>
    /// Base delay for exponential backoff
    /// </summary>
    public TimeSpan BaseDelay { get; set; } = TimeSpan.FromMilliseconds(500);

    /// <summary>
    /// Maximum delay for exponential backoff
    /// </summary>
    public TimeSpan MaxDelay { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Whether to add jitter to delays
    /// </summary>
    public bool UseJitter { get; set; } = true;

    /// <summary>
    /// Jitter factor (0.0 to 1.0)
    /// </summary>
    public double JitterFactor { get; set; } = 0.2;
}

/// <summary>
/// Health check configuration
/// </summary>
public class HealthCheckConfiguration
{
    /// <summary>
    /// Whether health checks are enabled
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Health check endpoint port
    /// </summary>
    public int Port { get; set; } = 8080;

    /// <summary>
    /// Health check interval
    /// </summary>
    public TimeSpan CheckInterval { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Health check timeout
    /// </summary>
    public TimeSpan CheckTimeout { get; set; } = TimeSpan.FromSeconds(10);
}

/// <summary>
/// Metrics configuration
/// </summary>
public class MetricsConfiguration
{
    /// <summary>
    /// Whether metrics collection is enabled
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Metrics endpoint port (same as health check by default)
    /// </summary>
    public int Port { get; set; } = 8080;

    /// <summary>
    /// Metrics collection interval
    /// </summary>
    public TimeSpan CollectionInterval { get; set; } = TimeSpan.FromSeconds(15);

    /// <summary>
    /// Whether to include detailed metrics
    /// </summary>
    public bool IncludeDetailedMetrics { get; set; } = true;
}
