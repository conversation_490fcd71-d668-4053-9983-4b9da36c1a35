using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Automation.Worker.Models
{
    /// <summary>
    /// Represents the result of a web automation test
    /// </summary>
    public class TestResult
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string TestName { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration => EndTime - StartTime;
        public TestStatus Status { get; set; }
        public string? ErrorMessage { get; set; }
        public string? StackTrace { get; set; }
        public List<TestStep> Steps { get; set; } = new();
        public List<string> Screenshots { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Represents a single step in a test
    /// </summary>
    public class TestStep
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Action { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public TestStepStatus Status { get; set; }
        public string? ErrorMessage { get; set; }
        public string? ScreenshotPath { get; set; }
        public Dictionary<string, object> Data { get; set; } = new();
    }

    /// <summary>
    /// Status of a test
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum TestStatus
    {
        Running,
        Passed,
        Failed,
        Skipped,
        Timeout
    }

    /// <summary>
    /// Status of a test step
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum TestStepStatus
    {
        Running,
        Passed,
        Failed,
        Skipped,
        Warning
    }

    /// <summary>
    /// Summary of test results
    /// </summary>
    public class TestResultSummary
    {
        public int TotalTests { get; set; }
        public int PassedTests { get; set; }
        public int FailedTests { get; set; }
        public int SkippedTests { get; set; }
        public int RunningTests { get; set; }
        public DateTime LastRunTime { get; set; }
        public TimeSpan TotalDuration { get; set; }
        public double SuccessRate => TotalTests > 0 ? (double)PassedTests / TotalTests * 100 : 0;
    }
}
