extern alias CSharpUtilities;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using StackExchange.Redis;
using Automation.Worker.Configuration;
using Automation.Worker.Services;
using Automation.Web.Extensions;
using Automation.Mobile.Extensions;
using Automation.AI.Infrastructure.Extensions;
using CSharpUtilities::Automation.Utilities.Logging;
using CSharpRetry = CSharpUtilities::Automation.Utilities.Common.RetryPolicy;
using CSharpRetryOptions = CSharpUtilities::Automation.Utilities.Common.RetryPolicyOptions;

namespace Automation.Worker;

public class Program
{
    public static async Task<int> Main(string[] args)
    {
        // Configure Serilog early
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File("logs/worker-.log", rollingInterval: RollingInterval.Day)
            .CreateBootstrapLogger();

        try
        {
            Log.Information("Starting Automation Worker Service");

            var host = CreateHostBuilder(args).Build();

            // Initialize logging
            var loggerFactory = host.Services.GetRequiredService<ILoggerFactory>();
            CSharpUtilities::Automation.Utilities.Logging.LoggerFactory.Initialize(loggerFactory);

            await host.RunAsync();
            return 0;
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Automation Worker Service terminated unexpectedly");
            return 1;
        }
        finally
        {
            await Log.CloseAndFlushAsync();
        }
    }

    public static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .UseWindowsService() // Enable Windows Service support
            .UseSerilog((context, configuration) => configuration
                .ReadFrom.Configuration(context.Configuration)
                .Enrich.FromLogContext()
                .WriteTo.Console()
                .WriteTo.File("logs/worker-.log",
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 7))
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                      .AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json",
                                 optional: true, reloadOnChange: true)
                      .AddEnvironmentVariables()  // Read all environment variables
                      .AddCommandLine(args);
            })
            .ConfigureServices((context, services) =>
            {
                // Configure worker configuration
                var workerConfig = WorkerConfiguration.CreateDefault();
                context.Configuration.GetSection("Worker").Bind(workerConfig);
                services.AddSingleton(workerConfig);

                // Configure Redis
                services.AddSingleton<IConnectionMultiplexer>(provider =>
                {
                    var config = provider.GetRequiredService<WorkerConfiguration>();
                    var options = ConfigurationOptions.Parse(config.Redis.ConnectionString);
                    options.ConnectTimeout = config.Redis.ConnectionTimeoutMs;
                    options.CommandMap = CommandMap.Create(new HashSet<string>(), false);
                    options.AbortOnConnectFail = config.Redis.AbortOnConnectFail;
                    
                    return ConnectionMultiplexer.Connect(options);
                });

                // Configure retry policy
                services.AddSingleton<CSharpRetryOptions>(provider =>
                {
                    var config = provider.GetRequiredService<WorkerConfiguration>();
                    return new CSharpRetryOptions
                    {
                        MaxAttempts = config.Retry.MaxAttempts,
                        InitialDelay = config.Retry.BaseDelay,
                        MaxDelay = config.Retry.MaxDelay,
                        UseJitter = config.Retry.UseJitter
                    };
                });
                services.AddTransient<CSharpRetry>();

                // Add automation services
                services.AddWebAutomationServicesHeadless();
                services.AddMobileAutomationServicesForAndroid();
                services.AddAIInfrastructureServicesForTesting();

                // Add worker services
                services.AddSingleton<ICircuitBreakerService>(provider =>
                {
                    var config = provider.GetRequiredService<WorkerConfiguration>();
                    var logger = provider.GetRequiredService<ILogger<CircuitBreakerService>>();
                    return new CircuitBreakerService(config.CircuitBreaker, logger);
                });

                services.AddTransient<ITaskProcessingService, TaskProcessingService>();
                services.AddHostedService<RedisSubscriberService>();
                services.AddHostedService<MetricsService>();

                // Add health checks
                services.AddHealthChecks()
                    .AddCheck<WorkerHealthCheck>("worker")
                    .AddCheck<RedisHealthCheck>("redis");
            });
}

/// <summary>
/// Health check for the worker service
/// </summary>
public class WorkerHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
{
    private readonly ICircuitBreakerService _circuitBreaker;
    private readonly ILogger<WorkerHealthCheck> _logger;

    public WorkerHealthCheck(
        ICircuitBreakerService circuitBreaker,
        ILogger<WorkerHealthCheck> logger)
    {
        _circuitBreaker = circuitBreaker;
        _logger = logger;
    }

    public Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cbStats = _circuitBreaker.GetStatistics();
            
            var data = new Dictionary<string, object>
            {
                ["circuit_breaker_state"] = cbStats.State.ToString(),
                ["failure_count"] = cbStats.FailureCount,
                ["success_count"] = cbStats.SuccessCount
            };

            if (cbStats.State == CircuitBreakerState.Open)
            {
                return Task.FromResult(
                    Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy(
                        "Circuit breaker is open", data: data));
            }

            return Task.FromResult(
                Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy(
                    "Worker is healthy", data: data));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Worker health check failed");
            return Task.FromResult(
                Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy(
                    "Worker health check failed", ex));
        }
    }
}

/// <summary>
/// Health check for Redis connection
/// </summary>
public class RedisHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
{
    private readonly IConnectionMultiplexer _redis;
    private readonly ILogger<RedisHealthCheck> _logger;

    public RedisHealthCheck(
        IConnectionMultiplexer redis,
        ILogger<RedisHealthCheck> logger)
    {
        _redis = redis;
        _logger = logger;
    }

    public async Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var database = _redis.GetDatabase();
            await database.PingAsync();

            var data = new Dictionary<string, object>
            {
                ["is_connected"] = _redis.IsConnected,
                ["configuration"] = _redis.Configuration ?? "unknown"
            };

            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy(
                "Redis is healthy", data: data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis health check failed");
            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy(
                "Redis is unhealthy", ex);
        }
    }
}
