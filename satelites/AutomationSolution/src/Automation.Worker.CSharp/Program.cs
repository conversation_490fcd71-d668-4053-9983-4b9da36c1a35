extern alias CSharpUtilities;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using StackExchange.Redis;
using Automation.Worker.Configuration;
using Automation.Worker.Services;
using Automation.Worker.Models;
using Automation.Web.Extensions;
using Automation.Mobile.Extensions;
using Automation.AI.Infrastructure.Extensions;
using CSharpUtilities::Automation.Utilities.Logging;
using CSharpRetry = CSharpUtilities::Automation.Utilities.Common.RetryPolicy;
using CSharpRetryOptions = CSharpUtilities::Automation.Utilities.Common.RetryPolicyOptions;

namespace Automation.Worker;

public class Program
{
    public static async Task<int> Main(string[] args)
    {
        // Configure Serilog early
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File("logs/worker-.log", rollingInterval: RollingInterval.Day)
            .CreateBootstrapLogger();

        try
        {
            Log.Information("Starting Automation Worker Service");

            var builder = Microsoft.AspNetCore.WebApplication.CreateBuilder(args);

            // Configure Serilog
            builder.Host.UseSerilog((context, configuration) => configuration
                .ReadFrom.Configuration(context.Configuration)
                .Enrich.FromLogContext()
                .WriteTo.Console()
                .WriteTo.File("logs/worker-.log",
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 7));

            // Configure worker configuration
            var workerConfig = WorkerConfiguration.CreateDefault();
            builder.Configuration.GetSection("Worker").Bind(workerConfig);
            builder.Services.AddSingleton(workerConfig);

            // Configure Redis
            builder.Services.AddSingleton<IConnectionMultiplexer>(provider =>
            {
                var config = provider.GetRequiredService<WorkerConfiguration>();
                var options = ConfigurationOptions.Parse(config.Redis.ConnectionString);
                options.ConnectTimeout = config.Redis.ConnectionTimeoutMs;
                options.CommandMap = CommandMap.Create(new HashSet<string>(), false);
                options.AbortOnConnectFail = config.Redis.AbortOnConnectFail;

                return ConnectionMultiplexer.Connect(options);
            });

            // Configure retry policy
            builder.Services.AddSingleton<CSharpRetryOptions>(provider =>
            {
                var config = provider.GetRequiredService<WorkerConfiguration>();
                return new CSharpRetryOptions
                {
                    MaxAttempts = config.Retry.MaxAttempts,
                    InitialDelay = config.Retry.BaseDelay,
                    MaxDelay = config.Retry.MaxDelay,
                    UseJitter = config.Retry.UseJitter
                };
            });
            builder.Services.AddTransient<CSharpRetry>();

            // Add automation services
            builder.Services.AddWebAutomationServicesHeadless();
            builder.Services.AddMobileAutomationServicesForAndroid();
            builder.Services.AddAIInfrastructureServicesForTesting();

            // Add worker services
            builder.Services.AddSingleton<ICircuitBreakerService>(provider =>
            {
                var config = provider.GetRequiredService<WorkerConfiguration>();
                var logger = provider.GetRequiredService<ILogger<CircuitBreakerService>>();
                return new CircuitBreakerService(config.CircuitBreaker, logger);
            });

            builder.Services.AddTransient<ITaskProcessingService, TaskProcessingService>();
            builder.Services.AddHostedService<RedisSubscriberService>();
            builder.Services.AddHostedService<MetricsService>();

            // Add test result services
            builder.Services.AddSingleton<ITestResultService, TestResultService>();
            builder.Services.AddTransient<IWebTestService, WebTestService>();

            // Add health checks
            builder.Services.AddHealthChecks()
                .AddCheck<WorkerHealthCheck>("worker")
                .AddCheck<RedisHealthCheck>("redis");

            var app = builder.Build();

            // Initialize logging
            var loggerFactory = app.Services.GetRequiredService<ILoggerFactory>();
            CSharpUtilities::Automation.Utilities.Logging.LoggerFactory.Initialize(loggerFactory);

            // Configure the HTTP request pipeline
            app.UseRouting();
            app.MapHealthChecks("/health");

            // Serve static files for the dashboard
            app.UseStaticFiles();

            // Add a simple test endpoint for Playwright
            app.MapGet("/test-playwright", async (Automation.Web.Services.IPlaywrightService playwrightService, ILogger<Program> logger) =>
            {
                try
                {
                    logger.LogInformation("Testing Playwright initialization...");
                    var playwright = await playwrightService.GetPlaywrightAsync();

                    if (playwright == null)
                    {
                        return Results.BadRequest(new { error = "Playwright is not available", available = playwrightService.IsAvailable });
                    }

                    return Results.Ok(new {
                        status = "Playwright is working",
                        available = playwrightService.IsAvailable,
                        version = playwright.Version
                    });
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error testing Playwright");
                    return Results.BadRequest(new { error = ex.Message, stackTrace = ex.StackTrace });
                }
            });

            // Add metrics endpoint
            app.MapGet("/metrics", (MetricsService metricsService) =>
            {
                return Results.Ok(metricsService.GetCurrentMetrics());
            });

            // Add test result endpoints
            app.MapGet("/test-results", async (ITestResultService testResultService) =>
            {
                var results = await testResultService.GetAllTestResultsAsync();
                return Results.Ok(results);
            });

            app.MapGet("/test-results/summary", async (ITestResultService testResultService) =>
            {
                var summary = await testResultService.GetTestSummaryAsync();
                return Results.Ok(summary);
            });

            app.MapGet("/test-results/recent", async (ITestResultService testResultService, int count = 10) =>
            {
                var results = await testResultService.GetRecentTestsAsync(count);
                return Results.Ok(results);
            });

            app.MapGet("/test-results/{testId}", async (string testId, ITestResultService testResultService) =>
            {
                var result = await testResultService.GetTestResultAsync(testId);
                return result != null ? Results.Ok(result) : Results.NotFound();
            });

            app.MapGet("/screenshots/{screenshotPath}", async (string screenshotPath, ITestResultService testResultService) =>
            {
                var screenshot = await testResultService.GetScreenshotAsync(screenshotPath);
                return screenshot != null
                    ? Results.File(screenshot, "image/png", screenshotPath)
                    : Results.NotFound();
            });

            // Add test execution endpoints
            app.MapPost("/run-test/navigation", async (string url, string? testName, IWebTestService webTestService) =>
            {
                try
                {
                    var testId = await webTestService.RunBasicNavigationTestAsync(url, testName);
                    return Results.Ok(new { testId, message = "Navigation test started", url });
                }
                catch (Exception ex)
                {
                    return Results.BadRequest(new { error = ex.Message });
                }
            });

            app.MapPost("/run-test/analysis", async (string url, string? testName, IWebTestService webTestService) =>
            {
                try
                {
                    var testId = await webTestService.RunPageAnalysisTestAsync(url, testName);
                    return Results.Ok(new { testId, message = "Page analysis test started", url });
                }
                catch (Exception ex)
                {
                    return Results.BadRequest(new { error = ex.Message });
                }
            });

            app.MapPost("/run-test/form-interaction", async (string url, string? testName, IWebTestService webTestService) =>
            {
                try
                {
                    var testId = await webTestService.RunFormInteractionTestAsync(url, testName);
                    return Results.Ok(new { testId, message = "Form interaction test started", url });
                }
                catch (Exception ex)
                {
                    return Results.BadRequest(new { error = ex.Message });
                }
            });

            // Add dashboard endpoint
            app.MapGet("/", () => Results.Content(GetDashboardHtml(), "text/html"));
            app.MapGet("/dashboard", () => Results.Content(GetDashboardHtml(), "text/html"));

            app.Urls.Add("http://0.0.0.0:8080");

            await app.RunAsync();
            return 0;
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Automation Worker Service terminated unexpectedly");
            return 1;
        }
        finally
        {
            await Log.CloseAndFlushAsync();
        }
    }

    /// <summary>
    /// Generate HTML for the test results dashboard
    /// </summary>
    private static string GetDashboardHtml()
    {
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automation Test Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .stat-label { color: #666; }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .running { color: #007bff; }
        .skipped { color: #6c757d; }
        .test-controls { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .test-results { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .test-item { border-bottom: 1px solid #eee; padding: 15px 0; }
        .test-item:last-child { border-bottom: none; }
        .test-header { display: flex; justify-content: between; align-items: center; margin-bottom: 10px; }
        .test-name { font-weight: bold; }
        .test-status { padding: 4px 8px; border-radius: 4px; color: white; font-size: 0.8em; }
        .test-meta { color: #666; font-size: 0.9em; }
        .screenshot { max-width: 200px; margin: 10px 0; border-radius: 4px; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        input[type="url"] { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin: 5px; }
        .loading { display: none; color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Automation Test Dashboard</h1>
            <p>Monitor and run web automation tests with Playwright</p>
        </div>

        <div class="stats" id="stats">
            <!-- Stats will be loaded here -->
        </div>

        <div class="test-controls">
            <h3>Run New Test</h3>
            <div>
                <input type="url" id="testUrl" placeholder="Enter URL to test (e.g., https://example.com)" value="https://example.com">
                <input type="text" id="testName" placeholder="Test name (optional)">
            </div>
            <div>
                <button onclick="runTest('navigation')">🌐 Navigation Test</button>
                <button onclick="runTest('analysis')">🔍 Page Analysis</button>
                <button onclick="runTest('form-interaction')">📝 Form Interaction</button>
                <button onclick="loadResults()">🔄 Refresh Results</button>
            </div>
            <div class="loading" id="loading">Running test...</div>
        </div>

        <div class="test-results">
            <h3>Recent Test Results</h3>
            <div id="results">
                <!-- Results will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        async function loadStats() {
            try {
                const response = await fetch('/test-results/summary');
                const stats = await response.json();

                document.getElementById('stats').innerHTML = `
                    <div class="stat-card">
                        <div class="stat-number">${stats.totalTests}</div>
                        <div class="stat-label">Total Tests</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number passed">${stats.passedTests}</div>
                        <div class="stat-label">Passed</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number failed">${stats.failedTests}</div>
                        <div class="stat-label">Failed</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number running">${stats.runningTests}</div>
                        <div class="stat-label">Running</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${stats.successRate.toFixed(1)}%</div>
                        <div class="stat-label">Success Rate</div>
                    </div>
                `;
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        async function loadResults() {
            try {
                const response = await fetch('/test-results/recent?count=10');
                const results = await response.json();

                const resultsHtml = results.map(result => `
                    <div class="test-item">
                        <div class="test-header">
                            <div>
                                <div class="test-name">${result.testName}</div>
                                <div class="test-meta">
                                    ${result.url} • ${new Date(result.startTime).toLocaleString()} • ${result.duration ? Math.round(result.duration.totalSeconds * 1000) / 1000 + 's' : 'Running...'}
                                </div>
                            </div>
                            <span class="test-status ${result.status.toLowerCase()}">${result.status}</span>
                        </div>
                        ${result.errorMessage ? `<div style="color: #dc3545; margin: 10px 0;">${result.errorMessage}</div>` : ''}
                        ${result.screenshots.length > 0 ? `
                            <div>
                                ${result.screenshots.map(screenshot => `
                                    <img src="/screenshots/${screenshot}" alt="Screenshot" class="screenshot" onclick="window.open('/screenshots/${screenshot}', '_blank')">
                                `).join('')}
                            </div>
                        ` : ''}
                        ${Object.keys(result.metadata).length > 0 ? `
                            <div class="test-meta">
                                ${Object.entries(result.metadata).map(([key, value]) => `${key}: ${value}`).join(' • ')}
                            </div>
                        ` : ''}
                    </div>
                `).join('');

                document.getElementById('results').innerHTML = resultsHtml || '<p>No test results yet. Run a test to get started!</p>';
            } catch (error) {
                console.error('Error loading results:', error);
                document.getElementById('results').innerHTML = '<p>Error loading results. Please try again.</p>';
            }
        }

        async function runTest(testType) {
            const url = document.getElementById('testUrl').value;
            const testName = document.getElementById('testName').value;

            if (!url) {
                alert('Please enter a URL to test');
                return;
            }

            document.getElementById('loading').style.display = 'block';

            try {
                const response = await fetch(`/run-test/${testType}?url=${encodeURIComponent(url)}&testName=${encodeURIComponent(testName)}`, {
                    method: 'POST'
                });

                const result = await response.json();

                if (response.ok) {
                    alert(`Test started successfully! Test ID: ${result.testId}`);
                    setTimeout(() => {
                        loadResults();
                        loadStats();
                    }, 1000);
                } else {
                    alert(`Error: ${result.error}`);
                }
            } catch (error) {
                console.error('Error running test:', error);
                alert('Error running test. Please try again.');
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }

        // Load initial data
        loadStats();
        loadResults();

        // Auto-refresh every 30 seconds
        setInterval(() => {
            loadStats();
            loadResults();
        }, 30000);
    </script>
</body>
</html>
""";
    }

}

/// <summary>
/// Health check for the worker service
/// </summary>
public class WorkerHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
{
    private readonly ICircuitBreakerService _circuitBreaker;
    private readonly ILogger<WorkerHealthCheck> _logger;

    public WorkerHealthCheck(
        ICircuitBreakerService circuitBreaker,
        ILogger<WorkerHealthCheck> logger)
    {
        _circuitBreaker = circuitBreaker;
        _logger = logger;
    }

    public Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cbStats = _circuitBreaker.GetStatistics();
            
            var data = new Dictionary<string, object>
            {
                ["circuit_breaker_state"] = cbStats.State.ToString(),
                ["failure_count"] = cbStats.FailureCount,
                ["success_count"] = cbStats.SuccessCount
            };

            if (cbStats.State == CircuitBreakerState.Open)
            {
                return Task.FromResult(
                    Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy(
                        "Circuit breaker is open", data: data));
            }

            return Task.FromResult(
                Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy(
                    "Worker is healthy", data: data));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Worker health check failed");
            return Task.FromResult(
                Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy(
                    "Worker health check failed", ex));
        }
    }
}

/// <summary>
/// Health check for Redis connection
/// </summary>
public class RedisHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
{
    private readonly IConnectionMultiplexer _redis;
    private readonly ILogger<RedisHealthCheck> _logger;

    public RedisHealthCheck(
        IConnectionMultiplexer redis,
        ILogger<RedisHealthCheck> logger)
    {
        _redis = redis;
        _logger = logger;
    }

    public async Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var database = _redis.GetDatabase();
            await database.PingAsync();

            var data = new Dictionary<string, object>
            {
                ["is_connected"] = _redis.IsConnected,
                ["configuration"] = _redis.Configuration ?? "unknown"
            };

            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy(
                "Redis is healthy", data: data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis health check failed");
            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy(
                "Redis is unhealthy", ex);
        }
    }
}
