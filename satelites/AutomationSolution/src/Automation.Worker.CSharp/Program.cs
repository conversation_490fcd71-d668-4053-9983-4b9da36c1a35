extern alias CSharpUtilities;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using StackExchange.Redis;
using Automation.Worker.Configuration;
using Automation.Worker.Services;
using Automation.Web.Extensions;
using Automation.Mobile.Extensions;
using Automation.AI.Infrastructure.Extensions;
using CSharpUtilities::Automation.Utilities.Logging;
using CSharpRetry = CSharpUtilities::Automation.Utilities.Common.RetryPolicy;
using CSharpRetryOptions = CSharpUtilities::Automation.Utilities.Common.RetryPolicyOptions;

namespace Automation.Worker;

public class Program
{
    public static async Task<int> Main(string[] args)
    {
        // Configure Serilog early
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File("logs/worker-.log", rollingInterval: RollingInterval.Day)
            .CreateBootstrapLogger();

        try
        {
            Log.Information("Starting Automation Worker Service");

            var builder = Microsoft.AspNetCore.WebApplication.CreateBuilder(args);

            // Configure Serilog
            builder.Host.UseSerilog((context, configuration) => configuration
                .ReadFrom.Configuration(context.Configuration)
                .Enrich.FromLogContext()
                .WriteTo.Console()
                .WriteTo.File("logs/worker-.log",
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 7));

            // Configure worker configuration
            var workerConfig = WorkerConfiguration.CreateDefault();
            builder.Configuration.GetSection("Worker").Bind(workerConfig);
            builder.Services.AddSingleton(workerConfig);

            // Configure Redis
            builder.Services.AddSingleton<IConnectionMultiplexer>(provider =>
            {
                var config = provider.GetRequiredService<WorkerConfiguration>();
                var options = ConfigurationOptions.Parse(config.Redis.ConnectionString);
                options.ConnectTimeout = config.Redis.ConnectionTimeoutMs;
                options.CommandMap = CommandMap.Create(new HashSet<string>(), false);
                options.AbortOnConnectFail = config.Redis.AbortOnConnectFail;

                return ConnectionMultiplexer.Connect(options);
            });

            // Configure retry policy
            builder.Services.AddSingleton<CSharpRetryOptions>(provider =>
            {
                var config = provider.GetRequiredService<WorkerConfiguration>();
                return new CSharpRetryOptions
                {
                    MaxAttempts = config.Retry.MaxAttempts,
                    InitialDelay = config.Retry.BaseDelay,
                    MaxDelay = config.Retry.MaxDelay,
                    UseJitter = config.Retry.UseJitter
                };
            });
            builder.Services.AddTransient<CSharpRetry>();

            // Add automation services
            builder.Services.AddWebAutomationServicesHeadless();
            builder.Services.AddMobileAutomationServicesForAndroid();
            builder.Services.AddAIInfrastructureServicesForTesting();

            // Add worker services
            builder.Services.AddSingleton<ICircuitBreakerService>(provider =>
            {
                var config = provider.GetRequiredService<WorkerConfiguration>();
                var logger = provider.GetRequiredService<ILogger<CircuitBreakerService>>();
                return new CircuitBreakerService(config.CircuitBreaker, logger);
            });

            builder.Services.AddTransient<ITaskProcessingService, TaskProcessingService>();
            builder.Services.AddHostedService<RedisSubscriberService>();
            builder.Services.AddHostedService<MetricsService>();

            // Add health checks
            builder.Services.AddHealthChecks()
                .AddCheck<WorkerHealthCheck>("worker")
                .AddCheck<RedisHealthCheck>("redis");

            var app = builder.Build();

            // Initialize logging
            var loggerFactory = app.Services.GetRequiredService<ILoggerFactory>();
            CSharpUtilities::Automation.Utilities.Logging.LoggerFactory.Initialize(loggerFactory);

            // Configure the HTTP request pipeline
            app.UseRouting();
            app.MapHealthChecks("/health");

            // Add a simple test endpoint for Playwright
            app.MapGet("/test-playwright", async (Automation.Web.Services.IPlaywrightService playwrightService, ILogger<Program> logger) =>
            {
                try
                {
                    logger.LogInformation("Testing Playwright initialization...");
                    var playwright = await playwrightService.GetPlaywrightAsync();

                    if (playwright == null)
                    {
                        return Results.BadRequest(new { error = "Playwright is not available", available = playwrightService.IsAvailable });
                    }

                    return Results.Ok(new {
                        status = "Playwright is working",
                        available = playwrightService.IsAvailable,
                        version = playwright.Version
                    });
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error testing Playwright");
                    return Results.BadRequest(new { error = ex.Message, stackTrace = ex.StackTrace });
                }
            });

            // Add metrics endpoint
            app.MapGet("/metrics", (MetricsService metricsService) =>
            {
                return Results.Ok(metricsService.GetCurrentMetrics());
            });

            app.Urls.Add("http://0.0.0.0:8080");

            await app.RunAsync();
            return 0;
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Automation Worker Service terminated unexpectedly");
            return 1;
        }
        finally
        {
            await Log.CloseAndFlushAsync();
        }
    }

}

/// <summary>
/// Health check for the worker service
/// </summary>
public class WorkerHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
{
    private readonly ICircuitBreakerService _circuitBreaker;
    private readonly ILogger<WorkerHealthCheck> _logger;

    public WorkerHealthCheck(
        ICircuitBreakerService circuitBreaker,
        ILogger<WorkerHealthCheck> logger)
    {
        _circuitBreaker = circuitBreaker;
        _logger = logger;
    }

    public Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cbStats = _circuitBreaker.GetStatistics();
            
            var data = new Dictionary<string, object>
            {
                ["circuit_breaker_state"] = cbStats.State.ToString(),
                ["failure_count"] = cbStats.FailureCount,
                ["success_count"] = cbStats.SuccessCount
            };

            if (cbStats.State == CircuitBreakerState.Open)
            {
                return Task.FromResult(
                    Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy(
                        "Circuit breaker is open", data: data));
            }

            return Task.FromResult(
                Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy(
                    "Worker is healthy", data: data));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Worker health check failed");
            return Task.FromResult(
                Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy(
                    "Worker health check failed", ex));
        }
    }
}

/// <summary>
/// Health check for Redis connection
/// </summary>
public class RedisHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
{
    private readonly IConnectionMultiplexer _redis;
    private readonly ILogger<RedisHealthCheck> _logger;

    public RedisHealthCheck(
        IConnectionMultiplexer redis,
        ILogger<RedisHealthCheck> logger)
    {
        _redis = redis;
        _logger = logger;
    }

    public async Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var database = _redis.GetDatabase();
            await database.PingAsync();

            var data = new Dictionary<string, object>
            {
                ["is_connected"] = _redis.IsConnected,
                ["configuration"] = _redis.Configuration ?? "unknown"
            };

            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy(
                "Redis is healthy", data: data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis health check failed");
            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy(
                "Redis is unhealthy", ex);
        }
    }
}
