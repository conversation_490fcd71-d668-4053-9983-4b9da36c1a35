{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "StackExchange.Redis": "Warning"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "StackExchange.Redis": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/worker-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext"]}, "Worker": {"Redis": {"ConnectionString": "localhost:6379", "TaskChannel": "automation_channel", "DeadLetterQueue": "automation_dlq", "ConnectionTimeoutMs": 5000, "CommandTimeoutMs": 30000, "AbortOnConnectFail": false}, "Concurrency": {"MaxConcurrentTasks": 3, "TaskTimeoutMs": 300000, "EnableTaskCancellation": true, "ShutdownTimeoutMs": 30000}, "CircuitBreaker": {"Enabled": true, "FailureThreshold": 5, "FailureWindow": "00:01:00", "OpenDuration": "00:01:00", "HalfOpenTestRequests": 3}, "Retry": {"MaxAttempts": 3, "BaseDelay": "00:00:00.500", "MaxDelay": "00:00:30", "UseJitter": true, "JitterFactor": 0.2}, "HealthCheck": {"Enabled": true, "Port": 8080, "CheckInterval": "00:00:30", "CheckTimeout": "00:00:10"}, "Metrics": {"Enabled": true, "Port": 8080, "CollectionInterval": "00:00:15", "IncludeDetailedMetrics": true}}, "WebAutomation": {"Browser": "Chromium", "Headless": true, "ResourceManagement": {"MaxConcurrentBrowsers": 2, "EnableBrowserPooling": true}}, "MobileAutomation": {"Platform": "Android", "Server": {"ServerUrl": "http://localhost:4723"}}, "AIInfrastructure": {"Caching": {"Enabled": true, "Provider": "Memory", "DefaultTtl": "00:30:00"}, "HttpClient": {"DefaultTimeout": "00:00:30", "MaxRetryAttempts": 3}}}