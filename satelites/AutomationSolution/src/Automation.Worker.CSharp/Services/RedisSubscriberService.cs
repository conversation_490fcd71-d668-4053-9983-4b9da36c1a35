using System.Collections.Concurrent;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using Automation.Worker.Configuration;

namespace Automation.Worker.Services;

/// <summary>
/// Redis subscriber service for receiving automation tasks
/// </summary>
public class RedisSubscriberService : BackgroundService
{
    private readonly IConnectionMultiplexer _redis;
    private readonly ITaskProcessingService _taskProcessor;
    private readonly WorkerConfiguration _config;
    private readonly ILogger<RedisSubscriberService> _logger;
    private readonly SemaphoreSlim _concurrencySemaphore;
    private readonly ConcurrentDictionary<string, CancellationTokenSource> _activeTasks = new();
    
    private ISubscriber? _subscriber;
    private long _messagesReceived = 0;
    private long _messagesProcessed = 0;
    private long _messagesFailed = 0;
    private DateTime _lastMessageReceived = DateTime.MinValue;

    public RedisSubscriberService(
        IConnectionMultiplexer redis,
        ITaskProcessingService taskProcessor,
        WorkerConfiguration config,
        ILogger<RedisSubscriberService> logger)
    {
        _redis = redis ?? throw new ArgumentNullException(nameof(redis));
        _taskProcessor = taskProcessor ?? throw new ArgumentNullException(nameof(taskProcessor));
        _config = config ?? throw new ArgumentNullException(nameof(config));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        _concurrencySemaphore = new SemaphoreSlim(_config.Concurrency.MaxConcurrentTasks);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Starting Redis subscriber service on channel: {Channel}", 
            _config.Redis.TaskChannel);

        try
        {
            _subscriber = _redis.GetSubscriber();
            
            await _subscriber.SubscribeAsync(
                RedisChannel.Literal(_config.Redis.TaskChannel),
                async (channel, message) => await HandleMessageAsync(message, stoppingToken));

            _logger.LogInformation("Redis subscriber service started successfully");

            // Keep the service running until cancellation is requested
            await Task.Delay(Timeout.Infinite, stoppingToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Redis subscriber service is stopping");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis subscriber service encountered an error");
            throw;
        }
        finally
        {
            await CleanupAsync();
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping Redis subscriber service...");

        // Cancel all active tasks
        var cancellationTasks = _activeTasks.Values.Select(cts =>
        {
            cts.Cancel();
            return Task.CompletedTask;
        });

        await Task.WhenAll(cancellationTasks);

        // Wait for graceful shutdown
        var shutdownTimeout = TimeSpan.FromMilliseconds(_config.Concurrency.ShutdownTimeoutMs);
        using var shutdownCts = new CancellationTokenSource(shutdownTimeout);
        
        try
        {
            await base.StopAsync(shutdownCts.Token);
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("Graceful shutdown timed out after {Timeout}ms", shutdownTimeout.TotalMilliseconds);
        }

        _logger.LogInformation("Redis subscriber service stopped");
    }

    private async Task HandleMessageAsync(RedisValue message, CancellationToken stoppingToken)
    {
        if (!message.HasValue)
        {
            _logger.LogWarning("Received empty message from Redis");
            return;
        }

        Interlocked.Increment(ref _messagesReceived);
        _lastMessageReceived = DateTime.UtcNow;

        var messageId = Guid.NewGuid().ToString();
        _logger.LogDebug("Received message {MessageId}: {MessageLength} characters", 
            messageId, message.ToString().Length);

        // Wait for available concurrency slot
        await _concurrencySemaphore.WaitAsync(stoppingToken);

        try
        {
            // Create cancellation token for this task
            using var taskCts = CancellationTokenSource.CreateLinkedTokenSource(stoppingToken);
            _activeTasks[messageId] = taskCts;

            // Set task timeout if configured
            if (_config.Concurrency.EnableTaskCancellation)
            {
                taskCts.CancelAfter(_config.Concurrency.TaskTimeoutMs);
            }

            // Process the message
            await ProcessMessageAsync(messageId, message.ToString(), taskCts.Token);
        }
        catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
        {
            _logger.LogInformation("Message processing cancelled due to service shutdown");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing message {MessageId}", messageId);
            Interlocked.Increment(ref _messagesFailed);
        }
        finally
        {
            _activeTasks.TryRemove(messageId, out _);
            _concurrencySemaphore.Release();
        }
    }

    private async Task ProcessMessageAsync(string messageId, string message, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Processing message {MessageId}", messageId);

            var result = await _taskProcessor.ProcessTaskAsync(message, cancellationToken);

            if (result.IsSuccess)
            {
                Interlocked.Increment(ref _messagesProcessed);
                _logger.LogInformation("Successfully processed message {MessageId} for task {TaskId} in {ProcessingTime}ms",
                    messageId, result.TaskId, result.ProcessingTime.TotalMilliseconds);
            }
            else
            {
                Interlocked.Increment(ref _messagesFailed);
                _logger.LogError("Failed to process message {MessageId} for task {TaskId}: {ErrorMessage}",
                    messageId, result.TaskId, result.ErrorMessage);

                // Send to dead letter queue if configured
                await SendToDeadLetterQueueAsync(message, result.ErrorMessage);
            }
        }
        catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
        {
            _logger.LogWarning("Message {MessageId} processing was cancelled", messageId);
            Interlocked.Increment(ref _messagesFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error processing message {MessageId}", messageId);
            Interlocked.Increment(ref _messagesFailed);
            
            await SendToDeadLetterQueueAsync(message, ex.Message);
        }
    }

    private async Task SendToDeadLetterQueueAsync(string message, string? errorMessage)
    {
        try
        {
            if (string.IsNullOrEmpty(_config.Redis.DeadLetterQueue))
            {
                return;
            }

            var database = _redis.GetDatabase();
            var dlqEntry = new
            {
                OriginalMessage = message,
                ErrorMessage = errorMessage,
                FailedAt = DateTime.UtcNow,
                WorkerId = Environment.MachineName
            };

            await database.ListLeftPushAsync(_config.Redis.DeadLetterQueue, 
                System.Text.Json.JsonSerializer.Serialize(dlqEntry));

            _logger.LogInformation("Sent failed message to dead letter queue: {DeadLetterQueue}", 
                _config.Redis.DeadLetterQueue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send message to dead letter queue");
        }
    }

    private async Task CleanupAsync()
    {
        try
        {
            if (_subscriber != null)
            {
                await _subscriber.UnsubscribeAsync(RedisChannel.Literal(_config.Redis.TaskChannel));
                _logger.LogInformation("Unsubscribed from Redis channel: {Channel}", _config.Redis.TaskChannel);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during Redis subscriber cleanup");
        }
    }

    /// <summary>
    /// Gets subscriber statistics
    /// </summary>
    public RedisSubscriberStatistics GetStatistics()
    {
        return new RedisSubscriberStatistics
        {
            MessagesReceived = _messagesReceived,
            MessagesProcessed = _messagesProcessed,
            MessagesFailed = _messagesFailed,
            ActiveTasks = _activeTasks.Count,
            LastMessageReceived = _lastMessageReceived,
            IsConnected = _redis.IsConnected
        };
    }
}

/// <summary>
/// Redis subscriber statistics
/// </summary>
public record RedisSubscriberStatistics
{
    public long MessagesReceived { get; init; }
    public long MessagesProcessed { get; init; }
    public long MessagesFailed { get; init; }
    public int ActiveTasks { get; init; }
    public DateTime LastMessageReceived { get; init; }
    public bool IsConnected { get; init; }
    public double SuccessRate => MessagesReceived > 0 ? (double)MessagesProcessed / MessagesReceived * 100 : 0;
}
