using System.Diagnostics;
using System.Text;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Automation.Worker.Configuration;

namespace Automation.Worker.Services;

/// <summary>
/// Service for exposing metrics and health endpoints
/// </summary>
public class MetricsService : IHostedService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly WorkerConfiguration _config;
    private readonly ILogger<MetricsService> _logger;
    private WebApplication? _app;

    public MetricsService(
        IServiceProvider serviceProvider,
        WorkerConfiguration config,
        ILogger<MetricsService> logger)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _config = config ?? throw new ArgumentNullException(nameof(config));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        if (!_config.Metrics.Enabled && !_config.HealthCheck.Enabled)
        {
            _logger.LogInformation("Metrics and health checks are disabled");
            return;
        }

        try
        {
            var builder = WebApplication.CreateBuilder();
            
            // Configure to listen on the specified port
            builder.WebHost.UseUrls($"http://0.0.0.0:{_config.Metrics.Port}");
            
            // Add minimal services
            builder.Services.AddSingleton(_serviceProvider);
            
            _app = builder.Build();

            // Configure health endpoint
            if (_config.HealthCheck.Enabled)
            {
                _app.MapGet("/health", HandleHealthCheck);
                _app.MapGet("/health/ready", HandleReadinessCheck);
                _app.MapGet("/health/live", HandleLivenessCheck);
            }

            // Configure metrics endpoint
            if (_config.Metrics.Enabled)
            {
                _app.MapGet("/metrics", HandleMetrics);
                _app.MapGet("/metrics/detailed", HandleDetailedMetrics);
            }

            await _app.StartAsync(cancellationToken);
            
            _logger.LogInformation("Metrics service started on port {Port}", _config.Metrics.Port);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start metrics service");
            throw;
        }
    }

    public async Task StopAsync(CancellationToken cancellationToken)
    {
        if (_app != null)
        {
            try
            {
                await _app.StopAsync(cancellationToken);
                await _app.DisposeAsync();
                _logger.LogInformation("Metrics service stopped");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error stopping metrics service");
            }
        }
    }

    private async Task HandleHealthCheck(HttpContext context)
    {
        try
        {
            var isHealthy = await CheckOverallHealthAsync();
            
            context.Response.StatusCode = isHealthy ? 200 : 503;
            context.Response.ContentType = "application/json";
            
            var response = new
            {
                status = isHealthy ? "healthy" : "unhealthy",
                timestamp = DateTime.UtcNow,
                version = GetType().Assembly.GetName().Version?.ToString() ?? "unknown"
            };

            await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(response));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in health check");
            context.Response.StatusCode = 503;
            await context.Response.WriteAsync("ERROR");
        }
    }

    private async Task HandleReadinessCheck(HttpContext context)
    {
        try
        {
            // Check if the service is ready to accept requests
            var redisSubscriber = _serviceProvider.GetService<RedisSubscriberService>();
            var isReady = redisSubscriber != null;

            context.Response.StatusCode = isReady ? 200 : 503;
            await context.Response.WriteAsync(isReady ? "READY" : "NOT_READY");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in readiness check");
            context.Response.StatusCode = 503;
            await context.Response.WriteAsync("ERROR");
        }
    }

    private async Task HandleLivenessCheck(HttpContext context)
    {
        // Simple liveness check - if we can respond, we're alive
        context.Response.StatusCode = 200;
        await context.Response.WriteAsync("ALIVE");
    }

    private async Task HandleMetrics(HttpContext context)
    {
        try
        {
            var metrics = await GeneratePrometheusMetricsAsync();
            
            context.Response.ContentType = "text/plain; version=0.0.4";
            await context.Response.WriteAsync(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating metrics");
            context.Response.StatusCode = 500;
            await context.Response.WriteAsync("ERROR");
        }
    }

    private async Task HandleDetailedMetrics(HttpContext context)
    {
        try
        {
            var metrics = await GenerateDetailedMetricsAsync();
            
            context.Response.ContentType = "application/json";
            await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(metrics, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true
            }));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating detailed metrics");
            context.Response.StatusCode = 500;
            await context.Response.WriteAsync("ERROR");
        }
    }

    private async Task<bool> CheckOverallHealthAsync()
    {
        try
        {
            // Check Redis connection
            var redisSubscriber = _serviceProvider.GetService<RedisSubscriberService>();
            if (redisSubscriber != null)
            {
                var stats = redisSubscriber.GetStatistics();
                if (!stats.IsConnected)
                {
                    return false;
                }
            }

            // Check circuit breaker
            var circuitBreaker = _serviceProvider.GetService<ICircuitBreakerService>();
            if (circuitBreaker != null)
            {
                var cbStats = circuitBreaker.GetStatistics();
                if (cbStats.State == CircuitBreakerState.Open)
                {
                    return false;
                }
            }

            return true;
        }
        catch
        {
            return false;
        }
    }

    private async Task<string> GeneratePrometheusMetricsAsync()
    {
        var sb = new StringBuilder();
        
        try
        {
            // Task processing metrics
            var taskProcessor = _serviceProvider.GetService<ITaskProcessingService>();
            if (taskProcessor != null)
            {
                var stats = taskProcessor.GetStatistics();
                
                sb.AppendLine("# HELP automation_tasks_total Total number of tasks processed");
                sb.AppendLine("# TYPE automation_tasks_total counter");
                sb.AppendLine($"automation_tasks_total {stats.TotalTasksProcessed}");
                
                sb.AppendLine("# HELP automation_tasks_successful_total Total number of successful tasks");
                sb.AppendLine("# TYPE automation_tasks_successful_total counter");
                sb.AppendLine($"automation_tasks_successful_total {stats.SuccessfulTasks}");
                
                sb.AppendLine("# HELP automation_tasks_failed_total Total number of failed tasks");
                sb.AppendLine("# TYPE automation_tasks_failed_total counter");
                sb.AppendLine($"automation_tasks_failed_total {stats.FailedTasks}");
                
                sb.AppendLine("# HELP automation_task_processing_duration_seconds Average task processing duration");
                sb.AppendLine("# TYPE automation_task_processing_duration_seconds gauge");
                sb.AppendLine($"automation_task_processing_duration_seconds {stats.AverageProcessingTime.TotalSeconds:F3}");
            }

            // Redis subscriber metrics
            var redisSubscriber = _serviceProvider.GetService<RedisSubscriberService>();
            if (redisSubscriber != null)
            {
                var stats = redisSubscriber.GetStatistics();
                
                sb.AppendLine("# HELP automation_redis_messages_received_total Total Redis messages received");
                sb.AppendLine("# TYPE automation_redis_messages_received_total counter");
                sb.AppendLine($"automation_redis_messages_received_total {stats.MessagesReceived}");
                
                sb.AppendLine("# HELP automation_redis_active_tasks Current number of active tasks");
                sb.AppendLine("# TYPE automation_redis_active_tasks gauge");
                sb.AppendLine($"automation_redis_active_tasks {stats.ActiveTasks}");
                
                sb.AppendLine("# HELP automation_redis_connected Redis connection status");
                sb.AppendLine("# TYPE automation_redis_connected gauge");
                sb.AppendLine($"automation_redis_connected {(stats.IsConnected ? 1 : 0)}");
            }

            // Circuit breaker metrics
            var circuitBreaker = _serviceProvider.GetService<ICircuitBreakerService>();
            if (circuitBreaker != null)
            {
                var stats = circuitBreaker.GetStatistics();
                
                sb.AppendLine("# HELP automation_circuit_breaker_state Circuit breaker state (0=Closed, 1=Open, 2=HalfOpen)");
                sb.AppendLine("# TYPE automation_circuit_breaker_state gauge");
                sb.AppendLine($"automation_circuit_breaker_state {(int)stats.State}");
                
                sb.AppendLine("# HELP automation_circuit_breaker_failures_total Total circuit breaker failures");
                sb.AppendLine("# TYPE automation_circuit_breaker_failures_total counter");
                sb.AppendLine($"automation_circuit_breaker_failures_total {stats.FailureCount}");
            }

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating Prometheus metrics");
            sb.AppendLine("# ERROR generating metrics");
        }

        return sb.ToString();
    }

    private async Task<object> GenerateDetailedMetricsAsync()
    {
        var metrics = new Dictionary<string, object>();

        try
        {
            // System metrics
            metrics["system"] = new
            {
                timestamp = DateTime.UtcNow,
                uptime = DateTime.UtcNow - Process.GetCurrentProcess().StartTime,
                machine_name = Environment.MachineName,
                processor_count = Environment.ProcessorCount,
                working_set = GC.GetTotalMemory(false),
                gc_collections = new
                {
                    gen0 = GC.CollectionCount(0),
                    gen1 = GC.CollectionCount(1),
                    gen2 = GC.CollectionCount(2)
                }
            };

            // Task processing metrics
            var taskProcessor = _serviceProvider.GetService<ITaskProcessingService>();
            if (taskProcessor != null)
            {
                metrics["task_processing"] = taskProcessor.GetStatistics();
            }

            // Redis subscriber metrics
            var redisSubscriber = _serviceProvider.GetService<RedisSubscriberService>();
            if (redisSubscriber != null)
            {
                metrics["redis_subscriber"] = redisSubscriber.GetStatistics();
            }

            // Circuit breaker metrics
            var circuitBreaker = _serviceProvider.GetService<ICircuitBreakerService>();
            if (circuitBreaker != null)
            {
                metrics["circuit_breaker"] = circuitBreaker.GetStatistics();
            }

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating detailed metrics");
            metrics["error"] = ex.Message;
        }

        return metrics;
    }
}
