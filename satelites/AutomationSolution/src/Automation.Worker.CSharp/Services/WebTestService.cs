using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using Automation.Web.Services;
using Automation.Worker.Models;

namespace Automation.Worker.Services
{
    /// <summary>
    /// Interface for web testing with result tracking
    /// </summary>
    public interface IWebTestService
    {
        Task<string> RunBasicNavigationTestAsync(string url, string? testName = null);
        Task<string> RunPageAnalysisTestAsync(string url, string? testName = null);
        Task<string> RunFormInteractionTestAsync(string url, string? testName = null);
    }

    /// <summary>
    /// Service for running web tests with Playwright and tracking results
    /// </summary>
    public class WebTestService : IWebTestService
    {
        private readonly IPlaywrightService _playwrightService;
        private readonly ITestResultService _testResultService;
        private readonly ILogger<WebTestService> _logger;
        private readonly string _screenshotsDirectory;

        public WebTestService(
            IPlaywrightService playwrightService,
            ITestResultService testResultService,
            ILogger<WebTestService> logger)
        {
            _playwrightService = playwrightService ?? throw new ArgumentNullException(nameof(playwrightService));
            _testResultService = testResultService ?? throw new ArgumentNullException(nameof(testResultService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            _screenshotsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "screenshots");
            Directory.CreateDirectory(_screenshotsDirectory);
        }

        public async Task<string> RunBasicNavigationTestAsync(string url, string? testName = null)
        {
            testName ??= $"Basic Navigation Test - {url}";
            var testId = await _testResultService.CreateTestAsync(testName, url);
            
            try
            {
                _logger.LogInformation("Starting basic navigation test for {Url}", url);
                
                // Initialize Playwright
                var stepId = await AddStepAsync(testId, "Initialize", "Initializing Playwright");
                var playwright = await _playwrightService.GetPlaywrightAsync();
                
                if (playwright == null)
                {
                    await UpdateStepAsync(testId, stepId, TestStepStatus.Failed, "Playwright is not available");
                    await _testResultService.UpdateTestAsync(testId, TestStatus.Failed, "Playwright is not available");
                    return testId;
                }
                
                await UpdateStepAsync(testId, stepId, TestStepStatus.Passed);

                // Launch browser
                stepId = await AddStepAsync(testId, "LaunchBrowser", "Launching browser");
                await using var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions 
                { 
                    Headless = true 
                });
                await UpdateStepAsync(testId, stepId, TestStepStatus.Passed);

                // Create page
                stepId = await AddStepAsync(testId, "CreatePage", "Creating new page");
                var page = await browser.NewPageAsync();
                await UpdateStepAsync(testId, stepId, TestStepStatus.Passed);

                // Navigate to URL
                stepId = await AddStepAsync(testId, "Navigate", $"Navigating to {url}");
                await page.GotoAsync(url, new PageGotoOptions { WaitUntil = WaitUntilState.NetworkIdle });
                await UpdateStepAsync(testId, stepId, TestStepStatus.Passed);

                // Take screenshot
                var screenshotPath = await TakeScreenshotAsync(testId, page, "navigation");
                await _testResultService.AddScreenshotAsync(testId, screenshotPath);

                // Get page title
                stepId = await AddStepAsync(testId, "GetTitle", "Getting page title");
                var title = await page.TitleAsync();
                await UpdateStepAsync(testId, stepId, TestStepStatus.Passed, screenshotPath: screenshotPath);

                // Get page URL
                stepId = await AddStepAsync(testId, "GetUrl", "Getting current URL");
                var currentUrl = page.Url;
                await UpdateStepAsync(testId, stepId, TestStepStatus.Passed);

                // Update test metadata
                var testResult = await _testResultService.GetTestResultAsync(testId);
                if (testResult != null)
                {
                    testResult.Metadata["title"] = title;
                    testResult.Metadata["finalUrl"] = currentUrl;
                    testResult.Metadata["screenshotCount"] = 1;
                }

                await _testResultService.UpdateTestAsync(testId, TestStatus.Passed);
                _logger.LogInformation("Basic navigation test completed successfully for {Url}", url);
                
                return testId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Basic navigation test failed for {Url}", url);
                await _testResultService.UpdateTestAsync(testId, TestStatus.Failed, ex.Message, ex.StackTrace);
                return testId;
            }
        }

        public async Task<string> RunPageAnalysisTestAsync(string url, string? testName = null)
        {
            testName ??= $"Page Analysis Test - {url}";
            var testId = await _testResultService.CreateTestAsync(testName, url);
            
            try
            {
                _logger.LogInformation("Starting page analysis test for {Url}", url);
                
                var playwright = await _playwrightService.GetPlaywrightAsync();
                if (playwright == null)
                {
                    await _testResultService.UpdateTestAsync(testId, TestStatus.Failed, "Playwright is not available");
                    return testId;
                }

                await using var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions { Headless = true });
                var page = await browser.NewPageAsync();

                // Navigate
                var stepId = await AddStepAsync(testId, "Navigate", $"Navigating to {url}");
                await page.GotoAsync(url, new PageGotoOptions { WaitUntil = WaitUntilState.NetworkIdle });
                await UpdateStepAsync(testId, stepId, TestStepStatus.Passed);

                // Analyze page elements
                stepId = await AddStepAsync(testId, "AnalyzeElements", "Analyzing page elements");
                var linkCount = await page.Locator("a").CountAsync();
                var imageCount = await page.Locator("img").CountAsync();
                var formCount = await page.Locator("form").CountAsync();
                var buttonCount = await page.Locator("button").CountAsync();
                await UpdateStepAsync(testId, stepId, TestStepStatus.Passed);

                // Take screenshot
                var screenshotPath = await TakeScreenshotAsync(testId, page, "analysis");
                await _testResultService.AddScreenshotAsync(testId, screenshotPath);

                // Check for common elements
                stepId = await AddStepAsync(testId, "CheckElements", "Checking for common elements");
                var hasNavigation = await page.Locator("nav").CountAsync() > 0;
                var hasHeader = await page.Locator("header").CountAsync() > 0;
                var hasFooter = await page.Locator("footer").CountAsync() > 0;
                await UpdateStepAsync(testId, stepId, TestStepStatus.Passed, screenshotPath: screenshotPath);

                // Update metadata
                var testResult = await _testResultService.GetTestResultAsync(testId);
                if (testResult != null)
                {
                    testResult.Metadata["title"] = await page.TitleAsync();
                    testResult.Metadata["linkCount"] = linkCount;
                    testResult.Metadata["imageCount"] = imageCount;
                    testResult.Metadata["formCount"] = formCount;
                    testResult.Metadata["buttonCount"] = buttonCount;
                    testResult.Metadata["hasNavigation"] = hasNavigation;
                    testResult.Metadata["hasHeader"] = hasHeader;
                    testResult.Metadata["hasFooter"] = hasFooter;
                }

                await _testResultService.UpdateTestAsync(testId, TestStatus.Passed);
                _logger.LogInformation("Page analysis test completed successfully for {Url}", url);
                
                return testId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Page analysis test failed for {Url}", url);
                await _testResultService.UpdateTestAsync(testId, TestStatus.Failed, ex.Message, ex.StackTrace);
                return testId;
            }
        }

        public async Task<string> RunFormInteractionTestAsync(string url, string? testName = null)
        {
            testName ??= $"Form Interaction Test - {url}";
            var testId = await _testResultService.CreateTestAsync(testName, url);
            
            try
            {
                _logger.LogInformation("Starting form interaction test for {Url}", url);
                
                var playwright = await _playwrightService.GetPlaywrightAsync();
                if (playwright == null)
                {
                    await _testResultService.UpdateTestAsync(testId, TestStatus.Failed, "Playwright is not available");
                    return testId;
                }

                await using var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions { Headless = true });
                var page = await browser.NewPageAsync();

                // Navigate
                var stepId = await AddStepAsync(testId, "Navigate", $"Navigating to {url}");
                await page.GotoAsync(url, new PageGotoOptions { WaitUntil = WaitUntilState.NetworkIdle });
                await UpdateStepAsync(testId, stepId, TestStepStatus.Passed);

                // Look for forms
                stepId = await AddStepAsync(testId, "FindForms", "Looking for forms on the page");
                var forms = await page.Locator("form").CountAsync();
                
                if (forms == 0)
                {
                    await UpdateStepAsync(testId, stepId, TestStepStatus.Warning, "No forms found on the page");
                    await _testResultService.UpdateTestAsync(testId, TestStatus.Passed);
                    return testId;
                }
                
                await UpdateStepAsync(testId, stepId, TestStepStatus.Passed);

                // Take screenshot before interaction
                var screenshotPath = await TakeScreenshotAsync(testId, page, "before-interaction");
                await _testResultService.AddScreenshotAsync(testId, screenshotPath);

                // Try to interact with input fields
                stepId = await AddStepAsync(testId, "InteractWithInputs", "Interacting with input fields");
                var inputs = await page.Locator("input[type='text'], input[type='email'], textarea").CountAsync();
                
                if (inputs > 0)
                {
                    // Fill first text input if available
                    var firstInput = page.Locator("input[type='text'], input[type='email'], textarea").First;
                    await firstInput.FillAsync("<EMAIL>");
                }
                
                await UpdateStepAsync(testId, stepId, TestStepStatus.Passed);

                // Take screenshot after interaction
                screenshotPath = await TakeScreenshotAsync(testId, page, "after-interaction");
                await _testResultService.AddScreenshotAsync(testId, screenshotPath);

                // Update metadata
                var testResult = await _testResultService.GetTestResultAsync(testId);
                if (testResult != null)
                {
                    testResult.Metadata["formCount"] = forms;
                    testResult.Metadata["inputCount"] = inputs;
                    testResult.Metadata["interactionPerformed"] = inputs > 0;
                }

                await _testResultService.UpdateTestAsync(testId, TestStatus.Passed);
                _logger.LogInformation("Form interaction test completed successfully for {Url}", url);
                
                return testId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Form interaction test failed for {Url}", url);
                await _testResultService.UpdateTestAsync(testId, TestStatus.Failed, ex.Message, ex.StackTrace);
                return testId;
            }
        }

        private async Task<string> AddStepAsync(string testId, string action, string description)
        {
            await _testResultService.AddTestStepAsync(testId, action, description);
            var testResult = await _testResultService.GetTestResultAsync(testId);
            return testResult?.Steps.LastOrDefault()?.Id ?? string.Empty;
        }

        private async Task UpdateStepAsync(string testId, string stepId, TestStepStatus status, string? errorMessage = null, string? screenshotPath = null)
        {
            await _testResultService.UpdateTestStepAsync(testId, stepId, status, errorMessage, screenshotPath);
        }

        private async Task<string> TakeScreenshotAsync(string testId, IPage page, string suffix)
        {
            var timestamp = DateTime.UtcNow.ToString("yyyyMMdd-HHmmss");
            var filename = $"{testId}-{suffix}-{timestamp}.png";
            var fullPath = Path.Combine(_screenshotsDirectory, filename);
            
            await page.ScreenshotAsync(new PageScreenshotOptions
            {
                Path = fullPath,
                FullPage = true
            });
            
            return filename;
        }
    }
}
