using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Automation.Worker.Configuration;

namespace Automation.Worker.Services;

/// <summary>
/// Circuit breaker states
/// </summary>
public enum CircuitBreakerState
{
    Closed,
    Open,
    HalfOpen
}

/// <summary>
/// Circuit breaker service for protecting against cascading failures
/// </summary>
public interface ICircuitBreakerService
{
    /// <summary>
    /// Current state of the circuit breaker
    /// </summary>
    CircuitBreakerState State { get; }

    /// <summary>
    /// Checks if the circuit allows execution
    /// </summary>
    bool CanExecute { get; }

    /// <summary>
    /// Records a successful execution
    /// </summary>
    void RecordSuccess();

    /// <summary>
    /// Records a failed execution
    /// </summary>
    void RecordFailure();

    /// <summary>
    /// Gets circuit breaker statistics
    /// </summary>
    CircuitBreakerStatistics GetStatistics();
}

/// <summary>
/// Circuit breaker statistics
/// </summary>
public record CircuitBreakerStatistics
{
    public CircuitBreakerState State { get; init; }
    public int FailureCount { get; init; }
    public int SuccessCount { get; init; }
    public DateTime? LastFailureTime { get; init; }
    public DateTime? LastSuccessTime { get; init; }
    public DateTime? CircuitOpenedAt { get; init; }
    public TimeSpan? TimeUntilRetry { get; init; }
    public int TestRequestsInHalfOpen { get; init; }
}

/// <summary>
/// Implementation of circuit breaker service
/// </summary>
public class CircuitBreakerService : ICircuitBreakerService
{
    private readonly CircuitBreakerConfiguration _config;
    private readonly ILogger<CircuitBreakerService> _logger;
    private readonly ConcurrentQueue<DateTime> _failureTimestamps = new();
    private readonly object _stateLock = new();
    
    private CircuitBreakerState _state = CircuitBreakerState.Closed;
    private DateTime? _circuitOpenedAt;
    private int _successCount = 0;
    private int _failureCount = 0;
    private int _testRequestsInHalfOpen = 0;
    private DateTime? _lastFailureTime;
    private DateTime? _lastSuccessTime;

    public CircuitBreakerService(
        CircuitBreakerConfiguration config,
        ILogger<CircuitBreakerService> logger)
    {
        _config = config ?? throw new ArgumentNullException(nameof(config));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public CircuitBreakerState State
    {
        get
        {
            lock (_stateLock)
            {
                return _state;
            }
        }
    }

    public bool CanExecute
    {
        get
        {
            if (!_config.Enabled)
                return true;

            lock (_stateLock)
            {
                switch (_state)
                {
                    case CircuitBreakerState.Closed:
                        return true;
                    
                    case CircuitBreakerState.Open:
                        // Check if we should transition to half-open
                        if (_circuitOpenedAt.HasValue && 
                            DateTime.UtcNow - _circuitOpenedAt.Value >= _config.OpenDuration)
                        {
                            TransitionToHalfOpen();
                            return true;
                        }
                        return false;
                    
                    case CircuitBreakerState.HalfOpen:
                        // Allow limited test requests
                        return _testRequestsInHalfOpen < _config.HalfOpenTestRequests;
                    
                    default:
                        return false;
                }
            }
        }
    }

    public void RecordSuccess()
    {
        lock (_stateLock)
        {
            _successCount++;
            _lastSuccessTime = DateTime.UtcNow;

            switch (_state)
            {
                case CircuitBreakerState.Closed:
                    // Clear old failures on success
                    ClearOldFailures();
                    break;
                
                case CircuitBreakerState.HalfOpen:
                    // Successful test request - consider closing circuit
                    _testRequestsInHalfOpen++;
                    if (_testRequestsInHalfOpen >= _config.HalfOpenTestRequests)
                    {
                        TransitionToClosed();
                    }
                    break;
            }

            _logger.LogDebug("Circuit breaker recorded success. State: {State}, Success count: {SuccessCount}", 
                _state, _successCount);
        }
    }

    public void RecordFailure()
    {
        lock (_stateLock)
        {
            _failureCount++;
            _lastFailureTime = DateTime.UtcNow;
            _failureTimestamps.Enqueue(DateTime.UtcNow);

            switch (_state)
            {
                case CircuitBreakerState.Closed:
                    ClearOldFailures();
                    if (_failureTimestamps.Count >= _config.FailureThreshold)
                    {
                        TransitionToOpen();
                    }
                    break;
                
                case CircuitBreakerState.HalfOpen:
                    // Failure during test - go back to open
                    TransitionToOpen();
                    break;
            }

            _logger.LogDebug("Circuit breaker recorded failure. State: {State}, Failure count: {FailureCount}", 
                _state, _failureCount);
        }
    }

    public CircuitBreakerStatistics GetStatistics()
    {
        lock (_stateLock)
        {
            TimeSpan? timeUntilRetry = null;
            if (_state == CircuitBreakerState.Open && _circuitOpenedAt.HasValue)
            {
                var elapsed = DateTime.UtcNow - _circuitOpenedAt.Value;
                if (elapsed < _config.OpenDuration)
                {
                    timeUntilRetry = _config.OpenDuration - elapsed;
                }
            }

            return new CircuitBreakerStatistics
            {
                State = _state,
                FailureCount = _failureCount,
                SuccessCount = _successCount,
                LastFailureTime = _lastFailureTime,
                LastSuccessTime = _lastSuccessTime,
                CircuitOpenedAt = _circuitOpenedAt,
                TimeUntilRetry = timeUntilRetry,
                TestRequestsInHalfOpen = _testRequestsInHalfOpen
            };
        }
    }

    private void TransitionToOpen()
    {
        _state = CircuitBreakerState.Open;
        _circuitOpenedAt = DateTime.UtcNow;
        _testRequestsInHalfOpen = 0;
        
        _logger.LogWarning("Circuit breaker opened due to {FailureCount} failures within {FailureWindow}. " +
                          "Will retry after {OpenDuration}",
            _config.FailureThreshold, _config.FailureWindow, _config.OpenDuration);
    }

    private void TransitionToHalfOpen()
    {
        _state = CircuitBreakerState.HalfOpen;
        _testRequestsInHalfOpen = 0;
        
        _logger.LogInformation("Circuit breaker transitioned to half-open. Testing with up to {TestRequests} requests",
            _config.HalfOpenTestRequests);
    }

    private void TransitionToClosed()
    {
        _state = CircuitBreakerState.Closed;
        _circuitOpenedAt = null;
        _testRequestsInHalfOpen = 0;
        
        // Clear failure history
        while (_failureTimestamps.TryDequeue(out _)) { }
        
        _logger.LogInformation("Circuit breaker closed. System is healthy again");
    }

    private void ClearOldFailures()
    {
        var cutoff = DateTime.UtcNow - _config.FailureWindow;
        
        while (_failureTimestamps.TryPeek(out var timestamp) && timestamp < cutoff)
        {
            _failureTimestamps.TryDequeue(out _);
        }
    }
}
