extern alias CSharpUtilities;

using System.Diagnostics;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Automation.Contracts;
using Automation.Web.Executors;
using Automation.Mobile.Executors;
using Automation.Worker.Configuration;
using CSharpRetry = CSharpUtilities::Automation.Utilities.Common.RetryPolicy;

namespace Automation.Worker.Services;

/// <summary>
/// Service for processing automation tasks
/// </summary>
public interface ITaskProcessingService
{
    /// <summary>
    /// Processes a task message
    /// </summary>
    /// <param name="message">Task message JSON</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Processing result</returns>
    Task<TaskProcessingResult> ProcessTaskAsync(string message, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets processing statistics
    /// </summary>
    /// <returns>Processing statistics</returns>
    TaskProcessingStatistics GetStatistics();
}

/// <summary>
/// Task processing result
/// </summary>
public record TaskProcessingResult
{
    public bool IsSuccess { get; init; }
    public string? ErrorMessage { get; init; }
    public TimeSpan ProcessingTime { get; init; }
    public string? TaskId { get; init; }
    public string? TaskType { get; init; }
    public int ActionsExecuted { get; init; }
    public Exception? Exception { get; init; }
}

/// <summary>
/// Task processing statistics
/// </summary>
public record TaskProcessingStatistics
{
    public long TotalTasksProcessed { get; init; }
    public long SuccessfulTasks { get; init; }
    public long FailedTasks { get; init; }
    public TimeSpan AverageProcessingTime { get; init; }
    public DateTime LastTaskProcessed { get; init; }
    public long WebTasksProcessed { get; init; }
    public long MobileTasksProcessed { get; init; }
    public IDictionary<string, long> ErrorCounts { get; init; } = new Dictionary<string, long>();
}

/// <summary>
/// Implementation of task processing service
/// </summary>
public class TaskProcessingService : ITaskProcessingService
{
    private readonly WebTaskExecutor _webExecutor;
    private readonly MobileTaskExecutor _mobileExecutor;
    private readonly ICircuitBreakerService _circuitBreaker;
    private readonly CSharpRetry _retryPolicy;
    private readonly ILogger<TaskProcessingService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;
    
    private long _totalTasksProcessed = 0;
    private long _successfulTasks = 0;
    private long _failedTasks = 0;
    private long _webTasksProcessed = 0;
    private long _mobileTasksProcessed = 0;
    private DateTime _lastTaskProcessed = DateTime.MinValue;
    private readonly List<TimeSpan> _processingTimes = new();
    private readonly Dictionary<string, long> _errorCounts = new();
    private readonly object _statsLock = new();

    public TaskProcessingService(
        WebTaskExecutor webExecutor,
        MobileTaskExecutor mobileExecutor,
        ICircuitBreakerService circuitBreaker,
        CSharpRetry retryPolicy,
        ILogger<TaskProcessingService> logger)
    {
        _webExecutor = webExecutor ?? throw new ArgumentNullException(nameof(webExecutor));
        _mobileExecutor = mobileExecutor ?? throw new ArgumentNullException(nameof(mobileExecutor));
        _circuitBreaker = circuitBreaker ?? throw new ArgumentNullException(nameof(circuitBreaker));
        _retryPolicy = retryPolicy ?? throw new ArgumentNullException(nameof(retryPolicy));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };
    }

    public async Task<TaskProcessingResult> ProcessTaskAsync(string message, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        Interlocked.Increment(ref _totalTasksProcessed);

        try
        {
            _logger.LogDebug("Processing task message: {MessageLength} characters", message.Length);

            // Check circuit breaker
            if (!_circuitBreaker.CanExecute)
            {
                var error = "Circuit breaker is open - rejecting task";
                _logger.LogWarning(error);
                RecordFailure(stopwatch.Elapsed, "CircuitBreakerOpen", null);
                return new TaskProcessingResult
                {
                    IsSuccess = false,
                    ErrorMessage = error,
                    ProcessingTime = stopwatch.Elapsed
                };
            }

            // Parse task message
            var taskMessage = JsonSerializer.Deserialize<TaskMessage>(message, _jsonOptions);
            if (taskMessage == null)
            {
                var error = "Failed to deserialize task message";
                _logger.LogError(error);
                RecordFailure(stopwatch.Elapsed, "DeserializationError", null);
                return new TaskProcessingResult
                {
                    IsSuccess = false,
                    ErrorMessage = error,
                    ProcessingTime = stopwatch.Elapsed
                };
            }

            _logger.LogInformation("Processing task {TaskId} of type {TaskType} with {ActionCount} actions",
                taskMessage.TaskId, taskMessage.TaskType, taskMessage.Actions?.Count ?? 0);

            // Process with retry policy
            var result = await _retryPolicy.ExecuteAsync(async () =>
            {
                return await ProcessTaskWithExecutor(taskMessage, cancellationToken);
            });

            stopwatch.Stop();

            if (result.IsSuccess)
            {
                RecordSuccess(stopwatch.Elapsed, taskMessage.TaskType);
                _circuitBreaker.RecordSuccess();
            }
            else
            {
                var errorType = result.Error?.FailedAction?.GetType().Name ?? "UnknownError";
                var exception = result.Error != null ? new Exception(result.Error.Message) : null;
                RecordFailure(stopwatch.Elapsed, errorType, exception);
                _circuitBreaker.RecordFailure();
            }

            return new TaskProcessingResult
            {
                IsSuccess = result.IsSuccess,
                ErrorMessage = result.Error?.Message,
                ProcessingTime = stopwatch.Elapsed,
                TaskId = taskMessage.TaskId,
                TaskType = taskMessage.TaskType,
                ActionsExecuted = taskMessage.Actions?.Count ?? 0,
                Exception = result.Error != null ? new Exception(result.Error.Message) : null
            };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Unexpected error processing task");
            
            RecordFailure(stopwatch.Elapsed, ex.GetType().Name, ex);
            _circuitBreaker.RecordFailure();

            return new TaskProcessingResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ProcessingTime = stopwatch.Elapsed,
                Exception = ex
            };
        }
    }

    public TaskProcessingStatistics GetStatistics()
    {
        lock (_statsLock)
        {
            var avgProcessingTime = _processingTimes.Count > 0
                ? TimeSpan.FromTicks((long)_processingTimes.Average(t => t.Ticks))
                : TimeSpan.Zero;

            return new TaskProcessingStatistics
            {
                TotalTasksProcessed = _totalTasksProcessed,
                SuccessfulTasks = _successfulTasks,
                FailedTasks = _failedTasks,
                AverageProcessingTime = avgProcessingTime,
                LastTaskProcessed = _lastTaskProcessed,
                WebTasksProcessed = _webTasksProcessed,
                MobileTasksProcessed = _mobileTasksProcessed,
                ErrorCounts = new Dictionary<string, long>(_errorCounts)
            };
        }
    }

    private async Task<TaskResult> ProcessTaskWithExecutor(TaskMessage taskMessage, CancellationToken cancellationToken)
    {
        if (taskMessage.Actions == null || !taskMessage.Actions.Any())
        {
            return TaskResult.Success("No actions to execute", TimeSpan.Zero);
        }

        return taskMessage.TaskType?.ToLowerInvariant() switch
        {
            "web" => await _webExecutor.ExecuteAsync(taskMessage.Actions, cancellationToken),
            "mobile" => await _mobileExecutor.ExecuteAsync(taskMessage.Actions, cancellationToken),
            _ => TaskResult.Failure($"Unsupported task type: {taskMessage.TaskType}")
        };
    }

    private void RecordSuccess(TimeSpan processingTime, string? taskType)
    {
        lock (_statsLock)
        {
            Interlocked.Increment(ref _successfulTasks);
            _lastTaskProcessed = DateTime.UtcNow;
            _processingTimes.Add(processingTime);

            if (_processingTimes.Count > 1000) // Keep only last 1000 measurements
            {
                _processingTimes.RemoveAt(0);
            }

            if (taskType?.ToLowerInvariant() == "web")
            {
                Interlocked.Increment(ref _webTasksProcessed);
            }
            else if (taskType?.ToLowerInvariant() == "mobile")
            {
                Interlocked.Increment(ref _mobileTasksProcessed);
            }
        }
    }

    private void RecordFailure(TimeSpan processingTime, string errorType, Exception? exception)
    {
        lock (_statsLock)
        {
            Interlocked.Increment(ref _failedTasks);
            _lastTaskProcessed = DateTime.UtcNow;
            _processingTimes.Add(processingTime);

            if (_processingTimes.Count > 1000)
            {
                _processingTimes.RemoveAt(0);
            }

            // Track error types
            if (!string.IsNullOrEmpty(errorType))
            {
                _errorCounts[errorType] = _errorCounts.GetValueOrDefault(errorType, 0) + 1;
            }
        }
    }
}

/// <summary>
/// Task message structure
/// </summary>
public record TaskMessage
{
    public string TaskId { get; init; } = string.Empty;
    public string TaskType { get; init; } = string.Empty;
    public List<object>? Actions { get; init; }
    public DateTime CreatedAt { get; init; } = DateTime.UtcNow;
    public int Priority { get; init; } = 0;
    public TimeSpan? Timeout { get; init; }
    public Dictionary<string, object>? Metadata { get; init; }
}
