using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Automation.Worker.Models;

namespace Automation.Worker.Services
{
    /// <summary>
    /// Interface for managing test results
    /// </summary>
    public interface ITestResultService
    {
        Task<string> CreateTestAsync(string testName, string url);
        Task UpdateTestAsync(string testId, TestStatus status, string? errorMessage = null, string? stackTrace = null);
        Task AddTestStepAsync(string testId, string action, string description, TestStepStatus status = TestStepStatus.Running);
        Task UpdateTestStepAsync(string testId, string stepId, TestStepStatus status, string? errorMessage = null, string? screenshotPath = null);
        Task AddScreenshotAsync(string testId, string screenshotPath);
        Task<TestResult?> GetTestResultAsync(string testId);
        Task<List<TestResult>> GetAllTestResultsAsync();
        Task<TestResultSummary> GetTestSummaryAsync();
        Task<byte[]?> GetScreenshotAsync(string screenshotPath);
        Task<List<TestResult>> GetRecentTestsAsync(int count = 10);
    }

    /// <summary>
    /// Service for managing test results with file-based storage
    /// </summary>
    public class TestResultService : ITestResultService
    {
        private readonly ILogger<TestResultService> _logger;
        private readonly ConcurrentDictionary<string, TestResult> _testResults;
        private readonly string _resultsDirectory;
        private readonly string _screenshotsDirectory;

        public TestResultService(ILogger<TestResultService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _testResults = new ConcurrentDictionary<string, TestResult>();
            
            // Create directories for storing results and screenshots
            _resultsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "test-results");
            _screenshotsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "screenshots");
            
            Directory.CreateDirectory(_resultsDirectory);
            Directory.CreateDirectory(_screenshotsDirectory);
            
            // Load existing results on startup
            _ = Task.Run(LoadExistingResultsAsync);
        }

        public async Task<string> CreateTestAsync(string testName, string url)
        {
            var testResult = new TestResult
            {
                TestName = testName,
                Url = url,
                StartTime = DateTime.UtcNow,
                Status = TestStatus.Running
            };

            _testResults.TryAdd(testResult.Id, testResult);
            await SaveTestResultAsync(testResult);
            
            _logger.LogInformation("Created test {TestId} for {TestName} on {Url}", 
                testResult.Id, testName, url);
            
            return testResult.Id;
        }

        public async Task UpdateTestAsync(string testId, TestStatus status, string? errorMessage = null, string? stackTrace = null)
        {
            if (_testResults.TryGetValue(testId, out var testResult))
            {
                testResult.Status = status;
                testResult.EndTime = DateTime.UtcNow;
                
                if (!string.IsNullOrEmpty(errorMessage))
                    testResult.ErrorMessage = errorMessage;
                
                if (!string.IsNullOrEmpty(stackTrace))
                    testResult.StackTrace = stackTrace;

                await SaveTestResultAsync(testResult);
                
                _logger.LogInformation("Updated test {TestId} with status {Status}", testId, status);
            }
        }

        public async Task AddTestStepAsync(string testId, string action, string description, TestStepStatus status = TestStepStatus.Running)
        {
            if (_testResults.TryGetValue(testId, out var testResult))
            {
                var step = new TestStep
                {
                    Action = action,
                    Description = description,
                    Timestamp = DateTime.UtcNow,
                    Status = status
                };

                testResult.Steps.Add(step);
                await SaveTestResultAsync(testResult);
                
                _logger.LogDebug("Added step {StepId} to test {TestId}: {Action}", 
                    step.Id, testId, action);
            }
        }

        public async Task UpdateTestStepAsync(string testId, string stepId, TestStepStatus status, string? errorMessage = null, string? screenshotPath = null)
        {
            if (_testResults.TryGetValue(testId, out var testResult))
            {
                var step = testResult.Steps.FirstOrDefault(s => s.Id == stepId);
                if (step != null)
                {
                    step.Status = status;
                    
                    if (!string.IsNullOrEmpty(errorMessage))
                        step.ErrorMessage = errorMessage;
                    
                    if (!string.IsNullOrEmpty(screenshotPath))
                        step.ScreenshotPath = screenshotPath;

                    await SaveTestResultAsync(testResult);
                    
                    _logger.LogDebug("Updated step {StepId} in test {TestId} with status {Status}", 
                        stepId, testId, status);
                }
            }
        }

        public async Task AddScreenshotAsync(string testId, string screenshotPath)
        {
            if (_testResults.TryGetValue(testId, out var testResult))
            {
                testResult.Screenshots.Add(screenshotPath);
                await SaveTestResultAsync(testResult);
                
                _logger.LogDebug("Added screenshot {ScreenshotPath} to test {TestId}", 
                    screenshotPath, testId);
            }
        }

        public Task<TestResult?> GetTestResultAsync(string testId)
        {
            _testResults.TryGetValue(testId, out var testResult);
            return Task.FromResult(testResult);
        }

        public Task<List<TestResult>> GetAllTestResultsAsync()
        {
            return Task.FromResult(_testResults.Values.OrderByDescending(t => t.StartTime).ToList());
        }

        public Task<List<TestResult>> GetRecentTestsAsync(int count = 10)
        {
            return Task.FromResult(_testResults.Values
                .OrderByDescending(t => t.StartTime)
                .Take(count)
                .ToList());
        }

        public Task<TestResultSummary> GetTestSummaryAsync()
        {
            var tests = _testResults.Values.ToList();
            
            var summary = new TestResultSummary
            {
                TotalTests = tests.Count,
                PassedTests = tests.Count(t => t.Status == TestStatus.Passed),
                FailedTests = tests.Count(t => t.Status == TestStatus.Failed),
                SkippedTests = tests.Count(t => t.Status == TestStatus.Skipped),
                RunningTests = tests.Count(t => t.Status == TestStatus.Running),
                LastRunTime = tests.Any() ? tests.Max(t => t.StartTime) : DateTime.MinValue,
                TotalDuration = TimeSpan.FromTicks(tests.Where(t => t.Status != TestStatus.Running).Sum(t => t.Duration.Ticks))
            };

            return Task.FromResult(summary);
        }

        public async Task<byte[]?> GetScreenshotAsync(string screenshotPath)
        {
            try
            {
                var fullPath = Path.Combine(_screenshotsDirectory, screenshotPath);
                if (File.Exists(fullPath))
                {
                    return await File.ReadAllBytesAsync(fullPath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading screenshot {ScreenshotPath}", screenshotPath);
            }
            
            return null;
        }

        private async Task SaveTestResultAsync(TestResult testResult)
        {
            try
            {
                var filePath = Path.Combine(_resultsDirectory, $"{testResult.Id}.json");
                var json = JsonSerializer.Serialize(testResult, new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
                
                await File.WriteAllTextAsync(filePath, json);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving test result {TestId}", testResult.Id);
            }
        }

        private async Task LoadExistingResultsAsync()
        {
            try
            {
                if (!Directory.Exists(_resultsDirectory))
                    return;

                var files = Directory.GetFiles(_resultsDirectory, "*.json");
                
                foreach (var file in files)
                {
                    try
                    {
                        var json = await File.ReadAllTextAsync(file);
                        var testResult = JsonSerializer.Deserialize<TestResult>(json, new JsonSerializerOptions
                        {
                            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                        });
                        
                        if (testResult != null)
                        {
                            _testResults.TryAdd(testResult.Id, testResult);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error loading test result from {File}", file);
                    }
                }
                
                _logger.LogInformation("Loaded {Count} existing test results", _testResults.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading existing test results");
            }
        }
    }
}
