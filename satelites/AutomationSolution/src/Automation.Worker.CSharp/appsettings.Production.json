{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "System": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}}, "Worker": {"Concurrency": {"MaxConcurrentTasks": 5}, "CircuitBreaker": {"FailureThreshold": 10, "FailureWindow": "00:02:00", "OpenDuration": "00:02:00"}, "Retry": {"MaxAttempts": 5, "MaxDelay": "00:01:00"}}, "WebAutomation": {"ResourceManagement": {"MaxConcurrentBrowsers": 5, "BrowserIdleTimeoutMinutes": 5}}, "AIInfrastructure": {"Caching": {"Provider": "Redis", "RedisConnectionString": "${REDIS_CONNECTION_STRING}", "DefaultTtl": "01:00:00"}, "HttpClient": {"MaxRetryAttempts": 5}}}