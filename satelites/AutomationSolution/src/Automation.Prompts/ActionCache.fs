namespace Automation.Prompts

open System
open System.Collections.Concurrent
open System.Diagnostics
open System.Text
open System.Text.Json
open System.Threading
open System.Threading.Tasks
// Types CacheOperation and ICacheMetricsCollector are defined in CacheMetrics.fs

// Types are defined in CacheMetrics.fs to avoid duplication

/// Cache configuration for action results
type CacheConfig = {
    /// Time after which the cached value expires
    TimeToLive: TimeSpan option
    /// Maximum number of items to keep in the cache
    MaxItems: int option
    /// Whether to compress large values
    EnableCompression: bool
}

/// Default cache configuration
[<RequireQualifiedAccess>]
module CacheConfig =
    /// Default cache configuration
    let Default = {
        TimeToLive = Some (TimeSpan.FromMinutes(30.0))
        MaxItems = Some 1000
        EnableCompression = true
    }

    /// Cache configuration that disables caching
    let NoCaching = {
        TimeToLive = None
        MaxItems = None
        EnableCompression = false
    }

/// Thread-safe in-memory cache for action results
module private CacheImpl =
    let private cache = ConcurrentDictionary<string, string>()
    let private timestamps = ConcurrentDictionary<string, DateTime>()
    let private locks = ConcurrentDictionary<string, SemaphoreSlim>()
    let private config = ref CacheConfig.Default
    
    let private cleanupLock = obj()
    
    /// Gets a semaphore for the specified key
    let getLock (key: string) =
        locks.GetOrAdd(key, fun _ -> new SemaphoreSlim(1, 1))
    
    /// Performs cache maintenance (eviction, cleanup)
    let private performMaintenance() =
        lock cleanupLock (fun () ->
            match (!config).MaxItems with
            | Some max when cache.Count > max ->
                // Remove oldest items if we're over capacity
                let toRemove = 
                    timestamps
                    |> Seq.sortBy (fun kvp -> kvp.Value)
                    |> Seq.take (cache.Count - max)
                    |> Seq.map (fun kvp -> kvp.Key)
                    |> List.ofSeq
                
                for key in toRemove do
                    cache.TryRemove(key) |> ignore
                    timestamps.TryRemove(key) |> ignore
                    locks.TryRemove(key) |> ignore
            | _ -> ()
            
            // Remove expired items
            let now = DateTime.UtcNow
            let entries = timestamps.ToArray()
            
            for KeyValue(key, timestamp) in entries do
                match (!config).TimeToLive with
                | Some ttl when timestamp > DateTime.MinValue && now - timestamp > ttl ->
                    // Try to remove the item from all dictionaries
                    cache.TryRemove(key) |> ignore
                    timestamps.TryRemove(key) |> ignore
                    locks.TryRemove(key) |> ignore
                | _ -> ()
        )
    
    /// Gets a value from cache if it exists and hasn't expired
    let tryGet<'T> (key: string) : 'T option =
        match cache.TryGetValue(key), timestamps.TryGetValue(key) with
        | (true, json), (true, timestamp) ->
            match (!config).TimeToLive with
            | Some ttl when DateTime.UtcNow - timestamp > ttl ->
                // Entry has expired
                cache.TryRemove(key) |> ignore
                timestamps.TryRemove(key) |> ignore
                None
            | _ ->
                // Entry is valid, deserialize it
                try
                    let result = JsonSerializer.Deserialize<'T>(json)
                    // Update last accessed time
                    timestamps.[key] <- DateTime.UtcNow
                    Some result
                with _ ->
                    // If deserialization fails, remove the invalid entry
                    cache.TryRemove(key) |> ignore
                    timestamps.TryRemove(key) |> ignore
                    None
        | _ -> None
    
    /// Adds or updates a value in the cache
    let set<'T> (key: string) (value: 'T) : unit =
        let json = JsonSerializer.Serialize(value)
        cache.[key] <- json
        timestamps.[key] <- DateTime.UtcNow
        performMaintenance()
    
    /// Removes a value from the cache
    let remove (key: string) : unit =
        cache.TryRemove(key) |> ignore
        timestamps.TryRemove(key) |> ignore
        locks.TryRemove(key) |> ignore
    
    /// Clears all values from the cache
    let clear () : unit =
        cache.Clear()
        timestamps.Clear()
        locks.Clear()
    
    /// Updates the cache configuration
    let updateConfig (newConfig: CacheConfig) : unit =
        config := newConfig
        performMaintenance()

/// Public interface for the Action Cache module
[<RequireQualifiedAccess>]
module ActionCache =
    open System.Text
    
    // Default metrics collector (no-op implementation)
    let private noopMetricsCollector =
        { new ICacheMetricsCollector with
            member _.RecordOperation(operation, key, duration, ?itemSize, ?error) = ()
            member _.GetMetrics() =
                {
                    TotalHits = 0L
                    TotalMisses = 0L
                    TotalSets = 0L
                    TotalRemoves = 0L
                    TotalClears = 0L
                    TotalErrors = 0L
                    TotalOperations = 0L
                    AverageHitLatencyMs = 0.0
                    AverageMissLatencyMs = 0.0
                    CurrentSize = 0L
                    MaxSize = 0L
                    LastUpdated = DateTime.UtcNow
                }
            member _.Reset() = () }
            
    // Local no-op metrics collector for when CacheMetrics is not available
    module private LocalMetrics =
        let noopCollector = noopMetricsCollector
    
    let private metricsCollector = ref noopMetricsCollector
    
    // Helper function to get item size with null handling
    let private getItemSize (value: 'T) : int64 =
        try
            match box value with
            | null -> 0L
            | _ ->
                let json = JsonSerializer.Serialize(value)
                int64 (Encoding.UTF8.GetByteCount(json))
        with _ -> 0L
    
    /// Configure the cache with the given settings and optional metrics collector
    let configure (config: CacheConfig) (metrics: ICacheMetricsCollector option) : unit =
        CacheImpl.updateConfig config
        metricsCollector := 
            match metrics with
            | Some m -> m
            | None -> LocalMetrics.noopCollector
    
    /// Gets a value from cache, or computes and caches it if not found
    let getOrAddAsync<'T> 
        (key: string) 
        (valueFactory: string -> Task<'T>) 
        (config: CacheConfig option) 
        : Task<'T> = 
        task {
            let stopwatch = Stopwatch.StartNew()
            
            try
                // Apply configuration if provided
                config |> Option.iter CacheImpl.updateConfig
                
                // Check cache first
                match CacheImpl.tryGet<'T> key with
                | Some value -> 
                    stopwatch.Stop()
                    let itemSize = getItemSize value
                    (!metricsCollector).RecordOperation(
                        CacheOperation.Hit, 
                        key, 
                        stopwatch.Elapsed,
                        itemSize = itemSize
                    )
                    return value
                | None ->
                    // Get the semaphore for this key to prevent concurrent loads
                    let semaphore = CacheImpl.getLock key
                    let mutable result = Unchecked.defaultof<'T>
                    let operationSucceeded = false
                    
                    try
                        do! semaphore.WaitAsync()
                        
                        // Double-check cache in case another thread loaded the value
                        match CacheImpl.tryGet<'T> key with
                        | Some value -> 
                            stopwatch.Stop()
                            let itemSize = getItemSize value
                            (!metricsCollector).RecordOperation(
                                CacheOperation.Hit, 
                                key, 
                                stopwatch.Elapsed,
                                itemSize = itemSize
                            )
                            return value
                        | None ->
                            try
                                // Compute the value
                                let! computedValue = valueFactory key
                                result <- computedValue
                                
                                // Cache the result
                                let resultSize = getItemSize computedValue
                                CacheImpl.set key computedValue
                                
                                stopwatch.Stop()
                                (!metricsCollector).RecordOperation(
                                    CacheOperation.Set, 
                                    key, 
                                    stopwatch.Elapsed,
                                    itemSize = resultSize
                                ) |> ignore
                                
                                return result
                            with ex ->
                                stopwatch.Stop()
                                (!metricsCollector).RecordOperation(
                                    CacheOperation.Error(ex.Message), 
                                    key, 
                                    stopwatch.Elapsed,
                                    error = ex
                                ) |> ignore
                                return raise ex
                    finally
                        semaphore.Release() |> ignore
            with ex ->
                stopwatch.Stop()
                (!metricsCollector).RecordOperation(
                    CacheOperation.Error(ex.Message), 
                    key, 
                    stopwatch.Elapsed,
                    error = ex
                ) |> ignore
                return raise ex
        }
    
    /// Explicitly adds or updates a value in the cache
    let set<'T> (key: string) (value: 'T) (config: CacheConfig option) : unit =
        let stopwatch = Stopwatch.StartNew()
        try
            config |> Option.iter CacheImpl.updateConfig
            let resultSize = JsonSerializer.Serialize(value) |> Encoding.UTF8.GetByteCount |> int64
            CacheImpl.set key value
            
            stopwatch.Stop()
            (!metricsCollector).RecordOperation(
                CacheOperation.Set, 
                key, 
                stopwatch.Elapsed,
                itemSize = resultSize
            )
        with ex ->
            stopwatch.Stop()
            (!metricsCollector).RecordOperation(
                CacheOperation.Error(ex.Message), 
                key, 
                stopwatch.Elapsed,
                error = ex
            )
            reraise()
    
    /// Removes a value from the cache
    let remove (key: string) : unit =
        let stopwatch = Stopwatch.StartNew()
        try
            // Get size before removal for metrics
            let itemSize = 
                match CacheImpl.tryGet<obj> key with
                | Some value -> 
                    Some (JsonSerializer.Serialize(value) |> Encoding.UTF8.GetByteCount |> int64)
                | None -> None
                
            CacheImpl.remove key
            
            stopwatch.Stop()
            (!metricsCollector).RecordOperation(
                CacheOperation.Remove, 
                key, 
                stopwatch.Elapsed,
                itemSize = (itemSize |> Option.defaultValue 0L)
            )
        with ex ->
            stopwatch.Stop()
            (!metricsCollector).RecordOperation(
                CacheOperation.Error(ex.Message), 
                key, 
                stopwatch.Elapsed,
                error = ex
            )
            reraise()
    
    /// Clears the entire cache
    let clear () : unit =
        let stopwatch = Stopwatch.StartNew()
        try
            CacheImpl.clear()
            stopwatch.Stop()
            (!metricsCollector).RecordOperation(
                CacheOperation.Clear, 
                "*", 
                stopwatch.Elapsed
            )
        with ex ->
            stopwatch.Stop()
            (!metricsCollector).RecordOperation(
                CacheOperation.Error(ex.Message), 
                "*", 
                stopwatch.Elapsed,
                error = ex
            )
            reraise()
    
    /// Gets the number of items in the cache
    let count () : int =
        try
            let cacheImplType = 
                System.AppDomain.CurrentDomain.GetAssemblies()
                |> Seq.collect (fun a -> 
                    try a.GetTypes() with _ -> [||])
                |> Seq.tryFind (fun t -> t.Name = "CacheImpl")
                
            match cacheImplType with
            | None -> 0
            | Some t ->
                let cacheField = t.GetField("cache", System.Reflection.BindingFlags.NonPublic ||| System.Reflection.BindingFlags.Static)
                match cacheField with
                | null -> 0
                | field ->
                    let cacheValue = field.GetValue(null)
                    match cacheValue with
                    | :? System.Collections.Concurrent.ConcurrentDictionary<string, string> as dict -> dict.Count
                    | _ -> 0
        with _ -> 0
    
    /// Gets the current cache configuration
    let getConfig () : CacheConfig =
        try
            let cacheImplType = 
                System.AppDomain.CurrentDomain.GetAssemblies()
                |> Seq.tryPick (fun a -> 
                    try 
                        a.GetTypes() 
                        |> Array.tryFind (fun t -> t.Name = "CacheImpl")
                    with _ -> None)
                
            match cacheImplType with
            | None -> CacheConfig.Default
            | Some t ->
                let configField = t.GetField("config", System.Reflection.BindingFlags.NonPublic ||| System.Reflection.BindingFlags.Static)
                if isNull configField then
                    CacheConfig.Default
                else
                    match configField.GetValue(null) with
                    | null -> CacheConfig.Default
                    | :? ref<CacheConfig> as configRef -> !configRef
                    | _ -> CacheConfig.Default
        with _ -> 
            CacheConfig.Default
        
    /// Gets the current cache metrics
    let getMetrics () : CacheMetrics =
        (!metricsCollector).GetMetrics()
        
    /// Resets all metrics counters
    let resetMetrics () : unit =
        (!metricsCollector).Reset()
