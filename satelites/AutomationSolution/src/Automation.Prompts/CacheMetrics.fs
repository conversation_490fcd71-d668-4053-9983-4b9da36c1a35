namespace Automation.Prompts

open System
open System.Collections.Concurrent
open System.Diagnostics
open System.Threading

/// Types of cache operations
[<RequireQualifiedAccess>]
type CacheOperation =
    | Hit
    | Miss
    | Set
    | Remove
    | Clear
    | Error of string

/// Detailed metrics for a single cache operation
type CacheOperationMetrics = {
    Operation: CacheOperation
    Key: string
    Timestamp: DateTime
    Duration: TimeSpan
    ItemSize: int64 option
    Error: string option
}

/// Aggregated cache metrics
type CacheMetrics = {
    TotalHits: int64
    TotalMisses: int64
    TotalSets: int64
    TotalRemoves: int64
    TotalClears: int64
    TotalErrors: int64
    TotalOperations: int64
    AverageHitLatencyMs: float
    AverageMissLatencyMs: float
    CurrentSize: int64
    MaxSize: int64
    LastUpdated: DateTime
}

/// Interface for cache metrics collection
[<Interface>]
type ICacheMetricsCollector =
    /// Record a cache operation with metrics
    abstract RecordOperation : operation:CacheOperation * key:string * duration:TimeSpan * ?itemSize:int64 * ?error:exn -> unit
    
    /// Get the current metrics snapshot
    abstract GetMetrics : unit -> CacheMetrics
    
    /// Reset all metrics
    abstract Reset : unit -> unit

/// Default implementation of ICacheMetricsCollector
module private CacheMetricsImpl =
    let private metrics = new ConcurrentDictionary<string, int64>()
    let private hitLatencies = ConcurrentBag<float>()
    let private missLatencies = ConcurrentBag<float>()
    let private lockObj = obj()
    
    let mutable private currentSize = 0L
    let mutable private maxSize = 0L
    
    let updateSize (delta: int64) =
        lock lockObj (fun () ->
            currentSize <- currentSize + delta
            if currentSize > maxSize then
                maxSize <- currentSize
        )
    
    let recordOperation (operation: CacheOperation) (key: string) (duration: TimeSpan) (itemSize: int64 option) (error: exn option) : unit =
        let opName = operation.ToString()
        
        // Update operation counts
        metrics.AddOrUpdate(opName, 1L, fun _ v -> v + 1L) |> ignore
        metrics.AddOrUpdate("total_operations", 1L, fun _ v -> v + 1L) |> ignore
        
        // Update size metrics if applicable
        itemSize |> Option.iter (fun size ->
            match operation with
            | CacheOperation.Set -> updateSize size
            | CacheOperation.Remove -> updateSize -size
            | CacheOperation.Clear -> 
                currentSize <- 0L
                metrics.AddOrUpdate("total_clears", 1L, fun _ v -> v + 1L) |> ignore
            | _ -> ()
        )
        
        // Record latencies
        match operation with
        | CacheOperation.Hit -> hitLatencies.Add(duration.TotalMilliseconds)
        | CacheOperation.Miss -> missLatencies.Add(duration.TotalMilliseconds)
        | _ -> ()
        
        // Record errors
        match operation with
        | CacheOperation.Error _ -> 
            metrics.AddOrUpdate("total_errors", 1L, fun _ v -> v + 1L) |> ignore
            match error with
            | Some ex -> 
                metrics.AddOrUpdate($"error_{ex.GetType().Name}", 1L, fun _ v -> v + 1L) |> ignore
            | None -> ()
        | _ -> ()

    let getMetrics () =
        let hits = metrics.GetOrAdd("Hit", fun _ -> 0L)
        let misses = metrics.GetOrAdd("Miss", fun _ -> 0L)
        let totalOps = metrics.GetOrAdd("total_operations", fun _ -> 0L)
        
        let avgHitLatency =
            if hitLatencies.Count > 0 then
                let values = hitLatencies.ToArray()
                values |> Array.average
            else 0.0
            
        let avgMissLatency =
            if missLatencies.Count > 0 then
                let values = missLatencies.ToArray()
                values |> Array.average
            else 0.0
        
        {
            TotalHits = hits
            TotalMisses = misses
            TotalSets = metrics.GetOrAdd("Set", fun _ -> 0L)
            TotalRemoves = metrics.GetOrAdd("Remove", fun _ -> 0L)
            TotalClears = metrics.GetOrAdd("Clear", fun _ -> 0L)
            TotalErrors = metrics.GetOrAdd("total_errors", fun _ -> 0L)
            TotalOperations = totalOps
            AverageHitLatencyMs = avgHitLatency
            AverageMissLatencyMs = avgMissLatency
            CurrentSize = currentSize
            MaxSize = maxSize
            LastUpdated = DateTime.UtcNow
        }
    
    let reset () =
        metrics.Clear()
        lock lockObj (fun () ->
            currentSize <- 0L
            maxSize <- 0L
        )
        while not hitLatencies.IsEmpty do
            match hitLatencies.TryTake() with _ -> ()
        while not missLatencies.IsEmpty do
            match missLatencies.TryTake() with _ -> ()

/// Public interface for cache metrics
[<RequireQualifiedAccess>]
module CacheMetrics =
    /// Create a new metrics collector
    let createCollector () : ICacheMetricsCollector =
        { new ICacheMetricsCollector with
            member _.RecordOperation(op, key, duration, ?itemSize, ?error) =
                CacheMetricsImpl.recordOperation op key duration itemSize error
                
            member _.GetMetrics() =
                CacheMetricsImpl.getMetrics()
                
            member _.Reset() =
                CacheMetricsImpl.reset()
        }
    
    /// Get a no-op metrics collector
    let noopCollector : ICacheMetricsCollector =
        { new ICacheMetricsCollector with
            member _.RecordOperation(operation, key, duration, ?itemSize, ?error) = ()
            member _.GetMetrics() = 
                {
                    TotalHits = 0L
                    TotalMisses = 0L
                    TotalSets = 0L
                    TotalRemoves = 0L
                    TotalClears = 0L
                    TotalErrors = 0L
                    TotalOperations = 0L
                    AverageHitLatencyMs = 0.0
                    AverageMissLatencyMs = 0.0
                    CurrentSize = 0L
                    MaxSize = 0L
                    LastUpdated = DateTime.UtcNow
                }
            member _.Reset() = ()
        }
    
    /// Format metrics as a readable string
    let format (metrics: CacheMetrics) : string =
        let hitRate = 
            if metrics.TotalHits + metrics.TotalMisses > 0L then
                float metrics.TotalHits / float (metrics.TotalHits + metrics.TotalMisses) * 100.0
            else 0.0
            
        sprintf """
        Cache Metrics (updated: %s)
        --------------------------
        Operations:
          Hits:     %d (%.1f%% hit rate)
          Misses:   %d
          Sets:     %d
          Removes:  %d
          Clears:   %d
          Errors:   %d
          
        Performance:
          Avg Hit Latency:  %.2f ms
          Avg Miss Latency: %.2f ms
          
        Memory Usage:
          Current Size: %d bytes
          Max Size:     %d bytes
        """ (metrics.LastUpdated.ToString("u")) metrics.TotalHits hitRate metrics.TotalMisses metrics.TotalSets metrics.TotalRemoves metrics.TotalClears metrics.TotalErrors metrics.AverageHitLatencyMs metrics.AverageMissLatencyMs metrics.CurrentSize metrics.MaxSize
        |> fun s -> s.Trim()