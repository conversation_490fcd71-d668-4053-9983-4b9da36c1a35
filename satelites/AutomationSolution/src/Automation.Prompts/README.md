# Automation.Prompts

This project is dedicated to the robust management of AI prompts and templates within the Automation Solution. It provides a centralized, dynamic, and version-controlled system for storing, retrieving, and caching prompts, ensuring that AI models receive consistent and up-to-date instructions without requiring application recompilation.

## Key Components

*   **`PromptStore`**: Manages the persistence of AI prompts. It uses Entity Framework Core with SQLite (configurable for other databases) to store `Prompt` entities, allowing for dynamic updates and retrieval.
    *   **`Prompt` Entity**: Represents a single AI prompt with an `Id`, `Text` content, and `UpdatedUtc` timestamp.
    *   **`PromptsDbContext`**: The EF Core `DbContext` for interacting with the prompt database.
*   **`PromptCache`**: An in-memory, thread-safe cache for `Prompt` texts. It supports Time-To-Live (TTL) for cached entries and uses semaphores to prevent concurrent loads for the same key, optimizing performance and reducing database hits.
*   **`CachedPromptStore`**: Acts as a facade over `PromptStore` and `PromptCache`, providing a unified interface for prompt retrieval that prioritizes cached values. It automatically handles caching and cache invalidation when prompts are updated.
*   **`ActionCache`**: A generic in-memory, thread-safe cache designed for action results. It supports configurable TTL, maximum item limits, and optional compression. It also integrates with `CacheMetrics` for performance monitoring.
*   **`CacheMetrics`**: Defines types and an interface (`ICacheMetricsCollector`) for collecting and reporting detailed statistics about cache operations (hits, misses, sets, removes, errors, latency). It provides a comprehensive view of cache performance.

## Features

*   **Centralized Prompt Management**: Stores all AI prompts in a single, accessible database, facilitating easy updates and version control.
*   **Dynamic Prompt Loading**: Prompts can be updated in the database and are immediately available to AI models without needing to redeploy the application.
*   **Performance Optimization with Caching**: Utilizes a multi-layered caching strategy (`PromptCache`, `ActionCache`) to significantly reduce latency and database load for frequently accessed prompts and action results.
*   **Cache Metrics & Monitoring**: Provides detailed metrics on cache performance, enabling administrators to monitor efficiency and identify potential bottlenecks.
*   **Extensible Storage**: While defaulting to SQLite, the `PromptStore` is built with Entity Framework Core, allowing for easy migration to other relational databases (e.g., PostgreSQL, SQL Server).
*   **Thread-Safe Operations**: All cache and store operations are thread-safe, ensuring data consistency in concurrent environments.
*   **Prompt Templating (Conceptual)**: Although not explicitly shown in the provided code, the `Prompt` entity's `Text` field can contain variables, implying support for a templating system where variables are replaced at runtime (as suggested by the original `README.md`).

## Usage

This project is a core dependency for any part of the Automation Solution that interacts with AI models, particularly `Automation.AI`. It ensures that AI prompts are managed efficiently and securely.

### Example: Retrieving and Updating Prompts

```fsharp
open System
open System.Threading.Tasks
open Automation.Prompts.CachedPromptStore
open Automation.Prompts.CacheMetrics

// Configure ActionCache with metrics collection (optional)
ActionCache.configure CacheMetrics.createCollector() (Some (CacheMetrics.createCollector()))

// Get a prompt (will be retrieved from cache if available, otherwise from DB)
let! visionPrompt = CachedPromptStore.get "vision.element_detection"

match visionPrompt with
| Some text ->
    printfn $"Retrieved prompt: {text.Substring(0, Math.Min(100, text.Length))}"
| None ->
    printfn "Prompt 'vision.element_detection' not found."

// Update a prompt (will update DB and invalidate cache)
let newPromptText = "You are an updated vision AI. Analyze the image and identify all interactive elements."
do! CachedPromptStore.upsert "vision.element_detection" newPromptText
printfn "Prompt 'vision.element_detection' updated."

// Retrieve the updated prompt (will now be fetched from DB and re-cached)
let! updatedVisionPrompt = CachedPromptStore.get "vision.element_detection"
updatedVisionPrompt |> Option.iter (fun text ->
    printfn $"Retrieved updated prompt: {text.Substring(0, Math.Min(100, text.Length))}"
)

// Get cache metrics
let metrics = ActionCache.getMetrics()
printfn $"\nCache Metrics:\n%s{CacheMetrics.format metrics}"
```

## Configuration

*   **`PromptStore`**: The SQLite database path is configured internally in `PromptStore.fs` (default: `~/Library/Application Support/automation_prompts/prompts.db` on macOS). This can be customized.
*   **`PromptCache` / `ActionCache`**: Cache behavior (TTL, max items, compression) is configured via `CacheConfig` objects passed during initialization or through `ActionCache.configure`.

## Dependencies

*   `Microsoft.EntityFrameworkCore` & `Microsoft.EntityFrameworkCore.Sqlite`: For database interaction.
*   `System.Text.Json`: For JSON serialization/deserialization.
*   `Automation.Core`: For core types if used within prompt definitions (e.g., `Action`).

## Development

This project is developed in F#. It emphasizes performance and maintainability for prompt management. The `PromptSeeder.fsx` script (in the `scripts` directory of the root solution) can be used to populate initial prompts into the database.
