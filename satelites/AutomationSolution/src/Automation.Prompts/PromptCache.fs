namespace Automation.Prompts

open System
open System.Collections.Concurrent
open System.Threading
open System.Threading.Tasks

/// Cache entry with timestamp for expiration
[<Struct>]
type private CacheEntry = {
    Value: string
    Timestamp: DateTime
    ExpiresAt: DateTime option
}

/// Thread-safe in-memory cache for prompts with TTL support
module private PromptCacheImpl =
    let private cache = ConcurrentDictionary<string, CacheEntry>()
    let private cacheLocks = ConcurrentDictionary<string, SemaphoreSlim>()
    
    /// Gets a semaphore for the specified key, creating it if it doesn't exist
    let getLock (key: string) =
        cacheLocks.GetOrAdd(key, fun _ -> new SemaphoreSlim(1, 1))

    /// Gets a value from cache if it exists and hasn't expired
    let tryGet (key: string) =
        match cache.TryGetValue(key) with
        | true, entry ->
            match entry.ExpiresAt with
            | Some expiresAt when DateTime.UtcNow > expiresAt ->
                // Entry has expired, remove it and return None
                cache.TryRemove(key) |> ignore
                None
            | _ ->
                // Entry is valid, return it
                Some entry.Value
        | _ -> None

    /// Adds or updates a value in the cache
    let set (key: string) (value: string) (ttl: TimeSpan option) : unit =
        let entry = {
            Value = value
            Timestamp = DateTime.UtcNow
            ExpiresAt = ttl |> Option.map (fun t -> DateTime.UtcNow.Add(t))
        }
        cache.AddOrUpdate(key, entry, fun _ _ -> entry) |> ignore

    /// Removes a value from the cache
    let remove (key: string) =
        cache.TryRemove(key) |> ignore

    /// Clears all values from the cache
    let clear () : unit =
        cache.Clear()

    /// Gets the number of items in the cache
    let count () : int =
        cache.Count

/// Public interface for the Prompt Cache module
module PromptCache =
    /// Gets a value from cache, or computes and caches it if not found
    let getOrAddAsync (key: string) (valueFactory: string -> Task<string>) (ttl: TimeSpan option) : Task<string> = 
        task {
            // Check cache first
            match PromptCacheImpl.tryGet key with
            | Some value -> 
                return value
            | None ->
                // Get the semaphore for this key to prevent concurrent loads
                let semaphore = PromptCacheImpl.getLock key
                do! semaphore.WaitAsync()
                try
                    // Double-check cache in case another thread loaded the value
                    match PromptCacheImpl.tryGet key with
                    | Some value -> 
                        return value
                    | None ->
                        // Compute the value and cache it
                        let! result = valueFactory key
                        PromptCacheImpl.set key result ttl
                        return result
                finally
                    semaphore.Release() |> ignore
        }

    /// Explicitly adds or updates a value in the cache
    let set (key: string) (value: string) (ttl: TimeSpan option) : unit =
        PromptCacheImpl.set key value ttl |> ignore

    /// Removes a value from the cache
    let remove (key: string) : unit =
        PromptCacheImpl.remove key

    /// Clears all values from the cache
    let clear () : unit =
        PromptCacheImpl.clear()

    /// Gets the number of items in the cache
    let count () : int =
        PromptCacheImpl.count()