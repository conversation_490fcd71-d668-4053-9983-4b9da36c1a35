namespace Automation.Prompts

open System
open System.Threading.Tasks

/// Default TTL for cached prompts (1 hour)
[<AutoOpen>]
module private Defaults =
    let defaultTtl = TimeSpan.FromHours(1.0)

/// Cached version of PromptStore with in-memory caching
[<RequireQualifiedAccess>]
module CachedPromptStore =
    /// Gets a prompt from cache or the database if not found
    let get (id: string) : Task<string option> = 
        task {
            let! result = 
                PromptCache.getOrAddAsync 
                    id 
                    (fun id -> task {
                        let! result = PromptStore.get id
                        return result |> Option.defaultValue null
                    })
                    (Some defaultTtl)
            match result with
            | null -> return None
            | text -> return Some text
        }

    /// Gets a prompt or returns the fallback value if not found
    let getOrDefault (id: string) (fallback: string) : Task<string> =
        task {
            let! result = get id
            return result |> Option.defaultValue fallback
        }

    /// Updates a prompt in both the database and cache
    let upsert (id: string) (text: string) : Task = 
        task {
            do! PromptStore.upsert id text
            // Invalidate the cache for this key
            PromptCache.remove id
        }

    /// Explicitly updates the cache for a prompt
    let refreshCache (id: string) : Task<unit> =
        task {
            let! result = PromptStore.get id
            match result with
            | Some text -> 
                PromptCache.set id text (Some defaultTtl)
            | None -> 
                PromptCache.remove id
        }

    /// Explicitly removes a prompt from the cache
    let invalidate (id: string) : unit =
        PromptCache.remove id

    /// Clears the entire prompt cache
    let clearCache () : unit =
        PromptCache.clear()
