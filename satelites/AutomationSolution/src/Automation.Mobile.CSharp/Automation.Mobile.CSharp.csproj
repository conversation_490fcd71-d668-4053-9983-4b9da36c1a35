<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <RootNamespace>Automation.Mobile</RootNamespace>
    <AssemblyName>Automation.Mobile.CSharp</AssemblyName>
    <Description>C# mobile automation engine with Appium integration</Description>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Automation.Contracts\Automation.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Appium.WebDriver" Version="5.0.0-rc.1" />
    <PackageReference Include="Microsoft.Extensions.ObjectPool" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.0" />
    <PackageReference Include="Selenium.WebDriver" Version="4.15.0" />
  </ItemGroup>

</Project>
