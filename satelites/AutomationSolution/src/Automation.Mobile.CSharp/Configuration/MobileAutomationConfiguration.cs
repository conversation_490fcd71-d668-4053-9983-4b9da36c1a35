namespace Automation.Mobile.Configuration;

/// <summary>
/// Configuration for mobile automation
/// </summary>
public class MobileAutomationConfiguration
{
    /// <summary>
    /// Platform type (iOS or Android)
    /// </summary>
    public MobilePlatform Platform { get; set; } = MobilePlatform.Android;

    /// <summary>
    /// Appium server configuration
    /// </summary>
    public AppiumServerConfiguration Server { get; set; } = new();

    /// <summary>
    /// Device capabilities
    /// </summary>
    public DeviceCapabilities Device { get; set; } = new();

    /// <summary>
    /// Resource management configuration
    /// </summary>
    public MobileResourceManagementConfiguration ResourceManagement { get; set; } = new();

    /// <summary>
    /// Error handling configuration
    /// </summary>
    public MobileErrorHandlingConfiguration ErrorHandling { get; set; } = new();

    /// <summary>
    /// Creates default configuration for Android
    /// </summary>
    public static MobileAutomationConfiguration CreateDefaultAndroid()
    {
        return new MobileAutomationConfiguration
        {
            Platform = MobilePlatform.Android,
            Device = DeviceCapabilities.CreateDefaultAndroid()
        };
    }

    /// <summary>
    /// Creates default configuration for iOS
    /// </summary>
    public static MobileAutomationConfiguration CreateDefaultiOS()
    {
        return new MobileAutomationConfiguration
        {
            Platform = MobilePlatform.iOS,
            Device = DeviceCapabilities.CreateDefaultiOS()
        };
    }
}

/// <summary>
/// Mobile platforms supported
/// </summary>
public enum MobilePlatform
{
    Android,
    iOS
}

/// <summary>
/// Appium server configuration
/// </summary>
public class AppiumServerConfiguration
{
    /// <summary>
    /// Appium server URL
    /// </summary>
    public string ServerUrl { get; set; } = "http://localhost:4723";

    /// <summary>
    /// Connection timeout in milliseconds
    /// </summary>
    public int ConnectionTimeoutMs { get; set; } = 30000;

    /// <summary>
    /// Command timeout in milliseconds
    /// </summary>
    public int CommandTimeoutMs { get; set; } = 60000;

    /// <summary>
    /// New command timeout in seconds
    /// </summary>
    public int NewCommandTimeoutSeconds { get; set; } = 300;
}

/// <summary>
/// Device capabilities configuration
/// </summary>
public class DeviceCapabilities
{
    /// <summary>
    /// Platform name (Android/iOS)
    /// </summary>
    public string PlatformName { get; set; } = string.Empty;

    /// <summary>
    /// Platform version
    /// </summary>
    public string? PlatformVersion { get; set; }

    /// <summary>
    /// Device name
    /// </summary>
    public string? DeviceName { get; set; }

    /// <summary>
    /// Device UDID (for real devices)
    /// </summary>
    public string? DeviceUdid { get; set; }

    /// <summary>
    /// App package/bundle identifier
    /// </summary>
    public string? AppPackage { get; set; }

    /// <summary>
    /// App activity (Android) or bundle ID (iOS)
    /// </summary>
    public string? AppActivity { get; set; }

    /// <summary>
    /// Path to app file (.apk for Android, .app/.ipa for iOS)
    /// </summary>
    public string? AppPath { get; set; }

    /// <summary>
    /// Whether to reset app state before session
    /// </summary>
    public bool NoReset { get; set; } = true;

    /// <summary>
    /// Whether to reinstall app before session
    /// </summary>
    public bool FullReset { get; set; } = false;

    /// <summary>
    /// Automation engine name
    /// </summary>
    public string AutomationName { get; set; } = string.Empty;

    /// <summary>
    /// Additional capabilities
    /// </summary>
    public Dictionary<string, object> AdditionalCapabilities { get; set; } = new();

    /// <summary>
    /// Creates default Android capabilities
    /// </summary>
    public static DeviceCapabilities CreateDefaultAndroid()
    {
        return new DeviceCapabilities
        {
            PlatformName = "Android",
            AutomationName = "UiAutomator2",
            DeviceName = "Android Emulator"
        };
    }

    /// <summary>
    /// Creates default iOS capabilities
    /// </summary>
    public static DeviceCapabilities CreateDefaultiOS()
    {
        return new DeviceCapabilities
        {
            PlatformName = "iOS",
            AutomationName = "XCUITest",
            DeviceName = "iPhone Simulator"
        };
    }
}

/// <summary>
/// Mobile resource management configuration
/// </summary>
public class MobileResourceManagementConfiguration
{
    /// <summary>
    /// Maximum number of concurrent device sessions
    /// </summary>
    public int MaxConcurrentSessions { get; set; } = 3;

    /// <summary>
    /// Session idle timeout in minutes
    /// </summary>
    public int SessionIdleTimeoutMinutes { get; set; } = 10;

    /// <summary>
    /// Whether to enable session pooling
    /// </summary>
    public bool EnableSessionPooling { get; set; } = true;

    /// <summary>
    /// Maximum session reuse count
    /// </summary>
    public int MaxSessionReuseCount { get; set; } = 10;
}

/// <summary>
/// Mobile error handling configuration
/// </summary>
public class MobileErrorHandlingConfiguration
{
    /// <summary>
    /// Whether to take screenshots on errors
    /// </summary>
    public bool TakeScreenshotOnError { get; set; } = true;

    /// <summary>
    /// Whether to capture page source on errors
    /// </summary>
    public bool CapturePageSourceOnError { get; set; } = true;

    /// <summary>
    /// Whether to capture device logs on errors
    /// </summary>
    public bool CaptureDeviceLogsOnError { get; set; } = true;

    /// <summary>
    /// Screenshot directory
    /// </summary>
    public string ScreenshotDirectory { get; set; } = "mobile_screenshots";

    /// <summary>
    /// Maximum number of retry attempts
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Retry delay in milliseconds
    /// </summary>
    public int RetryDelayMs { get; set; } = 2000;
}
