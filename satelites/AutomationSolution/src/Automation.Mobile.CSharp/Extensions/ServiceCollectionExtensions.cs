using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Automation.Mobile.Configuration;
using Automation.Mobile.Executors;

namespace Automation.Mobile.Extensions;

/// <summary>
/// Extension methods for configuring mobile automation services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds mobile automation services with default configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddMobileAutomationServices(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        return services.AddMobileAutomationServices(configuration, options => { });
    }

    /// <summary>
    /// Adds mobile automation services with custom configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <param name="configureOptions">Action to configure options</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddMobileAutomationServices(
        this IServiceCollection services,
        IConfiguration configuration,
        Action<MobileAutomationConfiguration> configureOptions)
    {
        // Configure options
        var options = MobileAutomationConfiguration.CreateDefaultAndroid();
        configuration.GetSection("MobileAutomation").Bind(options);
        configureOptions(options);

        // Register configuration
        services.AddSingleton(options);

        // Register mobile task executor
        services.AddTransient<MobileTaskExecutor>();

        return services;
    }

    /// <summary>
    /// Adds mobile automation services for Android
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="appiumServerUrl">Appium server URL</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddMobileAutomationServicesForAndroid(
        this IServiceCollection services,
        string? appiumServerUrl = null)
    {
        var config = new ConfigurationBuilder().Build();
        
        return services.AddMobileAutomationServices(config, options =>
        {
            options.Platform = MobilePlatform.Android;
            if (!string.IsNullOrEmpty(appiumServerUrl))
            {
                options.Server.ServerUrl = appiumServerUrl;
            }
        });
    }

    /// <summary>
    /// Adds mobile automation services for iOS
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="appiumServerUrl">Appium server URL</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddMobileAutomationServicesForIOS(
        this IServiceCollection services,
        string? appiumServerUrl = null)
    {
        var config = new ConfigurationBuilder().Build();
        
        return services.AddMobileAutomationServices(config, options =>
        {
            options.Platform = MobilePlatform.iOS;
            if (!string.IsNullOrEmpty(appiumServerUrl))
            {
                options.Server.ServerUrl = appiumServerUrl;
            }
        });
    }

    /// <summary>
    /// Adds mobile automation services for testing with custom device
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="platform">Mobile platform</param>
    /// <param name="deviceName">Device name</param>
    /// <param name="appPath">Path to app file</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddMobileAutomationServicesForTesting(
        this IServiceCollection services,
        MobilePlatform platform,
        string deviceName,
        string? appPath = null)
    {
        var config = new ConfigurationBuilder().Build();
        
        return services.AddMobileAutomationServices(config, options =>
        {
            options.Platform = platform;
            options.Device.DeviceName = deviceName;
            if (!string.IsNullOrEmpty(appPath))
            {
                options.Device.AppPath = appPath;
            }
            
            // Enable more detailed error handling for testing
            options.ErrorHandling.TakeScreenshotOnError = true;
            options.ErrorHandling.CapturePageSourceOnError = true;
            options.ErrorHandling.CaptureDeviceLogsOnError = true;
        });
    }
}
