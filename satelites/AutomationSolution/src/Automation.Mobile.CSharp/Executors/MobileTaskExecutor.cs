using System.Diagnostics;
using Microsoft.Extensions.Logging;
using OpenQA.Selenium;
using OpenQA.Selenium.Appium;
using OpenQA.Selenium.Appium.Android;
using OpenQA.Selenium.Appium.iOS;
using Automation.Contracts;
using Automation.Mobile.Configuration;

namespace Automation.Mobile.Executors;

/// <summary>
/// Modern C# mobile task executor with Appium integration
/// </summary>
public class MobileTaskExecutor : ITaskExecutor
{
    private readonly MobileAutomationConfiguration _configuration;
    private readonly ILogger<MobileTaskExecutor> _logger;
    private readonly string _executorType = "mobile";
    
    private int _totalExecutions = 0;
    private int _successfulExecutions = 0;
    private int _failedExecutions = 0;
    private DateTime _lastExecutionTime = DateTime.MinValue;
    private readonly List<TimeSpan> _executionTimes = new();
    private bool _disposed = false;

    public string ExecutorType => _executorType;

    public MobileTaskExecutor(
        MobileAutomationConfiguration configuration,
        ILogger<MobileTaskExecutor> logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<TaskResult> ExecuteAsync(IEnumerable<object> actions, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(MobileTaskExecutor));

        var startTime = DateTime.UtcNow;
        var stopwatch = Stopwatch.StartNew();
        Interlocked.Increment(ref _totalExecutions);

        AppiumDriver? driver = null;

        try
        {
            _logger.LogDebug("Starting mobile task execution with {ActionCount} actions", actions.Count());

            // Create driver session
            driver = await CreateDriverAsync(cancellationToken);

            var actionList = actions.ToList();
            var executedActions = new List<object>();

            foreach (var actionObj in actionList)
            {
                cancellationToken.ThrowIfCancellationRequested();

                try
                {
                    await ExecuteSingleActionAsync(driver, actionObj, cancellationToken);
                    executedActions.Add(actionObj);
                    
                    _logger.LogDebug("Successfully executed mobile action: {ActionType}", actionObj.GetType().Name);
                }
                catch (Exception actionEx)
                {
                    _logger.LogError(actionEx, "Failed to execute mobile action: {ActionType}", actionObj.GetType().Name);
                    
                    // Create detailed error information
                    var errorInfo = await CreateErrorInfoAsync(driver, actionObj, actionEx, cancellationToken);

                    Interlocked.Increment(ref _failedExecutions);
                    return TaskResult.Failure(errorInfo);
                }
            }

            stopwatch.Stop();
            _lastExecutionTime = DateTime.UtcNow;
            
            lock (_executionTimes)
            {
                _executionTimes.Add(stopwatch.Elapsed);
                if (_executionTimes.Count > 1000) // Keep only last 1000 execution times
                {
                    _executionTimes.RemoveAt(0);
                }
            }

            Interlocked.Increment(ref _successfulExecutions);
            
            _logger.LogInformation("Successfully executed {ActionCount} mobile actions in {ElapsedMs}ms",
                actionList.Count, stopwatch.ElapsedMilliseconds);

            return TaskResult.Success($"Successfully executed {actionList.Count} mobile actions.", stopwatch.Elapsed);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            Interlocked.Increment(ref _failedExecutions);
            
            _logger.LogError(ex, "Mobile task execution failed");

            var errorInfo = driver != null 
                ? await CreateErrorInfoAsync(driver, null, ex, cancellationToken)
                : new ActionExecutionError
                {
                    Message = ex.Message,
                    InnerException = ex,
                    StackTrace = ex.StackTrace,
                    OccurredAt = DateTime.UtcNow
                };

            return TaskResult.Failure(errorInfo);
        }
        finally
        {
            // Clean up driver session
            if (driver != null)
            {
                try
                {
                    driver.Quit();
                    driver.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error disposing mobile driver");
                }
            }
        }
    }

    public async Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) return false;

        try
        {
            // Try to create a driver session to test connectivity
            using var driver = await CreateDriverAsync(cancellationToken);
            return driver.SessionId != null;
        }
        catch
        {
            return false;
        }
    }

    public async Task<ExecutorMetrics> GetMetricsAsync(CancellationToken cancellationToken = default)
    {
        TimeSpan averageExecutionTime;
        lock (_executionTimes)
        {
            averageExecutionTime = _executionTimes.Count > 0 
                ? TimeSpan.FromTicks((long)_executionTimes.Average(t => t.Ticks))
                : TimeSpan.Zero;
        }

        var metrics = new ExecutorMetrics
        {
            TotalExecutions = _totalExecutions,
            SuccessfulExecutions = _successfulExecutions,
            FailedExecutions = _failedExecutions,
            AverageExecutionTime = averageExecutionTime,
            LastExecutionTime = _lastExecutionTime,
            IsHealthy = await IsHealthyAsync(cancellationToken),
            AdditionalMetrics = new Dictionary<string, object>
            {
                ["ExecutorType"] = _executorType,
                ["Platform"] = _configuration.Platform.ToString(),
                ["IsDisposed"] = _disposed
            }
        };

        return metrics;
    }

    private async Task<AppiumDriver> CreateDriverAsync(CancellationToken cancellationToken)
    {
        var serverUri = new Uri(_configuration.Server.ServerUrl);
        var options = CreateDriverOptions();

        return _configuration.Platform switch
        {
            MobilePlatform.Android => new AndroidDriver(serverUri, options, TimeSpan.FromMilliseconds(_configuration.Server.CommandTimeoutMs)),
            MobilePlatform.iOS => new IOSDriver(serverUri, options, TimeSpan.FromMilliseconds(_configuration.Server.CommandTimeoutMs)),
            _ => throw new ArgumentException($"Unsupported platform: {_configuration.Platform}")
        };
    }

    private AppiumOptions CreateDriverOptions()
    {
        var options = new AppiumOptions();
        var caps = _configuration.Device;

        // Set basic capabilities
        options.AddAdditionalAppiumOption("platformName", caps.PlatformName);
        options.AddAdditionalAppiumOption("automationName", caps.AutomationName);
        
        if (!string.IsNullOrEmpty(caps.PlatformVersion))
            options.AddAdditionalAppiumOption("platformVersion", caps.PlatformVersion);
        
        if (!string.IsNullOrEmpty(caps.DeviceName))
            options.AddAdditionalAppiumOption("deviceName", caps.DeviceName);
        
        if (!string.IsNullOrEmpty(caps.DeviceUdid))
            options.AddAdditionalAppiumOption("udid", caps.DeviceUdid);
        
        if (!string.IsNullOrEmpty(caps.AppPackage))
            options.AddAdditionalAppiumOption("appPackage", caps.AppPackage);
        
        if (!string.IsNullOrEmpty(caps.AppActivity))
            options.AddAdditionalAppiumOption("appActivity", caps.AppActivity);
        
        if (!string.IsNullOrEmpty(caps.AppPath))
            options.AddAdditionalAppiumOption("app", caps.AppPath);

        // Set reset options
        options.AddAdditionalAppiumOption("noReset", caps.NoReset);
        options.AddAdditionalAppiumOption("fullReset", caps.FullReset);

        // Set timeouts
        options.AddAdditionalAppiumOption("newCommandTimeout", _configuration.Server.NewCommandTimeoutSeconds);

        // Add additional capabilities
        foreach (var kvp in caps.AdditionalCapabilities)
        {
            options.AddAdditionalAppiumOption(kvp.Key, kvp.Value);
        }

        return options;
    }

    private async Task ExecuteSingleActionAsync(AppiumDriver driver, object actionObj, CancellationToken cancellationToken)
    {
        // This would need to be implemented based on your Action types from Automation.Contracts
        // For now, I'll create a basic implementation that handles common mobile actions
        
        switch (actionObj)
        {
            case MobileTapAction tap:
                var element = driver.FindElement(By.Id(tap.ElementId));
                element.Click();
                break;
                
            case MobileTypeAction type:
                var inputElement = driver.FindElement(By.Id(type.ElementId));
                inputElement.Clear();
                inputElement.SendKeys(type.Text);
                break;
                
            case MobileSwipeAction swipe:
                var actions = new OpenQA.Selenium.Interactions.Actions(driver);
                actions.ClickAndHold()
                       .MoveByOffset(swipe.DeltaX, swipe.DeltaY)
                       .Release()
                       .Perform();
                break;
                
            case MobileScreenshotAction screenshot:
                await EnsureDirectoryExists(screenshot.Path);
                var screenshotBytes = ((ITakesScreenshot)driver).GetScreenshot().AsByteArray;
                await File.WriteAllBytesAsync(screenshot.Path, screenshotBytes, cancellationToken);
                break;
                
            case MobileWaitAction wait:
                await Task.Delay(wait.DurationMs, cancellationToken);
                break;
                
            default:
                throw new NotSupportedException($"Mobile action type {actionObj.GetType().Name} is not supported");
        }
    }

    private async Task<ActionExecutionError> CreateErrorInfoAsync(
        AppiumDriver driver, 
        object? failedAction, 
        Exception exception, 
        CancellationToken cancellationToken)
    {
        var error = new ActionExecutionError
        {
            Message = exception.Message,
            InnerException = exception,
            StackTrace = exception.StackTrace,
            OccurredAt = DateTime.UtcNow
        };

        if (_configuration.ErrorHandling.TakeScreenshotOnError)
        {
            try
            {
                var screenshotPath = Path.Combine(
                    _configuration.ErrorHandling.ScreenshotDirectory,
                    $"mobile_error_{DateTime.UtcNow:yyyyMMdd_HHmmss}_{Guid.NewGuid():N}.png");
                
                await EnsureDirectoryExists(screenshotPath);
                var screenshot = ((ITakesScreenshot)driver).GetScreenshot();
                await File.WriteAllBytesAsync(screenshotPath, screenshot.AsByteArray, cancellationToken);
                
                error = error with { ScreenshotPath = screenshotPath };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to capture error screenshot");
            }
        }

        if (_configuration.ErrorHandling.CapturePageSourceOnError)
        {
            try
            {
                var pageSource = driver.PageSource;
                error = error with { HtmlContent = pageSource };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to capture page source");
            }
        }

        return error;
    }

    private static Task EnsureDirectoryExists(string filePath)
    {
        var directory = Path.GetDirectoryName(filePath);
        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }

        return Task.CompletedTask;
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
            _logger.LogDebug("Mobile task executor disposed");
        }
    }
}

// Basic mobile action types for demonstration - these should match your actual Action types
public record MobileTapAction(string ElementId);
public record MobileTypeAction(string ElementId, string Text);
public record MobileSwipeAction(int DeltaX, int DeltaY);
public record MobileScreenshotAction(string Path);
public record MobileWaitAction(int DurationMs);
