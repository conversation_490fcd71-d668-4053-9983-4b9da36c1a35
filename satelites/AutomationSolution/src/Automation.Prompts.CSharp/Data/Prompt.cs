using System.ComponentModel.DataAnnotations;

namespace Automation.Prompts.Data;

/// <summary>
/// Prompt entity stored in the database
/// </summary>
public class Prompt
{
    /// <summary>
    /// Unique identifier for the prompt
    /// </summary>
    [Key]
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// The prompt text content
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// When the prompt was last updated (UTC)
    /// </summary>
    public DateTime UpdatedUtc { get; set; }

    /// <summary>
    /// Optional metadata as JSON
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// Version number for optimistic concurrency
    /// </summary>
    public int Version { get; set; } = 1;

    /// <summary>
    /// When the prompt was created (UTC)
    /// </summary>
    public DateTime CreatedUtc { get; set; }

    /// <summary>
    /// Size of the prompt text in bytes
    /// </summary>
    public long SizeBytes { get; set; }

    /// <summary>
    /// Number of times this prompt has been accessed
    /// </summary>
    public long AccessCount { get; set; }

    /// <summary>
    /// Last time this prompt was accessed (UTC)
    /// </summary>
    public DateTime? LastAccessedUtc { get; set; }
}
