using Microsoft.EntityFrameworkCore;

namespace Automation.Prompts.Data;

/// <summary>
/// Entity Framework Core DbContext for prompts
/// Flexible so that we can swap the provider (e.g. PostgreSQL) later without changing callers
/// </summary>
public sealed class PromptsDbContext : DbContext
{
    public PromptsDbContext(DbContextOptions<PromptsDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// Prompts table
    /// </summary>
    public DbSet<Prompt> Prompts { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<Prompt>(entity =>
        {
            entity.HasKey(e => e.Id);
            
            entity.Property(e => e.Id)
                .HasMaxLength(255)
                .IsRequired();

            entity.Property(e => e.Text)
                .IsRequired();

            entity.Property(e => e.UpdatedUtc)
                .IsRequired();

            entity.Property(e => e.CreatedUtc)
                .IsRequired();

            entity.Property(e => e.Metadata)
                .HasColumnType("TEXT");

            entity.Property(e => e.Version)
                .IsRequired()
                .HasDefaultValue(1);

            entity.Property(e => e.SizeBytes)
                .IsRequired()
                .HasDefaultValue(0L);

            entity.Property(e => e.AccessCount)
                .IsRequired()
                .HasDefaultValue(0L);

            entity.Property(e => e.LastAccessedUtc);

            // Indexes for performance
            entity.HasIndex(e => e.UpdatedUtc)
                .HasDatabaseName("IX_Prompts_UpdatedUtc");

            entity.HasIndex(e => e.LastAccessedUtc)
                .HasDatabaseName("IX_Prompts_LastAccessedUtc");

            entity.HasIndex(e => e.AccessCount)
                .HasDatabaseName("IX_Prompts_AccessCount");
        });
    }
}
