namespace Automation.Prompts.Configuration;

/// <summary>
/// Configuration options for the prompts system
/// </summary>
public class PromptsConfiguration
{
    /// <summary>
    /// Database connection string or file path for SQLite
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// Database provider type
    /// </summary>
    public DatabaseProvider Provider { get; set; } = DatabaseProvider.SQLite;

    /// <summary>
    /// Whether to automatically create the database if it doesn't exist
    /// </summary>
    public bool EnsureCreated { get; set; } = true;

    /// <summary>
    /// Whether to enable detailed logging for EF Core
    /// </summary>
    public bool EnableSensitiveDataLogging { get; set; } = false;

    /// <summary>
    /// Cache configuration
    /// </summary>
    public CacheConfiguration Cache { get; set; } = new();

    /// <summary>
    /// Gets the default database path for SQLite
    /// </summary>
    /// <returns>Default SQLite database path</returns>
    public static string GetDefaultDatabasePath()
    {
        var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
        var dir = Path.Combine(appDataPath, "automation_prompts");
        
        if (!Directory.Exists(dir))
        {
            Directory.CreateDirectory(dir);
        }
        
        return Path.Combine(dir, "prompts.db");
    }

    /// <summary>
    /// Creates a default configuration with SQLite
    /// </summary>
    /// <returns>Default configuration</returns>
    public static PromptsConfiguration CreateDefault()
    {
        return new PromptsConfiguration
        {
            ConnectionString = $"Data Source={GetDefaultDatabasePath()}",
            Provider = DatabaseProvider.SQLite,
            EnsureCreated = true,
            EnableSensitiveDataLogging = false,
            Cache = CacheConfiguration.CreateDefault()
        };
    }
}

/// <summary>
/// Supported database providers
/// </summary>
public enum DatabaseProvider
{
    SQLite,
    PostgreSQL,
    SqlServer,
    InMemory
}

/// <summary>
/// Cache configuration options
/// </summary>
public class CacheConfiguration
{
    /// <summary>
    /// Whether caching is enabled
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Default TTL for cached items
    /// </summary>
    public TimeSpan DefaultTtl { get; set; } = TimeSpan.FromMinutes(30);

    /// <summary>
    /// Maximum number of items in cache
    /// </summary>
    public int MaxItems { get; set; } = 10000;

    /// <summary>
    /// Whether to enable cache metrics collection
    /// </summary>
    public bool EnableMetrics { get; set; } = true;

    /// <summary>
    /// How often to clean up expired cache entries
    /// </summary>
    public TimeSpan CleanupInterval { get; set; } = TimeSpan.FromMinutes(5);

    /// <summary>
    /// Creates default cache configuration
    /// </summary>
    /// <returns>Default cache configuration</returns>
    public static CacheConfiguration CreateDefault()
    {
        return new CacheConfiguration();
    }
}
