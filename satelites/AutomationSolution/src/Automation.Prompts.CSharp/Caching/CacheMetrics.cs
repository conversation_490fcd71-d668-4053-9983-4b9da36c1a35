using System.Collections.Concurrent;
using System.Diagnostics;

namespace Automation.Prompts.Caching;

/// <summary>
/// Types of cache operations
/// </summary>
public enum CacheOperation
{
    Hit,
    Miss,
    Set,
    Remove,
    Clear,
    Error
}

/// <summary>
/// Detailed metrics for a single cache operation
/// </summary>
public record CacheOperationMetrics
{
    public CacheOperation Operation { get; init; }
    public string Key { get; init; } = string.Empty;
    public DateTime Timestamp { get; init; }
    public TimeSpan Duration { get; init; }
    public long? ItemSize { get; init; }
    public string? Error { get; init; }
}

/// <summary>
/// Aggregated cache metrics
/// </summary>
public record CacheMetrics
{
    public long TotalHits { get; init; }
    public long TotalMisses { get; init; }
    public long TotalSets { get; init; }
    public long TotalRemoves { get; init; }
    public long TotalClears { get; init; }
    public long TotalErrors { get; init; }
    public long TotalOperations { get; init; }
    public double AverageHitLatencyMs { get; init; }
    public double AverageMissLatencyMs { get; init; }
    public long CurrentSize { get; init; }
    public long MaxSize { get; init; }
    public DateTime LastUpdated { get; init; }

    /// <summary>
    /// Calculates the cache hit rate as a percentage
    /// </summary>
    public double HitRate => TotalHits + TotalMisses > 0 
        ? (double)TotalHits / (TotalHits + TotalMisses) * 100.0 
        : 0.0;
}

/// <summary>
/// Interface for cache metrics collection
/// </summary>
public interface ICacheMetricsCollector
{
    /// <summary>
    /// Record a cache operation with metrics
    /// </summary>
    void RecordOperation(CacheOperation operation, string key, TimeSpan duration, long? itemSize = null, Exception? error = null);

    /// <summary>
    /// Get the current metrics snapshot
    /// </summary>
    CacheMetrics GetMetrics();

    /// <summary>
    /// Reset all metrics
    /// </summary>
    void Reset();
}

/// <summary>
/// Default implementation of ICacheMetricsCollector
/// </summary>
public class DefaultCacheMetricsCollector : ICacheMetricsCollector
{
    private readonly ConcurrentDictionary<string, long> _metrics = new();
    private readonly ConcurrentBag<double> _hitLatencies = new();
    private readonly ConcurrentBag<double> _missLatencies = new();
    private readonly object _lockObj = new();
    
    private long _currentSize = 0L;
    private long _maxSize = 0L;

    public void RecordOperation(CacheOperation operation, string key, TimeSpan duration, long? itemSize = null, Exception? error = null)
    {
        var opName = operation.ToString();
        
        // Update operation counts
        _metrics.AddOrUpdate(opName, 1L, (_, v) => v + 1L);
        _metrics.AddOrUpdate("total_operations", 1L, (_, v) => v + 1L);
        
        // Update size metrics if applicable
        if (itemSize.HasValue)
        {
            switch (operation)
            {
                case CacheOperation.Set:
                    UpdateSize(itemSize.Value);
                    break;
                case CacheOperation.Remove:
                    UpdateSize(-itemSize.Value);
                    break;
                case CacheOperation.Clear:
                    lock (_lockObj)
                    {
                        _currentSize = 0L;
                    }
                    break;
            }
        }
        
        // Record latencies
        switch (operation)
        {
            case CacheOperation.Hit:
                _hitLatencies.Add(duration.TotalMilliseconds);
                break;
            case CacheOperation.Miss:
                _missLatencies.Add(duration.TotalMilliseconds);
                break;
        }
        
        // Record errors
        if (operation == CacheOperation.Error)
        {
            _metrics.AddOrUpdate("total_errors", 1L, (_, v) => v + 1L);
            if (error != null)
            {
                _metrics.AddOrUpdate($"error_{error.GetType().Name}", 1L, (_, v) => v + 1L);
            }
        }
    }

    public CacheMetrics GetMetrics()
    {
        var hits = _metrics.GetOrAdd("Hit", _ => 0L);
        var misses = _metrics.GetOrAdd("Miss", _ => 0L);
        var totalOps = _metrics.GetOrAdd("total_operations", _ => 0L);
        
        var avgHitLatency = _hitLatencies.Count > 0 ? _hitLatencies.Average() : 0.0;
        var avgMissLatency = _missLatencies.Count > 0 ? _missLatencies.Average() : 0.0;
        
        return new CacheMetrics
        {
            TotalHits = hits,
            TotalMisses = misses,
            TotalSets = _metrics.GetOrAdd("Set", _ => 0L),
            TotalRemoves = _metrics.GetOrAdd("Remove", _ => 0L),
            TotalClears = _metrics.GetOrAdd("Clear", _ => 0L),
            TotalErrors = _metrics.GetOrAdd("total_errors", _ => 0L),
            TotalOperations = totalOps,
            AverageHitLatencyMs = avgHitLatency,
            AverageMissLatencyMs = avgMissLatency,
            CurrentSize = _currentSize,
            MaxSize = _maxSize,
            LastUpdated = DateTime.UtcNow
        };
    }

    public void Reset()
    {
        _metrics.Clear();
        lock (_lockObj)
        {
            _currentSize = 0L;
            _maxSize = 0L;
        }
        
        // Clear latency collections
        while (_hitLatencies.TryTake(out _)) { }
        while (_missLatencies.TryTake(out _)) { }
    }

    private void UpdateSize(long delta)
    {
        lock (_lockObj)
        {
            _currentSize += delta;
            if (_currentSize > _maxSize)
            {
                _maxSize = _currentSize;
            }
        }
    }
}

/// <summary>
/// No-op metrics collector for when metrics are disabled
/// </summary>
public class NoOpCacheMetricsCollector : ICacheMetricsCollector
{
    public void RecordOperation(CacheOperation operation, string key, TimeSpan duration, long? itemSize = null, Exception? error = null)
    {
        // No-op
    }

    public CacheMetrics GetMetrics()
    {
        return new CacheMetrics
        {
            LastUpdated = DateTime.UtcNow
        };
    }

    public void Reset()
    {
        // No-op
    }
}
