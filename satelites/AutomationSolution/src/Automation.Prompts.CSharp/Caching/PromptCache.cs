using System.Collections.Concurrent;
using System.Diagnostics;
using System.Text;
using Automation.Prompts.Configuration;

namespace Automation.Prompts.Caching;

/// <summary>
/// Cache entry with timestamp for expiration
/// </summary>
internal record CacheEntry
{
    public string Value { get; init; } = string.Empty;
    public DateTime Timestamp { get; init; }
    public DateTime? ExpiresAt { get; init; }
    public long SizeBytes { get; init; }
}

/// <summary>
/// Thread-safe in-memory cache for prompts with TTL support
/// </summary>
public interface IPromptCache : IDisposable
{
    /// <summary>
    /// Gets a value from cache, or computes and caches it if not found
    /// </summary>
    Task<string> GetOrAddAsync(string key, Func<string, Task<string>> valueFactory, TimeSpan? ttl = null);

    /// <summary>
    /// Explicitly adds or updates a value in the cache
    /// </summary>
    void Set(string key, string value, TimeSpan? ttl = null);

    /// <summary>
    /// Removes a value from the cache
    /// </summary>
    void Remove(string key);

    /// <summary>
    /// Clears all values from the cache
    /// </summary>
    void Clear();

    /// <summary>
    /// Gets the number of items in the cache
    /// </summary>
    int Count { get; }

    /// <summary>
    /// Gets the current cache metrics
    /// </summary>
    CacheMetrics GetMetrics();
}

/// <summary>
/// Default implementation of IPromptCache
/// </summary>
public class PromptCache : IPromptCache, IDisposable
{
    private readonly ConcurrentDictionary<string, CacheEntry> _cache = new();
    private readonly ConcurrentDictionary<string, SemaphoreSlim> _cacheLocks = new();
    private readonly ICacheMetricsCollector _metricsCollector;
    private readonly CacheConfiguration _configuration;
    private readonly Timer _cleanupTimer;
    private bool _disposed = false;

    public PromptCache(CacheConfiguration configuration, ICacheMetricsCollector? metricsCollector = null)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _metricsCollector = metricsCollector ?? new NoOpCacheMetricsCollector();
        
        // Setup cleanup timer
        _cleanupTimer = new Timer(CleanupExpiredEntries, null, _configuration.CleanupInterval, _configuration.CleanupInterval);
    }

    public int Count => _cache.Count;

    public async Task<string> GetOrAddAsync(string key, Func<string, Task<string>> valueFactory, TimeSpan? ttl = null)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PromptCache));
        
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // Check cache first
            if (TryGetValue(key, out var cachedValue))
            {
                _metricsCollector.RecordOperation(CacheOperation.Hit, key, stopwatch.Elapsed);
                return cachedValue;
            }

            // Get the semaphore for this key to prevent concurrent loads
            var semaphore = GetLock(key);
            await semaphore.WaitAsync();
            
            try
            {
                // Double-check cache in case another thread loaded the value
                if (TryGetValue(key, out cachedValue))
                {
                    _metricsCollector.RecordOperation(CacheOperation.Hit, key, stopwatch.Elapsed);
                    return cachedValue;
                }

                // Compute the value and cache it
                var result = await valueFactory(key);
                var effectiveTtl = ttl ?? _configuration.DefaultTtl;
                SetInternal(key, result, effectiveTtl);
                
                _metricsCollector.RecordOperation(CacheOperation.Miss, key, stopwatch.Elapsed, GetSizeBytes(result));
                return result;
            }
            finally
            {
                semaphore.Release();
            }
        }
        catch (Exception ex)
        {
            _metricsCollector.RecordOperation(CacheOperation.Error, key, stopwatch.Elapsed, error: ex);
            throw;
        }
    }

    public void Set(string key, string value, TimeSpan? ttl = null)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PromptCache));
        
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var effectiveTtl = ttl ?? _configuration.DefaultTtl;
            SetInternal(key, value, effectiveTtl);
            _metricsCollector.RecordOperation(CacheOperation.Set, key, stopwatch.Elapsed, GetSizeBytes(value));
        }
        catch (Exception ex)
        {
            _metricsCollector.RecordOperation(CacheOperation.Error, key, stopwatch.Elapsed, error: ex);
            throw;
        }
    }

    public void Remove(string key)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PromptCache));
        
        var stopwatch = Stopwatch.StartNew();
        try
        {
            long? size = null;
            if (_cache.TryGetValue(key, out var entry))
            {
                size = entry.SizeBytes;
            }
            
            _cache.TryRemove(key, out _);
            _metricsCollector.RecordOperation(CacheOperation.Remove, key, stopwatch.Elapsed, size);
        }
        catch (Exception ex)
        {
            _metricsCollector.RecordOperation(CacheOperation.Error, key, stopwatch.Elapsed, error: ex);
            throw;
        }
    }

    public void Clear()
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PromptCache));
        
        var stopwatch = Stopwatch.StartNew();
        try
        {
            _cache.Clear();
            _metricsCollector.RecordOperation(CacheOperation.Clear, "all", stopwatch.Elapsed);
        }
        catch (Exception ex)
        {
            _metricsCollector.RecordOperation(CacheOperation.Error, "all", stopwatch.Elapsed, error: ex);
            throw;
        }
    }

    public CacheMetrics GetMetrics()
    {
        return _metricsCollector.GetMetrics();
    }

    private bool TryGetValue(string key, out string value)
    {
        value = string.Empty;
        
        if (!_cache.TryGetValue(key, out var entry))
        {
            return false;
        }

        // Check if entry has expired
        if (entry.ExpiresAt.HasValue && DateTime.UtcNow > entry.ExpiresAt.Value)
        {
            // Entry has expired, remove it
            _cache.TryRemove(key, out _);
            return false;
        }

        value = entry.Value;
        return true;
    }

    private void SetInternal(string key, string value, TimeSpan ttl)
    {
        // Check cache size limits
        if (_cache.Count >= _configuration.MaxItems)
        {
            // Remove oldest entries (simple LRU approximation)
            var oldestEntries = _cache
                .OrderBy(kvp => kvp.Value.Timestamp)
                .Take(_configuration.MaxItems / 10) // Remove 10% of entries
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var oldKey in oldestEntries)
            {
                _cache.TryRemove(oldKey, out _);
            }
        }

        var entry = new CacheEntry
        {
            Value = value,
            Timestamp = DateTime.UtcNow,
            ExpiresAt = DateTime.UtcNow.Add(ttl),
            SizeBytes = GetSizeBytes(value)
        };

        _cache.AddOrUpdate(key, entry, (_, _) => entry);
    }

    private SemaphoreSlim GetLock(string key)
    {
        return _cacheLocks.GetOrAdd(key, _ => new SemaphoreSlim(1, 1));
    }

    private static long GetSizeBytes(string value)
    {
        return Encoding.UTF8.GetByteCount(value);
    }

    private void CleanupExpiredEntries(object? state)
    {
        if (_disposed) return;

        try
        {
            var now = DateTime.UtcNow;
            var expiredKeys = _cache
                .Where(kvp => kvp.Value.ExpiresAt.HasValue && now > kvp.Value.ExpiresAt.Value)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in expiredKeys)
            {
                _cache.TryRemove(key, out _);
            }
        }
        catch
        {
            // Ignore cleanup errors
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _cleanupTimer?.Dispose();
            _cache.Clear();
            
            foreach (var semaphore in _cacheLocks.Values)
            {
                semaphore.Dispose();
            }
            _cacheLocks.Clear();
            
            _disposed = true;
        }
    }
}
