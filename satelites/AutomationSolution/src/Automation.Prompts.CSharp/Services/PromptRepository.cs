using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text;
using Automation.Prompts.Data;

namespace Automation.Prompts.Services;

/// <summary>
/// Entity Framework implementation of IPromptRepository
/// </summary>
public class PromptRepository : IPromptRepository
{
    private readonly PromptsDbContext _context;
    private readonly ILogger<PromptRepository> _logger;

    public PromptRepository(PromptsDbContext context, ILogger<PromptRepository> logger)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<Prompt?> GetByIdAsync(string id, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting prompt by ID: {Id}", id);
        return await _context.Prompts.FindAsync([id], cancellationToken);
    }

    public async Task<string?> GetTextByIdAsync(string id, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting prompt text by ID: {Id}", id);
        
        var prompt = await _context.Prompts
            .Where(p => p.Id == id)
            .Select(p => new { p.Text })
            .FirstOrDefaultAsync(cancellationToken);

        return prompt?.Text;
    }

    public async Task<Prompt> UpsertAsync(string id, string text, string? metadata = null, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Upserting prompt: {Id}", id);

        var existing = await _context.Prompts.FindAsync([id], cancellationToken);
        var now = DateTime.UtcNow;
        var sizeBytes = Encoding.UTF8.GetByteCount(text);

        if (existing == null)
        {
            // Create new prompt
            var newPrompt = new Prompt
            {
                Id = id,
                Text = text,
                Metadata = metadata,
                CreatedUtc = now,
                UpdatedUtc = now,
                SizeBytes = sizeBytes,
                Version = 1,
                AccessCount = 0
            };

            _context.Prompts.Add(newPrompt);
            await _context.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Created new prompt: {Id}", id);
            return newPrompt;
        }
        else
        {
            // Update existing prompt
            existing.Text = text;
            existing.Metadata = metadata;
            existing.UpdatedUtc = now;
            existing.SizeBytes = sizeBytes;
            existing.Version++;

            await _context.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Updated existing prompt: {Id}", id);
            return existing;
        }
    }

    public async Task<bool> DeleteAsync(string id, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Deleting prompt: {Id}", id);

        var prompt = await _context.Prompts.FindAsync([id], cancellationToken);
        if (prompt == null)
        {
            return false;
        }

        _context.Prompts.Remove(prompt);
        await _context.SaveChangesAsync(cancellationToken);
        
        _logger.LogInformation("Deleted prompt: {Id}", id);
        return true;
    }

    public async Task<IList<Prompt>> GetAllAsync(int skip = 0, int take = 100, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting all prompts: skip={Skip}, take={Take}", skip, take);

        return await _context.Prompts
            .OrderBy(p => p.Id)
            .Skip(skip)
            .Take(take)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetCountAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Prompts.CountAsync(cancellationToken);
    }

    public async Task<IList<Prompt>> SearchAsync(string searchTerm, int skip = 0, int take = 100, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Searching prompts: term={SearchTerm}, skip={Skip}, take={Take}", searchTerm, skip, take);

        return await _context.Prompts
            .Where(p => p.Text.Contains(searchTerm) || p.Id.Contains(searchTerm))
            .OrderBy(p => p.Id)
            .Skip(skip)
            .Take(take)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> UpdateAccessAsync(string id, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Updating access for prompt: {Id}", id);

        var prompt = await _context.Prompts.FindAsync([id], cancellationToken);
        if (prompt == null)
        {
            return false;
        }

        prompt.AccessCount++;
        prompt.LastAccessedUtc = DateTime.UtcNow;

        await _context.SaveChangesAsync(cancellationToken);
        return true;
    }

    public async Task<IList<Prompt>> GetMostAccessedAsync(int take = 10, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting most accessed prompts: take={Take}", take);

        return await _context.Prompts
            .OrderByDescending(p => p.AccessCount)
            .ThenByDescending(p => p.LastAccessedUtc)
            .Take(take)
            .ToListAsync(cancellationToken);
    }

    public async Task<IList<Prompt>> GetRecentlyUpdatedAsync(int take = 10, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting recently updated prompts: take={Take}", take);

        return await _context.Prompts
            .OrderByDescending(p => p.UpdatedUtc)
            .Take(take)
            .ToListAsync(cancellationToken);
    }
}
