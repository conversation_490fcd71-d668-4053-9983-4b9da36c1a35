using Automation.Prompts.Caching;
using Automation.Prompts.Data;

namespace Automation.Prompts.Services;

/// <summary>
/// High-level interface for prompt storage and retrieval with caching
/// </summary>
public interface IPromptStore
{
    /// <summary>
    /// Gets a prompt text by ID, with caching
    /// </summary>
    /// <param name="id">Prompt ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Prompt text if found, null otherwise</returns>
    Task<string?> GetAsync(string id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a prompt text by ID, or returns the fallback if not found
    /// </summary>
    /// <param name="id">Prompt ID</param>
    /// <param name="fallback">Fallback text</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Prompt text or fallback</returns>
    Task<string> GetOrDefaultAsync(string id, string fallback, CancellationToken cancellationToken = default);

    /// <summary>
    /// Inserts or updates a prompt
    /// </summary>
    /// <param name="id">Prompt ID</param>
    /// <param name="text">Prompt text</param>
    /// <param name="metadata">Optional metadata</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The created or updated prompt</returns>
    Task<Prompt> UpsertAsync(string id, string text, string? metadata = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a prompt by ID
    /// </summary>
    /// <param name="id">Prompt ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if deleted, false if not found</returns>
    Task<bool> DeleteAsync(string id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches prompts by text content
    /// </summary>
    /// <param name="searchTerm">Search term</param>
    /// <param name="skip">Number of items to skip</param>
    /// <param name="take">Number of items to take</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of matching prompts</returns>
    Task<IList<Prompt>> SearchAsync(string searchTerm, int skip = 0, int take = 100, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all prompts with pagination
    /// </summary>
    /// <param name="skip">Number of items to skip</param>
    /// <param name="take">Number of items to take</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of prompts</returns>
    Task<IList<Prompt>> GetAllAsync(int skip = 0, int take = 100, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the total count of prompts
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Total count</returns>
    Task<int> GetCountAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the most frequently accessed prompts
    /// </summary>
    /// <param name="take">Number of items to take</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of most accessed prompts</returns>
    Task<IList<Prompt>> GetMostAccessedAsync(int take = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets recently updated prompts
    /// </summary>
    /// <param name="take">Number of items to take</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of recently updated prompts</returns>
    Task<IList<Prompt>> GetRecentlyUpdatedAsync(int take = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the current cache metrics
    /// </summary>
    /// <returns>Cache metrics</returns>
    CacheMetrics GetCacheMetrics();

    /// <summary>
    /// Clears the cache
    /// </summary>
    void ClearCache();

    /// <summary>
    /// Removes a specific item from the cache
    /// </summary>
    /// <param name="id">Prompt ID to remove from cache</param>
    void RemoveFromCache(string id);
}
