using Microsoft.Extensions.Logging;
using Automation.Prompts.Caching;
using Automation.Prompts.Configuration;
using Automation.Prompts.Data;

namespace Automation.Prompts.Services;

/// <summary>
/// Implementation of IPromptStore that combines repository and caching
/// </summary>
public class PromptStore : IPromptStore, IDisposable
{
    private readonly IPromptRepository _repository;
    private readonly IPromptCache _cache;
    private readonly ILogger<PromptStore> _logger;
    private readonly CacheConfiguration _cacheConfig;
    private bool _disposed = false;

    public PromptStore(
        IPromptRepository repository, 
        IPromptCache cache, 
        ILogger<PromptStore> logger,
        CacheConfiguration cacheConfig)
    {
        _repository = repository ?? throw new ArgumentNullException(nameof(repository));
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _cacheConfig = cacheConfig ?? throw new ArgumentNullException(nameof(cacheConfig));
    }

    public async Task<string?> GetAsync(string id, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PromptStore));
        
        _logger.LogDebug("Getting prompt: {Id}", id);

        if (!_cacheConfig.Enabled)
        {
            // Cache disabled, go directly to repository
            var result = await _repository.GetTextByIdAsync(id, cancellationToken);
            if (result != null)
            {
                // Update access tracking
                _ = Task.Run(() => _repository.UpdateAccessAsync(id, cancellationToken), cancellationToken);
            }
            return result;
        }

        // Use cache
        var cachedResult = await _cache.GetOrAddAsync(
            id,
            async key =>
            {
                var text = await _repository.GetTextByIdAsync(key, cancellationToken);
                if (text != null)
                {
                    // Update access tracking in background
                    _ = Task.Run(() => _repository.UpdateAccessAsync(key, cancellationToken), cancellationToken);
                }
                return text ?? string.Empty; // Cache doesn't handle nulls well
            },
            _cacheConfig.DefaultTtl);

        return string.IsNullOrEmpty(cachedResult) ? null : cachedResult;
    }

    public async Task<string> GetOrDefaultAsync(string id, string fallback, CancellationToken cancellationToken = default)
    {
        var result = await GetAsync(id, cancellationToken);
        return result ?? fallback;
    }

    public async Task<Prompt> UpsertAsync(string id, string text, string? metadata = null, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PromptStore));
        
        _logger.LogDebug("Upserting prompt: {Id}", id);

        var prompt = await _repository.UpsertAsync(id, text, metadata, cancellationToken);
        
        // Update cache if enabled
        if (_cacheConfig.Enabled)
        {
            _cache.Set(id, text, _cacheConfig.DefaultTtl);
        }

        return prompt;
    }

    public async Task<bool> DeleteAsync(string id, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PromptStore));
        
        _logger.LogDebug("Deleting prompt: {Id}", id);

        var deleted = await _repository.DeleteAsync(id, cancellationToken);
        
        // Remove from cache if enabled
        if (_cacheConfig.Enabled && deleted)
        {
            _cache.Remove(id);
        }

        return deleted;
    }

    public async Task<IList<Prompt>> SearchAsync(string searchTerm, int skip = 0, int take = 100, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PromptStore));
        
        _logger.LogDebug("Searching prompts: {SearchTerm}", searchTerm);
        return await _repository.SearchAsync(searchTerm, skip, take, cancellationToken);
    }

    public async Task<IList<Prompt>> GetAllAsync(int skip = 0, int take = 100, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PromptStore));
        
        return await _repository.GetAllAsync(skip, take, cancellationToken);
    }

    public async Task<int> GetCountAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PromptStore));
        
        return await _repository.GetCountAsync(cancellationToken);
    }

    public async Task<IList<Prompt>> GetMostAccessedAsync(int take = 10, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PromptStore));
        
        return await _repository.GetMostAccessedAsync(take, cancellationToken);
    }

    public async Task<IList<Prompt>> GetRecentlyUpdatedAsync(int take = 10, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PromptStore));
        
        return await _repository.GetRecentlyUpdatedAsync(take, cancellationToken);
    }

    public CacheMetrics GetCacheMetrics()
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PromptStore));
        
        return _cache.GetMetrics();
    }

    public void ClearCache()
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PromptStore));
        
        _logger.LogInformation("Clearing prompt cache");
        _cache.Clear();
    }

    public void RemoveFromCache(string id)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(PromptStore));
        
        _logger.LogDebug("Removing prompt from cache: {Id}", id);
        _cache.Remove(id);
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _cache?.Dispose();
            _disposed = true;
        }
    }
}
