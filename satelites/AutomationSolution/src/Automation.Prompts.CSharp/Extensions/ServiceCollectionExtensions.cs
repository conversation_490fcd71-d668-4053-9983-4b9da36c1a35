using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Automation.Prompts.Caching;
using Automation.Prompts.Configuration;
using Automation.Prompts.Data;
using Automation.Prompts.Services;

namespace Automation.Prompts.Extensions;

/// <summary>
/// Extension methods for configuring prompt services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds prompt services with default configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddPromptServices(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        return services.AddPromptServices(configuration, options => { });
    }

    /// <summary>
    /// Adds prompt services with custom configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <param name="configureOptions">Action to configure options</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddPromptServices(
        this IServiceCollection services,
        IConfiguration configuration,
        Action<PromptsConfiguration> configureOptions)
    {
        // Configure options
        var options = PromptsConfiguration.CreateDefault();
        configuration.GetSection("Prompts").Bind(options);
        configureOptions(options);

        // Register configuration
        services.AddSingleton(options);
        services.AddSingleton(options.Cache);

        // Configure Entity Framework
        services.AddDbContext<PromptsDbContext>(dbOptions =>
        {
            switch (options.Provider)
            {
                case DatabaseProvider.SQLite:
                    dbOptions.UseSqlite(options.ConnectionString);
                    break;
                case DatabaseProvider.InMemory:
                    dbOptions.UseInMemoryDatabase("PromptsInMemory");
                    break;
                case DatabaseProvider.PostgreSQL:
                    dbOptions.UseNpgsql(options.ConnectionString);
                    break;
                case DatabaseProvider.SqlServer:
                    dbOptions.UseSqlServer(options.ConnectionString);
                    break;
                default:
                    throw new ArgumentException($"Unsupported database provider: {options.Provider}");
            }

            if (options.EnableSensitiveDataLogging)
            {
                dbOptions.EnableSensitiveDataLogging();
            }
        });

        // Register cache services
        if (options.Cache.EnableMetrics)
        {
            services.AddSingleton<ICacheMetricsCollector, DefaultCacheMetricsCollector>();
        }
        else
        {
            services.AddSingleton<ICacheMetricsCollector, NoOpCacheMetricsCollector>();
        }

        services.AddSingleton<IPromptCache>(provider =>
        {
            var cacheConfig = provider.GetRequiredService<CacheConfiguration>();
            var metricsCollector = provider.GetRequiredService<ICacheMetricsCollector>();
            return new PromptCache(cacheConfig, metricsCollector);
        });

        // Register repository and store
        services.AddScoped<IPromptRepository, PromptRepository>();
        services.AddScoped<IPromptStore, PromptStore>();

        // Register database initialization service
        services.AddHostedService<DatabaseInitializationService>();

        return services;
    }

    /// <summary>
    /// Adds prompt services with SQLite configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="connectionString">SQLite connection string</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddPromptServicesWithSQLite(
        this IServiceCollection services,
        string? connectionString = null)
    {
        var config = new ConfigurationBuilder().Build();
        
        return services.AddPromptServices(config, options =>
        {
            options.Provider = DatabaseProvider.SQLite;
            options.ConnectionString = connectionString ?? $"Data Source={PromptsConfiguration.GetDefaultDatabasePath()}";
        });
    }

    /// <summary>
    /// Adds prompt services with in-memory database (for testing)
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddPromptServicesInMemory(this IServiceCollection services)
    {
        var config = new ConfigurationBuilder().Build();
        
        return services.AddPromptServices(config, options =>
        {
            options.Provider = DatabaseProvider.InMemory;
            options.EnsureCreated = true;
        });
    }
}

/// <summary>
/// Hosted service to initialize the database
/// </summary>
internal class DatabaseInitializationService : IHostedService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly PromptsConfiguration _configuration;
    private readonly ILogger<DatabaseInitializationService> _logger;

    public DatabaseInitializationService(
        IServiceProvider serviceProvider,
        PromptsConfiguration configuration,
        ILogger<DatabaseInitializationService> logger)
    {
        _serviceProvider = serviceProvider;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        if (_configuration.EnsureCreated)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<PromptsDbContext>();
            
            try
            {
                _logger.LogInformation("Ensuring prompts database is created");
                await context.Database.EnsureCreatedAsync(cancellationToken);
                _logger.LogInformation("Prompts database initialization completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize prompts database");
                throw;
            }
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }
}
