using System.ComponentModel.DataAnnotations;

namespace Automation.Contracts;

/// <summary>
/// Represents the result of a task execution
/// </summary>
public record TaskResult
{
    public bool IsSuccess { get; init; }
    public string Message { get; init; } = string.Empty;
    public ActionExecutionError? Error { get; init; }
    public DateTime ExecutedAt { get; init; } = DateTime.UtcNow;
    public TimeSpan Duration { get; init; }
    
    public static TaskResult Success(string message, TimeSpan duration = default)
        => new() { IsSuccess = true, Message = message, Duration = duration };
    
    public static TaskResult Failure(string message, ActionExecutionError? error = null)
        => new() { IsSuccess = false, Message = message, Error = error };
    
    public static TaskResult Failure(ActionExecutionError error)
        => new() { IsSuccess = false, Message = error.Message, Error = error };
}

/// <summary>
/// Represents an error that occurred during action execution
/// </summary>
public record ActionExecutionError
{
    [Required]
    public string Message { get; init; } = string.Empty;
    
    public string? FailedAction { get; init; }
    public string? ScreenshotPath { get; init; }
    public string? CurrentUrl { get; init; }
    public string? HtmlContent { get; init; }
    public IReadOnlyList<string> AttemptedSelectors { get; init; } = Array.Empty<string>();
    public Exception? InnerException { get; init; }
    public string? StackTrace { get; init; }
    public DateTime OccurredAt { get; init; } = DateTime.UtcNow;
}