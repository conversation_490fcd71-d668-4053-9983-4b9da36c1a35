namespace Automation.Contracts;

/// <summary>
/// Defines the contract for AI processing components
/// </summary>
public interface IAIProcessor
{
    /// <summary>
    /// Processes a natural language command into automation actions
    /// </summary>
    /// <param name="command">The natural language command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>A collection of automation actions</returns>
    Task<IEnumerable<object>> ProcessCommandAsync(string command, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Attempts to heal a failed execution by analyzing the failure context
    /// </summary>
    /// <param name="context">The failure context</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The healing result</returns>
    Task<HealingResult> AttemptHealingAsync(FailureContext context, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Validates if a command can be processed
    /// </summary>
    /// <param name="command">The command to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Validation result</returns>
    Task<ValidationResult> ValidateCommandAsync(string command, CancellationToken cancellationToken = default);
}

/// <summary>
/// Result of a healing attempt
/// </summary>
public record HealingResult
{
    public bool IsSuccess { get; init; }
    public string Message { get; init; } = string.Empty;
    public IEnumerable<object> SuggestedActions { get; init; } = Array.Empty<object>();
    public HealingStrategy Strategy { get; init; }
    public double Confidence { get; init; }
    public TimeSpan ProcessingTime { get; init; }
}

/// <summary>
/// Context information about a failure
/// </summary>
public record FailureContext
{
    public ActionExecutionError Error { get; init; } = new();
    public string? OriginalCommand { get; init; }
    public IEnumerable<object> FailedActions { get; init; } = Array.Empty<object>();
    public string? CurrentUrl { get; init; }
    public string? HtmlContent { get; init; }
    public string? ScreenshotPath { get; init; }
    public IDictionary<string, object> Metadata { get; init; } = new Dictionary<string, object>();
}

/// <summary>
/// Result of command validation
/// </summary>
public record ValidationResult
{
    public bool IsValid { get; init; }
    public string Message { get; init; } = string.Empty;
    public IEnumerable<string> Errors { get; init; } = Array.Empty<string>();
    public IEnumerable<string> Warnings { get; init; } = Array.Empty<string>();
    public double Confidence { get; init; }
    public CommandComplexity Complexity { get; init; }
}

/// <summary>
/// Healing strategies available
/// </summary>
public enum HealingStrategy
{
    None,
    RetryWithDelay,
    AlternativeSelector,
    NavigationCorrection,
    WaitForElement,
    ScrollToElement,
    AlternativeAction,
    PageRefresh,
    BrowserRestart
}

/// <summary>
/// Command complexity levels
/// </summary>
public enum CommandComplexity
{
    Simple,
    Medium,
    Complex,
    VeryComplex
}