namespace Automation.Contracts;

/// <summary>
/// Defines the contract for managing resource pools
/// </summary>
/// <typeparam name="T">The type of resource to manage</typeparam>
public interface IResourcePool<T> : IDisposable where T : IDisposable
{
    /// <summary>
    /// Acquires a resource from the pool
    /// </summary>
    /// <param name="timeout">Maximum time to wait for a resource</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The acquired resource</returns>
    Task<T> AcquireAsync(TimeSpan? timeout = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Releases a resource back to the pool
    /// </summary>
    /// <param name="resource">The resource to release</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the release operation</returns>
    Task ReleaseAsync(T resource, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets metrics about the pool's performance
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Pool metrics</returns>
    Task<PoolMetrics> GetMetricsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Metrics about a resource pool's performance
/// </summary>
public record PoolMetrics
{
    public int TotalResources { get; init; }
    public int AvailableResources { get; init; }
    public int AcquiredResources { get; init; }
    public int PendingAcquisitions { get; init; }
    public TimeSpan AverageAcquisitionTime { get; init; }
    public DateTime LastAcquisitionTime { get; init; }
    public int TotalAcquisitions { get; init; }
    public int FailedAcquisitions { get; init; }
    
    public double UtilizationRate => TotalResources > 0 ? (double)AcquiredResources / TotalResources : 0.0;
    public double SuccessRate => TotalAcquisitions > 0 ? (double)(TotalAcquisitions - FailedAcquisitions) / TotalAcquisitions : 0.0;
}

/// <summary>
/// Factory for creating resource pools
/// </summary>
public interface IResourcePoolFactory
{
    /// <summary>
    /// Creates a resource pool of the specified type
    /// </summary>
    /// <typeparam name="T">The type of resource</typeparam>
    /// <param name="poolOptions">Configuration for the pool</param>
    /// <returns>A new resource pool</returns>
    IResourcePool<T> CreatePool<T>(ResourcePoolOptions poolOptions) where T : IDisposable;
}

/// <summary>
/// Configuration options for resource pools
/// </summary>
public record ResourcePoolOptions
{
    public int MinSize { get; init; } = 1;
    public int MaxSize { get; init; } = Environment.ProcessorCount;
    public TimeSpan DefaultTimeout { get; init; } = TimeSpan.FromSeconds(30);
    public TimeSpan ResourceLifetime { get; init; } = TimeSpan.FromMinutes(10);
    public bool EnableMetrics { get; init; } = true;
    public string? PoolName { get; init; }
}