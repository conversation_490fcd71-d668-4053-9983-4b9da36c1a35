namespace Automation.Contracts;

/// <summary>
/// Defines the contract for task executors that can process automation actions
/// </summary>
public interface ITaskExecutor : IDisposable
{
    /// <summary>
    /// Gets the type of executor (e.g., "web", "mobile")
    /// </summary>
    string ExecutorType { get; }
    
    /// <summary>
    /// Executes a collection of automation actions
    /// </summary>
    /// <param name="actions">The actions to execute</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The result of the execution</returns>
    Task<TaskResult> ExecuteAsync(IEnumerable<object> actions, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Checks if the executor is healthy and ready to process tasks
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if healthy, false otherwise</returns>
    Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets metrics about the executor's performance
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Executor metrics</returns>
    Task<ExecutorMetrics> GetMetricsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Metrics about an executor's performance
/// </summary>
public record ExecutorMetrics
{
    public int TotalExecutions { get; init; }
    public int SuccessfulExecutions { get; init; }
    public int FailedExecutions { get; init; }
    public TimeSpan AverageExecutionTime { get; init; }
    public DateTime LastExecutionTime { get; init; }
    public bool IsHealthy { get; init; }
    public IDictionary<string, object> AdditionalMetrics { get; init; } = new Dictionary<string, object>();
    
    public double SuccessRate => TotalExecutions > 0 ? (double)SuccessfulExecutions / TotalExecutions : 0.0;
}