namespace Automation.Contracts;

/// <summary>
/// Defines the contract for performance monitoring
/// </summary>
public interface IPerformanceMonitor
{
    /// <summary>
    /// Records a metric value
    /// </summary>
    /// <param name="name">The metric name</param>
    /// <param name="value">The metric value</param>
    /// <param name="tags">Optional tags for categorization</param>
    /// <param name="timestamp">Optional timestamp, defaults to current time</param>
    void RecordMetric(string name, double value, IDictionary<string, object>? tags = null, DateTime? timestamp = null);
    
    /// <summary>
    /// Increments a counter metric
    /// </summary>
    /// <param name="name">The counter name</param>
    /// <param name="increment">The amount to increment by</param>
    /// <param name="tags">Optional tags for categorization</param>
    void IncrementCounter(string name, long increment = 1, IDictionary<string, object>? tags = null);
    
    /// <summary>
    /// Records the duration of an operation
    /// </summary>
    /// <param name="name">The operation name</param>
    /// <param name="duration">The operation duration</param>
    /// <param name="tags">Optional tags for categorization</param>
    void RecordDuration(string name, TimeSpan duration, IDictionary<string, object>? tags = null);
    
    /// <summary>
    /// Checks the health of the monitoring system
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Health status</returns>
    Task<HealthStatus> CheckHealthAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets aggregated metrics for a specified time period
    /// </summary>
    /// <param name="metricName">The metric name</param>
    /// <param name="from">Start time</param>
    /// <param name="to">End time</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Aggregated metrics</returns>
    Task<AggregatedMetrics> GetAggregatedMetricsAsync(string metricName, DateTime from, DateTime to, CancellationToken cancellationToken = default);
}

/// <summary>
/// Represents the health status of a component
/// </summary>
public record HealthStatus
{
    public bool IsHealthy { get; init; }
    public string Status { get; init; } = string.Empty;
    public string? Description { get; init; }
    public DateTime CheckedAt { get; init; } = DateTime.UtcNow;
    public TimeSpan ResponseTime { get; init; }
    public IDictionary<string, object> Data { get; init; } = new Dictionary<string, object>();
}

/// <summary>
/// Aggregated metrics for a time period
/// </summary>
public record AggregatedMetrics
{
    public string MetricName { get; init; } = string.Empty;
    public DateTime FromTime { get; init; }
    public DateTime ToTime { get; init; }
    public double Average { get; init; }
    public double Minimum { get; init; }
    public double Maximum { get; init; }
    public double Sum { get; init; }
    public long Count { get; init; }
    public double StandardDeviation { get; init; }
    public double Percentile95 { get; init; }
    public double Percentile99 { get; init; }
}