using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Automation.Contracts;

namespace Automation.DependencyInjection;

/// <summary>
/// Extensions for F# interoperability and service registration
/// </summary>
public static class FSharpInteropExtensions
{
    /// <summary>
    /// Registers a F# function as a service factory
    /// </summary>
    /// <typeparam name="TService">The service type</typeparam>
    /// <param name="services">The service collection</param>
    /// <param name="factory">F# factory function</param>
    /// <param name="lifetime">Service lifetime</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddFSharpService<TService>(
        this IServiceCollection services,
        Func<IServiceProvider, TService> factory,
        ServiceLifetime lifetime = ServiceLifetime.Singleton)
        where TService : class
    {
        var descriptor = new ServiceDescriptor(typeof(TService), factory, lifetime);
        services.Add(descriptor);
        return services;
    }

    /// <summary>
    /// Registers a F# function as a service factory with interface
    /// </summary>
    /// <typeparam name="TInterface">The interface type</typeparam>
    /// <typeparam name="TImplementation">The implementation type</typeparam>
    /// <param name="services">The service collection</param>
    /// <param name="factory">F# factory function</param>
    /// <param name="lifetime">Service lifetime</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddFSharpService<TInterface, TImplementation>(
        this IServiceCollection services,
        Func<IServiceProvider, TImplementation> factory,
        ServiceLifetime lifetime = ServiceLifetime.Singleton)
        where TInterface : class
        where TImplementation : class, TInterface
    {
        var descriptor = new ServiceDescriptor(typeof(TInterface), factory, lifetime);
        services.Add(descriptor);
        return services;
    }

    /// <summary>
    /// Creates a wrapper for F# async functions to be used in C# services
    /// </summary>
    /// <typeparam name="T">The return type</typeparam>
    /// <param name="fsharpAsyncFunc">F# async function</param>
    /// <returns>Task-based wrapper</returns>
    public static Func<Task<T>> WrapFSharpAsync<T>(Func<Microsoft.FSharp.Control.FSharpAsync<T>> fsharpAsyncFunc)
    {
        return () => Microsoft.FSharp.Control.FSharpAsync.StartAsTask(
            fsharpAsyncFunc(),
            Microsoft.FSharp.Core.FSharpOption<System.Threading.Tasks.TaskCreationOptions>.None,
            Microsoft.FSharp.Core.FSharpOption<CancellationToken>.None);
    }

    /// <summary>
    /// Creates a wrapper for F# async functions with parameters
    /// </summary>
    /// <typeparam name="TParam">The parameter type</typeparam>
    /// <typeparam name="TResult">The return type</typeparam>
    /// <param name="fsharpAsyncFunc">F# async function</param>
    /// <returns>Task-based wrapper</returns>
    public static Func<TParam, Task<TResult>> WrapFSharpAsync<TParam, TResult>(
        Func<TParam, Microsoft.FSharp.Control.FSharpAsync<TResult>> fsharpAsyncFunc)
    {
        return param => Microsoft.FSharp.Control.FSharpAsync.StartAsTask(
            fsharpAsyncFunc(param),
            Microsoft.FSharp.Core.FSharpOption<System.Threading.Tasks.TaskCreationOptions>.None,
            Microsoft.FSharp.Core.FSharpOption<CancellationToken>.None);
    }

    /// <summary>
    /// Registers a logger factory that can be used by F# modules
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddFSharpLogging(this IServiceCollection services)
    {
        services.AddSingleton<Func<string, ILogger>>(provider =>
        {
            var loggerFactory = provider.GetRequiredService<ILoggerFactory>();
            return categoryName => loggerFactory.CreateLogger(categoryName);
        });

        return services;
    }

    /// <summary>
    /// Registers a service resolver function for F# modules
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddFSharpServiceResolver(this IServiceCollection services)
    {
        services.AddSingleton<Func<Type, object>>(provider =>
        {
            return serviceType => provider.GetRequiredService(serviceType);
        });

        // Generic service resolver will be added per type as needed

        return services;
    }
}

/// <summary>
/// Helper class for creating F# compatible service wrappers
/// </summary>
public static class FSharpServiceWrapper
{
    /// <summary>
    /// Creates a wrapper for F# task executor
    /// </summary>
    /// <param name="fsharpExecutor">F# task executor function</param>
    /// <param name="logger">Logger instance</param>
    /// <param name="executorType">Type of executor</param>
    /// <returns>C# compatible task executor</returns>
    public static ITaskExecutor CreateTaskExecutorWrapper(
        Func<IEnumerable<object>, Microsoft.FSharp.Control.FSharpAsync<TaskResult>> fsharpExecutor,
        ILogger logger,
        string executorType = "fsharp")
    {
        return new FSharpTaskExecutorWrapper(fsharpExecutor, logger, executorType);
    }
}

/// <summary>
/// Wrapper implementation for F# task executor
/// </summary>
internal class FSharpTaskExecutorWrapper : ITaskExecutor
{
    private readonly Func<IEnumerable<object>, Microsoft.FSharp.Control.FSharpAsync<TaskResult>> _fsharpExecutor;
    private readonly ILogger _logger;
    private readonly string _executorType;
    private int _totalExecutions = 0;
    private int _successfulExecutions = 0;
    private int _failedExecutions = 0;
    private DateTime _lastExecutionTime = DateTime.MinValue;
    private readonly List<TimeSpan> _executionTimes = new();
    private bool _disposed = false;

    public string ExecutorType => _executorType;

    public FSharpTaskExecutorWrapper(
        Func<IEnumerable<object>, Microsoft.FSharp.Control.FSharpAsync<TaskResult>> fsharpExecutor,
        ILogger logger,
        string executorType)
    {
        _fsharpExecutor = fsharpExecutor;
        _logger = logger;
        _executorType = executorType;
    }

    public async Task<TaskResult> ExecuteAsync(IEnumerable<object> actions, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(FSharpTaskExecutorWrapper));

        var startTime = DateTime.UtcNow;
        Interlocked.Increment(ref _totalExecutions);

        try
        {
            _logger.LogDebug("Executing {ActionCount} actions via F# executor", actions.Count());
            var fsharpAsync = _fsharpExecutor(actions);
            var result = await Microsoft.FSharp.Control.FSharpAsync.StartAsTask(
                fsharpAsync,
                Microsoft.FSharp.Core.FSharpOption<System.Threading.Tasks.TaskCreationOptions>.None,
                Microsoft.FSharp.Core.FSharpOption<CancellationToken>.Some(cancellationToken));

            if (result.IsSuccess)
            {
                Interlocked.Increment(ref _successfulExecutions);
            }
            else
            {
                Interlocked.Increment(ref _failedExecutions);
            }

            _lastExecutionTime = DateTime.UtcNow;
            lock (_executionTimes)
            {
                _executionTimes.Add(_lastExecutionTime - startTime);
                if (_executionTimes.Count > 1000) // Keep only last 1000 execution times
                {
                    _executionTimes.RemoveAt(0);
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            Interlocked.Increment(ref _failedExecutions);
            _logger.LogError(ex, "Error executing actions via F# executor");
            return new TaskResult
            {
                IsSuccess = false,
                Error = new ActionExecutionError
                {
                    Message = ex.Message,
                    ErrorType = "FSharpExecutionError",
                    Timestamp = DateTime.UtcNow
                }
            };
        }
    }

    public Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default)
    {
        return Task.FromResult(!_disposed);
    }

    public Task<ExecutorMetrics> GetMetricsAsync(CancellationToken cancellationToken = default)
    {
        TimeSpan averageExecutionTime;
        lock (_executionTimes)
        {
            averageExecutionTime = _executionTimes.Count > 0
                ? TimeSpan.FromTicks((long)_executionTimes.Average(t => t.Ticks))
                : TimeSpan.Zero;
        }

        var metrics = new ExecutorMetrics
        {
            TotalExecutions = _totalExecutions,
            SuccessfulExecutions = _successfulExecutions,
            FailedExecutions = _failedExecutions,
            AverageExecutionTime = averageExecutionTime,
            LastExecutionTime = _lastExecutionTime,
            IsHealthy = !_disposed,
            AdditionalMetrics = new Dictionary<string, object>
            {
                ["ExecutorType"] = _executorType,
                ["IsDisposed"] = _disposed
            }
        };

        return Task.FromResult(metrics);
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
            _logger.LogDebug("F# task executor wrapper disposed");
        }
    }
}
