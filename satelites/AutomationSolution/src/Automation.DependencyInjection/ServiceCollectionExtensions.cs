extern alias CSharpUtilities;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Automation.Contracts;
using Automation.AI.Infrastructure;
using CSharpLogging = CSharpUtilities::Automation.Utilities.Logging;

namespace Automation.DependencyInjection;

/// <summary>
/// Extension methods for configuring services in the hybrid F#/C# architecture
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds all AI infrastructure services with default configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddAIInfrastructure(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        return services.AddAIInfrastructure(configuration, options => { });
    }

    /// <summary>
    /// Adds all AI infrastructure services with custom configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <param name="configureOptions">Action to configure options</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddAIInfrastructure(
        this IServiceCollection services, 
        IConfiguration configuration,
        Action<AIInfrastructureOptions> configureOptions)
    {
        // Configure options
        var options = new AIInfrastructureOptions();
        configuration.GetSection("AIInfrastructure").Bind(options);
        configureOptions(options);

        // Register options
        services.AddSingleton(Options.Create(options.ResourcePool));
        services.AddSingleton(Options.Create(options.PerformanceMonitor));

        // Register infrastructure services
        services.AddSingleton<ResourcePoolManager>();
        services.AddSingleton<IPerformanceMonitor, PerformanceMonitor>();

        return services;
    }

    /// <summary>
    /// Adds resource pool services with configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configureOptions">Action to configure resource pool options</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddResourcePool(
        this IServiceCollection services,
        Action<ResourcePoolOptions>? configureOptions = null)
    {
        var options = new ResourcePoolOptions();
        configureOptions?.Invoke(options);

        services.AddSingleton(Options.Create(options));
        services.AddSingleton<ResourcePoolManager>();

        return services;
    }

    /// <summary>
    /// Adds performance monitoring services with configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configureOptions">Action to configure performance monitor options</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddPerformanceMonitoring(
        this IServiceCollection services,
        Action<PerformanceMonitorOptions>? configureOptions = null)
    {
        var options = new PerformanceMonitorOptions();
        configureOptions?.Invoke(options);

        services.AddSingleton(Options.Create(options));
        services.AddSingleton<IPerformanceMonitor, PerformanceMonitor>();

        return services;
    }

    /// <summary>
    /// Adds F# core services (placeholder for F# service registration)
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddFSharpCore(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // This will be implemented when we integrate F# services
        // For now, this is a placeholder that can be extended
        
        // Example of how F# services would be registered:
        // services.AddSingleton<ITaskExecutor>(provider => 
        //     new FSharpTaskExecutorWrapper(/* F# implementation */));

        return services;
    }

    /// <summary>
    /// Adds utilities services (logging, retry policies, performance monitoring)
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddUtilities(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // Add Serilog logging
        CSharpLogging.LoggingConfigurationExtensions.AddSerilogLogging(services, configuration);

        // Add retry policy services
        services.AddSingleton<CSharpUtilities::Automation.Utilities.Common.RetryPolicyOptions>();
        services.AddTransient<CSharpUtilities::Automation.Utilities.Common.RetryPolicy>();

        // Initialize compatibility modules
        services.AddSingleton<Action<IServiceProvider>>(provider =>
            serviceProvider =>
            {
                var logger = serviceProvider.GetRequiredService<ILogger>();
                Automation.Utilities.Compatibility.Say.Initialize(logger);

                var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
                CSharpLogging.LoggerFactory.Initialize(loggerFactory);
            });

        return services;
    }

    /// <summary>
    /// Adds all automation services for the hybrid architecture
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddAutomationServices(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        return services
            .AddUtilities(configuration)
            .AddAIInfrastructure(configuration)
            .AddFSharpCore(configuration);
    }
}

/// <summary>
/// Configuration options for AI infrastructure
/// </summary>
public class AIInfrastructureOptions
{
    public ResourcePoolOptions ResourcePool { get; set; } = new();
    public PerformanceMonitorOptions PerformanceMonitor { get; set; } = new();
}

/// <summary>
/// Configuration options for performance monitoring
/// </summary>
public class PerformanceMonitorOptions
{
    public bool EnableDebugLogging { get; set; } = false;
    public int FlushIntervalSeconds { get; set; } = 30;
    public int MaxMetricsInMemory { get; set; } = 10000;
    public bool EnableHealthChecks { get; set; } = true;
}
