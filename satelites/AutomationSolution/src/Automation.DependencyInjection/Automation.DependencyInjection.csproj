<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <RootNamespace>Automation.DependencyInjection</RootNamespace>
    <AssemblyName>Automation.DependencyInjection</AssemblyName>
    <Description>Dependency injection configuration helpers for F#/C# hybrid architecture</Description>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Automation.Contracts\Automation.Contracts.csproj" />
    <ProjectReference Include="..\Automation.AI.Infrastructure\Automation.AI.Infrastructure.csproj" />
    <ProjectReference Include="..\Automation.Utilities.CSharp\Automation.Utilities.CSharp.csproj">
      <Aliases>CSharpUtilities</Aliases>
    </ProjectReference>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
  </ItemGroup>

</Project>
