open System
open System.Threading
open Automation.Core
open Automation.Core.DispatcherService
open Automation.Utilities.Logging

let main () =
    try
        info "[Main] Starting Sex-D-flexible Task Dispatcher..."
        
        // Configuration
        let config = {
            RedisConnectionString = "localhost"
            InputChannel = "automation_channel"
            LearnChannel = "learn_channel"
            ReplayChannel = "replay_channel"
            EnableLogging = true
        }
        
        info (sprintf "[Main] Configuration:")
        info (sprintf "[Main]   Input Channel: %s" config.InputChannel)
        info (sprintf "[Main]   Learn Channel: %s" config.LearnChannel)
        info (sprintf "[Main]   Replay Channel: %s" config.ReplayChannel)
        info (sprintf "[Main]   Redis: %s" config.RedisConnectionString)
        
        // Start dispatcher
        let (connection, _) = startDispatcher config
        
        info "[Main] Dispatcher started successfully!"
        info "[Main] Message routing logic:"
        info "[Main]   - JSON messages with mode='learn' -> learn_channel"
        info "[Main]   - JSON messages with mode='replay' -> replay_channel"  
        info "[Main]   - Plain text messages -> replay_channel (default)"
        info "[Main] Press Ctrl+C to exit."
        
        // Keep running
        Thread.Sleep(Timeout.Infinite)
        
    with
    | ex -> 
        error (sprintf "[Main] Dispatcher failed to start: %s" ex.Message)
        error (sprintf "[Main] Stack Trace: %s" ex.StackTrace)
        Environment.Exit(1)

main ()