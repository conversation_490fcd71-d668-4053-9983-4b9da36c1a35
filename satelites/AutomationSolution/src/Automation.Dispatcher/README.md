# Automation.Dispatcher

This project implements a dedicated service responsible for intelligently routing automation tasks to appropriate processing channels based on their operational mode (learn or replay). It acts as a central message broker, ensuring that tasks are directed to the correct downstream services for execution or learning.

## Features

*   **Intelligent Task Routing**: Routes incoming automation task messages to either a "learn" channel (for recording and analysis) or a "replay" channel (for execution) based on the message's content.
*   **Redis-based Messaging**: Leverages Redis Pub/Sub for efficient and scalable message queuing and distribution.
*   **Flexible Message Parsing**: Capable of parsing both structured JSON messages (containing operational mode and metadata) and plain text commands.
*   **Centralized Configuration**: Manages Redis connection settings and channel names through a clear configuration.
*   **Logging**: Provides detailed logging of message reception, parsing, and routing decisions for monitoring and debugging.

## Architecture Overview

The `Automation.Dispatcher` service is built around a simple yet effective message processing flow:

1.  **Configuration**: Loads Redis connection details and channel names (`InputChannel`, `LearnChannel`, `ReplayChannel`).
2.  **Redis Subscription**: Subscribes to a designated `InputChannel` in Redis, continuously listening for new automation task messages.
3.  **Message Handling**: Upon receiving a message:
    *   It parses the message to extract the command and determine its `OperationMode` (Learn or Replay).
    *   Based on the `OperationMode`, it identifies the correct `targetChannel` (`LearnChannel` or `ReplayChannel`).
    *   It enriches the message with routing metadata (if not already present).
    *   It publishes the message to the determined `targetChannel` in Redis.
4.  **Logging**: All key events and decisions are logged for transparency and troubleshooting.

## Usage

The `Automation.Dispatcher` is designed to run as a standalone service, acting as the entry point for automation tasks into the broader Automation Solution ecosystem.

### Running the Dispatcher

To start the dispatcher service:

```bash
dotnet run --project src/Automation.Dispatcher
```

### Message Routing Logic

The dispatcher uses the following logic to route messages:

*   **JSON messages with `mode='learn'`**: Routed to the `learn_channel`.
*   **JSON messages with `mode='replay'`**: Routed to the `replay_channel`.
*   **Plain text messages (or JSON without a `mode` field)**: Routed to the `replay_channel` by default.

### Example: Sending Messages to the Dispatcher (from another service)

```fsharp
open StackExchange.Redis
open System.Text.Json
open Automation.Core // Assuming TaskPayload and OperationMode are defined here

// Establish Redis connection
use redis = ConnectionMultiplexer.Connect("localhost")
let subscriber = redis.GetSubscriber()

// Example 1: Send a message for learning
let learnPayload = {
    Command = "Navigate to google.com and search for 'F# programming'"
    Mode = Some Automation.Core.OperationMode.Learn
    Metadata = Map.empty
}
let learnMessage = JsonSerializer.Serialize(learnPayload)
subscriber.Publish("automation_channel", learnMessage) |> ignore
printfn "Sent learn message."

// Example 2: Send a message for replay
let replayPayload = {
    Command = "Click the search button"
    Mode = Some Automation.Core.OperationMode.Replay
    Metadata = Map.ofList [("source", "manual_trigger")]
}
let replayMessage = JsonSerializer.Serialize(replayPayload)
subscriber.Publish("automation_channel", replayMessage) |> ignore
printfn "Sent replay message."

// Example 3: Send a plain text message (will default to replay)
subscriber.Publish("automation_channel", "Open browser and go to example.com") |> ignore
printfn "Sent plain text message."
```

## Configuration

The dispatcher's behavior is configured via environment variables or directly within `Program.fs` (for development):

*   `RedisConnectionString`: Connection string for the Redis server (default: `localhost`).
*   `InputChannel`: The Redis channel where the dispatcher listens for incoming tasks (default: `automation_channel`).
*   `LearnChannel`: The Redis channel where tasks in "learn" mode are forwarded (default: `learn_channel`).
*   `ReplayChannel`: The Redis channel where tasks in "replay" mode are forwarded (default: `replay_channel`).
*   `EnableLogging`: Boolean to enable/disable console logging (default: `true`).

## Dependencies

*   `StackExchange.Redis`: For Redis client functionality.
*   `Automation.Core`: Provides core types like `TaskPayload`, `OperationMode`, and `TaskDispatcher` module.
*   `Automation.Utilities`: For logging capabilities.

## Development

This project is developed in F#. It is a lightweight service designed for high throughput message routing.
