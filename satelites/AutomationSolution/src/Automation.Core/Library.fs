namespace Automation.Core

/// Represents a single action to be performed by the worker.
type Action =
    | Navigate of url: string
    | Click of selector: string
    | TypeText of selector: string * text: string
    | Screenshot of path: string
    | Tap of selector: string
    | GetText of selector: string

/// Represents an error that occurred during action execution.
type ActionExecutionError = {
    Message: string
    FailedAction: Action option
    ScreenshotPath: string option
    CurrentUrl: string option
    HtmlContent: string option
    AttemptedSelectors: string list
}

/// Represents the result of executing a series of actions.
type TaskResult =
    | Success of message: string
    | Failure of error: ActionExecutionError

/// Security policy enforcement result
type SecurityViolation =
    | AllowedSelectorViolation of selector: string
    | BannedUrlViolation of url: string
    | BannedActionViolation of action: string
    | MaxStepsViolation of currentSteps: int * maxSteps: int
    | MaxRuntimeViolation of currentRuntime: int * maxRuntime: int

type SecurityResult =
    | SecurityPass
    | SecurityFail of error: ActionExecutionError

/// Security policy configuration
type SecurityPolicy = {
    AllowedSelectors: string list
    BannedUrlPatterns: string list
    BannedActions: string list
    MaxSteps: int
    MaxRuntimeSeconds: int
}

/// Represents the mode of operation for task dispatch
type OperationMode = 
    | Learn
    | Replay

/// Represents a structured message payload for task dispatching
type TaskPayload = {
    Command: string
    Mode: OperationMode option
    Metadata: Map<string, string> option
}

/// Represents the routing destination for messages
type RoutingDestination =
    | LearnChannel
    | ReplayChannel

/// Defines the contract for any task executor (e.g., Web or Mobile).
type ITaskExecutor =
    abstract member ExecuteActions: actions: Action list -> Async<TaskResult>
    abstract member TakeScreenshot: path: string -> Async<unit>
    abstract member GetCurrentUrl: unit -> Async<string>
    abstract member GetCurrentHtmlContent: unit -> Async<string>

/// Security policy engine for validating actions before execution
module SecurityEngine =
    
    /// Default security policy
    let defaultPolicy = {
        AllowedSelectors = ["data-testid"; "aria-label"]
        BannedUrlPatterns = ["file://"; "javascript:"; "data:"]
        BannedActions = ["eval("; "script:"; "vbscript:"]
        MaxSteps = 15
        MaxRuntimeSeconds = 30
    }
    
    /// Check if a selector matches allowed patterns
    let private isAllowedSelector (policy: SecurityPolicy) (selector: string) =
        policy.AllowedSelectors 
        |> List.exists (fun pattern -> selector.Contains(pattern))
    
    /// Check if a URL contains banned patterns
    let private containsBannedPattern (policy: SecurityPolicy) (url: string) =
        policy.BannedUrlPatterns
        |> List.exists (fun pattern -> url.ToLower().Contains(pattern.ToLower()))
    
    /// Check if an action contains banned patterns
    let private containsBannedAction (policy: SecurityPolicy) (actionStr: string) =
        policy.BannedActions
        |> List.exists (fun pattern -> actionStr.ToLower().Contains(pattern.ToLower()))
    
    /// Validate a single action against security policy
    let private validateAction (policy: SecurityPolicy) (action: Action) : SecurityViolation option =
        match action with
        | Navigate url when containsBannedPattern policy url ->
            Some (BannedUrlViolation url)
        | Click selector when not (isAllowedSelector policy selector) ->
            Some (AllowedSelectorViolation selector)
        | TypeText (selector, text) when not (isAllowedSelector policy selector) ->
            Some (AllowedSelectorViolation selector)
        | TypeText (_, text) when containsBannedAction policy text ->
            Some (BannedActionViolation text)
        | Tap selector when not (isAllowedSelector policy selector) ->
            Some (AllowedSelectorViolation selector)
        | GetText selector when not (isAllowedSelector policy selector) ->
            Some (AllowedSelectorViolation selector)
        | _ -> None
    
    /// Get security violation message
    let getViolationMessage (violation: SecurityViolation) : string =
        match violation with
        | AllowedSelectorViolation selector -> 
            sprintf "Selector '%s' not in allowed list (data-testid, aria-label)" selector
        | BannedUrlViolation url -> 
            sprintf "URL '%s' contains banned patterns (file://, javascript:, data:)" url
        | BannedActionViolation action -> 
            sprintf "Action '%s' contains banned patterns (eval(), script:, vbscript:)" action
        | MaxStepsViolation (current, max) -> 
            sprintf "Too many steps: %d exceeds maximum of %d" current max
        | MaxRuntimeViolation (current, max) -> 
            sprintf "Runtime exceeded: %d seconds exceeds maximum of %d" current max

    /// Convert a SecurityViolation to an ActionExecutionError
    let toActionExecutionError (violation: SecurityViolation) : ActionExecutionError =
        { Message = getViolationMessage violation
          FailedAction = None
          ScreenshotPath = None
          CurrentUrl = None
          HtmlContent = None
          AttemptedSelectors = [] }

    /// Validate a list of actions against security policy
    let validateActions (policy: SecurityPolicy) (actions: Action list) : SecurityResult =
        // Check max steps violation
        let stepsViolation = 
            if actions.Length > policy.MaxSteps then
                Some (MaxStepsViolation (actions.Length, policy.MaxSteps))
            else None
        
        // Check individual action violations
        let actionViolations = 
            actions 
            |> List.choose (validateAction policy)
        
        // Combine all violations
        let allViolations = 
            match stepsViolation with
            | Some violation -> violation :: actionViolations
            | None -> actionViolations
        
        match allViolations with
        | [] -> SecurityPass
        | violations -> 
            let combinedMessage = 
                violations 
                |> List.map getViolationMessage 
                |> String.concat "; "
            SecurityFail { Message = combinedMessage
                           FailedAction = None
                           ScreenshotPath = None
                           CurrentUrl = None
                           HtmlContent = None
                           AttemptedSelectors = [] }

/// Task dispatcher for routing messages between learn and replay channels
module TaskDispatcher =
    
    open System.Text.Json
    
    /// Parse a message string into a TaskPayload
    let parseMessage (message: string) : TaskPayload =
        try
            // Try to parse as JSON first
            let jsonDoc = JsonDocument.Parse(message)
            let root = jsonDoc.RootElement
            
            let command = 
                try
                    root.GetProperty("command").GetString()
                with
                | _ -> message // fallback to original message
            
            let mode = 
                try
                    let modeStr = root.GetProperty("mode").GetString()
                    match modeStr.ToLower() with
                    | "learn" -> Some Learn
                    | "replay" -> Some Replay
                    | _ -> None
                with
                | _ -> None
            
            let metadata = 
                try
                    let metaElement = root.GetProperty("metadata")
                    let metaMap = 
                        metaElement.EnumerateObject()
                        |> Seq.map (fun prop -> (prop.Name, prop.Value.GetString()))
                        |> Map.ofSeq
                    Some metaMap
                with
                | _ -> None
            
            { Command = command; Mode = mode; Metadata = metadata }
        with
        | _ ->
            // If JSON parsing fails, treat as plain command message
            { Command = message; Mode = None; Metadata = None }
    
    /// Determine routing destination based on payload
    let routeMessage (payload: TaskPayload) : RoutingDestination =
        match payload.Mode with
        | Some Learn -> LearnChannel
        | Some Replay -> ReplayChannel
        | None -> ReplayChannel // Default to replay channel when mode is not specified
    
    /// Get channel name for routing destination
    let getChannelName (destination: RoutingDestination) : string =
        match destination with
        | LearnChannel -> "learn_channel"
        | ReplayChannel -> "replay_channel"
    
    /// Complete routing logic: parse message and determine destination channel
    let routeMessageToChannel (message: string) : string * TaskPayload =
        let payload = parseMessage message
        let destination = routeMessage payload
        let channelName = getChannelName destination
        (channelName, payload)
