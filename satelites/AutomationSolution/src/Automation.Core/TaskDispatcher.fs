namespace Automation.Core

open System
open StackExchange.Redis
open System.Text.Json
open Automation.Utilities.Logging

/// Main dispatcher service for routing messages between channels
module DispatcherService =
    
    /// Configuration for the dispatcher
    type DispatcherConfig = {
        RedisConnectionString: string
        InputChannel: string
        LearnChannel: string
        ReplayChannel: string
        EnableLogging: bool
    }
    
    /// Default dispatcher configuration
    let defaultConfig = {
        RedisConnectionString = "localhost"
        InputChannel = "automation_channel"
        LearnChannel = "learn_channel"
        ReplayChannel = "replay_channel"
        EnableLogging = true
    }
    
    /// Forward a message to the appropriate channel
    let private forwardMessage (connection: IConnectionMultiplexer) (targetChannel: string) (originalMessage: string) (payload: TaskPayload) : Async<bool> =
        async {
            try
                let subscriber = connection.GetSubscriber()
                
                // Create enriched message with routing metadata
                let enrichedMessage = 
                    if payload.Metadata.IsSome then
                        // Keep original message if it already has metadata
                        originalMessage
                    else
                        // Add routing metadata for tracking
                        let routingMetadata = Map.ofList [
                            ("routedAt", DateTime.UtcNow.ToString("O"))
                            ("originalChannel", defaultConfig.InputChannel)
                            ("targetChannel", targetChannel)
                        ]
                        let enrichedPayload = { payload with Metadata = Some routingMetadata }
                        JsonSerializer.Serialize(enrichedPayload)
                
                let! result = subscriber.PublishAsync(RedisChannel.Literal(targetChannel), enrichedMessage) |> Async.AwaitTask
                return result > 0L
            with
            | ex ->
                error (sprintf "[Dispatcher] Failed to forward message to %s: %s" targetChannel ex.Message)
                return false
        }
    
    /// Handle incoming message from the main automation channel
    let handleIncomingMessage (config: DispatcherConfig) (connection: IConnectionMultiplexer) (channel: RedisChannel) (message: RedisValue) : unit =
        async {
            try
                let messageStr = message.ToString()
                if config.EnableLogging then
                    info (sprintf "[Dispatcher] Received message: %s" messageStr)
                
                // Parse and route the message
                let (targetChannel, payload) = TaskDispatcher.routeMessageToChannel messageStr
                
                if config.EnableLogging then
                    let modeStr = match payload.Mode with
                                  | Some Learn -> "LEARN"
                                  | Some Replay -> "REPLAY"
                                  | None -> "REPLAY (default)"
                    info (sprintf "[Dispatcher] Routing to %s (mode: %s)" targetChannel modeStr)
                
                // Forward to appropriate channel
                let! success = forwardMessage connection targetChannel messageStr payload
                
                if success then
                    if config.EnableLogging then
                        info (sprintf "[Dispatcher] Successfully routed message to %s" targetChannel)
                else
                    error (sprintf "[Dispatcher] Failed to route message to %s" targetChannel)
                    
            with
            | ex ->
                error (sprintf "[Dispatcher] Error handling incoming message: %s" ex.Message)
        } |> Async.Start
    
    /// Start the dispatcher service
    let startDispatcher (config: DispatcherConfig) : IConnectionMultiplexer * unit =
        try
            info (sprintf "[Dispatcher] Starting dispatcher service...")
            info (sprintf "[Dispatcher] Input channel: %s" config.InputChannel)
            info (sprintf "[Dispatcher] Learn channel: %s" config.LearnChannel)
            info (sprintf "[Dispatcher] Replay channel: %s" config.ReplayChannel)
            
            let connection = ConnectionMultiplexer.Connect(config.RedisConnectionString)
            let subscriber = connection.GetSubscriber()
            
            // Subscribe to input channel and set up message handler using Literal method
            let messageHandler = handleIncomingMessage config connection
            subscriber.Subscribe(RedisChannel.Literal(config.InputChannel), messageHandler) |> ignore
            
            info (sprintf "[Dispatcher] Dispatcher listening on channel '%s'" config.InputChannel)
            (connection, ())
            
        with
        | ex ->
            error (sprintf "[Dispatcher] Failed to start dispatcher: %s" ex.Message)
            reraise()
    
    /// Create a test message with specific mode
    let createTestMessage (command: string) (mode: OperationMode option) (metadata: Map<string, string> option) : string =
        let payload = { Command = command; Mode = mode; Metadata = metadata }
        JsonSerializer.Serialize(payload)
    
    /// Test the dispatcher with sample messages
    let runDispatcherTest (config: DispatcherConfig) : Async<unit> =
        async {
            try
                info "[Dispatcher] Running dispatcher test..."
                
                use connection = ConnectionMultiplexer.Connect(config.RedisConnectionString)
                let subscriber = connection.GetSubscriber()
                
                // Test messages
                let learnMessage = createTestMessage "Navigate to google.com and search for F#" (Some Learn) None
                let replayMessage = createTestMessage "Click the search button" (Some Replay) None
                let plainMessage = "Open browser and go to example.com"
                
                info "[Dispatcher] Publishing test messages..."
                
                // Use RedisChannel.Literal for publishing
                // Publish test messages
                do! subscriber.PublishAsync(RedisChannel.Literal(config.InputChannel), learnMessage) |> Async.AwaitTask |> Async.Ignore
                do! Async.Sleep 100
                
                do! subscriber.PublishAsync(RedisChannel.Literal(config.InputChannel), replayMessage) |> Async.AwaitTask |> Async.Ignore
                do! Async.Sleep 100
                
                do! subscriber.PublishAsync(RedisChannel.Literal(config.InputChannel), plainMessage) |> Async.AwaitTask |> Async.Ignore
                do! Async.Sleep 100
                
                info "[Dispatcher] Test messages sent successfully"
                
            with
            | ex ->
                error (sprintf "[Dispatcher] Test failed: %s" ex.Message)
        }