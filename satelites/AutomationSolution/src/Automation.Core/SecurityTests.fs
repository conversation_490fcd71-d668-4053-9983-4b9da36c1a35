namespace Automation.Core.Tests

open Automation.Core

module SecurityEngineTests =
    
    /// Test case: Valid actions should pass security validation
    let testValidActionsPass () =
        let validActions = [
            Navigate "https://example.com"
            Click "[data-testid='submit-button']"
            TypeText ("[aria-label='username']", "testuser")
        ]
        
        let result = SecurityEngine.validateActions SecurityEngine.defaultPolicy validActions
        match result with
        | SecurityPass -> printfn "✓ Valid actions test passed"
        | SecurityFail _ -> printfn "✗ Valid actions test failed"
    
    /// Test case: Actions with banned URLs should fail
    let testBannedUrlFails () =
        let maliciousActions = [
            Navigate "file:///etc/passwd"
            Navigate "javascript:alert('xss')"
        ]
        
        let result = SecurityEngine.validateActions SecurityEngine.defaultPolicy maliciousActions
        match result with
        | SecurityFail error -> 
            printfn "✓ Banned URL test passed - error: %s" error.Message
        | _ -> printfn "✗ Banned URL test failed"
    
    /// Test case: Actions with disallowed selectors should fail
    let testDisallowedSelectorFails () =
        let badSelectorActions = [
            Click "#malicious-id"
            TypeText (".some-class", "text")
        ]
        
        let result = SecurityEngine.validateActions SecurityEngine.defaultPolicy badSelectorActions
        match result with
        | SecurityFail error -> 
            printfn "✓ Disallowed selector test passed - error: %s" error.Message
        | _ -> printfn "✗ Disallowed selector test failed"
    
    /// Test case: Too many steps should fail
    let testTooManyStepsFails () =
        let tooManyActions = 
            [1..20] |> List.map (fun i -> Navigate (sprintf "https://example.com/page%d" i))
        
        let result = SecurityEngine.validateActions SecurityEngine.defaultPolicy tooManyActions
        match result with
        | SecurityFail error -> 
            printfn "✓ Too many steps test passed - error: %s" error.Message
        | _ -> printfn "✗ Too many steps test failed"
    
    /// Test case: Banned action patterns should fail
    let testBannedActionPatternFails () =
        let dangerousActions = [
            TypeText ("[data-testid='input']", "eval(malicious_code())")
        ]
        
        let result = SecurityEngine.validateActions SecurityEngine.defaultPolicy dangerousActions
        match result with
        | SecurityFail error -> 
            printfn "✓ Banned action pattern test passed - error: %s" error.Message
        | _ -> printfn "✗ Banned action pattern test failed"
    
    /// Run all security tests
    let runAllTests () =
        printfn "=== Security Framework Tests ==="
        testValidActionsPass ()
        testBannedUrlFails ()
        testDisallowedSelectorFails ()
        testTooManyStepsFails ()
        testBannedActionPatternFails ()
        printfn "=== Tests completed ==="