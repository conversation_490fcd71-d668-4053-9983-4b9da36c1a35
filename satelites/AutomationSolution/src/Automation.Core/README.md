# Automation.Core

This project defines the fundamental building blocks, shared interfaces, and core utilities that underpin the entire Automation Solution. It establishes common data models and contracts, ensuring consistency and interoperability across different components like AI, Web, Mobile, and Dispatcher services.

## Key Components

### Domain Models

*   **`Action`**: Represents a single, atomic automation step (e.g., `Navigate`, `Click`, `TypeText`, `Screenshot`, `Tap`, `GetText`). This unified type allows different executors (Web, Mobile) to process a common set of instructions.
*   **`TaskResult`**: Encapsulates the outcome of executing a series of actions, indicating either `Success` with a message or `Failure` with detailed error information (`ActionExecutionError`).
*   **`ActionExecutionError`**: Provides comprehensive details about a failed action, including the error message, the specific action that failed, and optional context like screenshots, current URL, HTML content, and attempted selectors for debugging and self-healing.
*   **`SecurityPolicy`**: Defines rules and constraints for validating automation actions, including allowed UI selectors, banned URL patterns, forbidden action patterns, and limits on execution steps or runtime.
*   **`TaskPayload`**: A structured message format for dispatching automation commands, including the command itself, an `OperationMode` (Learn or Replay), and optional metadata.
*   **`OperationMode`**: Specifies whether a task is intended for `Learn` (recording/training) or `Replay` (execution) purposes.
*   **`RoutingDestination`**: Indicates the target channel (`LearnChannel` or `ReplayChannel`) for message routing within the system.

### Interfaces

*   **`ITaskExecutor`**: A crucial interface defining the contract for any component capable of executing automation actions. This abstraction allows for different implementations (e.g., web automation with Playwright, mobile automation with Appium) to be plugged into the system seamlessly.

### Core Modules

*   **`SecurityEngine`**: Implements the logic for validating automation actions against defined `SecurityPolicy` rules. It prevents malicious or unintended operations by checking selectors, URLs, action patterns, and execution limits.
*   **`TaskDispatcher`**: Provides utilities for parsing incoming messages (`TaskPayload`) and determining the appropriate `RoutingDestination` (Learn or Replay channel) based on the message's `OperationMode`.

## Features

*   **Unified Action Definition**: A common `Action` type simplifies the creation and execution of automation tasks across diverse platforms.
*   **Robust Error Reporting**: Detailed `ActionExecutionError` provides rich context for debugging and enables advanced features like AI-powered self-healing.
*   **Extensible Executor Model**: The `ITaskExecutor` interface allows for easy integration of new automation technologies (e.g., desktop automation, API testing) without modifying core logic.
*   **Policy-Driven Security**: The `SecurityEngine` enforces predefined rules to safeguard the automation environment from potentially harmful actions or data.
*   **Intelligent Task Routing**: The `TaskDispatcher` facilitates smart message distribution to different processing pipelines based on the task's intended mode (learn or replay).

## Usage

This project serves as the foundational library for other services within the Automation Solution. It is typically referenced by projects like `Automation.AI`, `Automation.Dispatcher`, `Automation.Web`, and `Automation.Mobile` to utilize its core types, interfaces, and security mechanisms.

### Example: Defining and Validating Actions

```fsharp
open Automation.Core

// Define some actions
let actions = [
    Navigate "https://www.example.com"
    Click "[data-testid='login-button']"
    TypeText ("[aria-label='username-input']", "myuser")
    Screenshot "login_page.png"
]

// Define a custom security policy
let mySecurityPolicy = {
    SecurityEngine.defaultPolicy with
        AllowedSelectors = ["data-testid"; "aria-label"; "id"] // Allow more selectors
        MaxSteps = 10 // Limit to 10 steps per task
}

// Validate actions against the policy
let validationResult = SecurityEngine.validateActions mySecurityPolicy actions

match validationResult with
| SecurityPass ->
    printfn "Actions passed security validation. Ready for execution."
    // Proceed to execute actions using an ITaskExecutor
| SecurityFail error ->
    printfn $"Security validation failed: {error.Message}"
    // Handle the security violation, e.g., log, alert, or stop execution
```

## Dependencies

*   `StackExchange.Redis`: Used for Redis-based task queue integration and message routing.
*   `System.Text.Json`: For efficient JSON serialization and deserialization of task payloads.
*   `Automation.Utilities`: Provides shared utility functions, especially for logging.

## Development

This project is developed in F#. It focuses on defining stable and reusable core components. Changes here often impact other dependent projects, so thorough testing is crucial. Refer to `SecurityTests.fs` for examples of unit tests for the `SecurityEngine`.
