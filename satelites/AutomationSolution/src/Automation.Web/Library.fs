namespace Automation.Web

open System
open System.Threading.Tasks
open Microsoft.Playwright
open Automation.Core
open Automation.Utilities.Logging

module WebAutomator =

    let private executeSingleAction (page: IPage) (action: Action) : Task<unit> =
        task {
            match action with
            | Navigate url ->
                let! _ = page.GotoAsync(url)
                return ()
            | Click selector -> do! page.ClickAsync(selector)
            | TypeText (selector, text) -> do! page.FillAsync(selector, text)
            | Screenshot path ->
                let! _ = page.ScreenshotAsync(PageScreenshotOptions(Path = path))
                return ()
            | Tap selector ->
                // Fallback to Click for tap action
                do! page.ClickAsync(selector)
            | GetText selector ->
                let! text = page.InnerTextAsync(selector)
                info (sprintf "[WebAutomator] GetText '%s' -> '%s'" selector text)
        }

    type WebTaskExecutor() =
        let mutable playwright: IPlaywright = null
        let mutable browser: IBrowser = null
        let mutable page: IPage = null

        do
            playwright <- Playwright.CreateAsync().Result
            browser <- playwright.Chromium.LaunchAsync(BrowserTypeLaunchOptions(Headless = true)).Result
            page <- browser.NewPageAsync().Result

        interface IDisposable with
            member this.Dispose() =
                if page <> null then
                    page.CloseAsync() |> ignore
                if browser <> null then
                    browser.CloseAsync() |> ignore
                if playwright <> null then
                    playwright.Dispose()

        interface ITaskExecutor with
            member this.ExecuteActions(actions) =
                task {
                    try
                        for action in actions do
                            do! executeSingleAction page action
                        
                        return Success($"Successfully executed {List.length actions} web actions.")
                    with ex ->
                        let failedAction = 
                            if actions.Length > 0 then Some (List.last actions) else None
                        
                        let! screenshotPath =
                            task {
                                try
                                    let path = $"error_screenshot_{Guid.NewGuid()}.png"
                                    let! _ = page.ScreenshotAsync(PageScreenshotOptions(Path = path))
                                    return Some path
                                with _ ->
                                    return None
                            }

                        let! currentUrl =
                            task {
                                try
                                    return Some page.Url
                                with _ ->
                                    return None
                            }

                        let! htmlContent =
                            task {
                                try
                                    let! content = page.ContentAsync()
                                    return Some content
                                with _ ->
                                    return None
                            }

                        return Failure({ Message = ex.Message
                                         FailedAction = failedAction
                                         ScreenshotPath = screenshotPath
                                         CurrentUrl = currentUrl
                                         HtmlContent = htmlContent
                                         AttemptedSelectors = [] })
                } |> Async.AwaitTask

            member _.TakeScreenshot(path) =
                async {
                    let! _ = page.ScreenshotAsync(PageScreenshotOptions(Path = path)) |> Async.AwaitTask
                    return ()
                }
            member _.GetCurrentUrl() =
                async { 
                    return page.Url
                }
            member _.GetCurrentHtmlContent() =
                async { 
                    return page.ContentAsync().Result
                }