# Automation.Web

This project provides robust web automation capabilities for the Automation Solution, leveraging the Playwright library for reliable and cross-browser interactions. It implements the core `ITaskExecutor` interface, allowing the system to perform automated actions within web browsers.

## Features

*   **Cross-Browser Automation**: Utilizes Playwright to support automation across various modern web browsers, including Chromium (for Chrome/Edge), Firefox, and WebKit (for Safari).
*   **Action Execution**: Implements the `ITaskExecutor` interface from `Automation.Core`, enabling the execution of a defined set of actions such as `Navigate`, `Click`, `TypeText`, `Screenshot`, `Tap`, and `GetText` within a web context.
*   **Headless and Headed Modes**: Supports running browser automation in both headless (background) and headed (visible UI) modes, offering flexibility for different testing and automation scenarios.
*   **Screenshot Capture**: Provides functionality to capture screenshots of web pages at any point during automation, useful for visual validation and debugging.
*   **Error Handling & Context**: Captures detailed error information, including screenshots, current URL, and HTML content, when an action fails. This rich context is crucial for debugging and can be used by AI-powered self-healing mechanisms.
*   **Resource Management**: Manages the lifecycle of Playwright browser instances and pages, ensuring proper disposal of resources.

## Components

*   **`WebAutomator` Module**: The main module containing the `WebTaskExecutor` type.
    *   **`executeSingleAction` function**: A private helper function that translates a generic `Action` into a specific Playwright browser operation (e.g., `page.ClickAsync`, `page.FillAsync`).
    *   **`WebTaskExecutor` Type**: Implements the `ITaskExecutor` interface. It initializes a Playwright browser instance (Chromium by default, in headless mode) and provides methods to execute a list of actions, take screenshots, get the current URL, and retrieve the page's HTML content. It also includes comprehensive error handling to capture context on failure.

## Usage

`Automation.Web` is primarily used by the `Automation.Worker` service to perform web automation tasks. It abstracts the complexities of browser interaction, providing a clean interface for the automation pipeline.

### Example: Executing Web Automation Actions

```fsharp
open System
open Automation.Core // For Action and TaskResult types
open Automation.Web.WebAutomator

// Create an instance of the web task executor
use webExecutor = new WebTaskExecutor() :> ITaskExecutor

// Define a list of actions to perform
let actionsToExecute = [
    Navigate "https://www.google.com"
    TypeText ("textarea[name='q']", "F# programming language") // Type into search bar
    Click ("input[name='btnK']") // Click search button
    Screenshot "google_search_results.png" // Take a screenshot
    GetText ("#searchform") // Get text from an element
]

// Execute the actions
let! result = webExecutor.ExecuteActions(actionsToExecute)

match result with
| Success message ->
    printfn $"Web automation successful: {message}"
| Failure error ->
    printfn $"Web automation failed: {error.Message}"
    error.ScreenshotPath |> Option.iter (fun path -> printfn $"Error screenshot saved to: {path}")
    error.CurrentUrl |> Option.iter (fun url -> printfn $"Current URL: {url}")
    error.HtmlContent |> Option.iter (fun html -> printfn $"HTML Content (partial): {html.Substring(0, Math.Min(500, html.Length))}...")
```

## Supported Browsers

*   **Chromium**: Used by default for Chrome and Microsoft Edge automation.
*   **Firefox**: Can be configured.
*   **WebKit**: Can be configured for Safari automation.

## Configuration

Currently, the browser launch options (e.g., `Headless`) are hardcoded within `WebTaskExecutor`. For production use, these would typically be configurable via `appsettings.json` or environment variables.

## Dependencies

*   `Microsoft.Playwright`: The official Playwright .NET library.
*   `Automation.Core`: Provides the core `Action` types and `ITaskExecutor` interface.
*   `Automation.Utilities`: For logging capabilities.

## Development

This project is developed in F#. When extending or modifying, ensure that Playwright's asynchronous APIs are correctly handled (`Async.AwaitTask` or `task { ... }`).
