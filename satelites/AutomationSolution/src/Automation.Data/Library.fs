﻿namespace Automation.Data

open System
open StackExchange.Redis
open Automation.Core
open System.Text.Json

/// Types for test plan caching
type TestPlan = {
    Steps: Action list
    Selectors: Map<string, string list>
    Timestamp: DateTime
    LocatorsHistory: string list
    Version: int
}

type CacheKey = {
    Organization: string
    Target: string
    Task: string
    Version: int
}

/// Redis-based cache for test plans
module TestPlanCache =
    
    /// Generate cache key in format: test:{org}:{target}:{task}:v{n}
    let private generateKey (key: <PERSON>ache<PERSON><PERSON>) : string =
        sprintf "test:%s:%s:%s:v%d" key.Organization key.Target key.Task key.Version
    
    /// Serialize test plan to JSON
    let private serializeTestPlan (plan: TestPlan) : string =
        JsonSerializer.Serialize(plan)
    
    /// Deserialize test plan from JSON
    let private deserializeTestPlan (json: string) : TestPlan option =
        try
            Some (JsonSerializer.Deserialize<TestPlan>(json))
        with
        | _ -> None
    
    /// Store a test plan in Redis cache
    let storeTestPlan (connectionString: string) (key: <PERSON><PERSON><PERSON><PERSON>) (plan: TestPlan) : Async<bool> =
        async {
            try
                use connection = ConnectionMultiplexer.Connect(connectionString)
                let db = connection.GetDatabase()
                let redisKey = generateKey key
                let serializedPlan = serializeTestPlan plan
                
                let! result = db.StringSetAsync(redisKey, serializedPlan) |> Async.AwaitTask
                return result
            with
            | _ -> return false
        }
    
    /// Retrieve a test plan from Redis cache
    let retrieveTestPlan (connectionString: string) (key: CacheKey) : Async<TestPlan option> =
        async {
            try
                use connection = ConnectionMultiplexer.Connect(connectionString)
                let db = connection.GetDatabase()
                let redisKey = generateKey key
                
                let! result = db.StringGetAsync(redisKey) |> Async.AwaitTask
                if result.HasValue then
                    return deserializeTestPlan (result.ToString())
                else
                    return None
            with
            | _ -> return None
        }
    
    /// Get the latest version number for a given org/target/task combination
    let getLatestVersion (connectionString: string) (org: string) (target: string) (task: string) : Async<int> =
        async {
            try
                use connection = ConnectionMultiplexer.Connect(connectionString)
                let db = connection.GetDatabase()
                let pattern = sprintf "test:%s:%s:%s:v*" org target task
                
                let server = connection.GetServer(connection.GetEndPoints().[0])
                let keys = server.Keys(pattern = RedisValue(pattern))
                
                let versions = 
                    keys
                    |> Seq.map (fun key -> key.ToString())
                    |> Seq.map (fun keyStr -> 
                        let parts = keyStr.Split(':')
                        if parts.Length >= 5 && parts.[4].StartsWith("v") then
                            match Int32.TryParse(parts.[4].Substring(1)) with
                            | (true, version) -> Some version
                            | _ -> None
                        else
                            None)
                    |> Seq.choose id
                    |> Seq.toList
                
                return if versions.IsEmpty then 0 else List.max versions
            with
            | _ -> return 0
        }
    
    /// Store a new version of test plan (auto-increments version)
    let storeNewVersion (connectionString: string) (org: string) (target: string) (task: string) (actions: Action list) (selectors: Map<string, string list>) (locatorsHistory: string list) : Async<int option> =
        async {
            try
                let! latestVersion = getLatestVersion connectionString org target task
                let newVersion = latestVersion + 1
                
                let plan = {
                    Steps = actions
                    Selectors = selectors
                    Timestamp = DateTime.UtcNow
                    LocatorsHistory = locatorsHistory
                    Version = newVersion
                }
                
                let key = {
                    Organization = org
                    Target = target
                    Task = task
                    Version = newVersion
                }
                
                let! success = storeTestPlan connectionString key plan
                return if success then Some newVersion else None
            with
            | _ -> return None
        }

/// Testing utilities for the cache
module TestPlanCacheTest =
    
    /// Run basic cache functionality test
    let runBasicCacheTest (connectionString: string) : Async<bool> =
        async {
            try
                printfn "[Cache Test] Starting basic cache functionality test..."
                
                // Test data
                let testActions = [
                    Navigate "https://example.com"
                    TypeText ("input[data-testid='search']", "test query")
                    Click "button[aria-label='Search']"
                ]
                
                let testSelectors = Map.ofList [
                    ("input", ["input[data-testid='search']"])
                    ("button", ["button[aria-label='Search']"])
                ]
                
                let testLocators = ["input[data-testid='search']"; "button[aria-label='Search']"]
                
                // Test storing a new version
                let! versionResult = TestPlanCache.storeNewVersion connectionString "test-org" "test-target" "test-task" testActions testSelectors testLocators
                
                match versionResult with
                | Some version ->
                    printfn $"[Cache Test] Successfully stored test plan as version {version}"
                    
                    // Test retrieving the stored plan
                    let key = { Organization = "test-org"; Target = "test-target"; Task = "test-task"; Version = version }
                    let! retrievedPlan = TestPlanCache.retrieveTestPlan connectionString key
                    
                    match retrievedPlan with
                    | Some plan ->
                        printfn $"[Cache Test] Successfully retrieved test plan version {plan.Version} with {plan.Steps.Length} steps"
                        
                        // Verify the data integrity
                        let actionsMatch = plan.Steps.Length = testActions.Length
                        let selectorsMatch = plan.Selectors.Count = testSelectors.Count
                        
                        if actionsMatch && selectorsMatch then
                            printfn "[Cache Test] ✅ Data integrity verified - all tests passed!"
                            return true
                        else
                            printfn "[Cache Test] ❌ Data integrity check failed"
                            return false
                    | None ->
                        printfn "[Cache Test] ❌ Failed to retrieve stored test plan"
                        return false
                | None ->
                    printfn "[Cache Test] ❌ Failed to store test plan"
                    return false
            with
            | ex ->
                printfn $"[Cache Test] ❌ Test failed with exception: {ex.Message}"
                return false
        }

module Say =
    let hello name =
        printfn "Hello %s" name
