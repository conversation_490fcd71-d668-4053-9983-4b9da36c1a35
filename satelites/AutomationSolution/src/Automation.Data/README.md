# Automation.Data

This project is responsible for defining data models and implementing data access strategies, primarily focusing on caching and retrieving "test plans" for the Automation Solution. It provides a Redis-based caching mechanism to store and manage automation test plans efficiently.

## Key Components

### Data Models

*   **`TestPlan`**: Represents a structured automation test plan, including a list of `Action` steps, UI selectors used, a timestamp, a history of locators, and a version number.
*   **`<PERSON><PERSON><PERSON><PERSON>`**: Defines the unique identifier for a cached test plan, composed of `Organization`, `Target`, `Task`, and `Version`.

### Core Modules

*   **`TestPlanCache`**: A Redis-based caching module for storing, retrieving, and managing versions of `TestPlan` objects. It provides functionalities such as:
    *   Generating unique cache keys.
    *   Serializing and deserializing `TestPlan` objects to/from JSON.
    *   Storing and retrieving test plans in Redis.
    *   Automatically managing versioning for test plans.
    *   Retrieving the latest version of a test plan.

## Features

*   **Redis-based Caching**: Leverages Redis for high-performance storage and retrieval of test plans.
*   **Test Plan Versioning**: Supports automatic versioning of test plans, allowing for tracking changes and retrieving specific iterations.
*   **Efficient Data Serialization**: Utilizes `System.Text.Json` for efficient JSON serialization and deserialization of complex test plan objects.
*   **Data Integrity**: Ensures that test plan data is consistently stored and retrieved.

## Usage

This project is primarily consumed by other services within the Automation Solution that need to store or retrieve structured test plan data. For instance, `Automation.AI` might use it to cache generated automation flows.

### Example: Storing and Retrieving a Test Plan

```fsharp
open System
open Automation.Core // Assuming Action type is defined here
open Automation.Data.TestPlanCache

// Assume 'connectionString' is configured for your Redis instance
let redisConnectionString = "localhost:6379"

// Define a sample test plan
let myActions = [
    Automation.Core.Action.Navigate "https://www.example.com"
    Automation.Core.Action.Click "[data-testid='submit-button']"
]

let mySelectors = Map.ofList [("button", ["button[data-testid='submit-button']"])]
let myLocatorsHistory = ["initial_locator"]

// Store a new version of the test plan
let storeResult = TestPlanCache.storeNewVersion
                    redisConnectionString
                    "my-org"
                    "web-app"
                    "login-flow"
                    myActions
                    mySelectors
                    myLocatorsHistory
                    |> Async.RunSynchronously

match storeResult with
| Some version ->
    printfn $"Successfully stored login-flow test plan as version {version}"

    // Retrieve the stored test plan
    let cacheKey = {
        Organization = "my-org"
        Target = "web-app"
        Task = "login-flow"
        Version = version
    }
    let retrievedPlan = TestPlanCache.retrieveTestPlan redisConnectionString cacheKey |> Async.RunSynchronously

    match retrievedPlan with
    | Some plan ->
        printfn $"Retrieved plan version: {plan.Version}"
        printfn $"Number of steps: {List.length plan.Steps}"
    | None ->
        printfn "Failed to retrieve the test plan."
| None ->
    printfn "Failed to store the test plan."
```

## Dependencies

*   `StackExchange.Redis`: The primary client library for interacting with Redis.
*   `System.Text.Json`: For JSON serialization and deserialization of data models.
*   `Automation.Core`: Provides core types like `Action` that are used within the `TestPlan` structure.

## Development

This project is developed in F#. It focuses on providing reliable data persistence and caching mechanisms. The `TestPlanCacheTest` module contains basic tests for the caching functionality.
