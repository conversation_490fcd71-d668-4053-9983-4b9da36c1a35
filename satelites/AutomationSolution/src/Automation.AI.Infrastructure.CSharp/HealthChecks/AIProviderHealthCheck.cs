using System.Diagnostics;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Automation.AI.Infrastructure.Http;

namespace Automation.AI.Infrastructure.HealthChecks;

/// <summary>
/// Health check for AI providers
/// </summary>
public class AIProviderHealthCheck : IHealthCheck
{
    private readonly IAIHttpClient _httpClient;
    private readonly AIProviderConfig _config;
    private readonly ILogger<AIProviderHealthCheck> _logger;

    public AIProviderHealthCheck(
        IAIHttpClient httpClient,
        AIProviderConfig config,
        ILogger<AIProviderHealthCheck> logger)
    {
        _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
        _config = config ?? throw new ArgumentNullException(nameof(config));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context, 
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogDebug("Performing health check for AI provider: {ProviderId}", _config.ProviderId);

            // Perform a lightweight health check request
            var response = await _httpClient.GetAsync("/health", cancellationToken: cancellationToken);
            
            stopwatch.Stop();

            var data = new Dictionary<string, object>
            {
                ["ProviderId"] = _config.ProviderId,
                ["ResponseTime"] = stopwatch.Elapsed.TotalMilliseconds,
                ["StatusCode"] = response.StatusCode,
                ["BaseUrl"] = _config.BaseUrl
            };

            if (response.IsSuccess)
            {
                _logger.LogDebug("Health check passed for provider: {ProviderId} in {ElapsedMs}ms", 
                    _config.ProviderId, stopwatch.ElapsedMilliseconds);

                // Check for rate limit information in headers
                if (response.Headers.TryGetValue("x-ratelimit-remaining", out var remaining) &&
                    !string.IsNullOrEmpty(remaining) &&
                    int.TryParse(remaining, out var remainingCount))
                {
                    data["RateLimitRemaining"] = remainingCount;
                }

                if (response.Headers.TryGetValue("x-ratelimit-limit", out var limit) &&
                    !string.IsNullOrEmpty(limit) &&
                    int.TryParse(limit, out var limitCount))
                {
                    data["RateLimitLimit"] = limitCount;
                }

                return HealthCheckResult.Healthy(
                    $"AI provider {_config.ProviderId} is healthy", 
                    data);
            }
            else
            {
                _logger.LogWarning("Health check failed for provider: {ProviderId} - {StatusCode}: {ErrorMessage}", 
                    _config.ProviderId, response.StatusCode, response.ErrorMessage);

                data["ErrorMessage"] = response.ErrorMessage;

                return HealthCheckResult.Unhealthy(
                    $"AI provider {_config.ProviderId} returned {response.StatusCode}: {response.ErrorMessage}",
                    data: data);
            }
        }
        catch (OperationCanceledException)
        {
            stopwatch.Stop();
            _logger.LogWarning("Health check timed out for provider: {ProviderId} after {ElapsedMs}ms", 
                _config.ProviderId, stopwatch.ElapsedMilliseconds);

            return HealthCheckResult.Unhealthy(
                $"AI provider {_config.ProviderId} health check timed out",
                data: new Dictionary<string, object>
                {
                    ["ProviderId"] = _config.ProviderId,
                    ["TimeoutMs"] = stopwatch.Elapsed.TotalMilliseconds,
                    ["ConfiguredTimeoutMs"] = _config.Timeout.TotalMilliseconds
                });
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Health check failed for provider: {ProviderId}", _config.ProviderId);

            return HealthCheckResult.Unhealthy(
                $"AI provider {_config.ProviderId} health check failed: {ex.Message}",
                ex,
                data: new Dictionary<string, object>
                {
                    ["ProviderId"] = _config.ProviderId,
                    ["ElapsedMs"] = stopwatch.Elapsed.TotalMilliseconds,
                    ["ExceptionType"] = ex.GetType().Name
                });
        }
    }
}

/// <summary>
/// Composite health check for multiple AI providers
/// </summary>
public class AIInfrastructureHealthCheck : IHealthCheck
{
    private readonly IEnumerable<AIProviderHealthCheck> _providerHealthChecks;
    private readonly ILogger<AIInfrastructureHealthCheck> _logger;

    public AIInfrastructureHealthCheck(
        IEnumerable<AIProviderHealthCheck> providerHealthChecks,
        ILogger<AIInfrastructureHealthCheck> logger)
    {
        _providerHealthChecks = providerHealthChecks ?? throw new ArgumentNullException(nameof(providerHealthChecks));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context, 
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var results = new List<HealthCheckResult>();
        var data = new Dictionary<string, object>();

        try
        {
            _logger.LogDebug("Performing composite AI infrastructure health check");

            // Run all provider health checks in parallel
            var healthCheckTasks = _providerHealthChecks.Select(async hc =>
            {
                try
                {
                    return await hc.CheckHealthAsync(context, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Provider health check failed");
                    return HealthCheckResult.Unhealthy($"Provider health check failed: {ex.Message}", ex);
                }
            });

            results.AddRange(await Task.WhenAll(healthCheckTasks));
            stopwatch.Stop();

            // Aggregate results
            var healthyCount = results.Count(r => r.Status == HealthStatus.Healthy);
            var degradedCount = results.Count(r => r.Status == HealthStatus.Degraded);
            var unhealthyCount = results.Count(r => r.Status == HealthStatus.Unhealthy);

            data["TotalProviders"] = results.Count;
            data["HealthyProviders"] = healthyCount;
            data["DegradedProviders"] = degradedCount;
            data["UnhealthyProviders"] = unhealthyCount;
            data["CheckDurationMs"] = stopwatch.Elapsed.TotalMilliseconds;

            // Add individual provider results
            for (int i = 0; i < results.Count; i++)
            {
                var result = results[i];
                data[$"Provider{i}Status"] = result.Status.ToString();
                if (result.Data != null)
                {
                    foreach (var kvp in result.Data)
                    {
                        data[$"Provider{i}{kvp.Key}"] = kvp.Value;
                    }
                }
            }

            // Determine overall health status
            if (unhealthyCount == results.Count)
            {
                return HealthCheckResult.Unhealthy(
                    "All AI providers are unhealthy",
                    data: data);
            }
            else if (unhealthyCount > 0 || degradedCount > 0)
            {
                return HealthCheckResult.Degraded(
                    $"{healthyCount}/{results.Count} AI providers are healthy",
                    data: data);
            }
            else
            {
                return HealthCheckResult.Healthy(
                    $"All {results.Count} AI providers are healthy",
                    data: data);
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "AI infrastructure health check failed");

            return HealthCheckResult.Unhealthy(
                $"AI infrastructure health check failed: {ex.Message}",
                ex,
                data: new Dictionary<string, object>
                {
                    ["ElapsedMs"] = stopwatch.Elapsed.TotalMilliseconds,
                    ["ExceptionType"] = ex.GetType().Name,
                    ["ProvidersChecked"] = results.Count
                });
        }
    }
}
