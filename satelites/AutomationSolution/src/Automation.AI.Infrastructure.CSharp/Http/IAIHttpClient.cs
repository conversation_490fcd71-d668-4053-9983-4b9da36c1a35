using System.Text.Json;

namespace Automation.AI.Infrastructure.Http;

/// <summary>
/// Interface for AI provider HTTP client
/// </summary>
public interface IAIHttpClient
{
    /// <summary>
    /// Sends a POST request to the AI provider
    /// </summary>
    /// <param name="endpoint">API endpoint</param>
    /// <param name="request">Request payload</param>
    /// <param name="headers">Additional headers</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Response content</returns>
    Task<AIHttpResponse> PostAsync(
        string endpoint, 
        object request, 
        IDictionary<string, string>? headers = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Sends a GET request to the AI provider
    /// </summary>
    /// <param name="endpoint">API endpoint</param>
    /// <param name="headers">Additional headers</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Response content</returns>
    Task<AIHttpResponse> GetAsync(
        string endpoint,
        IDictionary<string, string>? headers = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Sends a streaming POST request to the AI provider
    /// </summary>
    /// <param name="endpoint">API endpoint</param>
    /// <param name="request">Request payload</param>
    /// <param name="headers">Additional headers</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Streaming response</returns>
    IAsyncEnumerable<AIStreamResponse> PostStreamAsync(
        string endpoint,
        object request,
        IDictionary<string, string>? headers = null,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// HTTP response from AI provider
/// </summary>
public record AIHttpResponse
{
    public bool IsSuccess { get; init; }
    public int StatusCode { get; init; }
    public string Content { get; init; } = string.Empty;
    public IDictionary<string, string> Headers { get; init; } = new Dictionary<string, string>();
    public TimeSpan Duration { get; init; }
    public string? ErrorMessage { get; init; }
    public Exception? Exception { get; init; }
}

/// <summary>
/// Streaming response chunk from AI provider
/// </summary>
public record AIStreamResponse
{
    public string Content { get; init; } = string.Empty;
    public bool IsComplete { get; init; }
    public string? ErrorMessage { get; init; }
    public JsonElement? RawData { get; init; }
}

/// <summary>
/// AI provider configuration
/// </summary>
public record AIProviderConfig
{
    public string ProviderId { get; init; } = string.Empty;
    public string BaseUrl { get; init; } = string.Empty;
    public string ApiKey { get; init; } = string.Empty;
    public string? Organization { get; init; }
    public TimeSpan Timeout { get; init; } = TimeSpan.FromSeconds(30);
    public int MaxRetries { get; init; } = 3;
    public IDictionary<string, string> DefaultHeaders { get; init; } = new Dictionary<string, string>();
    public bool EnableStreaming { get; init; } = true;
}

/// <summary>
/// AI request metrics
/// </summary>
public record AIRequestMetrics
{
    public string ProviderId { get; init; } = string.Empty;
    public string Endpoint { get; init; } = string.Empty;
    public TimeSpan Duration { get; init; }
    public int StatusCode { get; init; }
    public bool IsSuccess { get; init; }
    public int? TokensUsed { get; init; }
    public decimal? Cost { get; init; }
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;
    public string? ErrorType { get; init; }
}

/// <summary>
/// Rate limit information
/// </summary>
public record RateLimitInfo
{
    public int Remaining { get; init; }
    public int Limit { get; init; }
    public DateTime ResetTime { get; init; }
    public TimeSpan RetryAfter { get; init; }
}

/// <summary>
/// AI provider health status
/// </summary>
public record AIProviderHealth
{
    public string ProviderId { get; init; } = string.Empty;
    public bool IsHealthy { get; init; }
    public TimeSpan ResponseTime { get; init; }
    public string? ErrorMessage { get; init; }
    public DateTime LastChecked { get; init; } = DateTime.UtcNow;
    public RateLimitInfo? RateLimit { get; init; }
    public IDictionary<string, object> AdditionalInfo { get; init; } = new Dictionary<string, object>();
}
