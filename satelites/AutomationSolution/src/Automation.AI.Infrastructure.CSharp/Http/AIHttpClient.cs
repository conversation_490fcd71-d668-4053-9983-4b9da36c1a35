using System.Diagnostics;
using System.Net;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Polly;
using Automation.AI.Infrastructure.Configuration;

namespace Automation.AI.Infrastructure.Http;

/// <summary>
/// HTTP client implementation for AI providers with resilience patterns
/// </summary>
public class AIHttpClient : IAIHttpClient, IDisposable
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<AIHttpClient> _logger;
    private readonly AIProviderConfig _config;
    private readonly JsonSerializerOptions _jsonOptions;
    private readonly ResiliencePipeline _resiliencePipeline;
    private bool _disposed = false;

    public AIHttpClient(
        HttpClient httpClient,
        AIProviderConfig config,
        ILogger<AIHttpClient> logger)
    {
        _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
        _config = config ?? throw new ArgumentNullException(nameof(config));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower,
            WriteIndented = false,
            DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
        };

        // Configure HTTP client
        _httpClient.BaseAddress = new Uri(_config.BaseUrl);
        _httpClient.Timeout = _config.Timeout;

        // Add default headers
        foreach (var header in _config.DefaultHeaders)
        {
            _httpClient.DefaultRequestHeaders.Add(header.Key, header.Value);
        }

        // Add API key header
        if (!string.IsNullOrEmpty(_config.ApiKey))
        {
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_config.ApiKey}");
        }

        // Configure resilience pipeline
        _resiliencePipeline = CreateResiliencePipeline();
    }

    public async Task<AIHttpResponse> PostAsync(
        string endpoint, 
        object request, 
        IDictionary<string, string>? headers = null,
        CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AIHttpClient));

        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogDebug("Sending POST request to {ProviderId} endpoint: {Endpoint}", 
                _config.ProviderId, endpoint);

            var response = await _resiliencePipeline.ExecuteAsync(async (ct) =>
            {
                var json = JsonSerializer.Serialize(request, _jsonOptions);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // Add custom headers
                if (headers != null)
                {
                    foreach (var header in headers)
                    {
                        content.Headers.Add(header.Key, header.Value);
                    }
                }

                return await _httpClient.PostAsync(endpoint, content, ct);
            }, cancellationToken);

            stopwatch.Stop();

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var responseHeaders = response.Headers.ToDictionary(h => h.Key, h => string.Join(",", h.Value));

            var result = new AIHttpResponse
            {
                IsSuccess = response.IsSuccessStatusCode,
                StatusCode = (int)response.StatusCode,
                Content = responseContent,
                Headers = responseHeaders,
                Duration = stopwatch.Elapsed
            };

            if (!response.IsSuccessStatusCode)
            {
                result = result with { ErrorMessage = $"HTTP {response.StatusCode}: {response.ReasonPhrase}" };
                _logger.LogWarning("AI provider request failed: {StatusCode} {ReasonPhrase}", 
                    response.StatusCode, response.ReasonPhrase);
            }

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "AI provider request failed for {ProviderId} endpoint: {Endpoint}", 
                _config.ProviderId, endpoint);

            return new AIHttpResponse
            {
                IsSuccess = false,
                StatusCode = 0,
                Duration = stopwatch.Elapsed,
                ErrorMessage = ex.Message,
                Exception = ex
            };
        }
    }

    public async Task<AIHttpResponse> GetAsync(
        string endpoint,
        IDictionary<string, string>? headers = null,
        CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AIHttpClient));

        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogDebug("Sending GET request to {ProviderId} endpoint: {Endpoint}", 
                _config.ProviderId, endpoint);

            var response = await _resiliencePipeline.ExecuteAsync(async (ct) =>
            {
                var request = new HttpRequestMessage(HttpMethod.Get, endpoint);

                // Add custom headers
                if (headers != null)
                {
                    foreach (var header in headers)
                    {
                        request.Headers.Add(header.Key, header.Value);
                    }
                }

                return await _httpClient.SendAsync(request, ct);
            }, cancellationToken);

            stopwatch.Stop();

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var responseHeaders = response.Headers.ToDictionary(h => h.Key, h => string.Join(",", h.Value));

            var result = new AIHttpResponse
            {
                IsSuccess = response.IsSuccessStatusCode,
                StatusCode = (int)response.StatusCode,
                Content = responseContent,
                Headers = responseHeaders,
                Duration = stopwatch.Elapsed
            };

            if (!response.IsSuccessStatusCode)
            {
                result = result with { ErrorMessage = $"HTTP {response.StatusCode}: {response.ReasonPhrase}" };
            }

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "AI provider GET request failed for {ProviderId} endpoint: {Endpoint}", 
                _config.ProviderId, endpoint);

            return new AIHttpResponse
            {
                IsSuccess = false,
                StatusCode = 0,
                Duration = stopwatch.Elapsed,
                ErrorMessage = ex.Message,
                Exception = ex
            };
        }
    }

    public async IAsyncEnumerable<AIStreamResponse> PostStreamAsync(
        string endpoint,
        object request,
        IDictionary<string, string>? headers = null,
        [System.Runtime.CompilerServices.EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(AIHttpClient));

        if (!_config.EnableStreaming)
        {
            throw new NotSupportedException("Streaming is not enabled for this provider");
        }

        _logger.LogDebug("Sending streaming POST request to {ProviderId} endpoint: {Endpoint}",
            _config.ProviderId, endpoint);

        // For now, return a simple non-streaming response
        // This can be enhanced later with proper streaming implementation
        var response = await PostAsync(endpoint, request, headers, cancellationToken);

        if (response.IsSuccess)
        {
            yield return new AIStreamResponse
            {
                Content = response.Content,
                IsComplete = true
            };
        }
        else
        {
            yield return new AIStreamResponse
            {
                ErrorMessage = response.ErrorMessage,
                IsComplete = true
            };
        }
    }



    private ResiliencePipeline CreateResiliencePipeline()
    {
        return new ResiliencePipelineBuilder()
            .AddRetry(new Polly.Retry.RetryStrategyOptions
            {
                ShouldHandle = new PredicateBuilder()
                    .Handle<HttpRequestException>()
                    .Handle<TaskCanceledException>(),
                MaxRetryAttempts = _config.MaxRetries,
                Delay = TimeSpan.FromMilliseconds(500),
                BackoffType = DelayBackoffType.Exponential,
                UseJitter = true
            })
            .AddTimeout(TimeSpan.FromSeconds(60))
            .Build();
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _httpClient?.Dispose();
            _disposed = true;
        }
    }
}
