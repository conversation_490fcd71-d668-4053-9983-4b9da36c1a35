namespace Automation.AI.Infrastructure.Configuration;

/// <summary>
/// Configuration for AI infrastructure components
/// </summary>
public class AIInfrastructureConfiguration
{
    /// <summary>
    /// HTTP client configuration
    /// </summary>
    public HttpClientConfiguration HttpClient { get; set; } = new();

    /// <summary>
    /// Caching configuration
    /// </summary>
    public CachingConfiguration Caching { get; set; } = new();

    /// <summary>
    /// Health check configuration
    /// </summary>
    public HealthCheckConfiguration HealthChecks { get; set; } = new();

    /// <summary>
    /// Rate limiting configuration
    /// </summary>
    public RateLimitingConfiguration RateLimiting { get; set; } = new();

    /// <summary>
    /// Observability configuration
    /// </summary>
    public ObservabilityConfiguration Observability { get; set; } = new();

    /// <summary>
    /// Creates default configuration
    /// </summary>
    public static AIInfrastructureConfiguration CreateDefault()
    {
        return new AIInfrastructureConfiguration();
    }
}

/// <summary>
/// HTTP client configuration
/// </summary>
public class HttpClientConfiguration
{
    /// <summary>
    /// Default timeout for HTTP requests
    /// </summary>
    public TimeSpan DefaultTimeout { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Maximum number of retry attempts
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Base delay for exponential backoff
    /// </summary>
    public TimeSpan RetryBaseDelay { get; set; } = TimeSpan.FromMilliseconds(500);

    /// <summary>
    /// Maximum delay for exponential backoff
    /// </summary>
    public TimeSpan RetryMaxDelay { get; set; } = TimeSpan.FromSeconds(10);

    /// <summary>
    /// Whether to enable jitter in retry delays
    /// </summary>
    public bool EnableJitter { get; set; } = true;

    /// <summary>
    /// Connection pool size
    /// </summary>
    public int ConnectionPoolSize { get; set; } = 100;
}

/// <summary>
/// Caching configuration
/// </summary>
public class CachingConfiguration
{
    /// <summary>
    /// Whether to enable caching
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Cache provider type
    /// </summary>
    public CacheProvider Provider { get; set; } = CacheProvider.Memory;

    /// <summary>
    /// Default cache TTL
    /// </summary>
    public TimeSpan DefaultTtl { get; set; } = TimeSpan.FromMinutes(30);

    /// <summary>
    /// Maximum cache size (for memory cache)
    /// </summary>
    public long MaxCacheSize { get; set; } = 100_000_000; // 100MB

    /// <summary>
    /// Redis connection string (if using Redis)
    /// </summary>
    public string? RedisConnectionString { get; set; }

    /// <summary>
    /// Cache key prefix
    /// </summary>
    public string KeyPrefix { get; set; } = "ai:";
}

/// <summary>
/// Cache provider types
/// </summary>
public enum CacheProvider
{
    Memory,
    Redis,
    Hybrid
}

/// <summary>
/// Health check configuration
/// </summary>
public class HealthCheckConfiguration
{
    /// <summary>
    /// Whether to enable health checks
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Health check interval
    /// </summary>
    public TimeSpan CheckInterval { get; set; } = TimeSpan.FromMinutes(1);

    /// <summary>
    /// Health check timeout
    /// </summary>
    public TimeSpan CheckTimeout { get; set; } = TimeSpan.FromSeconds(10);

    /// <summary>
    /// Whether to include detailed health information
    /// </summary>
    public bool IncludeDetails { get; set; } = true;
}

/// <summary>
/// Rate limiting configuration
/// </summary>
public class RateLimitingConfiguration
{
    /// <summary>
    /// Whether to enable rate limiting
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Maximum requests per time window
    /// </summary>
    public int MaxRequests { get; set; } = 100;

    /// <summary>
    /// Time window for rate limiting
    /// </summary>
    public TimeSpan TimeWindow { get; set; } = TimeSpan.FromMinutes(1);

    /// <summary>
    /// Whether to use sliding window
    /// </summary>
    public bool UseSlidingWindow { get; set; } = true;
}

/// <summary>
/// Observability configuration
/// </summary>
public class ObservabilityConfiguration
{
    /// <summary>
    /// Whether to enable metrics collection
    /// </summary>
    public bool EnableMetrics { get; set; } = true;

    /// <summary>
    /// Whether to enable distributed tracing
    /// </summary>
    public bool EnableTracing { get; set; } = true;

    /// <summary>
    /// Whether to enable detailed logging
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = false;

    /// <summary>
    /// Metrics collection interval
    /// </summary>
    public TimeSpan MetricsInterval { get; set; } = TimeSpan.FromSeconds(30);
}
