using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Http.Resilience;
using Microsoft.Extensions.Logging;
using Polly;
using Automation.AI.Infrastructure.Caching;
using Automation.AI.Infrastructure.Configuration;
using Automation.AI.Infrastructure.HealthChecks;
using Automation.AI.Infrastructure.Http;

namespace Automation.AI.Infrastructure.Extensions;

/// <summary>
/// Extension methods for configuring AI infrastructure services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds AI infrastructure services with default configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddAIInfrastructureServices(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        return services.AddAIInfrastructureServices(configuration, options => { });
    }

    /// <summary>
    /// Adds AI infrastructure services with custom configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <param name="configureOptions">Action to configure options</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddAIInfrastructureServices(
        this IServiceCollection services,
        IConfiguration configuration,
        Action<AIInfrastructureConfiguration> configureOptions)
    {
        // Configure options
        var options = AIInfrastructureConfiguration.CreateDefault();
        configuration.GetSection("AIInfrastructure").Bind(options);
        configureOptions(options);

        // Register configuration
        services.AddSingleton(options);
        services.AddSingleton(options.HttpClient);
        services.AddSingleton(options.Caching);
        services.AddSingleton(options.HealthChecks);

        // Add caching services
        services.AddAICaching(options.Caching);

        // Add HTTP client services
        services.AddAIHttpClients(options.HttpClient);

        // Add health checks
        if (options.HealthChecks.Enabled)
        {
            services.AddAIHealthChecks(options.HealthChecks);
        }

        return services;
    }

    /// <summary>
    /// Adds AI caching services
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="config">Caching configuration</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddAICaching(
        this IServiceCollection services,
        CachingConfiguration config)
    {
        switch (config.Provider)
        {
            case CacheProvider.Memory:
                services.AddMemoryCache(options =>
                {
                    options.SizeLimit = config.MaxCacheSize;
                });
                break;

            case CacheProvider.Redis:
                if (!string.IsNullOrEmpty(config.RedisConnectionString))
                {
                    services.AddStackExchangeRedisCache(options =>
                    {
                        options.Configuration = config.RedisConnectionString;
                    });
                }
                break;

            case CacheProvider.Hybrid:
                services.AddMemoryCache(options =>
                {
                    options.SizeLimit = config.MaxCacheSize / 2; // Split between memory and distributed
                });
                
                if (!string.IsNullOrEmpty(config.RedisConnectionString))
                {
                    services.AddStackExchangeRedisCache(options =>
                    {
                        options.Configuration = config.RedisConnectionString;
                    });
                }
                break;
        }

        services.AddSingleton<IAICacheService, AICacheService>();
        return services;
    }

    /// <summary>
    /// Adds AI HTTP client services
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="config">HTTP client configuration</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddAIHttpClients(
        this IServiceCollection services,
        HttpClientConfiguration config)
    {
        // Configure named HTTP clients for different AI providers
        services.AddHttpClient("OpenAI", client =>
        {
            client.Timeout = config.DefaultTimeout;
            client.DefaultRequestHeaders.Add("User-Agent", "AutomationSolution/1.0");
        })
        .AddStandardResilienceHandler();

        services.AddHttpClient("Anthropic", client =>
        {
            client.Timeout = config.DefaultTimeout;
            client.DefaultRequestHeaders.Add("User-Agent", "AutomationSolution/1.0");
        })
        .AddStandardResilienceHandler();

        services.AddHttpClient("Azure", client =>
        {
            client.Timeout = config.DefaultTimeout;
            client.DefaultRequestHeaders.Add("User-Agent", "AutomationSolution/1.0");
        })
        .AddStandardResilienceHandler();

        // Register AI HTTP client factory
        services.AddTransient<IAIHttpClient>(provider =>
        {
            var httpClientFactory = provider.GetRequiredService<IHttpClientFactory>();
            var logger = provider.GetRequiredService<ILogger<AIHttpClient>>();
            
            // This would typically be configured per provider
            var providerConfig = new AIProviderConfig
            {
                ProviderId = "default",
                BaseUrl = "https://api.openai.com/v1",
                Timeout = config.DefaultTimeout,
                MaxRetries = config.MaxRetryAttempts
            };

            var httpClient = httpClientFactory.CreateClient("OpenAI");
            return new AIHttpClient(httpClient, providerConfig, logger);
        });

        return services;
    }

    /// <summary>
    /// Adds AI health check services
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="config">Health check configuration</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddAIHealthChecks(
        this IServiceCollection services,
        HealthCheckConfiguration config)
    {
        var healthChecksBuilder = services.AddHealthChecks();

        // Add AI infrastructure health check
        healthChecksBuilder.AddCheck<AIInfrastructureHealthCheck>(
            "ai_infrastructure",
            HealthStatus.Degraded,
            new[] { "ai", "infrastructure" },
            config.CheckTimeout);

        // Add individual provider health checks
        services.AddTransient<AIProviderHealthCheck>();

        return services;
    }

    /// <summary>
    /// Adds AI infrastructure services for testing
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddAIInfrastructureServicesForTesting(this IServiceCollection services)
    {
        var config = new ConfigurationBuilder().Build();
        
        return services.AddAIInfrastructureServices(config, options =>
        {
            // Use memory caching for testing
            options.Caching.Provider = CacheProvider.Memory;
            options.Caching.DefaultTtl = TimeSpan.FromMinutes(5);
            
            // Shorter timeouts for testing
            options.HttpClient.DefaultTimeout = TimeSpan.FromSeconds(10);
            options.HttpClient.MaxRetryAttempts = 1;
            
            // Enable detailed logging for testing
            options.Observability.EnableDetailedLogging = true;
        });
    }


}
