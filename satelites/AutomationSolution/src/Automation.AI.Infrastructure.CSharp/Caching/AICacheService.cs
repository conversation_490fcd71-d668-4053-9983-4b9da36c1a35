using System.Diagnostics;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Automation.AI.Infrastructure.Configuration;

namespace Automation.AI.Infrastructure.Caching;

/// <summary>
/// AI cache service implementation with support for memory and distributed caching
/// </summary>
public class AICacheService : IAICacheService
{
    private readonly IMemoryCache? _memoryCache;
    private readonly IDistributedCache? _distributedCache;
    private readonly CachingConfiguration _config;
    private readonly ILogger<AICacheService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;
    
    private long _totalRequests = 0;
    private long _cacheHits = 0;
    private long _cacheMisses = 0;
    private readonly List<TimeSpan> _responseTimes = new();
    private readonly object _statsLock = new();

    public AICacheService(
        CachingConfiguration config,
        ILogger<AICacheService> logger,
        IMemoryCache? memoryCache = null,
        IDistributedCache? distributedCache = null)
    {
        _config = config ?? throw new ArgumentNullException(nameof(config));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _memoryCache = memoryCache;
        _distributedCache = distributedCache;

        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };

        if (!_config.Enabled)
        {
            _logger.LogInformation("AI caching is disabled");
        }
        else
        {
            _logger.LogInformation("AI caching enabled with provider: {Provider}", _config.Provider);
        }
    }

    public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class
    {
        if (!_config.Enabled) return null;

        var stopwatch = Stopwatch.StartNew();
        Interlocked.Increment(ref _totalRequests);

        try
        {
            var fullKey = GetFullKey(key);
            T? result = null;

            // Try memory cache first (if available and using hybrid)
            if (_memoryCache != null && (_config.Provider == CacheProvider.Memory || _config.Provider == CacheProvider.Hybrid))
            {
                result = _memoryCache.Get<T>(fullKey);
                if (result != null)
                {
                    RecordHit(stopwatch.Elapsed);
                    _logger.LogDebug("Cache hit (memory) for key: {Key}", key);
                    return result;
                }
            }

            // Try distributed cache
            if (_distributedCache != null && (_config.Provider == CacheProvider.Redis || _config.Provider == CacheProvider.Hybrid))
            {
                var cachedBytes = await _distributedCache.GetAsync(fullKey, cancellationToken);
                if (cachedBytes != null)
                {
                    var cachedJson = Encoding.UTF8.GetString(cachedBytes);
                    result = JsonSerializer.Deserialize<T>(cachedJson, _jsonOptions);
                    
                    if (result != null)
                    {
                        // Also cache in memory for faster subsequent access
                        if (_memoryCache != null && _config.Provider == CacheProvider.Hybrid)
                        {
                            var memoryOptions = new MemoryCacheEntryOptions
                            {
                                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5), // Short memory cache
                                Size = cachedBytes.Length
                            };
                            _memoryCache.Set(fullKey, result, memoryOptions);
                        }

                        RecordHit(stopwatch.Elapsed);
                        _logger.LogDebug("Cache hit (distributed) for key: {Key}", key);
                        return result;
                    }
                }
            }

            RecordMiss(stopwatch.Elapsed);
            _logger.LogDebug("Cache miss for key: {Key}", key);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error retrieving from cache for key: {Key}", key);
            RecordMiss(stopwatch.Elapsed);
            return null;
        }
    }

    public async Task SetAsync<T>(string key, T value, TimeSpan? ttl = null, CancellationToken cancellationToken = default) where T : class
    {
        if (!_config.Enabled || value == null) return;

        try
        {
            var fullKey = GetFullKey(key);
            var effectiveTtl = ttl ?? _config.DefaultTtl;
            var json = JsonSerializer.Serialize(value, _jsonOptions);
            var bytes = Encoding.UTF8.GetBytes(json);

            // Set in memory cache
            if (_memoryCache != null && (_config.Provider == CacheProvider.Memory || _config.Provider == CacheProvider.Hybrid))
            {
                var memoryOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = effectiveTtl,
                    Size = bytes.Length
                };
                _memoryCache.Set(fullKey, value, memoryOptions);
            }

            // Set in distributed cache
            if (_distributedCache != null && (_config.Provider == CacheProvider.Redis || _config.Provider == CacheProvider.Hybrid))
            {
                var distributedOptions = new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = effectiveTtl
                };
                await _distributedCache.SetAsync(fullKey, bytes, distributedOptions, cancellationToken);
            }

            _logger.LogDebug("Cached value for key: {Key} with TTL: {Ttl}", key, effectiveTtl);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error setting cache for key: {Key}", key);
        }
    }

    public async Task RemoveAsync(string key, CancellationToken cancellationToken = default)
    {
        if (!_config.Enabled) return;

        try
        {
            var fullKey = GetFullKey(key);

            // Remove from memory cache
            if (_memoryCache != null)
            {
                _memoryCache.Remove(fullKey);
            }

            // Remove from distributed cache
            if (_distributedCache != null)
            {
                await _distributedCache.RemoveAsync(fullKey, cancellationToken);
            }

            _logger.LogDebug("Removed cache entry for key: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error removing cache for key: {Key}", key);
        }
    }

    public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? ttl = null, CancellationToken cancellationToken = default) where T : class
    {
        var cached = await GetAsync<T>(key, cancellationToken);
        if (cached != null)
        {
            return cached;
        }

        var value = await factory();
        if (value != null)
        {
            await SetAsync(key, value, ttl, cancellationToken);
        }

        return value;
    }

    public async Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default)
    {
        if (!_config.Enabled) return false;

        try
        {
            var fullKey = GetFullKey(key);

            // Check memory cache first
            if (_memoryCache != null && _memoryCache.TryGetValue(fullKey, out _))
            {
                return true;
            }

            // Check distributed cache
            if (_distributedCache != null)
            {
                var cachedBytes = await _distributedCache.GetAsync(fullKey, cancellationToken);
                return cachedBytes != null;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error checking cache existence for key: {Key}", key);
            return false;
        }
    }

    public Task<CacheStatistics> GetStatisticsAsync()
    {
        lock (_statsLock)
        {
            var avgResponseTime = _responseTimes.Count > 0 
                ? TimeSpan.FromTicks((long)_responseTimes.Average(t => t.Ticks))
                : TimeSpan.Zero;

            var stats = new CacheStatistics
            {
                TotalRequests = _totalRequests,
                CacheHits = _cacheHits,
                CacheMisses = _cacheMisses,
                AverageResponseTime = avgResponseTime,
                AdditionalMetrics = new Dictionary<string, object>
                {
                    ["Provider"] = _config.Provider.ToString(),
                    ["Enabled"] = _config.Enabled,
                    ["DefaultTtl"] = _config.DefaultTtl.ToString()
                }
            };

            return Task.FromResult(stats);
        }
    }

    public Task ClearAsync(CancellationToken cancellationToken = default)
    {
        if (!_config.Enabled) return Task.CompletedTask;

        try
        {
            // Clear memory cache (if it's our own instance)
            if (_memoryCache is MemoryCache memCache)
            {
                memCache.Compact(1.0); // Remove all entries
            }

            // For distributed cache, we can't easily clear all entries with a prefix
            // This would need to be implemented based on the specific cache provider
            _logger.LogInformation("Cache cleared");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error clearing cache");
        }

        return Task.CompletedTask;
    }

    public string GenerateKey(string providerId, string model, string prompt, object? parameters = null)
    {
        var keyBuilder = new StringBuilder();
        keyBuilder.Append($"{providerId}:{model}:");

        // Hash the prompt for consistent key generation
        var promptHash = ComputeHash(prompt);
        keyBuilder.Append(promptHash);

        // Add parameters hash if provided
        if (parameters != null)
        {
            var parametersJson = JsonSerializer.Serialize(parameters, _jsonOptions);
            var parametersHash = ComputeHash(parametersJson);
            keyBuilder.Append($":{parametersHash}");
        }

        var key = keyBuilder.ToString();
        
        // Ensure key length doesn't exceed limits
        if (key.Length > 250)
        {
            key = ComputeHash(key);
        }

        return key;
    }

    private string GetFullKey(string key)
    {
        return $"{_config.KeyPrefix}{key}";
    }

    private void RecordHit(TimeSpan responseTime)
    {
        Interlocked.Increment(ref _cacheHits);
        RecordResponseTime(responseTime);
    }

    private void RecordMiss(TimeSpan responseTime)
    {
        Interlocked.Increment(ref _cacheMisses);
        RecordResponseTime(responseTime);
    }

    private void RecordResponseTime(TimeSpan responseTime)
    {
        lock (_statsLock)
        {
            _responseTimes.Add(responseTime);
            if (_responseTimes.Count > 1000) // Keep only last 1000 measurements
            {
                _responseTimes.RemoveAt(0);
            }
        }
    }

    private static string ComputeHash(string input)
    {
        var bytes = SHA256.HashData(Encoding.UTF8.GetBytes(input));
        return Convert.ToHexString(bytes)[..16]; // Take first 16 characters
    }
}
