namespace Automation.AI.Infrastructure.Caching;

/// <summary>
/// Interface for AI response caching service
/// </summary>
public interface IAICacheService
{
    /// <summary>
    /// Gets a cached response
    /// </summary>
    /// <param name="key">Cache key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Cached response or null if not found</returns>
    Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Sets a cached response
    /// </summary>
    /// <param name="key">Cache key</param>
    /// <param name="value">Value to cache</param>
    /// <param name="ttl">Time to live</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SetAsync<T>(string key, T value, TimeSpan? ttl = null, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Removes a cached response
    /// </summary>
    /// <param name="key">Cache key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task RemoveAsync(string key, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets or sets a cached response using a factory function
    /// </summary>
    /// <param name="key">Cache key</param>
    /// <param name="factory">Factory function to create value if not cached</param>
    /// <param name="ttl">Time to live</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Cached or newly created value</returns>
    Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? ttl = null, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Checks if a key exists in cache
    /// </summary>
    /// <param name="key">Cache key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if key exists</returns>
    Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets cache statistics
    /// </summary>
    /// <returns>Cache statistics</returns>
    Task<CacheStatistics> GetStatisticsAsync();

    /// <summary>
    /// Clears all cached items
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    Task ClearAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates a cache key for AI requests
    /// </summary>
    /// <param name="providerId">AI provider ID</param>
    /// <param name="model">Model name</param>
    /// <param name="prompt">Prompt text</param>
    /// <param name="parameters">Additional parameters</param>
    /// <returns>Generated cache key</returns>
    string GenerateKey(string providerId, string model, string prompt, object? parameters = null);
}

/// <summary>
/// Cache statistics
/// </summary>
public record CacheStatistics
{
    public long TotalRequests { get; init; }
    public long CacheHits { get; init; }
    public long CacheMisses { get; init; }
    public double HitRate => TotalRequests > 0 ? (double)CacheHits / TotalRequests * 100 : 0;
    public long TotalItems { get; init; }
    public long TotalMemoryUsage { get; init; }
    public DateTime LastUpdated { get; init; } = DateTime.UtcNow;
    public TimeSpan AverageResponseTime { get; init; }
    public IDictionary<string, object> AdditionalMetrics { get; init; } = new Dictionary<string, object>();
}

/// <summary>
/// Cache entry metadata
/// </summary>
public record CacheEntryMetadata
{
    public string Key { get; init; } = string.Empty;
    public DateTime CreatedAt { get; init; } = DateTime.UtcNow;
    public DateTime? ExpiresAt { get; init; }
    public TimeSpan? Ttl { get; init; }
    public long SizeBytes { get; init; }
    public int AccessCount { get; init; }
    public DateTime LastAccessed { get; init; } = DateTime.UtcNow;
    public string? Tags { get; init; }
}

/// <summary>
/// Cache configuration for specific AI operations
/// </summary>
public record AICacheConfig
{
    public bool EnableCaching { get; init; } = true;
    public TimeSpan DefaultTtl { get; init; } = TimeSpan.FromHours(1);
    public TimeSpan? PromptCacheTtl { get; init; } = TimeSpan.FromHours(24);
    public TimeSpan? ModelCacheTtl { get; init; } = TimeSpan.FromDays(7);
    public TimeSpan? EmbeddingCacheTtl { get; init; } = TimeSpan.FromDays(30);
    public bool CacheFailures { get; init; } = false;
    public TimeSpan FailureCacheTtl { get; init; } = TimeSpan.FromMinutes(5);
    public int MaxCacheKeyLength { get; init; } = 250;
    public bool CompressLargeValues { get; init; } = true;
    public int CompressionThreshold { get; init; } = 1024; // bytes
}
