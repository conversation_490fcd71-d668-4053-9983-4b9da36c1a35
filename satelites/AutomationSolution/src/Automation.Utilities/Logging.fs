namespace Automation.Utilities

open System
open System.IO

module Logging =
    type LogLevel =
        | Info
        | Warning
        | Error

    let private levelToString = function
        | Info -> "INFO"
        | Warning -> "WARN"
        | Error -> "ERROR"

    let private writeToFile (line:string) =
        try
            let dir = Path.Combine(Environment.CurrentDirectory, "logs")
            if not (Directory.Exists(dir)) then Directory.CreateDirectory(dir) |> ignore
            let filePath = Path.Combine(dir, DateTime.UtcNow.ToString("yyyy-MM-dd") + ".log")
            File.AppendAllText(filePath, line + Environment.NewLine)
        with _ -> ()

    let log level (message: string) =
        let timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        let line = sprintf "[%s] [%s] %s" timestamp (levelToString level) message
        printfn "%s" line
        writeToFile line

    let info msg = log Info msg
    let warn msg = log Warning msg
    let error msg = log Error msg
