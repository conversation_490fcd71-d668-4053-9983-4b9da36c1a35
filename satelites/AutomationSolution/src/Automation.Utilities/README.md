# Automation.Utilities

This project provides a collection of shared utility functions and common extensions designed to be used across various components of the Automation Solution. Its primary focus is on standardized logging and general-purpose helper functions that enhance code reusability and maintainability.

## Key Components

*   **`Logging` Module**: Implements a simple yet effective logging mechanism for the entire solution.
    *   **`LogLevel`**: Defines standard logging levels: `Info`, `Warning`, and `Error`.
    *   **`log` function**: A core function for writing log messages with a specified level, timestamp, and message content.
    *   **`info`, `warn`, `error` functions**: Convenience functions for logging messages at `Info`, `Warning`, and `Error` levels, respectively.
    *   **File-based Logging**: Automatically writes log messages to daily rotating log files within a `logs` directory (created in the application's current directory).
    *   **Console Output**: All log messages are also printed to the console.
*   **`Library` Module**: Contains general utility functions and examples of how to use the logging module.
    *   **`Say.hello` function**: A simple example demonstrating the use of the `info` logging function.

## Features

*   **Standardized Logging**: Provides a consistent way to log information, warnings, and errors across all projects in the Automation Solution.
*   **File Logging**: Automatically persists log messages to files, aiding in post-mortem analysis and long-term monitoring.
*   **Console Output**: Facilitates real-time monitoring during development and execution.
*   **Reusability**: Offers common utility functions that can be easily integrated into other F# projects within the solution.
*   **Simplicity**: Designed to be straightforward to use, minimizing boilerplate code for common tasks.

## Usage

This project is a foundational dependency for almost all other projects within the `AutomationSolution` that require logging or general utility functions.

### Example: Using the Logging Module

```fsharp
open Automation.Utilities.Logging

// Log an informational message
info "Application started successfully."

// Log a warning message
warn "Configuration file not found, using default settings."

// Log an error message
try
    failwith "An unexpected error occurred during processing."
with
| ex ->
    error $"Failed to process data: {ex.Message}"

// Example from Library.fs
open Automation.Utilities.Say
Say.hello "World" // This will log "Hello World" at Info level
```

## Configuration

The logging module's configuration is currently embedded within the `Logging.fs` file (e.g., log file directory). For more advanced logging configurations (like different sinks, log levels per module, etc.), external logging frameworks like Serilog (as hinted in the original `README.md`'s "Features" section, though not fully implemented in the provided `.fs` files) would typically be integrated.

## Dependencies

This project has no external project dependencies within the `AutomationSolution`. It relies only on standard F# and .NET libraries.

## Development

This project is developed in F#. It serves as a central place for common utilities. When adding new functionalities, ensure they are generic enough to be useful across multiple projects and adhere to the existing coding style.
