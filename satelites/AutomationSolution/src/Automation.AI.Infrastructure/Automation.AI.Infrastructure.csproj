<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <RootNamespace>Automation.AI.Infrastructure</RootNamespace>
    <AssemblyName>Automation.AI.Infrastructure</AssemblyName>
    <Description>Infrastructure services for AI processing (C# implementation)</Description>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Automation.Contracts\Automation.Contracts.csproj" />
    <ProjectReference Include="..\Automation.Core\Automation.Core.fsproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.ObjectPool" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
    <PackageReference Include="Polly" Version="8.2.0" />
    <PackageReference Include="Polly.Extensions.Http" Version="3.0.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.0" />
  </ItemGroup>

</Project>