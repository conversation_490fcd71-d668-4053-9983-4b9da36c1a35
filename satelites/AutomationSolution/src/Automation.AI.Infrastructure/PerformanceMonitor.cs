using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Automation.Contracts;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace Automation.AI.Infrastructure;

/// <summary>
/// Performance monitoring implementation
/// </summary>
public class PerformanceMonitor : IPerformanceMonitor, IDisposable
{
    private readonly ILogger<PerformanceMonitor> _logger;
    private readonly PerformanceMonitorOptions _options;
    private readonly ConcurrentDictionary<string, List<MetricEntry>> _metrics = new();
    private readonly ConcurrentDictionary<string, long> _counters = new();
    private readonly Timer _flushTimer;
    private bool _disposed = false;

    public PerformanceMonitor(ILogger<PerformanceMonitor> logger, IOptions<PerformanceMonitorOptions> options)
    {
        _logger = logger;
        _options = options.Value;
        
        // Setup periodic flush
        _flushTimer = new Timer(FlushMetrics, null, 
            TimeSpan.FromSeconds(_options.FlushIntervalSeconds), 
            TimeSpan.FromSeconds(_options.FlushIntervalSeconds));
    }

    public void RecordMetric(string name, double value, IDictionary<string, object>? tags = null, DateTime? timestamp = null)
    {
        if (string.IsNullOrEmpty(name))
        {
            _logger.LogWarning("Attempted to record metric with empty name");
            return;
        }

        var entry = new MetricEntry
        {
            Name = name,
            Value = value,
            Tags = tags ?? new Dictionary<string, object>(),
            Timestamp = timestamp ?? DateTime.UtcNow
        };

        _metrics.AddOrUpdate(name, 
            new List<MetricEntry> { entry }, 
            (key, existing) =>
            {
                lock (existing)
                {
                    existing.Add(entry);
                    // Keep only recent entries to prevent memory issues
                    if (existing.Count > _options.MaxMetricsPerName)
                    {
                        existing.RemoveAt(0);
                    }
                }
                return existing;
            });

        if (_options.EnableDebugLogging)
        {
            _logger.LogDebug("Recorded metric: {Name} = {Value}", name, value);
        }
    }

    public void IncrementCounter(string name, long increment = 1, IDictionary<string, object>? tags = null)
    {
        if (string.IsNullOrEmpty(name))
        {
            _logger.LogWarning("Attempted to increment counter with empty name");
            return;
        }

        _counters.AddOrUpdate(name, increment, (key, existing) => existing + increment);

        if (_options.EnableDebugLogging)
        {
            _logger.LogDebug("Incremented counter: {Name} by {Increment}", name, increment);
        }
    }

    public void RecordDuration(string name, TimeSpan duration, IDictionary<string, object>? tags = null)
    {
        RecordMetric(name, duration.TotalMilliseconds, tags);
    }

    public async Task<HealthStatus> CheckHealthAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // Basic health checks
            var metricsCount = _metrics.Count;
            var countersCount = _counters.Count;
            var memoryUsage = GC.GetTotalMemory(false);
            
            stopwatch.Stop();
            
            var isHealthy = metricsCount < _options.MaxMetricTypes && 
                           countersCount < _options.MaxCounterTypes;
            
            return new HealthStatus
            {
                IsHealthy = isHealthy,
                Status = isHealthy ? "Healthy" : "Unhealthy",
                Description = isHealthy ? "Performance monitor is operating normally" : "Performance monitor has exceeded limits",
                ResponseTime = stopwatch.Elapsed,
                Data = new Dictionary<string, object>
                {
                    ["MetricsCount"] = metricsCount,
                    ["CountersCount"] = countersCount,
                    ["MemoryUsage"] = memoryUsage,
                    ["FlushInterval"] = _options.FlushIntervalSeconds
                }
            };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Health check failed");
            
            return new HealthStatus
            {
                IsHealthy = false,
                Status = "Error",
                Description = $"Health check failed: {ex.Message}",
                ResponseTime = stopwatch.Elapsed,
                Data = new Dictionary<string, object>
                {
                    ["Error"] = ex.Message
                }
            };
        }
    }

    public async Task<AggregatedMetrics> GetAggregatedMetricsAsync(string metricName, DateTime from, DateTime to, CancellationToken cancellationToken = default)
    {
        if (!_metrics.TryGetValue(metricName, out var metricList))
        {
            return new AggregatedMetrics
            {
                MetricName = metricName,
                FromTime = from,
                ToTime = to
            };
        }

        List<MetricEntry> entries;
        lock (metricList)
        {
            entries = metricList
                .Where(m => m.Timestamp >= from && m.Timestamp <= to)
                .ToList();
        }

        if (entries.Count == 0)
        {
            return new AggregatedMetrics
            {
                MetricName = metricName,
                FromTime = from,
                ToTime = to
            };
        }

        var values = entries.Select(e => e.Value).ToList();
        var average = values.Average();
        
        return await Task.FromResult(new AggregatedMetrics
        {
            MetricName = metricName,
            FromTime = from,
            ToTime = to,
            Average = average,
            Minimum = values.Min(),
            Maximum = values.Max(),
            Sum = values.Sum(),
            Count = values.Count,
            StandardDeviation = CalculateStandardDeviation(values, average),
            Percentile95 = CalculatePercentile(values, 0.95),
            Percentile99 = CalculatePercentile(values, 0.99)
        });
    }

    private void FlushMetrics(object? state)
    {
        if (_disposed) return;

        try
        {
            var metricsCount = _metrics.Sum(kvp => kvp.Value.Count);
            var countersCount = _counters.Count;
            
            if (_options.EnableDebugLogging)
            {
                _logger.LogDebug("Flushing metrics: {MetricsCount} metrics, {CountersCount} counters", 
                    metricsCount, countersCount);
            }

            // In a real implementation, you might flush to external systems here
            // For now, we just log the activity
            if (metricsCount > 0 || countersCount > 0)
            {
                _logger.LogInformation("Performance Monitor Status: {MetricsCount} metrics, {CountersCount} counters", 
                    metricsCount, countersCount);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during metrics flush");
        }
    }

    private static double CalculateStandardDeviation(List<double> values, double mean)
    {
        if (values.Count < 2) return 0;
        
        var variance = values.Select(x => Math.Pow(x - mean, 2)).Average();
        return Math.Sqrt(variance);
    }

    private static double CalculatePercentile(List<double> values, double percentile)
    {
        if (values.Count == 0) return 0;
        
        var sorted = values.OrderBy(x => x).ToList();
        var index = (int)Math.Ceiling(percentile * sorted.Count) - 1;
        return sorted[Math.Max(0, Math.Min(index, sorted.Count - 1))];
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                _flushTimer?.Dispose();
                _metrics.Clear();
                _counters.Clear();
            }
            _disposed = true;
        }
    }

    public void Dispose()
    {
        Dispose(disposing: true);
        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// Configuration options for performance monitor
/// </summary>
public class PerformanceMonitorOptions
{
    public int FlushIntervalSeconds { get; set; } = 60;
    public int MaxMetricsPerName { get; set; } = 1000;
    public int MaxMetricTypes { get; set; } = 100;
    public int MaxCounterTypes { get; set; } = 50;
    public bool EnableDebugLogging { get; set; } = false;
}

/// <summary>
/// Internal metric entry structure
/// </summary>
internal class MetricEntry
{
    public string Name { get; set; } = string.Empty;
    public double Value { get; set; }
    public IDictionary<string, object> Tags { get; set; } = new Dictionary<string, object>();
    public DateTime Timestamp { get; set; }
}