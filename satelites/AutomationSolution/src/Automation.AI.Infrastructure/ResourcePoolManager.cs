using Microsoft.Extensions.ObjectPool;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Automation.Contracts;
using System.Collections.Concurrent;

namespace Automation.AI.Infrastructure;

/// <summary>
/// Manages resource pools for AI processing components
/// </summary>
public class ResourcePoolManager : IDisposable
{
    private readonly ILogger<ResourcePoolManager> _logger;
    private readonly ResourcePoolOptions _options;
    private readonly ConcurrentDictionary<string, IResourcePool<HttpClient>> _httpClientPools = new();
    private readonly ConcurrentDictionary<string, object> _genericPools = new();
    private bool _disposed = false;

    public ResourcePoolManager(ILogger<ResourcePoolManager> logger, IOptions<ResourcePoolOptions> options)
    {
        _logger = logger;
        _options = options.Value;
    }

    /// <summary>
    /// Gets an HTTP client from the specified pool
    /// </summary>
    public async Task<HttpClient> GetHttpClientAsync(string providerId, CancellationToken cancellationToken = default)
    {
        var pool = _httpClientPools.GetOrAdd(providerId, _ => CreateHttpClientPool(providerId));
        return await pool.AcquireAsync(TimeSpan.FromSeconds(30), cancellationToken);
    }

    /// <summary>
    /// Returns an HTTP client to the specified pool
    /// </summary>
    public async Task ReturnHttpClientAsync(string providerId, HttpClient client, CancellationToken cancellationToken = default)
    {
        if (_httpClientPools.TryGetValue(providerId, out var pool))
        {
            await pool.ReleaseAsync(client, cancellationToken);
        }
        else
        {
            _logger.LogWarning("Attempted to return HTTP client to unknown pool: {ProviderId}", providerId);
            client.Dispose();
        }
    }

    /// <summary>
    /// Gets metrics for all pools
    /// </summary>
    public async Task<IReadOnlyDictionary<string, PoolMetrics>> GetAllPoolMetricsAsync(CancellationToken cancellationToken = default)
    {
        var metrics = new Dictionary<string, PoolMetrics>();
        
        foreach (var kvp in _httpClientPools)
        {
            var poolMetrics = await kvp.Value.GetMetricsAsync(cancellationToken);
            metrics[kvp.Key] = poolMetrics;
        }

        return metrics;
    }

    private IResourcePool<HttpClient> CreateHttpClientPool(string providerId)
    {
        _logger.LogDebug("Creating HTTP client pool for provider: {ProviderId}", providerId);
        
        var poolOptions = new ResourcePoolOptions
        {
            MinSize = _options.MinSize,
            MaxSize = _options.MaxSize,
            DefaultTimeout = _options.DefaultTimeout,
            ResourceLifetime = _options.ResourceLifetime,
            PoolName = $"HttpClient-{providerId}"
        };

        return new HttpClientPool(providerId, poolOptions, _logger);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                foreach (var pool in _httpClientPools.Values)
                {
                    pool.Dispose();
                }
                _httpClientPools.Clear();
                _genericPools.Clear();
            }
            _disposed = true;
        }
    }

    public void Dispose()
    {
        Dispose(disposing: true);
        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// HTTP client pool implementation
/// </summary>
internal class HttpClientPool : IResourcePool<HttpClient>
{
    private readonly string _providerId;
    private readonly ResourcePoolOptions _options;
    private readonly ILogger _logger;
    private readonly ConcurrentQueue<HttpClient> _pool = new();
    private readonly ConcurrentDictionary<HttpClient, DateTime> _activeResources = new();
    private readonly SemaphoreSlim _semaphore;
    private readonly Timer _cleanupTimer;
    private bool _disposed = false;

    // Metrics
    private long _totalAcquisitions = 0;
    private long _failedAcquisitions = 0;
    private long _totalReleases = 0;

    public HttpClientPool(string providerId, ResourcePoolOptions options, ILogger logger)
    {
        _providerId = providerId;
        _options = options;
        _logger = logger;
        _semaphore = new SemaphoreSlim(options.MaxSize, options.MaxSize);
        
        // Initialize pool with minimum resources
        for (int i = 0; i < options.MinSize; i++)
        {
            _pool.Enqueue(CreateHttpClient());
        }

        // Setup cleanup timer
        _cleanupTimer = new Timer(CleanupExpiredResources, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }

    public async Task<HttpClient> AcquireAsync(TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        var effectiveTimeout = timeout ?? _options.DefaultTimeout;
        
        if (!await _semaphore.WaitAsync(effectiveTimeout, cancellationToken))
        {
            Interlocked.Increment(ref _failedAcquisitions);
            throw new TimeoutException($"Failed to acquire HTTP client for provider {_providerId} within {effectiveTimeout}");
        }

        try
        {
            HttpClient client;
            if (_pool.TryDequeue(out client))
            {
                // Check if client is still valid
                if (IsClientValid(client))
                {
                    _activeResources[client] = DateTime.UtcNow;
                    Interlocked.Increment(ref _totalAcquisitions);
                    return client;
                }
                else
                {
                    client.Dispose();
                }
            }

            // Create new client
            client = CreateHttpClient();
            _activeResources[client] = DateTime.UtcNow;
            Interlocked.Increment(ref _totalAcquisitions);
            return client;
        }
        catch
        {
            _semaphore.Release();
            Interlocked.Increment(ref _failedAcquisitions);
            throw;
        }
    }

    public async Task ReleaseAsync(HttpClient resource, CancellationToken cancellationToken = default)
    {
        if (resource == null) return;

        try
        {
            _activeResources.TryRemove(resource, out _);
            
            if (!_disposed && IsClientValid(resource))
            {
                _pool.Enqueue(resource);
                Interlocked.Increment(ref _totalReleases);
            }
            else
            {
                resource.Dispose();
            }
        }
        finally
        {
            _semaphore.Release();
        }

        await Task.CompletedTask;
    }

    public async Task<PoolMetrics> GetMetricsAsync(CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(new PoolMetrics
        {
            TotalResources = _activeResources.Count + _pool.Count,
            AvailableResources = _pool.Count,
            AcquiredResources = _activeResources.Count,
            PendingAcquisitions = _options.MaxSize - _semaphore.CurrentCount - _activeResources.Count,
            TotalAcquisitions = (int)_totalAcquisitions,
            FailedAcquisitions = (int)_failedAcquisitions,
            LastAcquisitionTime = _activeResources.Values.DefaultIfEmpty().Max()
        });
    }

    private HttpClient CreateHttpClient()
    {
        var client = new HttpClient();
        client.Timeout = TimeSpan.FromSeconds(30);
        
        // Configure for specific provider
        switch (_providerId.ToLowerInvariant())
        {
            case "openai":
                client.DefaultRequestHeaders.Add("User-Agent", "AutomationSolution/1.0");
                break;
            case "anthropic":
                client.DefaultRequestHeaders.Add("User-Agent", "AutomationSolution/1.0");
                break;
            default:
                client.DefaultRequestHeaders.Add("User-Agent", "AutomationSolution/1.0");
                break;
        }

        return client;
    }

    private bool IsClientValid(HttpClient client)
    {
        // Simple validation - in production, you might want more sophisticated checks
        return client != null && !client.IsDisposed();
    }

    private void CleanupExpiredResources(object? state)
    {
        if (_disposed) return;

        var expiredThreshold = DateTime.UtcNow - _options.ResourceLifetime;
        var expiredClients = _activeResources
            .Where(kvp => kvp.Value < expiredThreshold)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var client in expiredClients)
        {
            if (_activeResources.TryRemove(client, out _))
            {
                client.Dispose();
                _semaphore.Release();
            }
        }
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                _cleanupTimer?.Dispose();
                _semaphore?.Dispose();
                
                // Dispose all pooled resources
                while (_pool.TryDequeue(out var client))
                {
                    client.Dispose();
                }
                
                // Dispose all active resources
                foreach (var client in _activeResources.Keys)
                {
                    client.Dispose();
                }
                _activeResources.Clear();
            }
            _disposed = true;
        }
    }

    public void Dispose()
    {
        Dispose(disposing: true);
        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// Extension methods for HTTP client
/// </summary>
public static class HttpClientExtensions
{
    public static bool IsDisposed(this HttpClient client)
    {
        try
        {
            // Try to access a property that throws if disposed
            _ = client.BaseAddress;
            return false;
        }
        catch (ObjectDisposedException)
        {
            return true;
        }
    }
}