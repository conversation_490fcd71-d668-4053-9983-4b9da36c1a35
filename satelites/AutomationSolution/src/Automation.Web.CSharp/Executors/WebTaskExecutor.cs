using System.Diagnostics;
using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using Automation.Contracts;
using Automation.Web.Configuration;
using Automation.Web.ResourceManagement;

namespace Automation.Web.Executors;

/// <summary>
/// Modern C# web task executor with Playwright integration
/// </summary>
public class WebTaskExecutor : ITaskExecutor
{
    private readonly IBrowserPool _browserPool;
    private readonly WebAutomationConfiguration _configuration;
    private readonly ILogger<WebTaskExecutor> _logger;
    private readonly string _executorType = "web";
    
    private int _totalExecutions = 0;
    private int _successfulExecutions = 0;
    private int _failedExecutions = 0;
    private DateTime _lastExecutionTime = DateTime.MinValue;
    private readonly List<TimeSpan> _executionTimes = new();
    private bool _disposed = false;

    public string ExecutorType => _executorType;

    public WebTaskExecutor(
        IBrowserPool browserPool,
        WebAutomationConfiguration configuration,
        ILogger<WebTaskExecutor> logger)
    {
        _browserPool = browserPool ?? throw new ArgumentNullException(nameof(browserPool));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<TaskResult> ExecuteAsync(IEnumerable<object> actions, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(WebTaskExecutor));

        var startTime = DateTime.UtcNow;
        var stopwatch = Stopwatch.StartNew();
        Interlocked.Increment(ref _totalExecutions);

        IBrowser? browser = null;
        IPage? page = null;

        try
        {
            _logger.LogDebug("Starting web task execution with {ActionCount} actions", actions.Count());

            // Get browser from pool
            browser = await _browserPool.GetBrowserAsync(cancellationToken);
            
            // Create new page with configuration
            page = await CreateConfiguredPageAsync(browser, cancellationToken);

            var actionList = actions.ToList();
            var executedActions = new List<object>();

            foreach (var actionObj in actionList)
            {
                cancellationToken.ThrowIfCancellationRequested();

                try
                {
                    await ExecuteSingleActionAsync(page, actionObj, cancellationToken);
                    executedActions.Add(actionObj);
                    
                    _logger.LogDebug("Successfully executed action: {ActionType}", actionObj.GetType().Name);
                }
                catch (Exception actionEx)
                {
                    _logger.LogError(actionEx, "Failed to execute action: {ActionType}", actionObj.GetType().Name);
                    
                    // Create detailed error information
                    var errorInfo = await CreateErrorInfoAsync(page, actionObj, actionEx, cancellationToken);

                    Interlocked.Increment(ref _failedExecutions);
                    return TaskResult.Failure(errorInfo);
                }
            }

            stopwatch.Stop();
            _lastExecutionTime = DateTime.UtcNow;
            
            lock (_executionTimes)
            {
                _executionTimes.Add(stopwatch.Elapsed);
                if (_executionTimes.Count > 1000) // Keep only last 1000 execution times
                {
                    _executionTimes.RemoveAt(0);
                }
            }

            Interlocked.Increment(ref _successfulExecutions);
            
            _logger.LogInformation("Successfully executed {ActionCount} web actions in {ElapsedMs}ms",
                actionList.Count, stopwatch.ElapsedMilliseconds);

            return TaskResult.Success($"Successfully executed {actionList.Count} web actions.", stopwatch.Elapsed);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            Interlocked.Increment(ref _failedExecutions);
            
            _logger.LogError(ex, "Web task execution failed");

            var errorInfo = page != null 
                ? await CreateErrorInfoAsync(page, null, ex, cancellationToken)
                : new ActionExecutionError
                {
                    Message = ex.Message,
                    InnerException = ex,
                    StackTrace = ex.StackTrace,
                    OccurredAt = DateTime.UtcNow
                };

            return TaskResult.Failure(errorInfo);
        }
        finally
        {
            // Clean up resources
            if (page != null)
            {
                try
                {
                    await page.CloseAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error closing page");
                }
            }

            if (browser != null)
            {
                try
                {
                    await _browserPool.ReturnBrowserAsync(browser, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error returning browser to pool");
                }
            }
        }
    }

    public async Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) return false;

        try
        {
            // Try to get and return a browser to test pool health
            var browser = await _browserPool.GetBrowserAsync(cancellationToken);
            await _browserPool.ReturnBrowserAsync(browser, cancellationToken);
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<ExecutorMetrics> GetMetricsAsync(CancellationToken cancellationToken = default)
    {
        TimeSpan averageExecutionTime;
        lock (_executionTimes)
        {
            averageExecutionTime = _executionTimes.Count > 0 
                ? TimeSpan.FromTicks((long)_executionTimes.Average(t => t.Ticks))
                : TimeSpan.Zero;
        }

        var browserStats = _browserPool.GetStatistics();

        var metrics = new ExecutorMetrics
        {
            TotalExecutions = _totalExecutions,
            SuccessfulExecutions = _successfulExecutions,
            FailedExecutions = _failedExecutions,
            AverageExecutionTime = averageExecutionTime,
            LastExecutionTime = _lastExecutionTime,
            IsHealthy = await IsHealthyAsync(cancellationToken),
            AdditionalMetrics = new Dictionary<string, object>
            {
                ["ExecutorType"] = _executorType,
                ["BrowserPool"] = browserStats,
                ["IsDisposed"] = _disposed
            }
        };

        return metrics;
    }

    private async Task<IPage> CreateConfiguredPageAsync(IBrowser browser, CancellationToken cancellationToken)
    {
        var contextOptions = new BrowserNewContextOptions
        {
            UserAgent = _configuration.PageOptions.UserAgent,
            ExtraHTTPHeaders = _configuration.PageOptions.ExtraHeaders,
            JavaScriptEnabled = _configuration.PageOptions.JavaScriptEnabled,
            AcceptDownloads = _configuration.PageOptions.AcceptDownloads,
            IgnoreHTTPSErrors = _configuration.LaunchOptions.IgnoreHTTPSErrors
        };

        if (_configuration.PageOptions.Viewport != null)
        {
            contextOptions.ViewportSize = new Microsoft.Playwright.ViewportSize
            {
                Width = _configuration.PageOptions.Viewport.Width,
                Height = _configuration.PageOptions.Viewport.Height
            };
        }

        var context = await browser.NewContextAsync(contextOptions);

        var page = await context.NewPageAsync();
        
        // Set timeouts
        page.SetDefaultNavigationTimeout(_configuration.PageOptions.NavigationTimeoutMs);
        page.SetDefaultTimeout(_configuration.PageOptions.ActionTimeoutMs);

        return page;
    }

    private async Task ExecuteSingleActionAsync(IPage page, object actionObj, CancellationToken cancellationToken)
    {
        // This would need to be implemented based on your Action types from Automation.Contracts
        // For now, I'll create a basic implementation that handles common web actions
        
        switch (actionObj)
        {
            case NavigateAction navigate:
                await page.GotoAsync(navigate.Url, new PageGotoOptions 
                { 
                    WaitUntil = WaitUntilState.NetworkIdle 
                });
                break;
                
            case ClickAction click:
                await page.ClickAsync(click.Selector, new PageClickOptions
                {
                    Timeout = _configuration.PageOptions.ActionTimeoutMs
                });
                break;
                
            case TypeAction type:
                await page.FillAsync(type.Selector, type.Text, new PageFillOptions
                {
                    Timeout = _configuration.PageOptions.ActionTimeoutMs
                });
                break;
                
            case ScreenshotAction screenshot:
                await EnsureDirectoryExists(screenshot.Path);
                await page.ScreenshotAsync(new PageScreenshotOptions 
                { 
                    Path = screenshot.Path,
                    FullPage = true
                });
                break;
                
            default:
                throw new NotSupportedException($"Action type {actionObj.GetType().Name} is not supported");
        }
    }

    private async Task<ActionExecutionError> CreateErrorInfoAsync(
        IPage page, 
        object? failedAction, 
        Exception exception, 
        CancellationToken cancellationToken)
    {
        var error = new ActionExecutionError
        {
            Message = exception.Message,
            InnerException = exception,
            StackTrace = exception.StackTrace,
            OccurredAt = DateTime.UtcNow
        };

        if (_configuration.ErrorHandling.TakeScreenshotOnError)
        {
            try
            {
                var screenshotPath = Path.Combine(
                    _configuration.ErrorHandling.ScreenshotDirectory,
                    $"error_{DateTime.UtcNow:yyyyMMdd_HHmmss}_{Guid.NewGuid():N}.png");
                
                await EnsureDirectoryExists(screenshotPath);
                await page.ScreenshotAsync(new PageScreenshotOptions { Path = screenshotPath });

                error = error with { ScreenshotPath = screenshotPath };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to capture error screenshot");
            }
        }

        if (_configuration.ErrorHandling.CapturePageContentOnError)
        {
            try
            {
                var pageContent = await page.ContentAsync();
                error = error with { HtmlContent = pageContent, CurrentUrl = page.Url };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to capture page content");
            }
        }

        return error;
    }

    private static Task EnsureDirectoryExists(string filePath)
    {
        var directory = Path.GetDirectoryName(filePath);
        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }

        return Task.CompletedTask;
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
            _logger.LogDebug("Web task executor disposed");
        }
    }
}

// Basic action types for demonstration - these should match your actual Action types
public record NavigateAction(string Url);
public record ClickAction(string Selector);
public record TypeAction(string Selector, string Text);
public record ScreenshotAction(string Path);
