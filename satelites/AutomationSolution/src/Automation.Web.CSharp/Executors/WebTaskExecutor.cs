using System.Diagnostics;
using System.Reflection;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using Automation.Contracts;
using Automation.Web.Configuration;
using Automation.Web.ResourceManagement;

namespace Automation.Web.Executors;

/// <summary>
/// Modern C# web task executor with Playwright integration
/// </summary>
public class WebTaskExecutor : ITaskExecutor
{
    private readonly IBrowserPool _browserPool;
    private readonly WebAutomationConfiguration _configuration;
    private readonly ILogger<WebTaskExecutor> _logger;
    private readonly string _executorType = "web";
    
    private int _totalExecutions = 0;
    private int _successfulExecutions = 0;
    private int _failedExecutions = 0;
    private DateTime _lastExecutionTime = DateTime.MinValue;
    private readonly List<TimeSpan> _executionTimes = new();
    private bool _disposed = false;

    public string ExecutorType => _executorType;

    public WebTaskExecutor(
        IBrowserPool browserPool,
        WebAutomationConfiguration configuration,
        ILogger<WebTaskExecutor> logger)
    {
        _browserPool = browserPool ?? throw new ArgumentNullException(nameof(browserPool));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<TaskResult> ExecuteAsync(IEnumerable<object> actions, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(WebTaskExecutor));

        var startTime = DateTime.UtcNow;
        var stopwatch = Stopwatch.StartNew();
        Interlocked.Increment(ref _totalExecutions);

        IBrowser? browser = null;
        IPage? page = null;

        try
        {
            _logger.LogDebug("Starting web task execution with {ActionCount} actions", actions.Count());

            // Get browser from pool
            browser = await _browserPool.GetBrowserAsync(cancellationToken);
            
            // Create new page with configuration
            page = await CreateConfiguredPageAsync(browser, cancellationToken);

            var actionList = actions.ToList();
            var executedActions = new List<object>();

            foreach (var actionObj in actionList)
            {
                cancellationToken.ThrowIfCancellationRequested();

                try
                {
                    await ExecuteSingleActionAsync(page, actionObj, cancellationToken);
                    executedActions.Add(actionObj);
                    
                    _logger.LogDebug("Successfully executed action: {ActionType}", actionObj.GetType().Name);
                }
                catch (Exception actionEx)
                {
                    _logger.LogError(actionEx, "Failed to execute action: {ActionType}", actionObj.GetType().Name);
                    
                    // Create detailed error information
                    var errorInfo = await CreateErrorInfoAsync(page, actionObj, actionEx, cancellationToken);

                    Interlocked.Increment(ref _failedExecutions);
                    return TaskResult.Failure(errorInfo);
                }
            }

            stopwatch.Stop();
            _lastExecutionTime = DateTime.UtcNow;
            
            lock (_executionTimes)
            {
                _executionTimes.Add(stopwatch.Elapsed);
                if (_executionTimes.Count > 1000) // Keep only last 1000 execution times
                {
                    _executionTimes.RemoveAt(0);
                }
            }

            Interlocked.Increment(ref _successfulExecutions);
            
            _logger.LogInformation("Successfully executed {ActionCount} web actions in {ElapsedMs}ms",
                actionList.Count, stopwatch.ElapsedMilliseconds);

            return TaskResult.Success($"Successfully executed {actionList.Count} web actions.", stopwatch.Elapsed);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            Interlocked.Increment(ref _failedExecutions);
            
            _logger.LogError(ex, "Web task execution failed");

            var errorInfo = page != null 
                ? await CreateErrorInfoAsync(page, null, ex, cancellationToken)
                : new ActionExecutionError
                {
                    Message = ex.Message,
                    InnerException = ex,
                    StackTrace = ex.StackTrace,
                    OccurredAt = DateTime.UtcNow
                };

            return TaskResult.Failure(errorInfo);
        }
        finally
        {
            // Clean up resources
            if (page != null)
            {
                try
                {
                    await page.CloseAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error closing page");
                }
            }

            if (browser != null)
            {
                try
                {
                    await _browserPool.ReturnBrowserAsync(browser, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error returning browser to pool");
                }
            }
        }
    }

    public async Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) return false;

        try
        {
            // Try to get and return a browser to test pool health
            var browser = await _browserPool.GetBrowserAsync(cancellationToken);
            await _browserPool.ReturnBrowserAsync(browser, cancellationToken);
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<ExecutorMetrics> GetMetricsAsync(CancellationToken cancellationToken = default)
    {
        TimeSpan averageExecutionTime;
        lock (_executionTimes)
        {
            averageExecutionTime = _executionTimes.Count > 0 
                ? TimeSpan.FromTicks((long)_executionTimes.Average(t => t.Ticks))
                : TimeSpan.Zero;
        }

        var browserStats = _browserPool.GetStatistics();

        var metrics = new ExecutorMetrics
        {
            TotalExecutions = _totalExecutions,
            SuccessfulExecutions = _successfulExecutions,
            FailedExecutions = _failedExecutions,
            AverageExecutionTime = averageExecutionTime,
            LastExecutionTime = _lastExecutionTime,
            IsHealthy = await IsHealthyAsync(cancellationToken),
            AdditionalMetrics = new Dictionary<string, object>
            {
                ["ExecutorType"] = _executorType,
                ["BrowserPool"] = browserStats,
                ["IsDisposed"] = _disposed
            }
        };

        return metrics;
    }

    private async Task<IPage> CreateConfiguredPageAsync(IBrowser browser, CancellationToken cancellationToken)
    {
        var contextOptions = new BrowserNewContextOptions
        {
            UserAgent = _configuration.PageOptions.UserAgent,
            ExtraHTTPHeaders = _configuration.PageOptions.ExtraHeaders,
            JavaScriptEnabled = _configuration.PageOptions.JavaScriptEnabled,
            AcceptDownloads = _configuration.PageOptions.AcceptDownloads,
            IgnoreHTTPSErrors = _configuration.LaunchOptions.IgnoreHTTPSErrors
        };

        if (_configuration.PageOptions.Viewport != null)
        {
            contextOptions.ViewportSize = new Microsoft.Playwright.ViewportSize
            {
                Width = _configuration.PageOptions.Viewport.Width,
                Height = _configuration.PageOptions.Viewport.Height
            };
        }

        var context = await browser.NewContextAsync(contextOptions);

        var page = await context.NewPageAsync();
        
        // Set timeouts
        page.SetDefaultNavigationTimeout(_configuration.PageOptions.NavigationTimeoutMs);
        page.SetDefaultTimeout(_configuration.PageOptions.ActionTimeoutMs);

        return page;
    }

    private async Task ExecuteSingleActionAsync(IPage page, object actionObj, CancellationToken cancellationToken)
    {
        // Handle dynamic JSON objects from task messages
        var actionType = GetActionProperty(actionObj, "type")?.ToString()?.ToLowerInvariant();

        if (string.IsNullOrEmpty(actionType))
        {
            throw new ArgumentException("Action must have a 'type' property");
        }

        _logger.LogDebug("Executing web action: {ActionType}", actionType);

        switch (actionType)
        {
            case "navigate":
                var url = GetActionProperty(actionObj, "url")?.ToString();
                if (string.IsNullOrEmpty(url))
                {
                    throw new ArgumentException("Navigate action must have a 'url' property");
                }

                _logger.LogDebug("Navigating to: {Url}", url);
                await page.GotoAsync(url, new PageGotoOptions
                {
                    WaitUntil = WaitUntilState.NetworkIdle,
                    Timeout = _configuration.PageOptions.NavigationTimeoutMs
                });
                break;

            case "click":
                var clickSelector = GetActionProperty(actionObj, "selector")?.ToString();
                if (string.IsNullOrEmpty(clickSelector))
                {
                    throw new ArgumentException("Click action must have a 'selector' property");
                }

                _logger.LogDebug("Clicking element: {Selector}", clickSelector);
                await page.ClickAsync(clickSelector, new PageClickOptions
                {
                    Timeout = _configuration.PageOptions.ActionTimeoutMs
                });
                break;

            case "type":
                var typeSelector = GetActionProperty(actionObj, "selector")?.ToString();
                var text = GetActionProperty(actionObj, "text")?.ToString();

                if (string.IsNullOrEmpty(typeSelector))
                {
                    throw new ArgumentException("Type action must have a 'selector' property");
                }
                if (string.IsNullOrEmpty(text))
                {
                    throw new ArgumentException("Type action must have a 'text' property");
                }

                _logger.LogDebug("Typing text into element: {Selector}", typeSelector);
                await page.FillAsync(typeSelector, text, new PageFillOptions
                {
                    Timeout = _configuration.PageOptions.ActionTimeoutMs
                });
                break;

            case "screenshot":
                var fileName = GetActionProperty(actionObj, "fileName")?.ToString()
                             ?? GetActionProperty(actionObj, "path")?.ToString()
                             ?? $"screenshot_{DateTime.UtcNow:yyyyMMdd_HHmmss}.png";

                var screenshotPath = Path.IsPathRooted(fileName)
                    ? fileName
                    : Path.Combine(_configuration.ErrorHandling.ScreenshotDirectory, fileName);

                _logger.LogDebug("Taking screenshot: {Path}", screenshotPath);
                await EnsureDirectoryExists(screenshotPath);
                await page.ScreenshotAsync(new PageScreenshotOptions
                {
                    Path = screenshotPath,
                    FullPage = true
                });
                break;

            case "waitforelement":
                var waitSelector = GetActionProperty(actionObj, "selector")?.ToString();
                if (string.IsNullOrEmpty(waitSelector))
                {
                    throw new ArgumentException("WaitForElement action must have a 'selector' property");
                }

                var timeoutMs = GetActionProperty(actionObj, "timeout")?.ToString();
                var timeout = int.TryParse(timeoutMs, out var parsedTimeout)
                    ? parsedTimeout
                    : _configuration.PageOptions.ActionTimeoutMs;

                _logger.LogDebug("Waiting for element: {Selector}", waitSelector);
                await page.WaitForSelectorAsync(waitSelector, new PageWaitForSelectorOptions
                {
                    Timeout = timeout
                });
                break;

            case "gettext":
                var getTextSelector = GetActionProperty(actionObj, "selector")?.ToString();
                if (string.IsNullOrEmpty(getTextSelector))
                {
                    throw new ArgumentException("GetText action must have a 'selector' property");
                }

                _logger.LogDebug("Getting text from element: {Selector}", getTextSelector);
                var elementText = await page.TextContentAsync(getTextSelector);
                _logger.LogInformation("Element text: {Text}", elementText);
                break;

            default:
                throw new NotSupportedException($"Action type '{actionType}' is not supported. Supported types: navigate, click, type, screenshot, waitforelement, gettext");
        }
    }

    private static object? GetActionProperty(object actionObj, string propertyName)
    {
        if (actionObj is System.Text.Json.JsonElement jsonElement)
        {
            if (jsonElement.TryGetProperty(propertyName, out var property))
            {
                return property.ValueKind switch
                {
                    JsonValueKind.String => property.GetString(),
                    JsonValueKind.Number => property.GetInt32(),
                    JsonValueKind.True => true,
                    JsonValueKind.False => false,
                    _ => property.ToString()
                };
            }
        }
        else
        {
            // Handle dynamic objects or dictionaries
            var type = actionObj.GetType();
            var property = type.GetProperty(propertyName, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
            if (property != null)
            {
                return property.GetValue(actionObj);
            }

            // Try as dictionary
            if (actionObj is IDictionary<string, object> dict)
            {
                return dict.TryGetValue(propertyName, out var value) ? value : null;
            }
        }

        return null;
    }

    private async Task<ActionExecutionError> CreateErrorInfoAsync(
        IPage page, 
        object? failedAction, 
        Exception exception, 
        CancellationToken cancellationToken)
    {
        var error = new ActionExecutionError
        {
            Message = exception.Message,
            InnerException = exception,
            StackTrace = exception.StackTrace,
            OccurredAt = DateTime.UtcNow
        };

        if (_configuration.ErrorHandling.TakeScreenshotOnError)
        {
            try
            {
                var screenshotPath = Path.Combine(
                    _configuration.ErrorHandling.ScreenshotDirectory,
                    $"error_{DateTime.UtcNow:yyyyMMdd_HHmmss}_{Guid.NewGuid():N}.png");
                
                await EnsureDirectoryExists(screenshotPath);
                await page.ScreenshotAsync(new PageScreenshotOptions { Path = screenshotPath });

                error = error with { ScreenshotPath = screenshotPath };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to capture error screenshot");
            }
        }

        if (_configuration.ErrorHandling.CapturePageContentOnError)
        {
            try
            {
                var pageContent = await page.ContentAsync();
                error = error with { HtmlContent = pageContent, CurrentUrl = page.Url };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to capture page content");
            }
        }

        return error;
    }

    private static Task EnsureDirectoryExists(string filePath)
    {
        var directory = Path.GetDirectoryName(filePath);
        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }

        return Task.CompletedTask;
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
            _logger.LogDebug("Web task executor disposed");
        }
    }
}


