using Microsoft.Playwright;

namespace Automation.Web.Configuration;

/// <summary>
/// Configuration for web automation
/// </summary>
public class WebAutomationConfiguration
{
    /// <summary>
    /// Browser type to use
    /// </summary>
    public BrowserType Browser { get; set; } = BrowserType.Chromium;

    /// <summary>
    /// Whether to run browser in headless mode
    /// </summary>
    public bool Headless { get; set; } = true;

    /// <summary>
    /// Browser launch options
    /// </summary>
    public BrowserLaunchConfiguration LaunchOptions { get; set; } = new();

    /// <summary>
    /// Page configuration options
    /// </summary>
    public PageConfiguration PageOptions { get; set; } = new();

    /// <summary>
    /// Resource management configuration
    /// </summary>
    public ResourceManagementConfiguration ResourceManagement { get; set; } = new();

    /// <summary>
    /// Error handling configuration
    /// </summary>
    public ErrorHandlingConfiguration ErrorHandling { get; set; } = new();

    /// <summary>
    /// Creates default configuration
    /// </summary>
    public static WebAutomationConfiguration CreateDefault()
    {
        return new WebAutomationConfiguration();
    }
}

/// <summary>
/// Browser types supported
/// </summary>
public enum BrowserType
{
    Chromium,
    Firefox,
    WebKit
}

/// <summary>
/// Browser launch configuration
/// </summary>
public class BrowserLaunchConfiguration
{
    /// <summary>
    /// Browser executable path (optional)
    /// </summary>
    public string? ExecutablePath { get; set; }

    /// <summary>
    /// Additional browser arguments
    /// </summary>
    public List<string> Args { get; set; } = new();

    /// <summary>
    /// Whether to ignore HTTPS errors
    /// </summary>
    public bool IgnoreHTTPSErrors { get; set; } = true;

    /// <summary>
    /// Browser timeout in milliseconds
    /// </summary>
    public int TimeoutMs { get; set; } = 30000;

    /// <summary>
    /// Whether to enable developer tools
    /// </summary>
    public bool Devtools { get; set; } = false;

    /// <summary>
    /// Slow motion delay in milliseconds
    /// </summary>
    public int SlowMo { get; set; } = 0;

    /// <summary>
    /// Downloads path
    /// </summary>
    public string? DownloadsPath { get; set; }
}

/// <summary>
/// Page configuration options
/// </summary>
public class PageConfiguration
{
    /// <summary>
    /// Default navigation timeout in milliseconds
    /// </summary>
    public int NavigationTimeoutMs { get; set; } = 30000;

    /// <summary>
    /// Default action timeout in milliseconds
    /// </summary>
    public int ActionTimeoutMs { get; set; } = 10000;

    /// <summary>
    /// Viewport size
    /// </summary>
    public ViewportSize? Viewport { get; set; } = new() { Width = 1920, Height = 1080 };

    /// <summary>
    /// User agent string
    /// </summary>
    public string? UserAgent { get; set; }

    /// <summary>
    /// Extra HTTP headers
    /// </summary>
    public Dictionary<string, string> ExtraHeaders { get; set; } = new();

    /// <summary>
    /// Whether to enable JavaScript
    /// </summary>
    public bool JavaScriptEnabled { get; set; } = true;

    /// <summary>
    /// Whether to accept downloads
    /// </summary>
    public bool AcceptDownloads { get; set; } = true;
}

/// <summary>
/// Viewport size configuration
/// </summary>
public class ViewportSize
{
    public int Width { get; set; }
    public int Height { get; set; }
}

/// <summary>
/// Resource management configuration
/// </summary>
public class ResourceManagementConfiguration
{
    /// <summary>
    /// Maximum number of concurrent browser instances
    /// </summary>
    public int MaxConcurrentBrowsers { get; set; } = 5;

    /// <summary>
    /// Maximum number of pages per browser
    /// </summary>
    public int MaxPagesPerBrowser { get; set; } = 10;

    /// <summary>
    /// Browser idle timeout in minutes
    /// </summary>
    public int BrowserIdleTimeoutMinutes { get; set; } = 10;

    /// <summary>
    /// Page idle timeout in minutes
    /// </summary>
    public int PageIdleTimeoutMinutes { get; set; } = 5;

    /// <summary>
    /// Whether to enable browser pooling
    /// </summary>
    public bool EnableBrowserPooling { get; set; } = true;

    /// <summary>
    /// Whether to enable page pooling
    /// </summary>
    public bool EnablePagePooling { get; set; } = true;
}

/// <summary>
/// Error handling configuration
/// </summary>
public class ErrorHandlingConfiguration
{
    /// <summary>
    /// Whether to take screenshots on errors
    /// </summary>
    public bool TakeScreenshotOnError { get; set; } = true;

    /// <summary>
    /// Whether to capture page content on errors
    /// </summary>
    public bool CapturePageContentOnError { get; set; } = true;

    /// <summary>
    /// Whether to capture console logs on errors
    /// </summary>
    public bool CaptureConsoleLogsOnError { get; set; } = true;

    /// <summary>
    /// Screenshot directory
    /// </summary>
    public string ScreenshotDirectory { get; set; } = "screenshots";

    /// <summary>
    /// Maximum number of retry attempts
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Retry delay in milliseconds
    /// </summary>
    public int RetryDelayMs { get; set; } = 1000;
}
