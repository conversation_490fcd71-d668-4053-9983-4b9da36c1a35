using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace Automation.Web.Services
{
    /// <summary>
    /// Service for managing Playwright initialization with better error handling
    /// </summary>
    public interface IPlaywrightService
    {
        Task<IPlaywright?> GetPlaywrightAsync(CancellationToken cancellationToken = default);
        bool IsAvailable { get; }
    }

    /// <summary>
    /// Implementation of Playwright service with robust error handling
    /// </summary>
    public class PlaywrightService : IPlaywrightService, IDisposable
    {
        private readonly ILogger<PlaywrightService> _logger;
        private readonly SemaphoreSlim _initializationSemaphore;
        private IPlaywright? _playwright;
        private bool _initializationAttempted;
        private bool _isAvailable;
        private bool _disposed;

        public PlaywrightService(ILogger<PlaywrightService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _initializationSemaphore = new SemaphoreSlim(1, 1);
        }

        public bool IsAvailable => _isAvailable;

        public async Task<IPlaywright?> GetPlaywrightAsync(CancellationToken cancellationToken = default)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(PlaywrightService));

            if (_playwright != null)
                return _playwright;

            if (_initializationAttempted && !_isAvailable)
                return null;

            await _initializationSemaphore.WaitAsync(cancellationToken);
            try
            {
                if (_playwright != null)
                    return _playwright;

                if (_initializationAttempted && !_isAvailable)
                    return null;

                return await InitializePlaywrightAsync(cancellationToken);
            }
            finally
            {
                _initializationSemaphore.Release();
            }
        }

        private async Task<IPlaywright?> InitializePlaywrightAsync(CancellationToken cancellationToken)
        {
            _initializationAttempted = true;

            try
            {
                _logger.LogInformation("Initializing Playwright...");

                // Set up environment variables for container compatibility
                SetupEnvironmentVariables();

                // Verify Node.js availability
                if (!VerifyNodeJsAvailability())
                {
                    _logger.LogError("Node.js is not available. Playwright requires Node.js to function.");
                    return null;
                }

                // Verify browsers are installed
                if (!VerifyBrowsersInstalled())
                {
                    _logger.LogWarning("Playwright browsers may not be properly installed.");
                }

                // Initialize Playwright
                _playwright = await Playwright.CreateAsync();
                _isAvailable = true;

                _logger.LogInformation("Playwright initialized successfully");
                return _playwright;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize Playwright. Web automation features will be unavailable.");
                _isAvailable = false;
                return null;
            }
        }

        private void SetupEnvironmentVariables()
        {
            // Set Node.js path if not already set
            var nodePath = Environment.GetEnvironmentVariable("PLAYWRIGHT_NODEJS_PATH");
            if (string.IsNullOrEmpty(nodePath))
            {
                // Try common Node.js locations
                var possiblePaths = new[] { "/usr/bin/node", "/usr/local/bin/node", "/opt/node/bin/node" };
                foreach (var path in possiblePaths)
                {
                    if (File.Exists(path))
                    {
                        Environment.SetEnvironmentVariable("PLAYWRIGHT_NODEJS_PATH", path);
                        _logger.LogInformation("Set PLAYWRIGHT_NODEJS_PATH to: {NodePath}", path);
                        break;
                    }
                }
            }

            // Set browsers path if not already set
            var browsersPath = Environment.GetEnvironmentVariable("PLAYWRIGHT_BROWSERS_PATH");
            if (string.IsNullOrEmpty(browsersPath))
            {
                Environment.SetEnvironmentVariable("PLAYWRIGHT_BROWSERS_PATH", "/ms-playwright");
                _logger.LogInformation("Set PLAYWRIGHT_BROWSERS_PATH to: /ms-playwright");
            }

            _logger.LogDebug("Playwright environment variables:");
            _logger.LogDebug("  PLAYWRIGHT_NODEJS_PATH: {NodePath}", 
                Environment.GetEnvironmentVariable("PLAYWRIGHT_NODEJS_PATH"));
            _logger.LogDebug("  PLAYWRIGHT_BROWSERS_PATH: {BrowsersPath}", 
                Environment.GetEnvironmentVariable("PLAYWRIGHT_BROWSERS_PATH"));
        }

        private bool VerifyNodeJsAvailability()
        {
            var nodePath = Environment.GetEnvironmentVariable("PLAYWRIGHT_NODEJS_PATH");
            if (!string.IsNullOrEmpty(nodePath) && File.Exists(nodePath))
            {
                _logger.LogDebug("Node.js found at: {NodePath}", nodePath);
                return true;
            }

            // Check if node is in PATH
            try
            {
                var processInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "node",
                    Arguments = "--version",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = System.Diagnostics.Process.Start(processInfo);
                if (process != null)
                {
                    process.WaitForExit(5000); // 5 second timeout
                    if (process.ExitCode == 0)
                    {
                        _logger.LogDebug("Node.js is available in PATH");
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Failed to check Node.js availability");
            }

            return false;
        }

        private bool VerifyBrowsersInstalled()
        {
            var browsersPath = Environment.GetEnvironmentVariable("PLAYWRIGHT_BROWSERS_PATH") ?? "/ms-playwright";
            
            if (!Directory.Exists(browsersPath))
            {
                _logger.LogWarning("Browsers directory does not exist: {BrowsersPath}", browsersPath);
                return false;
            }

            // Check for common browser directories
            var browserDirs = Directory.GetDirectories(browsersPath, "*", SearchOption.TopDirectoryOnly);
            if (browserDirs.Length == 0)
            {
                _logger.LogWarning("No browser directories found in: {BrowsersPath}", browsersPath);
                return false;
            }

            _logger.LogDebug("Found {BrowserCount} browser directories in: {BrowsersPath}", 
                browserDirs.Length, browsersPath);
            return true;
        }

        public void Dispose()
        {
            if (_disposed)
                return;

            _playwright?.Dispose();
            _initializationSemaphore?.Dispose();
            _disposed = true;
        }
    }
}
