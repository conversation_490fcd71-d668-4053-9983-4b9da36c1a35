using Microsoft.Playwright;

namespace Automation.Web.ResourceManagement;

/// <summary>
/// Interface for browser resource pooling
/// </summary>
public interface IBrowserPool : IDisposable
{
    /// <summary>
    /// Gets a browser instance from the pool
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Browser instance</returns>
    Task<IBrowser> GetBrowserAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Returns a browser instance to the pool
    /// </summary>
    /// <param name="browser"><PERSON><PERSON><PERSON> to return</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task ReturnBrowserAsync(IBrowser browser, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets pool statistics
    /// </summary>
    BrowserPoolStatistics GetStatistics();
}

/// <summary>
/// Interface for page resource pooling
/// </summary>
public interface IPagePool : IDisposable
{
    /// <summary>
    /// Gets a page instance from the pool
    /// </summary>
    /// <param name="browser"><PERSON><PERSON><PERSON> to create page from</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Page instance</returns>
    Task<IPage> GetPageAsync(IBrowser browser, CancellationToken cancellationToken = default);

    /// <summary>
    /// Returns a page instance to the pool
    /// </summary>
    /// <param name="page">Page to return</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task ReturnPageAsync(IPage page, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets pool statistics
    /// </summary>
    PagePoolStatistics GetStatistics();
}

/// <summary>
/// Browser pool statistics
/// </summary>
public record BrowserPoolStatistics
{
    public int TotalBrowsers { get; init; }
    public int AvailableBrowsers { get; init; }
    public int BusyBrowsers { get; init; }
    public int CreatedBrowsers { get; init; }
    public int DisposedBrowsers { get; init; }
    public TimeSpan AverageCreationTime { get; init; }
    public DateTime LastActivity { get; init; }
}

/// <summary>
/// Page pool statistics
/// </summary>
public record PagePoolStatistics
{
    public int TotalPages { get; init; }
    public int AvailablePages { get; init; }
    public int BusyPages { get; init; }
    public int CreatedPages { get; init; }
    public int DisposedPages { get; init; }
    public TimeSpan AverageCreationTime { get; init; }
    public DateTime LastActivity { get; init; }
}

/// <summary>
/// Pooled browser wrapper
/// </summary>
internal class PooledBrowser : IDisposable
{
    public IBrowser Browser { get; }
    public DateTime CreatedAt { get; }
    public DateTime LastUsed { get; set; }
    public bool IsInUse { get; set; }
    public int UsageCount { get; set; }

    public PooledBrowser(IBrowser browser)
    {
        Browser = browser;
        CreatedAt = DateTime.UtcNow;
        LastUsed = DateTime.UtcNow;
        IsInUse = false;
        UsageCount = 0;
    }

    public void Dispose()
    {
        Browser?.CloseAsync().GetAwaiter().GetResult();
        Browser?.DisposeAsync().GetAwaiter().GetResult();
    }
}

/// <summary>
/// Pooled page wrapper
/// </summary>
internal class PooledPage : IDisposable
{
    public IPage Page { get; }
    public DateTime CreatedAt { get; }
    public DateTime LastUsed { get; set; }
    public bool IsInUse { get; set; }
    public int UsageCount { get; set; }
    public string? AssociatedBrowserId { get; set; }

    public PooledPage(IPage page)
    {
        Page = page;
        CreatedAt = DateTime.UtcNow;
        LastUsed = DateTime.UtcNow;
        IsInUse = false;
        UsageCount = 0;
    }

    public void Dispose()
    {
        Page?.CloseAsync().GetAwaiter().GetResult();
    }
}
