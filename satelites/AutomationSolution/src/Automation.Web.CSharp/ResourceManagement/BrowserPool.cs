using System.Collections.Concurrent;
using System.Diagnostics;
using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using Automation.Web.Configuration;
using Automation.Web.Services;

namespace Automation.Web.ResourceManagement;

/// <summary>
/// Browser pool implementation with resource management
/// </summary>
public class BrowserPool : IBrowserPool
{
    private readonly IPlaywrightService _playwrightService;
    private readonly WebAutomationConfiguration _configuration;
    private readonly ILogger<BrowserPool> _logger;
    private readonly ConcurrentQueue<PooledBrowser> _availableBrowsers = new();
    private readonly ConcurrentDictionary<string, PooledBrowser> _allBrowsers = new();
    private readonly Timer _cleanupTimer;
    private readonly SemaphoreSlim _creationSemaphore;
    
    private int _createdCount = 0;
    private int _disposedCount = 0;
    private readonly List<TimeSpan> _creationTimes = new();
    private bool _disposed = false;

    public BrowserPool(
        IPlaywrightService playwrightService,
        WebAutomationConfiguration configuration,
        ILogger<BrowserPool> logger)
    {
        _playwrightService = playwrightService ?? throw new ArgumentNullException(nameof(playwrightService));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        _creationSemaphore = new SemaphoreSlim(_configuration.ResourceManagement.MaxConcurrentBrowsers);
        
        // Setup cleanup timer to run every minute
        _cleanupTimer = new Timer(CleanupIdleBrowsers, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
        
        _logger.LogInformation("Browser pool initialized with max {MaxBrowsers} browsers", 
            _configuration.ResourceManagement.MaxConcurrentBrowsers);
    }

    public async Task<IBrowser> GetBrowserAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(BrowserPool));

        if (!_playwrightService.IsAvailable)
        {
            throw new InvalidOperationException(
                "Playwright is not available. Cannot create browsers. " +
                "Please ensure Playwright is properly installed and configured.");
        }

        // Try to get an available browser first
        if (_availableBrowsers.TryDequeue(out var pooledBrowser))
        {
            if (!pooledBrowser.Browser.IsConnected)
            {
                // Browser is disconnected, dispose and create new one
                _logger.LogWarning("Found disconnected browser in pool, disposing and creating new one");
                await DisposeBrowserAsync(pooledBrowser);
            }
            else
            {
                pooledBrowser.IsInUse = true;
                pooledBrowser.LastUsed = DateTime.UtcNow;
                pooledBrowser.UsageCount++;
                
                _logger.LogDebug("Reusing browser from pool, usage count: {UsageCount}", pooledBrowser.UsageCount);
                return pooledBrowser.Browser;
            }
        }

        // Need to create a new browser
        await _creationSemaphore.WaitAsync(cancellationToken);
        
        try
        {
            // Double-check if we're under the limit
            if (_allBrowsers.Count >= _configuration.ResourceManagement.MaxConcurrentBrowsers)
            {
                _logger.LogWarning("Maximum browser limit reached ({MaxBrowsers}), waiting for available browser", 
                    _configuration.ResourceManagement.MaxConcurrentBrowsers);
                
                // Wait for a browser to become available
                var timeout = TimeSpan.FromSeconds(30);
                var startTime = DateTime.UtcNow;
                
                while (DateTime.UtcNow - startTime < timeout)
                {
                    if (_availableBrowsers.TryDequeue(out pooledBrowser))
                    {
                        if (pooledBrowser.Browser.IsConnected)
                        {
                            pooledBrowser.IsInUse = true;
                            pooledBrowser.LastUsed = DateTime.UtcNow;
                            pooledBrowser.UsageCount++;
                            return pooledBrowser.Browser;
                        }
                        else
                        {
                            await DisposeBrowserAsync(pooledBrowser);
                        }
                    }
                    
                    await Task.Delay(100, cancellationToken);
                }
                
                throw new InvalidOperationException("Timeout waiting for available browser");
            }

            // Create new browser
            var stopwatch = Stopwatch.StartNew();
            var browser = await CreateBrowserAsync(cancellationToken);
            stopwatch.Stop();
            
            lock (_creationTimes)
            {
                _creationTimes.Add(stopwatch.Elapsed);
                if (_creationTimes.Count > 100) // Keep only last 100 measurements
                {
                    _creationTimes.RemoveAt(0);
                }
            }

            var newPooledBrowser = new PooledBrowser(browser)
            {
                IsInUse = true
            };
            
            var browserId = Guid.NewGuid().ToString();
            _allBrowsers[browserId] = newPooledBrowser;
            Interlocked.Increment(ref _createdCount);
            
            _logger.LogInformation("Created new browser in {ElapsedMs}ms, total browsers: {TotalBrowsers}", 
                stopwatch.ElapsedMilliseconds, _allBrowsers.Count);
            
            return browser;
        }
        finally
        {
            _creationSemaphore.Release();
        }
    }

    public async Task ReturnBrowserAsync(IBrowser browser, CancellationToken cancellationToken = default)
    {
        if (_disposed) return;
        if (browser == null) return;

        var pooledBrowser = _allBrowsers.Values.FirstOrDefault(pb => pb.Browser == browser);
        if (pooledBrowser == null)
        {
            _logger.LogWarning("Attempted to return unknown browser to pool");
            return;
        }

        pooledBrowser.IsInUse = false;
        pooledBrowser.LastUsed = DateTime.UtcNow;

        if (!browser.IsConnected)
        {
            _logger.LogWarning("Returned browser is disconnected, disposing");
            await DisposeBrowserAsync(pooledBrowser);
            return;
        }

        // Reset browser state by closing all pages except one
        try
        {
            var pages = browser.Contexts.SelectMany(c => c.Pages).ToList();
            if (pages.Count > 1)
            {
                for (int i = 1; i < pages.Count; i++)
                {
                    await pages[i].CloseAsync();
                }
            }
            
            // Navigate the remaining page to about:blank to clear state
            if (pages.Count > 0)
            {
                await pages[0].GotoAsync("about:blank");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error resetting browser state, disposing browser");
            await DisposeBrowserAsync(pooledBrowser);
            return;
        }

        _availableBrowsers.Enqueue(pooledBrowser);
        _logger.LogDebug("Returned browser to pool, available browsers: {AvailableBrowsers}", _availableBrowsers.Count);
    }

    public BrowserPoolStatistics GetStatistics()
    {
        var availableCount = _availableBrowsers.Count;
        var totalCount = _allBrowsers.Count;
        var busyCount = totalCount - availableCount;
        
        TimeSpan averageCreationTime;
        lock (_creationTimes)
        {
            averageCreationTime = _creationTimes.Count > 0 
                ? TimeSpan.FromTicks((long)_creationTimes.Average(t => t.Ticks))
                : TimeSpan.Zero;
        }

        return new BrowserPoolStatistics
        {
            TotalBrowsers = totalCount,
            AvailableBrowsers = availableCount,
            BusyBrowsers = busyCount,
            CreatedBrowsers = _createdCount,
            DisposedBrowsers = _disposedCount,
            AverageCreationTime = averageCreationTime,
            LastActivity = _allBrowsers.Values.Any() 
                ? _allBrowsers.Values.Max(b => b.LastUsed)
                : DateTime.MinValue
        };
    }

    private async Task<IBrowser> CreateBrowserAsync(CancellationToken cancellationToken)
    {
        var playwright = await _playwrightService.GetPlaywrightAsync(cancellationToken);
        if (playwright == null)
        {
            throw new InvalidOperationException(
                "Cannot create browser: Playwright is not available. " +
                "Please ensure Playwright is properly installed and configured.");
        }

        var launchOptions = new BrowserTypeLaunchOptions
        {
            Headless = _configuration.Headless,
            Args = _configuration.LaunchOptions.Args,
            ExecutablePath = _configuration.LaunchOptions.ExecutablePath,
            Timeout = _configuration.LaunchOptions.TimeoutMs,
            SlowMo = _configuration.LaunchOptions.SlowMo,
            DownloadsPath = _configuration.LaunchOptions.DownloadsPath
        };

        return _configuration.Browser switch
        {
            Configuration.BrowserType.Chromium => await playwright.Chromium.LaunchAsync(launchOptions),
            Configuration.BrowserType.Firefox => await playwright.Firefox.LaunchAsync(launchOptions),
            Configuration.BrowserType.WebKit => await playwright.Webkit.LaunchAsync(launchOptions),
            _ => throw new ArgumentException($"Unsupported browser type: {_configuration.Browser}")
        };
    }

    private Task DisposeBrowserAsync(PooledBrowser pooledBrowser)
    {
        var browserId = _allBrowsers.FirstOrDefault(kvp => kvp.Value == pooledBrowser).Key;
        if (browserId != null)
        {
            _allBrowsers.TryRemove(browserId, out _);
        }

        try
        {
            pooledBrowser.Dispose();
            Interlocked.Increment(ref _disposedCount);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error disposing browser");
        }

        return Task.CompletedTask;
    }

    private void CleanupIdleBrowsers(object? state)
    {
        if (_disposed) return;

        var idleTimeout = TimeSpan.FromMinutes(_configuration.ResourceManagement.BrowserIdleTimeoutMinutes);
        var now = DateTime.UtcNow;
        var browsersToDispose = new List<PooledBrowser>();

        // Find idle browsers
        foreach (var pooledBrowser in _allBrowsers.Values)
        {
            if (!pooledBrowser.IsInUse && (now - pooledBrowser.LastUsed) > idleTimeout)
            {
                browsersToDispose.Add(pooledBrowser);
            }
        }

        // Dispose idle browsers
        foreach (var browser in browsersToDispose)
        {
            _logger.LogDebug("Disposing idle browser, last used: {LastUsed}", browser.LastUsed);
            _ = Task.Run(() => DisposeBrowserAsync(browser));
        }
    }

    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;

        _cleanupTimer?.Dispose();
        _creationSemaphore?.Dispose();

        // Dispose all browsers
        var disposeTasks = _allBrowsers.Values.Select(pb => Task.Run(() => pb.Dispose())).ToArray();
        Task.WaitAll(disposeTasks, TimeSpan.FromSeconds(10));

        _allBrowsers.Clear();
        while (_availableBrowsers.TryDequeue(out _)) { }

        _logger.LogInformation("Browser pool disposed");
    }
}
