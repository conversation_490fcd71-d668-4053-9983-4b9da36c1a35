using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using Automation.Web.Configuration;
using Automation.Web.Executors;
using Automation.Web.ResourceManagement;

namespace Automation.Web.Extensions;

/// <summary>
/// Extension methods for configuring web automation services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds web automation services with default configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddWebAutomationServices(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        return services.AddWebAutomationServices(configuration, options => { });
    }

    /// <summary>
    /// Adds web automation services with custom configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <param name="configureOptions">Action to configure options</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddWebAutomationServices(
        this IServiceCollection services,
        IConfiguration configuration,
        Action<WebAutomationConfiguration> configureOptions)
    {
        // Configure options
        var options = WebAutomationConfiguration.CreateDefault();
        configuration.GetSection("WebAutomation").Bind(options);
        configureOptions(options);

        // Register configuration
        services.AddSingleton(options);

        // Register Playwright
        services.AddSingleton<IPlaywright>(provider =>
        {
            var playwright = Playwright.CreateAsync().GetAwaiter().GetResult();
            return playwright;
        });

        // Register browser pool
        services.AddSingleton<IBrowserPool, BrowserPool>();

        // Register web task executor
        services.AddTransient<WebTaskExecutor>();

        // Register hosted service for Playwright installation
        services.AddHostedService<PlaywrightInstallationService>();

        return services;
    }

    /// <summary>
    /// Adds web automation services with headless Chrome configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddWebAutomationServicesHeadless(this IServiceCollection services)
    {
        var config = new ConfigurationBuilder().Build();
        
        return services.AddWebAutomationServices(config, options =>
        {
            options.Browser = Configuration.BrowserType.Chromium;
            options.Headless = true;
        });
    }

    /// <summary>
    /// Adds web automation services for testing (with visible browser)
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddWebAutomationServicesForTesting(this IServiceCollection services)
    {
        var config = new ConfigurationBuilder().Build();
        
        return services.AddWebAutomationServices(config, options =>
        {
            options.Browser = Configuration.BrowserType.Chromium;
            options.Headless = false;
            options.LaunchOptions.Devtools = true;
            options.LaunchOptions.SlowMo = 100;
        });
    }
}

/// <summary>
/// Hosted service to ensure Playwright browsers are installed
/// </summary>
internal class PlaywrightInstallationService : IHostedService
{
    private readonly ILogger<PlaywrightInstallationService> _logger;

    public PlaywrightInstallationService(ILogger<PlaywrightInstallationService> logger)
    {
        _logger = logger;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Checking Playwright browser installation");

            // Install browsers if needed - run in background to avoid blocking startup
            await Task.Run(() =>
            {
                var exitCode = Program.Main(new[] { "install" });
                if (exitCode == 0)
                {
                    _logger.LogInformation("Playwright browsers are ready");
                }
                else
                {
                    _logger.LogWarning("Playwright browser installation returned exit code: {ExitCode}", exitCode);
                }
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to install Playwright browsers");
            // Don't throw - let the application start even if browser installation fails
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }
}
