using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using Automation.Web.Configuration;
using Automation.Web.Executors;
using Automation.Web.ResourceManagement;
using Automation.Web.Services;

namespace Automation.Web.Extensions;

/// <summary>
/// Extension methods for configuring web automation services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds web automation services with default configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddWebAutomationServices(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        return services.AddWebAutomationServices(configuration, options => { });
    }

    /// <summary>
    /// Adds web automation services with custom configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <param name="configureOptions">Action to configure options</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddWebAutomationServices(
        this IServiceCollection services,
        IConfiguration configuration,
        Action<WebAutomationConfiguration> configureOptions)
    {
        // Configure options
        var options = WebAutomationConfiguration.CreateDefault();
        configuration.GetSection("WebAutomation").Bind(options);
        configureOptions(options);

        // Register configuration
        services.AddSingleton(options);

        // Register Playwright service for better error handling
        services.AddSingleton<IPlaywrightService, PlaywrightService>();

        // Register browser pool
        services.AddSingleton<IBrowserPool, BrowserPool>();

        // Register web task executor
        services.AddTransient<WebTaskExecutor>();

        // Register hosted service for Playwright installation
        services.AddHostedService<PlaywrightInstallationService>();

        return services;
    }

    /// <summary>
    /// Adds web automation services with headless Chrome configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddWebAutomationServicesHeadless(this IServiceCollection services)
    {
        var config = new ConfigurationBuilder().Build();
        
        return services.AddWebAutomationServices(config, options =>
        {
            options.Browser = Configuration.BrowserType.Chromium;
            options.Headless = true;
        });
    }

    /// <summary>
    /// Adds web automation services for testing (with visible browser)
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddWebAutomationServicesForTesting(this IServiceCollection services)
    {
        var config = new ConfigurationBuilder().Build();
        
        return services.AddWebAutomationServices(config, options =>
        {
            options.Browser = Configuration.BrowserType.Chromium;
            options.Headless = false;
            options.LaunchOptions.Devtools = true;
            options.LaunchOptions.SlowMo = 100;
        });
    }
}

/// <summary>
/// Hosted service to ensure Playwright browsers are installed
/// </summary>
internal class PlaywrightInstallationService : IHostedService
{
    private readonly ILogger<PlaywrightInstallationService> _logger;

    public PlaywrightInstallationService(ILogger<PlaywrightInstallationService> logger)
    {
        _logger = logger;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Checking Playwright browser installation");

            // Check if we're in a container and browsers are already installed
            var browsersPath = Environment.GetEnvironmentVariable("PLAYWRIGHT_BROWSERS_PATH") ?? "/ms-playwright";
            var skipDownload = Environment.GetEnvironmentVariable("PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD") == "1";

            if (skipDownload)
            {
                _logger.LogInformation("Skipping browser download as PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD is set");
                return;
            }

            if (Directory.Exists(browsersPath))
            {
                _logger.LogInformation("Playwright browsers directory exists at: {BrowsersPath}", browsersPath);

                // Check if browsers are actually installed
                var chromiumPath = Path.Combine(browsersPath, "chromium-*");
                if (Directory.GetDirectories(Path.GetDirectoryName(chromiumPath) ?? browsersPath, "chromium-*").Length > 0)
                {
                    _logger.LogInformation("Playwright browsers appear to be installed");
                    return;
                }
            }

            // Install browsers if needed - run in background to avoid blocking startup
            _logger.LogInformation("Installing Playwright browsers...");
            await Task.Run(async () =>
            {
                try
                {
                    // Use npx to install browsers if available
                    var processInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = "npx",
                        Arguments = "playwright install --with-deps",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true
                    };

                    using var process = System.Diagnostics.Process.Start(processInfo);
                    if (process != null)
                    {
                        await process.WaitForExitAsync(cancellationToken);
                        if (process.ExitCode == 0)
                        {
                            _logger.LogInformation("Playwright browsers installed successfully");
                        }
                        else
                        {
                            _logger.LogWarning("Playwright browser installation returned exit code: {ExitCode}", process.ExitCode);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to install browsers using npx, trying alternative method");

                    // Fallback: try using the Playwright CLI directly
                    try
                    {
                        var exitCode = Microsoft.Playwright.Program.Main(new[] { "install" });
                        if (exitCode == 0)
                        {
                            _logger.LogInformation("Playwright browsers installed successfully using CLI");
                        }
                        else
                        {
                            _logger.LogWarning("Playwright CLI installation returned exit code: {ExitCode}", exitCode);
                        }
                    }
                    catch (Exception cliEx)
                    {
                        _logger.LogError(cliEx, "Failed to install browsers using Playwright CLI");
                    }
                }
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to install Playwright browsers");
            // Don't throw - let the application start even if browser installation fails
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }
}
