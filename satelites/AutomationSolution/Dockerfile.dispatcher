# Multi-stage Dockerfile for Dispatcher
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy project files
COPY src/Automation.Dispatcher/Automation.Dispatcher.fsproj src/Automation.Dispatcher/
COPY src/Automation.Core/Automation.Core.fsproj src/Automation.Core/
COPY src/Automation.Utilities/Automation.Utilities.fsproj src/Automation.Utilities/

# Restore dependencies
RUN dotnet restore src/Automation.Dispatcher/Automation.Dispatcher.fsproj

# Copy source code
COPY src/ src/

# Build application
RUN dotnet build src/Automation.Dispatcher/Automation.Dispatcher.fsproj -c Release --no-restore

# Publish application
RUN dotnet publish src/Automation.Dispatcher/Automation.Dispatcher.fsproj -c Release --no-build -o /app/publish

# Runtime stage
FROM mcr.microsoft.com/dotnet/runtime:8.0 AS runtime
WORKDIR /app

# Copy published application
COPY --from=build /app/publish .

# Set environment variables
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=1

# Run the application
ENTRYPOINT ["dotnet", "Automation.Dispatcher.dll"]
