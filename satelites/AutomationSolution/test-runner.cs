using Microsoft.Playwright;
using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;

public class TestResult
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TestName { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string Status { get; set; } = "Running";
    public string? ErrorMessage { get; set; }
    public List<string> Screenshots { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

class TestRunner 
{
    public static async Task<TestResult> RunSingleTest(string url, string? testName = null)
    {
        testName ??= $"Test for {url}";
        
        var testResult = new TestResult
        {
            TestName = testName,
            Url = url,
            StartTime = DateTime.UtcNow
        };

        try 
        {
            Console.WriteLine($"🚀 Starting test: {testName}");
            Console.WriteLine($"📋 Test ID: {testResult.Id}");
            
            Environment.SetEnvironmentVariable("PLAYWRIGHT_BROWSERS_PATH", "/ms-playwright");
            
            using var playwright = await Playwright.CreateAsync();
            await using var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions { Headless = true });
            var page = await browser.NewPageAsync();
            
            // Navigate to URL
            Console.WriteLine($"🌐 Navigating to {url}...");
            await page.GotoAsync(url, new PageGotoOptions 
            { 
                WaitUntil = WaitUntilState.NetworkIdle,
                Timeout = 20000 // 20 second timeout
            });
            Console.WriteLine("✅ Navigation completed!");
            
            // Take screenshot
            var screenshotDir = "/tmp/screenshots";
            Directory.CreateDirectory(screenshotDir);
            
            var screenshotPath = Path.Combine(screenshotDir, $"{testResult.Id}-{SanitizeFilename(testName)}.png");
            await page.ScreenshotAsync(new PageScreenshotOptions
            {
                Path = screenshotPath,
                FullPage = true
            });
            testResult.Screenshots.Add(screenshotPath);
            Console.WriteLine($"📸 Screenshot saved: {screenshotPath}");
            
            // Get page information
            var title = await page.TitleAsync();
            var currentUrl = page.Url;
            
            // Analyze page elements
            var linkCount = await page.Locator("a").CountAsync();
            var imageCount = await page.Locator("img").CountAsync();
            var formCount = await page.Locator("form").CountAsync();
            var buttonCount = await page.Locator("button").CountAsync();
            var inputCount = await page.Locator("input").CountAsync();
            
            // Check for common elements
            var hasNavigation = await page.Locator("nav").CountAsync() > 0;
            var hasHeader = await page.Locator("header").CountAsync() > 0;
            var hasFooter = await page.Locator("footer").CountAsync() > 0;
            
            // Check for forms and try to interact
            if (formCount > 0)
            {
                try
                {
                    var textInputs = await page.Locator("input[type='text'], input[type='email'], input[type='search']").CountAsync();
                    if (textInputs > 0)
                    {
                        // Take screenshot before interaction
                        var beforeInteractionPath = Path.Combine(screenshotDir, $"{testResult.Id}-before-interaction.png");
                        await page.ScreenshotAsync(new PageScreenshotOptions { Path = beforeInteractionPath, FullPage = true });
                        testResult.Screenshots.Add(beforeInteractionPath);
                        
                        // Try to fill first text input
                        var firstInput = page.Locator("input[type='text'], input[type='email'], input[type='search']").First;
                        await firstInput.FillAsync("test automation");
                        
                        // Take screenshot after interaction
                        var afterInteractionPath = Path.Combine(screenshotDir, $"{testResult.Id}-after-interaction.png");
                        await page.ScreenshotAsync(new PageScreenshotOptions { Path = afterInteractionPath, FullPage = true });
                        testResult.Screenshots.Add(afterInteractionPath);
                        
                        testResult.Metadata["formInteraction"] = "Filled text input";
                    }
                }
                catch (Exception ex)
                {
                    testResult.Metadata["formInteractionError"] = ex.Message;
                }
            }
            
            // Store metadata
            testResult.Metadata["title"] = title;
            testResult.Metadata["finalUrl"] = currentUrl;
            testResult.Metadata["linkCount"] = linkCount;
            testResult.Metadata["imageCount"] = imageCount;
            testResult.Metadata["formCount"] = formCount;
            testResult.Metadata["buttonCount"] = buttonCount;
            testResult.Metadata["inputCount"] = inputCount;
            testResult.Metadata["hasNavigation"] = hasNavigation;
            testResult.Metadata["hasHeader"] = hasHeader;
            testResult.Metadata["hasFooter"] = hasFooter;
            
            Console.WriteLine($"📄 Page title: {title}");
            Console.WriteLine($"🔗 Links: {linkCount}, 🖼️ Images: {imageCount}, 📝 Forms: {formCount}");
            Console.WriteLine($"🔘 Buttons: {buttonCount}, ⌨️ Inputs: {inputCount}");
            
            // Mark test as passed
            testResult.Status = "Passed";
            testResult.EndTime = DateTime.UtcNow;
            
            Console.WriteLine("🎉 Test completed successfully!");
        } 
        catch (Exception ex) 
        {
            testResult.Status = "Failed";
            testResult.ErrorMessage = ex.Message;
            testResult.EndTime = DateTime.UtcNow;
            
            Console.WriteLine($"❌ Test failed: {ex.Message}");
        }
        
        // Save test results to JSON
        var resultsDir = "/tmp/test-results";
        Directory.CreateDirectory(resultsDir);
        
        var resultPath = Path.Combine(resultsDir, $"{testResult.Id}.json");
        var json = JsonSerializer.Serialize(testResult, new JsonSerializerOptions 
        { 
            WriteIndented = true 
        });
        
        await File.WriteAllTextAsync(resultPath, json);
        Console.WriteLine($"💾 Test results saved: {resultPath}");
        
        return testResult;
    }

    static string SanitizeFilename(string filename)
    {
        var invalid = Path.GetInvalidFileNameChars();
        return string.Join("_", filename.Split(invalid, StringSplitOptions.RemoveEmptyEntries));
    }
}

class Program 
{
    static async Task Main(string[] args) 
    {
        if (args.Length == 0)
        {
            Console.WriteLine("Usage: dotnet run <url> [test-name]");
            Console.WriteLine("Example: dotnet run https://example.com \"My Test\"");
            return;
        }

        var url = args[0];
        var testName = args.Length > 1 ? args[1] : null;

        var result = await TestRunner.RunSingleTest(url, testName);
        
        // Display summary
        var duration = result.EndTime - result.StartTime;
        Console.WriteLine("\n📊 TEST SUMMARY:");
        Console.WriteLine($"   Test ID: {result.Id}");
        Console.WriteLine($"   Status: {result.Status}");
        Console.WriteLine($"   Duration: {duration.TotalSeconds:F2} seconds");
        Console.WriteLine($"   Screenshots: {result.Screenshots.Count}");
        
        if (result.Status == "Passed")
        {
            Console.WriteLine("\n✅ Test passed! 🎉");
        }
        else
        {
            Console.WriteLine($"\n❌ Test failed: {result.ErrorMessage}");
        }
    }
}
