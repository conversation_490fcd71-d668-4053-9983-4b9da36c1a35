using System;
using System.IO;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;

public class TestResult
{
    public string Id { get; set; } = string.Empty;
    public string TestName { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }
    public List<string> Screenshots { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

class SimpleTestServer
{
    private readonly HttpListener _listener;
    private readonly string _resultsPath = "/tmp/test-results";
    private readonly string _screenshotsPath = "/tmp/screenshots";

    public SimpleTestServer()
    {
        _listener = new HttpListener();
        _listener.Prefixes.Add("http://+:8082/");
    }

    public async Task StartAsync()
    {
        _listener.Start();
        Console.WriteLine("🌐 Test Results Server started at http://localhost:8082");
        Console.WriteLine("📊 Dashboard: http://localhost:8082/dashboard");
        Console.WriteLine("📋 API: http://localhost:8082/api/results");
        
        while (_listener.IsListening)
        {
            try
            {
                var context = await _listener.GetContextAsync();
                _ = Task.Run(() => HandleRequestAsync(context));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }
    }

    private async Task HandleRequestAsync(HttpListenerContext context)
    {
        var request = context.Request;
        var response = context.Response;

        try
        {
            var path = request.Url?.AbsolutePath ?? "/";
            
            Console.WriteLine($"📥 {request.HttpMethod} {path}");

            switch (path)
            {
                case "/":
                case "/dashboard":
                    await ServeDashboardAsync(response);
                    break;
                case "/api/results":
                    await ServeResultsAsync(response);
                    break;
                case var p when p.StartsWith("/api/screenshot/"):
                    var filename = Path.GetFileName(p);
                    await ServeScreenshotAsync(response, filename);
                    break;
                default:
                    response.StatusCode = 404;
                    await WriteResponseAsync(response, "Not Found", "text/plain");
                    break;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error handling request: {ex.Message}");
            response.StatusCode = 500;
            await WriteResponseAsync(response, "Internal Server Error", "text/plain");
        }
        finally
        {
            response.Close();
        }
    }

    private async Task ServeDashboardAsync(HttpListenerResponse response)
    {
        var html = GetDashboardHtml();
        await WriteResponseAsync(response, html, "text/html");
    }

    private async Task ServeResultsAsync(HttpListenerResponse response)
    {
        var results = await GetTestResultsAsync();
        var json = JsonSerializer.Serialize(results, new JsonSerializerOptions { WriteIndented = true });
        await WriteResponseAsync(response, json, "application/json");
    }

    private async Task ServeScreenshotAsync(HttpListenerResponse response, string filename)
    {
        var filePath = Path.Combine(_screenshotsPath, filename);
        
        if (File.Exists(filePath))
        {
            var bytes = await File.ReadAllBytesAsync(filePath);
            response.ContentType = "image/png";
            response.ContentLength64 = bytes.Length;
            await response.OutputStream.WriteAsync(bytes, 0, bytes.Length);
        }
        else
        {
            response.StatusCode = 404;
            await WriteResponseAsync(response, "Screenshot not found", "text/plain");
        }
    }

    private async Task<List<TestResult>> GetTestResultsAsync()
    {
        var results = new List<TestResult>();
        
        if (!Directory.Exists(_resultsPath))
            return results;

        var files = Directory.GetFiles(_resultsPath, "*.json");
        
        foreach (var file in files)
        {
            try
            {
                var json = await File.ReadAllTextAsync(file);
                var result = JsonSerializer.Deserialize<TestResult>(json);
                if (result != null)
                {
                    results.Add(result);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Error reading {file}: {ex.Message}");
            }
        }
        
        return results.OrderByDescending(r => r.StartTime).ToList();
    }

    private async Task WriteResponseAsync(HttpListenerResponse response, string content, string contentType)
    {
        response.ContentType = contentType;
        var bytes = Encoding.UTF8.GetBytes(content);
        response.ContentLength64 = bytes.Length;
        await response.OutputStream.WriteAsync(bytes, 0, bytes.Length);
    }

    private string GetDashboardHtml()
    {
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Automation Test Results</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .results { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .test-item { border-bottom: 1px solid #eee; padding: 15px 0; }
        .test-item:last-child { border-bottom: none; }
        .test-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
        .test-name { font-weight: bold; font-size: 1.1em; }
        .test-status { padding: 4px 12px; border-radius: 20px; color: white; font-size: 0.9em; font-weight: bold; }
        .status-passed { background: #28a745; }
        .status-failed { background: #dc3545; }
        .status-running { background: #007bff; }
        .test-meta { color: #666; font-size: 0.9em; margin: 5px 0; }
        .test-url { color: #007bff; text-decoration: none; }
        .test-url:hover { text-decoration: underline; }
        .screenshots { margin: 10px 0; }
        .screenshot { max-width: 200px; margin: 5px; border-radius: 4px; cursor: pointer; border: 2px solid #ddd; }
        .screenshot:hover { border-color: #007bff; }
        .metadata { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; font-size: 0.9em; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .loading { text-align: center; padding: 20px; color: #666; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Automation Test Results Dashboard</h1>
            <p>Real-time test results with screenshots and detailed analysis</p>
            <button onclick="loadResults()">🔄 Refresh Results</button>
        </div>

        <div class="results">
            <h3>📋 Test Results</h3>
            <div id="results" class="loading">Loading test results...</div>
        </div>
    </div>

    <script>
        async function loadResults() {
            try {
                document.getElementById('results').innerHTML = '<div class="loading">Loading test results...</div>';
                
                const response = await fetch('/api/results');
                const results = await response.json();
                
                if (results.length === 0) {
                    document.getElementById('results').innerHTML = '<p>No test results found. Run some tests to see results here!</p>';
                    return;
                }
                
                const resultsHtml = results.map(result => {
                    const duration = new Date(result.EndTime) - new Date(result.StartTime);
                    const durationSeconds = (duration / 1000).toFixed(2);
                    
                    return `
                        <div class="test-item">
                            <div class="test-header">
                                <div class="test-name">${result.TestName}</div>
                                <span class="test-status status-${result.Status.toLowerCase()}">${result.Status}</span>
                            </div>
                            <div class="test-meta">
                                🌐 <a href="${result.Url}" target="_blank" class="test-url">${result.Url}</a>
                            </div>
                            <div class="test-meta">
                                📅 ${new Date(result.StartTime).toLocaleString()} • ⏱️ ${durationSeconds}s • 🆔 ${result.Id}
                            </div>
                            ${result.ErrorMessage ? `<div class="error">❌ ${result.ErrorMessage}</div>` : ''}
                            ${result.Screenshots.length > 0 ? `
                                <div class="screenshots">
                                    <strong>📸 Screenshots:</strong><br>
                                    ${result.Screenshots.map(screenshot => {
                                        const filename = screenshot.split('/').pop();
                                        return `<img src="/api/screenshot/${filename}" alt="Screenshot" class="screenshot" onclick="window.open('/api/screenshot/${filename}', '_blank')" title="Click to view full size">`;
                                    }).join('')}
                                </div>
                            ` : ''}
                            ${Object.keys(result.Metadata).length > 0 ? `
                                <div class="metadata">
                                    <strong>📊 Analysis:</strong><br>
                                    ${Object.entries(result.Metadata).map(([key, value]) => `<strong>${key}:</strong> ${value}`).join(' • ')}
                                </div>
                            ` : ''}
                        </div>
                    `;
                }).join('');
                
                document.getElementById('results').innerHTML = resultsHtml;
            } catch (error) {
                console.error('Error loading results:', error);
                document.getElementById('results').innerHTML = '<div class="error">❌ Error loading results. Please try again.</div>';
            }
        }

        // Load results on page load
        loadResults();
        
        // Auto-refresh every 30 seconds
        setInterval(loadResults, 30000);
    </script>
</body>
</html>
""";
    }

    public void Stop()
    {
        _listener.Stop();
    }
}

class Program
{
    static async Task Main(string[] args)
    {
        var server = new SimpleTestServer();
        
        Console.WriteLine("🚀 Starting Test Results Server...");
        
        // Handle Ctrl+C gracefully
        Console.CancelKeyPress += (sender, e) =>
        {
            e.Cancel = true;
            server.Stop();
            Console.WriteLine("\n👋 Server stopped.");
            Environment.Exit(0);
        };
        
        await server.StartAsync();
    }
}
