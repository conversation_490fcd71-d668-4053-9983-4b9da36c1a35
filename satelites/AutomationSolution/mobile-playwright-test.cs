using Microsoft.Playwright;
using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;

public class MobileTestResult
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TestName { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string Status { get; set; } = "Running";
    public string? ErrorMessage { get; set; }
    public List<string> Screenshots { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

class MobilePlaywrightTestRunner 
{
    public static async Task<MobileTestResult> RunMobileTest(string url, string? testName = null, string deviceType = "iPhone 12")
    {
        testName ??= $"Mobile Test for {url}";
        
        var testResult = new MobileTestResult
        {
            TestName = testName,
            Url = url,
            Platform = $"Mobile - {deviceType}",
            StartTime = DateTime.UtcNow
        };

        try 
        {
            Console.WriteLine($"📱 Starting mobile test: {testName}");
            Console.WriteLine($"📋 Test ID: {testResult.Id}");
            Console.WriteLine($"📱 Device: {deviceType}");
            
            Environment.SetEnvironmentVariable("PLAYWRIGHT_BROWSERS_PATH", "/ms-playwright");
            
            using var playwright = await Playwright.CreateAsync();
            Console.WriteLine("✅ Playwright initialized!");
            
            // Get device configuration
            var device = playwright.Devices[deviceType];
            
            await using var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions 
            { 
                Headless = true 
            });
            Console.WriteLine("✅ Browser launched!");
            
            // Create context with mobile device emulation
            await using var context = await browser.NewContextAsync(new BrowserNewContextOptions
            {
                ViewportSize = device.ViewportSize,
                UserAgent = device.UserAgent,
                DeviceScaleFactor = device.DeviceScaleFactor,
                IsMobile = device.IsMobile,
                HasTouch = device.HasTouch
            });
            
            var page = await context.NewPageAsync();
            Console.WriteLine($"✅ Mobile context created with viewport: {device.ViewportSize?.Width}x{device.ViewportSize?.Height}");
            
            // Navigate to URL
            Console.WriteLine($"🌐 Navigating to {url}...");
            await page.GotoAsync(url, new PageGotoOptions 
            { 
                WaitUntil = WaitUntilState.NetworkIdle,
                Timeout = 20000
            });
            Console.WriteLine("✅ Navigation completed!");
            
            // Take screenshot
            var screenshotDir = "/tmp/screenshots";
            Directory.CreateDirectory(screenshotDir);
            
            var screenshotPath = Path.Combine(screenshotDir, $"{testResult.Id}-mobile-{SanitizeFilename(testName)}.png");
            await page.ScreenshotAsync(new PageScreenshotOptions
            {
                Path = screenshotPath,
                FullPage = true
            });
            testResult.Screenshots.Add(screenshotPath);
            Console.WriteLine($"📸 Mobile screenshot saved: {screenshotPath}");
            
            // Get page information
            var title = await page.TitleAsync();
            var currentUrl = page.Url;
            
            // Analyze mobile-specific elements
            var linkCount = await page.Locator("a").CountAsync();
            var imageCount = await page.Locator("img").CountAsync();
            var formCount = await page.Locator("form").CountAsync();
            var buttonCount = await page.Locator("button").CountAsync();
            var inputCount = await page.Locator("input").CountAsync();
            
            // Check for mobile-specific meta tags
            var hasViewportMeta = await page.Locator("meta[name='viewport']").CountAsync() > 0;
            var hasAppleMobileWebAppCapable = await page.Locator("meta[name='apple-mobile-web-app-capable']").CountAsync() > 0;
            var hasAppleMobileWebAppTitle = await page.Locator("meta[name='apple-mobile-web-app-title']").CountAsync() > 0;
            
            // Check for touch-friendly elements
            var touchElements = await page.Locator("[onclick], [ontouchstart], [ontouchend]").CountAsync();
            
            // Get viewport size
            var viewportSize = await page.EvaluateAsync<dynamic>("() => ({ width: window.innerWidth, height: window.innerHeight })");
            
            // Check if page is responsive
            var isResponsive = await page.EvaluateAsync<bool>(@"
                () => {
                    const metaViewport = document.querySelector('meta[name=""viewport""]');
                    if (!metaViewport) return false;
                    const content = metaViewport.getAttribute('content') || '';
                    return content.includes('width=device-width') || content.includes('initial-scale');
                }
            ");
            
            // Test mobile interactions
            var mobileInteractions = new List<string>();
            
            // Try to test touch interactions if there are forms
            if (formCount > 0)
            {
                try
                {
                    // Take screenshot before interaction
                    var beforeInteractionPath = Path.Combine(screenshotDir, $"{testResult.Id}-mobile-before-interaction.png");
                    await page.ScreenshotAsync(new PageScreenshotOptions { Path = beforeInteractionPath, FullPage = true });
                    testResult.Screenshots.Add(beforeInteractionPath);
                    
                    // Try to find and interact with input fields
                    var textInputs = await page.Locator("input[type='text'], input[type='email'], input[type='search'], textarea").CountAsync();
                    if (textInputs > 0)
                    {
                        var firstInput = page.Locator("input[type='text'], input[type='email'], input[type='search'], textarea").First;
                        
                        // Simulate mobile touch interaction
                        await firstInput.TapAsync();
                        await firstInput.FillAsync("mobile test input");
                        
                        mobileInteractions.Add("Text input interaction");
                        
                        // Take screenshot after interaction
                        var afterInteractionPath = Path.Combine(screenshotDir, $"{testResult.Id}-mobile-after-interaction.png");
                        await page.ScreenshotAsync(new PageScreenshotOptions { Path = afterInteractionPath, FullPage = true });
                        testResult.Screenshots.Add(afterInteractionPath);
                    }
                }
                catch (Exception ex)
                {
                    mobileInteractions.Add($"Interaction failed: {ex.Message}");
                }
            }
            
            // Test scroll behavior
            try
            {
                var beforeScrollPath = Path.Combine(screenshotDir, $"{testResult.Id}-mobile-before-scroll.png");
                await page.ScreenshotAsync(new PageScreenshotOptions { Path = beforeScrollPath, FullPage = false });
                testResult.Screenshots.Add(beforeScrollPath);
                
                // Scroll down
                await page.EvaluateAsync("window.scrollBy(0, window.innerHeight)");
                await Task.Delay(1000);
                
                var afterScrollPath = Path.Combine(screenshotDir, $"{testResult.Id}-mobile-after-scroll.png");
                await page.ScreenshotAsync(new PageScreenshotOptions { Path = afterScrollPath, FullPage = false });
                testResult.Screenshots.Add(afterScrollPath);
                
                mobileInteractions.Add("Scroll interaction");
            }
            catch (Exception ex)
            {
                mobileInteractions.Add($"Scroll failed: {ex.Message}");
            }
            
            // Store metadata
            testResult.Metadata["title"] = title;
            testResult.Metadata["finalUrl"] = currentUrl;
            testResult.Metadata["linkCount"] = linkCount;
            testResult.Metadata["imageCount"] = imageCount;
            testResult.Metadata["formCount"] = formCount;
            testResult.Metadata["buttonCount"] = buttonCount;
            testResult.Metadata["inputCount"] = inputCount;
            testResult.Metadata["viewportWidth"] = viewportSize.width;
            testResult.Metadata["viewportHeight"] = viewportSize.height;
            testResult.Metadata["hasViewportMeta"] = hasViewportMeta;
            testResult.Metadata["hasAppleMobileWebAppCapable"] = hasAppleMobileWebAppCapable;
            testResult.Metadata["hasAppleMobileWebAppTitle"] = hasAppleMobileWebAppTitle;
            testResult.Metadata["touchElements"] = touchElements;
            testResult.Metadata["isResponsive"] = isResponsive;
            testResult.Metadata["deviceType"] = deviceType;
            testResult.Metadata["userAgent"] = device.UserAgent;
            testResult.Metadata["mobileInteractions"] = mobileInteractions;
            
            Console.WriteLine($"📄 Page title: {title}");
            Console.WriteLine($"📱 Viewport: {viewportSize.width}x{viewportSize.height}");
            Console.WriteLine($"🔗 Links: {linkCount}, 🖼️ Images: {imageCount}, 📝 Forms: {formCount}");
            Console.WriteLine($"🔘 Buttons: {buttonCount}, ⌨️ Inputs: {inputCount}");
            Console.WriteLine($"👆 Touch elements: {touchElements}");
            Console.WriteLine($"📐 Responsive: {isResponsive}");
            Console.WriteLine($"🤳 Mobile interactions: {mobileInteractions.Count}");
            
            // Mark test as passed
            testResult.Status = "Passed";
            testResult.EndTime = DateTime.UtcNow;
            
            Console.WriteLine("🎉 Mobile test completed successfully!");
        } 
        catch (Exception ex) 
        {
            testResult.Status = "Failed";
            testResult.ErrorMessage = ex.Message;
            testResult.EndTime = DateTime.UtcNow;
            
            Console.WriteLine($"❌ Mobile test failed: {ex.Message}");
        }
        
        // Save test results to JSON
        var resultsDir = "/tmp/test-results";
        Directory.CreateDirectory(resultsDir);
        
        var resultPath = Path.Combine(resultsDir, $"{testResult.Id}.json");
        var json = JsonSerializer.Serialize(testResult, new JsonSerializerOptions 
        { 
            WriteIndented = true 
        });
        
        await File.WriteAllTextAsync(resultPath, json);
        Console.WriteLine($"💾 Mobile test results saved: {resultPath}");
        
        return testResult;
    }

    static string SanitizeFilename(string filename)
    {
        var invalid = Path.GetInvalidFileNameChars();
        return string.Join("_", filename.Split(invalid, StringSplitOptions.RemoveEmptyEntries));
    }
}

class Program 
{
    static async Task Main(string[] args) 
    {
        if (args.Length == 0)
        {
            Console.WriteLine("Usage: dotnet run <url> [test-name] [device-type]");
            Console.WriteLine("Example: dotnet run https://m.wikipedia.org \"Mobile Wikipedia Test\" \"iPhone 12\"");
            Console.WriteLine("Available devices: iPhone 12, iPhone 13, iPad, Galaxy S9+, Pixel 5, etc.");
            return;
        }

        var url = args[0];
        var testName = args.Length > 1 ? args[1] : null;
        var deviceType = args.Length > 2 ? args[2] : "iPhone 12";

        var result = await MobilePlaywrightTestRunner.RunMobileTest(url, testName, deviceType);
        
        // Display summary
        var duration = result.EndTime - result.StartTime;
        Console.WriteLine("\n📊 MOBILE TEST SUMMARY:");
        Console.WriteLine($"   Test ID: {result.Id}");
        Console.WriteLine($"   Platform: {result.Platform}");
        Console.WriteLine($"   Status: {result.Status}");
        Console.WriteLine($"   Duration: {duration.TotalSeconds:F2} seconds");
        Console.WriteLine($"   Screenshots: {result.Screenshots.Count}");
        
        if (result.Status == "Passed")
        {
            Console.WriteLine("\n✅ Mobile test passed! 📱🎉");
        }
        else
        {
            Console.WriteLine($"\n❌ Mobile test failed: {result.ErrorMessage}");
        }
    }
}
