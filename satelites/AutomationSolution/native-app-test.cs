using OpenQA.Selenium;
using OpenQA.Selenium.Appium;
using OpenQA.Selenium.Appium.Android;
using OpenQA.Selenium.Chrome;
using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;

public class NativeAppTestResult
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TestName { get; set; } = string.Empty;
    public string AppUrl { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string Status { get; set; } = "Running";
    public string? ErrorMessage { get; set; }
    public List<string> Screenshots { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public List<string> TestSteps { get; set; } = new();
}

class NativeAppTestRunner 
{
    public static async Task<NativeAppTestResult> RunNativeAppTest(string appUrl, string? testName = null)
    {
        testName ??= "Native App Simulation Test";
        
        var testResult = new NativeAppTestResult
        {
            TestName = testName,
            AppUrl = appUrl,
            Platform = "Android Chrome (Native Mode)",
            StartTime = DateTime.UtcNow
        };

        AndroidDriver? driver = null;

        try 
        {
            Console.WriteLine($"📱 Starting native app test: {testName}");
            Console.WriteLine($"📋 Test ID: {testResult.Id}");
            Console.WriteLine($"🌐 App URL: {appUrl}");
            
            // Configure Appium options for native-like Chrome experience
            var options = new AppiumOptions();
            options.PlatformName = "Android";
            options.AutomationName = "UiAutomator2";
            options.DeviceName = "Android Device";
            options.AddAdditionalAppiumOption("browserName", "Chrome");
            options.AddAdditionalAppiumOption("chromedriverAutodownload", true);
            
            // Native app simulation options
            var chromeOptions = new ChromeOptions();
            chromeOptions.AddArgument("--no-sandbox");
            chromeOptions.AddArgument("--disable-dev-shm-usage");
            chromeOptions.AddArgument("--disable-gpu");
            chromeOptions.AddArgument("--headless");
            chromeOptions.AddArgument("--disable-web-security");
            chromeOptions.AddArgument("--allow-running-insecure-content");
            chromeOptions.AddArgument("--user-agent=Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36");
            
            // Simulate native app behavior
            chromeOptions.AddArgument("--app=" + appUrl);
            chromeOptions.AddArgument("--window-size=390,844"); // iPhone-like dimensions
            
            options.AddAdditionalAppiumOption("goog:chromeOptions", chromeOptions.ToCapabilities()["goog:chromeOptions"]);
            
            // Create driver
            var serverUri = new Uri("http://localhost:4723");
            driver = new AndroidDriver(serverUri, options, TimeSpan.FromSeconds(60));
            
            Console.WriteLine("✅ Native app driver initialized!");
            testResult.TestSteps.Add("Driver initialized successfully");
            
            // Navigate to app
            Console.WriteLine($"🚀 Launching app: {appUrl}");
            driver.Navigate().GoToUrl(appUrl);
            await Task.Delay(3000); // Wait for app to load
            
            Console.WriteLine("✅ App launched successfully!");
            testResult.TestSteps.Add("App launched and loaded");
            
            // Take initial screenshot
            var screenshotDir = "/tmp/screenshots";
            Directory.CreateDirectory(screenshotDir);
            
            var initialScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-native-app-initial.png");
            var screenshot = ((ITakesScreenshot)driver).GetScreenshot();
            await File.WriteAllBytesAsync(initialScreenshot, screenshot.AsByteArray);
            testResult.Screenshots.Add(initialScreenshot);
            Console.WriteLine($"📸 Initial screenshot captured");
            
            // Test 1: Fill demo data
            Console.WriteLine("🧪 Test 1: Filling demo data...");
            try
            {
                var fillDemoButton = driver.FindElement(By.XPath("//button[contains(text(), 'Fill Demo Data')]"));
                fillDemoButton.Click();
                await Task.Delay(1000);
                
                var demoScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-native-app-demo-filled.png");
                screenshot = ((ITakesScreenshot)driver).GetScreenshot();
                await File.WriteAllBytesAsync(demoScreenshot, screenshot.AsByteArray);
                testResult.Screenshots.Add(demoScreenshot);
                
                testResult.TestSteps.Add("Demo data filled successfully");
                Console.WriteLine("✅ Demo data filled");
            }
            catch (Exception ex)
            {
                testResult.TestSteps.Add($"Demo data fill failed: {ex.Message}");
                Console.WriteLine($"⚠️ Demo data fill failed: {ex.Message}");
            }
            
            // Test 2: Login form submission
            Console.WriteLine("🧪 Test 2: Testing login form...");
            try
            {
                var loginButton = driver.FindElement(By.XPath("//button[@type='submit' and contains(text(), 'Login')]"));
                loginButton.Click();
                await Task.Delay(2000);
                
                var loginScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-native-app-login.png");
                screenshot = ((ITakesScreenshot)driver).GetScreenshot();
                await File.WriteAllBytesAsync(loginScreenshot, screenshot.AsByteArray);
                testResult.Screenshots.Add(loginScreenshot);
                
                testResult.TestSteps.Add("Login form submitted successfully");
                Console.WriteLine("✅ Login form submitted");
            }
            catch (Exception ex)
            {
                testResult.TestSteps.Add($"Login form submission failed: {ex.Message}");
                Console.WriteLine($"⚠️ Login failed: {ex.Message}");
            }
            
            // Test 3: Navigate to Features tab
            Console.WriteLine("🧪 Test 3: Navigating to Features tab...");
            try
            {
                var featuresTab = driver.FindElement(By.XPath("//div[contains(@class, 'tab') and contains(text(), 'Features')]"));
                featuresTab.Click();
                await Task.Delay(1000);
                
                var featuresScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-native-app-features.png");
                screenshot = ((ITakesScreenshot)driver).GetScreenshot();
                await File.WriteAllBytesAsync(featuresScreenshot, screenshot.AsByteArray);
                testResult.Screenshots.Add(featuresScreenshot);
                
                testResult.TestSteps.Add("Navigated to Features tab");
                Console.WriteLine("✅ Features tab opened");
            }
            catch (Exception ex)
            {
                testResult.TestSteps.Add($"Features tab navigation failed: {ex.Message}");
                Console.WriteLine($"⚠️ Features navigation failed: {ex.Message}");
            }
            
            // Test 4: Test feature button
            Console.WriteLine("🧪 Test 4: Testing feature button...");
            try
            {
                var testFeatureButton = driver.FindElement(By.XPath("//button[contains(text(), 'Test Feature')]"));
                testFeatureButton.Click();
                await Task.Delay(1000);
                
                var featureTestScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-native-app-feature-test.png");
                screenshot = ((ITakesScreenshot)driver).GetScreenshot();
                await File.WriteAllBytesAsync(featureTestScreenshot, screenshot.AsByteArray);
                testResult.Screenshots.Add(featureTestScreenshot);
                
                testResult.TestSteps.Add("Feature test button clicked successfully");
                Console.WriteLine("✅ Feature test completed");
            }
            catch (Exception ex)
            {
                testResult.TestSteps.Add($"Feature test failed: {ex.Message}");
                Console.WriteLine($"⚠️ Feature test failed: {ex.Message}");
            }
            
            // Test 5: Navigate to Settings and fill form
            Console.WriteLine("🧪 Test 5: Testing Settings tab...");
            try
            {
                var settingsTab = driver.FindElement(By.XPath("//div[contains(@class, 'tab') and contains(text(), 'Settings')]"));
                settingsTab.Click();
                await Task.Delay(1000);
                
                // Fill settings form
                var themeSelect = driver.FindElement(By.Id("theme"));
                themeSelect.Click();
                var darkOption = driver.FindElement(By.XPath("//option[@value='dark']"));
                darkOption.Click();
                
                var feedbackTextarea = driver.FindElement(By.Id("feedback"));
                feedbackTextarea.Clear();
                feedbackTextarea.SendKeys("This is an automated test feedback from Appium!");
                
                var saveButton = driver.FindElement(By.XPath("//button[contains(text(), 'Save Settings')]"));
                saveButton.Click();
                await Task.Delay(1000);
                
                var settingsScreenshot = Path.Combine(screenshotDir, $"{testResult.Id}-native-app-settings.png");
                screenshot = ((ITakesScreenshot)driver).GetScreenshot();
                await File.WriteAllBytesAsync(settingsScreenshot, screenshot.AsByteArray);
                testResult.Screenshots.Add(settingsScreenshot);
                
                testResult.TestSteps.Add("Settings form filled and saved successfully");
                Console.WriteLine("✅ Settings test completed");
            }
            catch (Exception ex)
            {
                testResult.TestSteps.Add($"Settings test failed: {ex.Message}");
                Console.WriteLine($"⚠️ Settings test failed: {ex.Message}");
            }
            
            // Get app information
            var title = driver.Title;
            var currentUrl = driver.Url;
            var windowSize = driver.Manage().Window.Size;
            
            // Store metadata
            testResult.Metadata["title"] = title;
            testResult.Metadata["finalUrl"] = currentUrl;
            testResult.Metadata["windowWidth"] = windowSize.Width;
            testResult.Metadata["windowHeight"] = windowSize.Height;
            testResult.Metadata["totalTestSteps"] = testResult.TestSteps.Count;
            testResult.Metadata["successfulSteps"] = testResult.TestSteps.Count(s => s.Contains("successfully"));
            testResult.Metadata["failedSteps"] = testResult.TestSteps.Count(s => s.Contains("failed"));
            
            Console.WriteLine($"📄 App title: {title}");
            Console.WriteLine($"📱 Window size: {windowSize.Width}x{windowSize.Height}");
            Console.WriteLine($"✅ Test steps completed: {testResult.TestSteps.Count}");
            
            // Mark test as passed
            testResult.Status = "Passed";
            testResult.EndTime = DateTime.UtcNow;
            
            Console.WriteLine("🎉 Native app test completed successfully!");
        } 
        catch (Exception ex) 
        {
            testResult.Status = "Failed";
            testResult.ErrorMessage = ex.Message;
            testResult.EndTime = DateTime.UtcNow;
            testResult.TestSteps.Add($"Test failed with error: {ex.Message}");
            
            Console.WriteLine($"❌ Native app test failed: {ex.Message}");
        }
        finally
        {
            // Clean up driver
            if (driver != null)
            {
                try
                {
                    driver.Quit();
                    driver.Dispose();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"⚠️ Error disposing driver: {ex.Message}");
                }
            }
        }
        
        // Save test results to JSON
        var resultsDir = "/tmp/test-results";
        Directory.CreateDirectory(resultsDir);
        
        var resultPath = Path.Combine(resultsDir, $"{testResult.Id}.json");
        var json = JsonSerializer.Serialize(testResult, new JsonSerializerOptions 
        { 
            WriteIndented = true 
        });
        
        await File.WriteAllTextAsync(resultPath, json);
        Console.WriteLine($"💾 Native app test results saved: {resultPath}");
        
        return testResult;
    }
}

class Program 
{
    static async Task Main(string[] args) 
    {
        var appUrl = args.Length > 0 ? args[0] : "http://localhost:8083";
        var testName = args.Length > 1 ? args[1] : "Native Mobile App Test";

        var result = await NativeAppTestRunner.RunNativeAppTest(appUrl, testName);
        
        // Display summary
        var duration = result.EndTime - result.StartTime;
        Console.WriteLine("\n📊 NATIVE APP TEST SUMMARY:");
        Console.WriteLine($"   Test ID: {result.Id}");
        Console.WriteLine($"   Platform: {result.Platform}");
        Console.WriteLine($"   Status: {result.Status}");
        Console.WriteLine($"   Duration: {duration.TotalSeconds:F2} seconds");
        Console.WriteLine($"   Screenshots: {result.Screenshots.Count}");
        Console.WriteLine($"   Test Steps: {result.TestSteps.Count}");
        
        if (result.Status == "Passed")
        {
            Console.WriteLine("\n✅ Native app test passed! 📱🎉");
        }
        else
        {
            Console.WriteLine($"\n❌ Native app test failed: {result.ErrorMessage}");
        }
    }
}
