# Docker Deployment Guide

## 🐳 Overview

This guide provides comprehensive instructions for deploying the Automation Solution using Docker containers. The solution includes multi-stage Dockerfiles, production-ready Docker Compose configurations, and automated deployment scripts.

## 📋 Prerequisites

### System Requirements
- **Docker**: Version 20.10+ with BuildKit support
- **Docker Compose**: Version 2.0+
- **System Memory**: Minimum 4GB RAM, Recommended 8GB+
- **Storage**: Minimum 20GB free space
- **Network**: Outbound HTTPS access for image pulls

### Optional Tools
- **Docker Buildx**: For multi-platform builds
- **Trivy**: For security scanning
- **ReportGenerator**: For test coverage reports

## 🏗️ Architecture Overview

### Container Structure
```
┌─────────────────────────────────────────────────────────────┐
│                    Docker Architecture                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   Nginx     │  │  Automation │  │ Prometheus  │        │
│  │Load Balancer│  │   Worker    │  │  Metrics    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│         │                 │                 │             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │    Redis    │  │   Grafana   │  │   Volumes   │        │
│  │Message Queue│  │ Dashboards  │  │    Data     │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

### Available Images
- **automation-solution/worker**: Main application service
- **automation-solution/worker:minimal**: Lightweight version without browsers
- **redis:7.2-alpine**: Message queue and caching
- **nginx:1.25-alpine**: Load balancer and reverse proxy
- **prometheus:v2.47.0**: Metrics collection
- **grafana:10.1.0**: Metrics visualization

## 🚀 Quick Start

### 1. Development Environment

```bash
# Clone and navigate to project
git clone <repository-url>
cd AutomationSolution

# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# Check status
docker-compose -f docker-compose.dev.yml ps

# View logs
docker-compose -f docker-compose.dev.yml logs -f automation-worker-dev
```

**Development URLs:**
- **Application**: http://localhost:8080
- **Redis Commander**: http://localhost:8081 (admin/admin123)
- **Prometheus**: http://localhost:9090
- **Health Check**: http://localhost:8080/health
- **Metrics**: http://localhost:8080/metrics

### 2. Production Environment

```bash
# Set environment variables
cp config/environments/.env.production .env
# Edit .env file with your production settings

# Deploy to production
./scripts/deployment/deploy.sh -e production deploy

# Check status
./scripts/deployment/deploy.sh status

# View logs
./scripts/deployment/deploy.sh logs
```

## 🔧 Building Images

### Using Build Script

```bash
# Build production image
./scripts/docker/build.sh

# Build development image
./scripts/docker/build.sh -t development

# Build and push to registry
./scripts/docker/build.sh -v 1.0.0 -r your-registry.com -p

# Build minimal image (no browsers)
./scripts/docker/build.sh --minimal

# Clean build (no cache)
./scripts/docker/build.sh -c
```

### Manual Build

```bash
# Production build
docker build -f Dockerfile.worker -t automation-solution/worker:latest .

# Development build
docker build -f Dockerfile.worker --target development -t automation-solution/worker:dev .

# Testing build
docker build -f Dockerfile.worker --target testing -t automation-solution/worker:test .

# Minimal build
docker build -f Dockerfile.worker.minimal -t automation-solution/worker:minimal .
```

## 🚀 Deployment Options

### 1. Development Deployment

```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# Scale workers
docker-compose -f docker-compose.dev.yml up -d --scale automation-worker-dev=3

# Stop environment
docker-compose -f docker-compose.dev.yml down
```

### 2. Production Deployment

```bash
# Using deployment script (recommended)
./scripts/deployment/deploy.sh -e production deploy

# Manual deployment
docker-compose -f docker-compose.production.yml --env-file config/environments/.env.production up -d

# Scale workers
docker-compose -f docker-compose.production.yml up -d --scale automation-worker=5
```

### 3. Staging Deployment

```bash
# Deploy to staging
./scripts/deployment/deploy.sh -e staging deploy

# Update staging
./scripts/deployment/deploy.sh -e staging update
```

## 🧪 Testing

### Running Tests in Docker

```bash
# Run all tests
./scripts/docker/test.sh

# Run specific test category
./scripts/docker/test.sh -c Integration

# Run with coverage
./scripts/docker/test.sh --coverage

# Verbose output
./scripts/docker/test.sh -v

# Keep containers for debugging
./scripts/docker/test.sh --keep
```

### Manual Test Execution

```bash
# Start test environment
docker-compose -f docker-compose.dev.yml --profile testing up -d

# Run tests
docker-compose -f docker-compose.dev.yml run --rm test-runner

# Cleanup
docker-compose -f docker-compose.dev.yml --profile testing down
```

## 📊 Monitoring and Observability

### Accessing Monitoring Services

**Production URLs:**
- **Grafana**: http://localhost:3000 (admin/SecurePassword123!)
- **Prometheus**: http://localhost:9090
- **Application Metrics**: http://localhost:8080/metrics
- **Health Checks**: http://localhost:8080/health

### Custom Dashboards

Grafana dashboards are automatically provisioned from `config/grafana/dashboards/`.

### Alerts

Prometheus alerts are configured in `config/alert_rules.yml` and include:
- High error rates
- Service downtime
- Memory/CPU usage
- Circuit breaker states
- Task queue backlogs

## 🔒 Security Configuration

### Container Security

```yaml
# Security options applied to all containers
security_opt:
  - no-new-privileges:true
cap_drop:
  - ALL
cap_add:
  - NET_BIND_SERVICE  # Only for services that need it
read_only: true  # Where possible
user: "1001:1001"  # Non-root user
```

### Network Security

```yaml
# Isolated networks
networks:
  automation-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

### Secrets Management

```bash
# Use Docker secrets for sensitive data
echo "your-secret-password" | docker secret create redis_password -

# Reference in compose file
secrets:
  - redis_password
```

## 📈 Performance Optimization

### Resource Limits

```yaml
deploy:
  resources:
    limits:
      memory: 2G
      cpus: '1.0'
    reservations:
      memory: 512M
      cpus: '0.5'
```

### Build Optimization

```dockerfile
# Multi-stage builds for smaller images
FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS build
# ... build stage

FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine AS runtime
# ... runtime stage
```

### Volume Optimization

```yaml
volumes:
  # Use named volumes for better performance
  redis_data:
    driver: local
  # Use tmpfs for temporary data
tmpfs:
  - /tmp:noexec,nosuid,size=100m
```

## 🔄 Scaling and Load Balancing

### Horizontal Scaling

```bash
# Scale workers
docker-compose -f docker-compose.production.yml up -d --scale automation-worker=5

# Using deployment script
./scripts/deployment/deploy.sh --scale 5 deploy
```

### Load Balancing

Nginx is configured as a load balancer with:
- **Algorithm**: Least connections
- **Health checks**: Automatic failover
- **Rate limiting**: API protection
- **SSL termination**: HTTPS support

### Auto-scaling (Docker Swarm)

```bash
# Initialize swarm
docker swarm init

# Deploy stack
docker stack deploy -c docker-compose.production.yml automation

# Scale service
docker service scale automation_automation-worker=5
```

## 🛠️ Troubleshooting

### Common Issues

#### 1. Container Won't Start
```bash
# Check logs
docker-compose logs automation-worker

# Check resource usage
docker stats

# Inspect container
docker inspect automation-worker
```

#### 2. Health Check Failures
```bash
# Test health endpoint manually
curl http://localhost:8080/health

# Check container health
docker ps --format "table {{.Names}}\t{{.Status}}"
```

#### 3. Network Issues
```bash
# List networks
docker network ls

# Inspect network
docker network inspect automation-network

# Test connectivity
docker-compose exec automation-worker ping redis
```

#### 4. Volume Issues
```bash
# List volumes
docker volume ls

# Inspect volume
docker volume inspect automation_redis_data

# Backup volume
docker run --rm -v automation_redis_data:/data -v $(pwd):/backup alpine tar czf /backup/redis_backup.tar.gz -C /data .
```

### Performance Issues

```bash
# Monitor resource usage
docker stats --no-stream

# Check container logs for errors
docker-compose logs --tail=100 automation-worker

# Analyze metrics
curl http://localhost:8080/metrics | grep automation_
```

### Debug Mode

```bash
# Run container in debug mode
docker run -it --rm automation-solution/worker:dev bash

# Attach to running container
docker exec -it automation-worker bash

# Enable verbose logging
docker-compose -f docker-compose.dev.yml up -d
```

## 📋 Maintenance

### Regular Tasks

```bash
# Update images
docker-compose pull

# Clean unused resources
docker system prune -f

# Backup data
./scripts/deployment/deploy.sh backup

# Update application
./scripts/deployment/deploy.sh update
```

### Log Management

```bash
# Rotate logs
docker-compose exec automation-worker logrotate /etc/logrotate.conf

# View logs with timestamps
docker-compose logs -t automation-worker

# Export logs
docker-compose logs --no-color automation-worker > automation.log
```

## 🔐 Production Checklist

### Before Deployment
- [ ] Update environment variables in `.env.production`
- [ ] Configure SSL certificates
- [ ] Set strong passwords for Grafana and Redis
- [ ] Review resource limits
- [ ] Test backup and restore procedures
- [ ] Configure monitoring alerts
- [ ] Review security settings
- [ ] Test health checks
- [ ] Validate network configuration
- [ ] Prepare rollback plan

### After Deployment
- [ ] Verify all services are healthy
- [ ] Test application functionality
- [ ] Check monitoring dashboards
- [ ] Validate log collection
- [ ] Test backup procedures
- [ ] Monitor resource usage
- [ ] Verify SSL certificates
- [ ] Test scaling procedures
- [ ] Document any customizations
- [ ] Schedule regular maintenance

## 📚 Additional Resources

- [Docker Best Practices](https://docs.docker.com/develop/best-practices/)
- [Docker Compose Reference](https://docs.docker.com/compose/compose-file/)
- [Container Security](https://docs.docker.com/engine/security/)
- [Production Deployment Guide](PRODUCTION_DEPLOYMENT.md)
- [Performance Optimization Guide](PERFORMANCE_OPTIMIZATION.md)
