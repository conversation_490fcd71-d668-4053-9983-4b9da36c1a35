# Testing Guide

## Overview

This guide covers all testing aspects of the Automation Solution, including unit tests, integration tests, performance tests, and security tests.

## Test Structure

```
tests/
├── Automation.Integration.Tests/     # Integration and E2E tests
│   ├── Infrastructure/               # Test infrastructure
│   ├── EndToEndTests.cs             # Complete workflow tests
│   ├── PerformanceTests.cs          # Performance validation
│   ├── SecurityTests.cs             # Security validation
│   ├── ProductionReadinessTests.cs  # Production readiness
│   └── TestData/                    # Test data files
└── [Future unit test projects]
```

## Running Tests

### Prerequisites

1. **Docker**: For Redis test containers
2. **.NET 8.0 SDK**: For building and running tests
3. **Chrome/Chromium**: For web automation tests

### Quick Start

```bash
# Run all integration tests
dotnet test tests/Automation.Integration.Tests/

# Run specific test class
dotnet test tests/Automation.Integration.Tests/ --filter "ClassName=EndToEndTests"

# Run with detailed output
dotnet test tests/Automation.Integration.Tests/ --logger "console;verbosity=detailed"
```

### Test Categories

#### 1. End-to-End Tests
```bash
# Complete workflow validation
dotnet test --filter "Category=EndToEnd"

# Test specific scenarios
dotnet test --filter "TestCategory=WebAutomation"
dotnet test --filter "TestCategory=MobileAutomation"
```

#### 2. Performance Tests
```bash
# Performance validation
dotnet test --filter "Category=Performance"

# Load testing
dotnet test --filter "TestCategory=LoadTest"

# Memory testing
dotnet test --filter "TestCategory=MemoryTest"
```

#### 3. Security Tests
```bash
# Security validation
dotnet test --filter "Category=Security"

# Input validation tests
dotnet test --filter "TestCategory=InputValidation"
```

#### 4. Production Readiness Tests
```bash
# Production readiness validation
dotnet test --filter "Category=ProductionReadiness"
```

## Test Configuration

### Environment Variables

Set these environment variables for testing:

```bash
# Test environment
export ASPNETCORE_ENVIRONMENT=Testing

# Redis (will be provided by test containers)
export REDIS_CONNECTION_STRING=localhost:6379

# Disable external dependencies for unit tests
export AUTOMATION_DISABLE_EXTERNAL_CALLS=true

# Test timeouts
export AUTOMATION_TEST_TIMEOUT_MS=30000
```

### Test Settings

Create `testsettings.json`:

```json
{
  "TestSettings": {
    "EnableContainerTests": true,
    "TestTimeout": "00:00:30",
    "RedisTimeout": "00:00:10",
    "BrowserTimeout": "00:00:30",
    "ParallelExecution": false,
    "RetryCount": 3
  },
  "Worker": {
    "Redis": {
      "TaskChannel": "test_automation_channel",
      "DeadLetterQueue": "test_automation_dlq"
    },
    "Concurrency": {
      "MaxConcurrentTasks": 2
    },
    "CircuitBreaker": {
      "FailureThreshold": 3
    }
  }
}
```

## Test Data Management

### Sample Test Data

The `TestData/sample-tasks.json` file contains predefined test scenarios:

```json
{
  "webTasks": [
    {
      "taskId": "web-001",
      "taskType": "web",
      "description": "Simple navigation test",
      "actions": [...]
    }
  ],
  "errorTasks": [
    {
      "taskId": "error-001",
      "taskType": "web",
      "description": "Invalid selector test",
      "actions": [...]
    }
  ]
}
```

### Creating Custom Test Data

```csharp
// In your test class
var testTask = CreateTestTaskMessage("web", 
    CreateWebAction("Navigate", "", "https://example.com"),
    CreateWebAction("Click", "#button", ""),
    CreateWebAction("Screenshot", "", "test-result.png"));
```

## Performance Testing

### Load Testing

```csharp
[Fact]
public async Task LoadTest_ShouldHandleHighVolume()
{
    // Arrange
    var tasks = new List<Task<TaskProcessingResult>>();
    
    // Act - Create 100 concurrent tasks
    for (int i = 0; i < 100; i++)
    {
        tasks.Add(ProcessTaskAsync(CreateTestTask()));
    }
    
    var results = await Task.WhenAll(tasks);
    
    // Assert
    var successRate = results.Count(r => r.IsSuccess) / (double)results.Length;
    successRate.Should().BeGreaterThan(0.95); // 95% success rate
}
```

### Performance Benchmarks

Expected performance benchmarks:

| Metric | Target | Measurement |
|--------|--------|-------------|
| Task Processing | < 10s average | Response time |
| Throughput | > 10 tasks/sec | Concurrent processing |
| Memory Usage | < 50MB increase | Long-running tests |
| Success Rate | > 95% | Under normal load |
| P95 Response Time | < 15s | 95th percentile |

### Memory Testing

```csharp
[Fact]
public async Task MemoryTest_ShouldNotLeak()
{
    var initialMemory = GC.GetTotalMemory(true);
    
    // Process many tasks
    for (int i = 0; i < 100; i++)
    {
        await ProcessTaskAsync(CreateTestTask());
        if (i % 20 == 0) GC.Collect();
    }
    
    var finalMemory = GC.GetTotalMemory(true);
    var increase = finalMemory - initialMemory;
    
    increase.Should().BeLessThan(50 * 1024 * 1024); // < 50MB
}
```

## Security Testing

### Input Validation Tests

```csharp
[Theory]
[InlineData("'; DROP TABLE users; --")]
[InlineData("<script>alert('xss')</script>")]
[InlineData("../../../etc/passwd")]
public async Task InputValidation_ShouldRejectMaliciousInput(string maliciousInput)
{
    var result = await ProcessTaskAsync(CreateMaliciousTask(maliciousInput));
    
    // Should either fail gracefully or sanitize input
    if (result.IsSuccess)
    {
        result.ActionsExecuted.Should().Be(0);
    }
    else
    {
        result.ErrorMessage.Should().NotBeNullOrEmpty();
    }
}
```

### Security Checklist

- [ ] Input sanitization for all user inputs
- [ ] URL validation for navigation actions
- [ ] File path validation for screenshots
- [ ] Resource limits to prevent DoS
- [ ] Error message sanitization
- [ ] Configuration secret protection
- [ ] Network access restrictions

## Continuous Integration

### GitHub Actions Workflow

Create `.github/workflows/tests.yml`:

```yaml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '8.0.x'
    
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y chromium-browser
    
    - name: Restore dependencies
      run: dotnet restore
    
    - name: Build
      run: dotnet build --no-restore
    
    - name: Test
      run: dotnet test --no-build --verbosity normal --collect:"XPlat Code Coverage"
      env:
        REDIS_CONNECTION_STRING: localhost:6379
        ASPNETCORE_ENVIRONMENT: Testing
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
```

### Test Reporting

```bash
# Generate test report
dotnet test --logger "trx;LogFileName=test-results.trx"

# Generate coverage report
dotnet test --collect:"XPlat Code Coverage" --results-directory ./coverage

# Convert coverage to HTML
reportgenerator -reports:"./coverage/*/coverage.cobertura.xml" -targetdir:"./coverage/html"
```

## Test Maintenance

### Regular Test Review

1. **Weekly**: Review failing tests and flaky tests
2. **Monthly**: Update test data and scenarios
3. **Quarterly**: Performance benchmark review
4. **Annually**: Complete test strategy review

### Test Data Refresh

```bash
# Update test data
curl -o tests/Automation.Integration.Tests/TestData/sample-tasks.json \
  https://api.example.com/test-scenarios

# Validate test data
dotnet run --project tools/TestDataValidator
```

### Flaky Test Management

```csharp
// Add retry logic for flaky tests
[Fact]
[Retry(3)]
public async Task FlakyTest_ShouldEventuallyPass()
{
    // Test implementation with potential timing issues
}
```

## Debugging Tests

### Local Debugging

```bash
# Run tests with debugging
dotnet test --logger "console;verbosity=diagnostic"

# Run specific test with debugging
dotnet test --filter "FullyQualifiedName=Automation.Integration.Tests.EndToEndTests.CompleteWorkflow_ShouldProcessTaskSuccessfully"

# Attach debugger
dotnet test --logger "console;verbosity=diagnostic" --debug
```

### Test Containers Debugging

```csharp
// Enable container logging
var redis = new RedisBuilder()
    .WithImage("redis:7-alpine")
    .WithPortBinding(6379, true)
    .WithWaitStrategy(Wait.ForUnixContainer().UntilPortIsAvailable(6379))
    .WithLogger(TestContext.Progress.WriteLine) // Enable logging
    .Build();
```

### Common Issues

#### 1. Container Startup Issues
```bash
# Check Docker daemon
docker ps
docker logs <container-id>

# Clean up containers
docker system prune -f
```

#### 2. Browser Issues
```bash
# Install browser dependencies
sudo apt-get install -y libnss3 libatk-bridge2.0-0 libdrm2

# Check browser installation
chromium-browser --version
```

#### 3. Redis Connection Issues
```bash
# Test Redis connection
redis-cli ping

# Check Redis logs
docker logs <redis-container-id>
```

## Test Metrics

### Key Metrics to Track

1. **Test Coverage**: > 80% code coverage
2. **Test Execution Time**: < 5 minutes for full suite
3. **Flaky Test Rate**: < 5% of tests
4. **Test Success Rate**: > 98% on main branch

### Monitoring Test Health

```bash
# Test execution time tracking
dotnet test --logger "console;verbosity=normal" | grep "Total time"

# Coverage tracking
dotnet test --collect:"XPlat Code Coverage" | grep "Coverage"

# Flaky test detection
grep -r "Retry" test-results/ | wc -l
```

## Best Practices

### Test Organization
- Group related tests in the same class
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)
- Keep tests independent and isolated

### Test Data
- Use realistic test data
- Avoid hardcoded values
- Clean up test data after tests
- Use test data builders for complex objects

### Performance
- Run performance tests in isolated environment
- Use consistent hardware for benchmarks
- Monitor test execution time trends
- Parallelize tests where possible

### Security
- Test with malicious inputs
- Validate error messages don't leak information
- Test resource limits and timeouts
- Verify secure configuration handling

## Troubleshooting

### Common Test Failures

1. **Timeout Issues**: Increase test timeouts or optimize test performance
2. **Container Issues**: Ensure Docker is running and has sufficient resources
3. **Browser Issues**: Install required browser dependencies
4. **Redis Issues**: Check Redis container startup and connectivity
5. **Flaky Tests**: Add retry logic or improve test stability

### Getting Help

- Check test logs for detailed error information
- Review test documentation and examples
- Use debugging tools and breakpoints
- Consult team knowledge base and runbooks
