# Production Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the Automation Solution to production environments.

## Prerequisites

### System Requirements
- **Operating System**: Windows Server 2019+ or Linux (Ubuntu 20.04+)
- **Runtime**: .NET 8.0 Runtime
- **Memory**: Minimum 4GB RAM, Recommended 8GB+
- **Storage**: Minimum 10GB free space
- **Network**: Outbound HTTPS access for web automation

### Dependencies
- **Redis**: Version 6.0+ for message queuing and caching
- **Browser**: Chromium/Chrome for web automation
- **Android SDK**: For mobile automation (if required)
- **Appium Server**: Version 2.0+ for mobile automation

## Installation Steps

### 1. Install .NET 8.0 Runtime

#### Windows
```powershell
# Download and install from Microsoft
winget install Microsoft.DotNet.Runtime.8
```

#### Linux (Ubuntu)
```bash
# Add Microsoft package repository
wget https://packages.microsoft.com/config/ubuntu/20.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb

# Install .NET 8.0 Runtime
sudo apt-get update
sudo apt-get install -y dotnet-runtime-8.0
```

### 2. Install Redis

#### Windows
```powershell
# Using Chocolatey
choco install redis-64

# Or download from Redis website
# https://redis.io/download
```

#### Linux
```bash
# Ubuntu/Debian
sudo apt-get install redis-server

# Start Redis service
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

### 3. Deploy Application

#### Option A: Self-Contained Deployment
```bash
# Build self-contained deployment
dotnet publish src/Automation.Worker.CSharp/Automation.Worker.CSharp.csproj \
  -c Release \
  -r win-x64 \
  --self-contained true \
  -o ./publish/worker

# Copy to production server
scp -r ./publish/worker user@server:/opt/automation/
```

#### Option B: Framework-Dependent Deployment
```bash
# Build framework-dependent deployment
dotnet publish src/Automation.Worker.CSharp/Automation.Worker.CSharp.csproj \
  -c Release \
  -o ./publish/worker

# Copy to production server
scp -r ./publish/worker user@server:/opt/automation/
```

## Configuration

### 1. Production Configuration File

Create `appsettings.Production.json`:

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "System": "Warning"
    }
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information"
    },
    "WriteTo": [
      {
        "Name": "Console"
      },
      {
        "Name": "File",
        "Args": {
          "path": "/var/log/automation/worker-.log",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 30
        }
      }
    ]
  },
  "Worker": {
    "Redis": {
      "ConnectionString": "${REDIS_CONNECTION_STRING}",
      "TaskChannel": "automation_channel",
      "DeadLetterQueue": "automation_dlq"
    },
    "Concurrency": {
      "MaxConcurrentTasks": 10,
      "TaskTimeoutMs": 600000
    },
    "CircuitBreaker": {
      "FailureThreshold": 10,
      "FailureWindow": "00:02:00",
      "OpenDuration": "00:02:00"
    },
    "HealthCheck": {
      "Enabled": true,
      "Port": 8080
    },
    "Metrics": {
      "Enabled": true,
      "Port": 8080
    }
  },
  "WebAutomation": {
    "Headless": true,
    "ResourceManagement": {
      "MaxConcurrentBrowsers": 5,
      "BrowserIdleTimeoutMinutes": 5
    }
  }
}
```

### 2. Environment Variables

Set the following environment variables:

```bash
# Redis connection
export REDIS_CONNECTION_STRING="localhost:6379"

# Environment
export ASPNETCORE_ENVIRONMENT="Production"

# Logging
export AUTOMATION_LOG_LEVEL="Information"

# Security (if using authentication)
export AUTOMATION_API_KEY="your-secure-api-key"
```

### 3. Security Configuration

#### Redis Security
```bash
# Configure Redis authentication
echo "requirepass your-secure-password" >> /etc/redis/redis.conf
echo "bind 127.0.0.1" >> /etc/redis/redis.conf

# Restart Redis
sudo systemctl restart redis-server
```

#### Firewall Configuration
```bash
# Allow only necessary ports
sudo ufw allow 8080/tcp  # Health checks and metrics
sudo ufw allow 6379/tcp  # Redis (internal only)
sudo ufw enable
```

## Service Installation

### Windows Service

#### 1. Install as Windows Service
```powershell
# Create service using sc command
sc create "AutomationWorker" `
  binPath="C:\automation\Automation.Worker.CSharp.exe" `
  start=auto `
  DisplayName="Automation Worker Service"

# Start service
sc start AutomationWorker
```

#### 2. Service Configuration
Create `service-config.xml`:
```xml
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>
  <appSettings>
    <add key="ASPNETCORE_ENVIRONMENT" value="Production" />
  </appSettings>
</configuration>
```

### Linux Systemd Service

#### 1. Create Service File
Create `/etc/systemd/system/automation-worker.service`:

```ini
[Unit]
Description=Automation Worker Service
After=network.target redis.service

[Service]
Type=notify
ExecStart=/opt/automation/Automation.Worker.CSharp
Restart=always
RestartSec=10
User=automation
Group=automation
Environment=ASPNETCORE_ENVIRONMENT=Production
Environment=REDIS_CONNECTION_STRING=localhost:6379
WorkingDirectory=/opt/automation
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

#### 2. Enable and Start Service
```bash
# Create automation user
sudo useradd -r -s /bin/false automation
sudo chown -R automation:automation /opt/automation

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable automation-worker
sudo systemctl start automation-worker

# Check status
sudo systemctl status automation-worker
```

## Monitoring and Observability

### 1. Health Checks

The service exposes health check endpoints:

- **Health**: `http://localhost:8080/health`
- **Readiness**: `http://localhost:8080/health/ready`
- **Liveness**: `http://localhost:8080/health/live`

### 2. Metrics

Prometheus-compatible metrics are available at:
- **Metrics**: `http://localhost:8080/metrics`
- **Detailed Metrics**: `http://localhost:8080/metrics/detailed`

### 3. Logging

Logs are written to:
- **Console**: Structured JSON logs
- **File**: `/var/log/automation/worker-YYYY-MM-DD.log`
- **System Journal**: `journalctl -u automation-worker`

### 4. Monitoring Setup

#### Prometheus Configuration
```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'automation-worker'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/metrics'
    scrape_interval: 15s
```

#### Grafana Dashboard
Import the provided Grafana dashboard from `docs/grafana-dashboard.json`

## Performance Tuning

### 1. Resource Allocation

#### Memory Settings
```bash
# Set memory limits
export DOTNET_GCHeapHardLimit=**********  # 2GB
export DOTNET_GCHighMemPercent=75
```

#### CPU Settings
```json
{
  "Worker": {
    "Concurrency": {
      "MaxConcurrentTasks": 10  // Adjust based on CPU cores
    }
  }
}
```

### 2. Redis Optimization

```bash
# Redis memory optimization
echo "maxmemory 1gb" >> /etc/redis/redis.conf
echo "maxmemory-policy allkeys-lru" >> /etc/redis/redis.conf
```

### 3. Browser Optimization

```json
{
  "WebAutomation": {
    "ResourceManagement": {
      "MaxConcurrentBrowsers": 5,
      "BrowserIdleTimeoutMinutes": 5,
      "EnableBrowserPooling": true
    },
    "LaunchOptions": {
      "Args": [
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--disable-gpu",
        "--memory-pressure-off"
      ]
    }
  }
}
```

## Backup and Recovery

### 1. Configuration Backup
```bash
# Backup configuration files
tar -czf automation-config-$(date +%Y%m%d).tar.gz \
  /opt/automation/appsettings.Production.json \
  /etc/systemd/system/automation-worker.service
```

### 2. Redis Backup
```bash
# Create Redis backup
redis-cli BGSAVE
cp /var/lib/redis/dump.rdb /backup/redis-$(date +%Y%m%d).rdb
```

### 3. Log Backup
```bash
# Archive old logs
find /var/log/automation -name "*.log" -mtime +30 -exec gzip {} \;
find /var/log/automation -name "*.gz" -mtime +90 -delete
```

## Troubleshooting

### Common Issues

#### 1. Service Won't Start
```bash
# Check service status
sudo systemctl status automation-worker

# Check logs
sudo journalctl -u automation-worker -f

# Check configuration
dotnet /opt/automation/Automation.Worker.CSharp.dll --validate-config
```

#### 2. Redis Connection Issues
```bash
# Test Redis connection
redis-cli ping

# Check Redis logs
sudo tail -f /var/log/redis/redis-server.log
```

#### 3. Browser Issues
```bash
# Install browser dependencies
sudo apt-get install -y \
  libnss3 libatk-bridge2.0-0 libdrm2 libxkbcommon0 \
  libxcomposite1 libxdamage1 libxrandr2 libgbm1 \
  libxss1 libasound2
```

#### 4. Performance Issues
```bash
# Monitor resource usage
htop
iotop
netstat -tulpn

# Check application metrics
curl http://localhost:8080/metrics/detailed
```

### Log Analysis

#### Common Log Patterns
```bash
# Find errors
grep "ERROR" /var/log/automation/worker-*.log

# Find circuit breaker events
grep "Circuit breaker" /var/log/automation/worker-*.log

# Find performance issues
grep "Processing time" /var/log/automation/worker-*.log | awk '{print $NF}' | sort -n
```

## Security Considerations

### 1. Network Security
- Use TLS for Redis connections in production
- Implement network segmentation
- Use VPN for remote access
- Regular security updates

### 2. Application Security
- Rotate API keys regularly
- Use secure configuration management
- Implement audit logging
- Regular vulnerability scanning

### 3. Data Protection
- Encrypt sensitive configuration
- Secure log file access
- Implement data retention policies
- Regular backup testing

## Scaling

### Horizontal Scaling
```bash
# Deploy multiple worker instances
for i in {1..3}; do
  docker run -d --name worker-$i \
    -e REDIS_CONNECTION_STRING="redis-cluster:6379" \
    automation-worker:latest
done
```

### Load Balancing
```nginx
# nginx.conf
upstream automation-workers {
    server worker1:8080;
    server worker2:8080;
    server worker3:8080;
}

server {
    listen 80;
    location /health {
        proxy_pass http://automation-workers;
    }
}
```

## Maintenance

### Regular Tasks
- **Daily**: Check service status and logs
- **Weekly**: Review performance metrics
- **Monthly**: Update dependencies and security patches
- **Quarterly**: Performance testing and capacity planning

### Update Procedure
1. Test updates in staging environment
2. Create backup of current deployment
3. Deploy new version with blue-green deployment
4. Validate health checks and metrics
5. Monitor for issues and rollback if necessary

## Support

For production support:
- **Documentation**: `/docs` directory
- **Logs**: `/var/log/automation`
- **Metrics**: `http://localhost:8080/metrics`
- **Health**: `http://localhost:8080/health`
