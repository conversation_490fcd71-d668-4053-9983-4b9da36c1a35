using Microsoft.Playwright;
using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;

public class TestResult
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TestName { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string Status { get; set; } = "Running";
    public string? ErrorMessage { get; set; }
    public List<string> Screenshots { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

class Program 
{
    static async Task Main() 
    {
        var testSites = new[]
        {
            new { Url = "https://example.com", Name = "Example Domain Test" },
            new { Url = "https://httpbin.org", Name = "HTTPBin API Test" },
            new { Url = "https://jsonplaceholder.typicode.com", Name = "JSON Placeholder Test" },
            new { Url = "https://www.google.com", Name = "Google Search Test" },
            new { Url = "https://github.com", Name = "GitHub Test" }
        };

        Console.WriteLine("🚀 Starting Multi-Site Test Suite");
        Console.WriteLine($"📋 Testing {testSites.Length} websites");
        
        Environment.SetEnvironmentVariable("PLAYWRIGHT_BROWSERS_PATH", "/ms-playwright");
        
        using var playwright = await Playwright.CreateAsync();
        Console.WriteLine("✅ Playwright initialized!");
        
        await using var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions { Headless = true });
        Console.WriteLine("✅ Browser launched!");

        var allResults = new List<TestResult>();

        foreach (var site in testSites)
        {
            var testResult = await RunSiteTest(browser, site.Url, site.Name);
            allResults.Add(testResult);
            
            // Small delay between tests
            await Task.Delay(1000);
        }

        // Generate summary
        var passed = allResults.Count(r => r.Status == "Passed");
        var failed = allResults.Count(r => r.Status == "Failed");
        
        Console.WriteLine("\n🎯 MULTI-SITE TEST SUMMARY:");
        Console.WriteLine($"   Total Tests: {allResults.Count}");
        Console.WriteLine($"   ✅ Passed: {passed}");
        Console.WriteLine($"   ❌ Failed: {failed}");
        Console.WriteLine($"   📊 Success Rate: {(double)passed / allResults.Count * 100:F1}%");
        
        if (failed == 0)
        {
            Console.WriteLine("\n🎉 All tests passed! 🎉");
        }
        else
        {
            Console.WriteLine($"\n⚠️ {failed} test(s) failed. Check individual results for details.");
        }
    }

    static async Task<TestResult> RunSiteTest(IBrowser browser, string url, string testName)
    {
        var testResult = new TestResult
        {
            TestName = testName,
            Url = url,
            StartTime = DateTime.UtcNow
        };

        try 
        {
            Console.WriteLine($"\n🌐 Testing: {testName} ({url})");
            Console.WriteLine($"📋 Test ID: {testResult.Id}");
            
            var page = await browser.NewPageAsync();
            
            // Navigate with timeout
            Console.WriteLine($"🔄 Navigating to {url}...");
            await page.GotoAsync(url, new PageGotoOptions 
            { 
                WaitUntil = WaitUntilState.NetworkIdle,
                Timeout = 15000 // 15 second timeout
            });
            Console.WriteLine("✅ Navigation completed!");
            
            // Take screenshot
            var screenshotDir = "/tmp/screenshots";
            Directory.CreateDirectory(screenshotDir);
            
            var screenshotPath = Path.Combine(screenshotDir, $"{testResult.Id}-{SanitizeFilename(testName)}.png");
            await page.ScreenshotAsync(new PageScreenshotOptions
            {
                Path = screenshotPath,
                FullPage = true
            });
            testResult.Screenshots.Add(screenshotPath);
            Console.WriteLine($"📸 Screenshot saved");
            
            // Get page information
            var title = await page.TitleAsync();
            var currentUrl = page.Url;
            
            // Analyze page elements
            var linkCount = await page.Locator("a").CountAsync();
            var imageCount = await page.Locator("img").CountAsync();
            var formCount = await page.Locator("form").CountAsync();
            var buttonCount = await page.Locator("button").CountAsync();
            var inputCount = await page.Locator("input").CountAsync();
            
            // Check for common elements
            var hasNavigation = await page.Locator("nav").CountAsync() > 0;
            var hasHeader = await page.Locator("header").CountAsync() > 0;
            var hasFooter = await page.Locator("footer").CountAsync() > 0;
            
            // Store metadata
            testResult.Metadata["title"] = title;
            testResult.Metadata["finalUrl"] = currentUrl;
            testResult.Metadata["linkCount"] = linkCount;
            testResult.Metadata["imageCount"] = imageCount;
            testResult.Metadata["formCount"] = formCount;
            testResult.Metadata["buttonCount"] = buttonCount;
            testResult.Metadata["inputCount"] = inputCount;
            testResult.Metadata["hasNavigation"] = hasNavigation;
            testResult.Metadata["hasHeader"] = hasHeader;
            testResult.Metadata["hasFooter"] = hasFooter;
            
            Console.WriteLine($"📄 Page title: {title}");
            Console.WriteLine($"🔗 Links: {linkCount}, 🖼️ Images: {imageCount}, 📝 Forms: {formCount}");
            Console.WriteLine($"🔘 Buttons: {buttonCount}, ⌨️ Inputs: {inputCount}");
            
            // Check if page loaded properly
            if (string.IsNullOrEmpty(title))
            {
                testResult.Status = "Failed";
                testResult.ErrorMessage = "Page title is empty - possible loading issue";
            }
            else
            {
                testResult.Status = "Passed";
            }
            
            testResult.EndTime = DateTime.UtcNow;
            
            await page.CloseAsync();
            
            Console.WriteLine($"✅ Test completed: {testResult.Status}");
        } 
        catch (Exception ex) 
        {
            testResult.Status = "Failed";
            testResult.ErrorMessage = ex.Message;
            testResult.EndTime = DateTime.UtcNow;
            
            Console.WriteLine($"❌ Test failed: {ex.Message}");
        }
        
        // Save test results to JSON
        var resultsDir = "/tmp/test-results";
        Directory.CreateDirectory(resultsDir);
        
        var resultPath = Path.Combine(resultsDir, $"{testResult.Id}.json");
        var json = JsonSerializer.Serialize(testResult, new JsonSerializerOptions 
        { 
            WriteIndented = true 
        });
        
        await File.WriteAllTextAsync(resultPath, json);
        
        return testResult;
    }

    static string SanitizeFilename(string filename)
    {
        var invalid = Path.GetInvalidFileNameChars();
        return string.Join("_", filename.Split(invalid, StringSplitOptions.RemoveEmptyEntries));
    }
}
