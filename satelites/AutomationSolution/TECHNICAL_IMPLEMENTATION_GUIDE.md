# 🔧 Guía Técnica de Implementación - Migración F#/C#

## 📋 Índice
1. [Configuración de Proyectos](#configuración-de-proyectos)
2. [Patrones de Interoperabilidad](#patrones-de-interoperabilidad)
3. [Dependency Injection](#dependency-injection)
4. [Testing Strategy](#testing-strategy)
5. [Performance Optimization](#performance-optimization)
6. [Deployment Strategy](#deployment-strategy)

## 🏗️ Configuración de Proyectos

### Estructura de Solución Híbrida

```
AutomationSolution.Hybrid/
├── src/
│   ├── FSharp.Core/                    # F# Core Projects
│   │   ├── Automation.Core/
│   │   ├── Automation.AI.Core/
│   │   └── Automation.Data/
│   ├── CSharp.Infrastructure/          # C# Infrastructure
│   │   ├── Automation.Web/
│   │   ├── Automation.Mobile/
│   │   ├── Automation.Worker/
│   │   ├── Automation.Dispatcher/
│   │   ├── Automation.AI.Infrastructure/
│   │   ├── Automation.AI.Integration/
│   │   ├── Automation.Utilities/
│   │   └── Automation.Prompts/
│   └── Shared/                         # Shared Interfaces
│       └── Automation.Contracts/
├── tests/
│   ├── Unit.Tests/
│   ├── Integration.Tests/
│   └── Performance.Tests/
└── docs/
```

### Directory.Build.props
```xml
<Project>
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <Optimize>true</Optimize>
    <DebugType>portable</DebugType>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
  </ItemGroup>
</Project>
```

### Automation.Contracts (Shared Interfaces)
```csharp
// ITaskExecutor.cs - Shared interface
namespace Automation.Contracts;

public interface ITaskExecutor : IDisposable
{
    Task<TaskResult> ExecuteAsync(IEnumerable<Action> actions, CancellationToken cancellationToken = default);
    Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default);
    string ExecutorType { get; }
}

// IResourcePool.cs
public interface IResourcePool<T> : IDisposable where T : IDisposable
{
    Task<T> AcquireAsync(TimeSpan? timeout = null, CancellationToken cancellationToken = default);
    Task ReleaseAsync(T resource, CancellationToken cancellationToken = default);
    Task<PoolMetrics> GetMetricsAsync(CancellationToken cancellationToken = default);
}

// IAIProcessor.cs
public interface IAIProcessor
{
    Task<IEnumerable<Action>> ProcessCommandAsync(string command, CancellationToken cancellationToken = default);
    Task<HealingResult> AttemptHealingAsync(FailureContext context, CancellationToken cancellationToken = default);
}
```

## 🔗 Patrones de Interoperabilidad

### 1. F# Types → C# Consumption

```fsharp
// F# - Automation.Core/Types.fs
namespace Automation.Core

[<CLIMutable>]
type Action =
    | Navigate of url: string
    | Click of selector: string
    | TypeText of selector: string * text: string
    | Screenshot of path: string
    | Tap of selector: string
    | GetText of selector: string

[<CLIMutable>]
type TaskResult =
    | Success of message: string
    | Failure of error: ActionExecutionError

// Extension methods for C# consumption
[<Extension>]
type ActionExtensions =
    [<Extension>]
    static member IsNavigateAction(action: Action) =
        match action with
        | Navigate _ -> true
        | _ -> false
    
    [<Extension>]
    static member GetSelector(action: Action) =
        match action with
        | Click selector | Tap selector | GetText selector -> Some selector
        | TypeText (selector, _) -> Some selector
        | _ -> None
```

```csharp
// C# - Consuming F# types
using Automation.Core;

public class WebTaskExecutor : ITaskExecutor
{
    public async Task<TaskResult> ExecuteAsync(IEnumerable<Action> actions, CancellationToken cancellationToken = default)
    {
        foreach (var action in actions)
        {
            // F# discriminated unions work seamlessly in C#
            var result = action switch
            {
                Action.Navigate navigate => await NavigateAsync(navigate.url),
                Action.Click click => await ClickAsync(click.selector),
                Action.TypeText typeText => await TypeTextAsync(typeText.selector, typeText.text),
                Action.Screenshot screenshot => await ScreenshotAsync(screenshot.path),
                Action.Tap tap => await TapAsync(tap.selector),
                Action.GetText getText => await GetTextAsync(getText.selector),
                _ => throw new NotSupportedException($"Action type not supported: {action}")
            };
            
            if (result.IsFailure)
                return result;
        }
        
        return TaskResult.NewSuccess("All actions completed successfully");
    }
}
```

### 2. C# Services → F# Consumption

```csharp
// C# - Automation.AI.Infrastructure/ResourcePoolManager.cs
public class ResourcePoolManager : IResourcePoolManager
{
    private readonly ConcurrentDictionary<string, IResourcePool<HttpClient>> _httpPools = new();
    
    public async Task<HttpClient> GetHttpClientAsync(string providerId, CancellationToken cancellationToken = default)
    {
        var pool = _httpPools.GetOrAdd(providerId, _ => CreateHttpClientPool(providerId));
        return await pool.AcquireAsync(TimeSpan.FromSeconds(30), cancellationToken);
    }
    
    public async Task ReturnHttpClientAsync(string providerId, HttpClient client, CancellationToken cancellationToken = default)
    {
        if (_httpPools.TryGetValue(providerId, out var pool))
        {
            await pool.ReleaseAsync(client, cancellationToken);
        }
    }
}
```

```fsharp
// F# - Automation.AI.Core/AIProcessor.fs
open Automation.AI.Infrastructure
open System.Threading.Tasks

module AIProcessor =
    
    let processCommandWithInfrastructure (resourceManager: IResourcePoolManager) (command: string) =
        async {
            try
                // Use C# resource manager from F#
                let! client = resourceManager.GetHttpClientAsync("openai") |> Async.AwaitTask
                
                try
                    // Process with F# logic
                    let! response = callAIProvider client command
                    let actions = parseAIResponse response
                    
                    return Ok actions
                finally
                    // Return resource to pool
                    do! resourceManager.ReturnHttpClientAsync("openai", client) |> Async.AwaitTask
            with
            | ex -> return Error $"AI processing failed: {ex.Message}"
        }
```

### 3. Async Interoperability

```fsharp
// F# - Async<'T> ↔ Task<T> conversion helpers
module AsyncHelpers =
    
    let inline awaitTask (task: Task<'T>) = 
        task |> Async.AwaitTask
    
    let inline startAsTask (computation: Async<'T>) = 
        computation |> Async.StartAsTask
    
    let inline startAsTaskCancellable (computation: Async<'T>) (cancellationToken: CancellationToken) = 
        Async.StartAsTask(computation, cancellationToken = cancellationToken)
```

```csharp
// C# - Extension methods for F# async
public static class FSharpAsyncExtensions
{
    public static Task<T> AsTask<T>(this FSharpAsync<T> async, CancellationToken cancellationToken = default)
    {
        return FSharpAsync.StartAsTask(async, cancellationToken: cancellationToken);
    }
    
    public static FSharpAsync<T> AsAsync<T>(this Task<T> task)
    {
        return FSharpAsync.AwaitTask(task);
    }
}
```

## 🏭 Dependency Injection

### Startup Configuration

```csharp
// Program.cs - Main application
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Automation.Core;
using Automation.AI.Core;
using Automation.AI.Infrastructure;

var builder = Host.CreateApplicationBuilder(args);

// Configure F# services
builder.Services.ConfigureFSharpServices();

// Configure C# services  
builder.Services.ConfigureCSharpServices();

// Configure cross-cutting concerns
builder.Services.ConfigureLogging();
builder.Services.ConfigureMonitoring();

var host = builder.Build();
await host.RunAsync();
```

```csharp
// ServiceCollectionExtensions.cs
public static class ServiceCollectionExtensions
{
    public static IServiceCollection ConfigureFSharpServices(this IServiceCollection services)
    {
        // Register F# modules as singletons
        services.AddSingleton<AIProcessor>();
        services.AddSingleton<AutoHealAgent>();
        services.AddSingleton<CostOptimizer>();
        
        // Register F# functions as delegates
        services.AddSingleton<Func<string, Task<IEnumerable<Action>>>>(provider =>
        {
            var processor = provider.GetRequiredService<AIProcessor>();
            return command => processor.ProcessCommandAsync(command).AsTask();
        });
        
        return services;
    }
    
    public static IServiceCollection ConfigureCSharpServices(this IServiceCollection services)
    {
        // Infrastructure services
        services.AddSingleton<IResourcePoolManager, ResourcePoolManager>();
        services.AddScoped<IPerformanceMonitor, PerformanceMonitor>();
        
        // Task executors
        services.AddScoped<ITaskExecutor, WebTaskExecutor>();
        services.AddKeyedScoped<ITaskExecutor, MobileTaskExecutor>("mobile");
        
        // Hosted services
        services.AddHostedService<WorkerService>();
        services.AddHostedService<HealthCheckService>();
        
        return services;
    }
}
```

### Factory Pattern for Executors

```csharp
// ITaskExecutorFactory.cs
public interface ITaskExecutorFactory
{
    ITaskExecutor CreateExecutor(string executorType);
    ITaskExecutor CreateExecutor(TaskPayload payload);
}

// TaskExecutorFactory.cs
public class TaskExecutorFactory : ITaskExecutorFactory
{
    private readonly IServiceProvider _serviceProvider;
    
    public TaskExecutorFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }
    
    public ITaskExecutor CreateExecutor(string executorType)
    {
        return executorType.ToLowerInvariant() switch
        {
            "web" => _serviceProvider.GetRequiredService<ITaskExecutor>(),
            "mobile" => _serviceProvider.GetRequiredKeyedService<ITaskExecutor>("mobile"),
            _ => throw new NotSupportedException($"Executor type '{executorType}' not supported")
        };
    }
    
    public ITaskExecutor CreateExecutor(TaskPayload payload)
    {
        var executorType = payload.Metadata?.GetValueOrDefault("target", "web") ?? "web";
        return CreateExecutor(executorType);
    }
}
```

## 🧪 Testing Strategy

### Unit Testing F#/C# Interop

```csharp
// InteroperabilityTests.cs
[TestClass]
public class FSharpCSharpInteroperabilityTests
{
    private IServiceProvider _serviceProvider;
    
    [TestInitialize]
    public void Setup()
    {
        var services = new ServiceCollection();
        services.ConfigureFSharpServices();
        services.ConfigureCSharpServices();
        services.AddLogging();
        
        _serviceProvider = services.BuildServiceProvider();
    }
    
    [TestMethod]
    public async Task FSharp_AIProcessor_With_CSharp_ResourceManager_Integration()
    {
        // Arrange
        var aiProcessor = _serviceProvider.GetRequiredService<AIProcessor>();
        var resourceManager = _serviceProvider.GetRequiredService<IResourcePoolManager>();
        
        // Act
        var actions = await aiProcessor.ProcessCommandWithInfrastructure(resourceManager, "test command");
        
        // Assert
        Assert.IsTrue(actions.IsOk);
        Assert.IsTrue(actions.ResultValue.Any());
    }
    
    [TestMethod]
    public async Task CSharp_WebExecutor_With_FSharp_Actions()
    {
        // Arrange
        var executor = _serviceProvider.GetRequiredService<ITaskExecutor>();
        var actions = new[]
        {
            Action.NewNavigate("https://example.com"),
            Action.NewClick("#test-button"),
            Action.NewTypeText("#input", "test text")
        };
        
        // Act
        var result = await executor.ExecuteAsync(actions);
        
        // Assert
        Assert.IsTrue(result.IsSuccess);
    }
}
```

### Integration Testing

```csharp
// IntegrationTestBase.cs
public abstract class IntegrationTestBase : IAsyncLifetime
{
    protected IHost Host { get; private set; }
    protected IServiceProvider Services => Host.Services;
    
    public async Task InitializeAsync()
    {
        var builder = Host.CreateApplicationBuilder();
        
        // Override configuration for testing
        builder.Configuration.AddInMemoryCollection(new Dictionary<string, string>
        {
            ["Redis:ConnectionString"] = "localhost:6379",
            ["AI:EnableCaching"] = "false",
            ["Logging:LogLevel:Default"] = "Debug"
        });
        
        ConfigureServices(builder.Services);
        
        Host = builder.Build();
        await Host.StartAsync();
    }
    
    protected virtual void ConfigureServices(IServiceCollection services)
    {
        services.ConfigureFSharpServices();
        services.ConfigureCSharpServices();
    }
    
    public async Task DisposeAsync()
    {
        if (Host != null)
        {
            await Host.StopAsync();
            Host.Dispose();
        }
    }
}

// WorkerIntegrationTests.cs
public class WorkerIntegrationTests : IntegrationTestBase
{
    [Fact]
    public async Task Worker_Processes_Task_End_To_End()
    {
        // Arrange
        var workerService = Services.GetRequiredService<IHostedService>();
        var taskFactory = Services.GetRequiredService<ITaskExecutorFactory>();
        
        // Act
        var executor = taskFactory.CreateExecutor("web");
        var actions = new[] { Action.NewNavigate("https://example.com") };
        var result = await executor.ExecuteAsync(actions);
        
        // Assert
        Assert.True(result.IsSuccess);
    }
}
```

### Performance Testing

```csharp
// PerformanceBenchmarks.cs
[MemoryDiagnoser]
[SimpleJob(RuntimeMoniker.Net80)]
public class MigrationPerformanceBenchmarks
{
    private IServiceProvider _serviceProvider;
    private ITaskExecutor _csharpExecutor;
    private AIProcessor _fsharpProcessor;
    
    [GlobalSetup]
    public void Setup()
    {
        var services = new ServiceCollection();
        services.ConfigureFSharpServices();
        services.ConfigureCSharpServices();
        _serviceProvider = services.BuildServiceProvider();
        
        _csharpExecutor = _serviceProvider.GetRequiredService<ITaskExecutor>();
        _fsharpProcessor = _serviceProvider.GetRequiredService<AIProcessor>();
    }
    
    [Benchmark]
    public async Task<TaskResult> CSharp_WebExecutor_Performance()
    {
        var actions = new[] { Action.NewClick("#test") };
        return await _csharpExecutor.ExecuteAsync(actions);
    }
    
    [Benchmark]
    public async Task<IEnumerable<Action>> FSharp_AIProcessor_Performance()
    {
        return await _fsharpProcessor.ProcessCommandAsync("click button");
    }
}
```

## 📊 Performance Optimization

### Memory Management

```csharp
// ObjectPooling.cs
public class ObjectPoolService<T> : IObjectPoolService<T> where T : class, new()
{
    private readonly ObjectPool<T> _pool;
    
    public ObjectPoolService(IServiceProvider serviceProvider)
    {
        var provider = serviceProvider.GetRequiredService<ObjectPoolProvider>();
        _pool = provider.Create<T>();
    }
    
    public T Get() => _pool.Get();
    public void Return(T obj) => _pool.Return(obj);
}

// Usage in services
public class WebTaskExecutor : ITaskExecutor
{
    private readonly IObjectPoolService<StringBuilder> _stringBuilderPool;
    
    public WebTaskExecutor(IObjectPoolService<StringBuilder> stringBuilderPool)
    {
        _stringBuilderPool = stringBuilderPool;
    }
    
    private async Task<string> BuildSelectorAsync(string baseSelector)
    {
        var sb = _stringBuilderPool.Get();
        try
        {
            sb.Clear();
            sb.Append(baseSelector);
            // Build complex selector...
            return sb.ToString();
        }
        finally
        {
            _stringBuilderPool.Return(sb);
        }
    }
}
```

### Async Optimization

```csharp
// AsyncHelpers.cs
public static class AsyncOptimizations
{
    public static async ValueTask<T> ConfigureAwaitFalse<T>(this Task<T> task)
    {
        return await task.ConfigureAwait(false);
    }
    
    public static async ValueTask ConfigureAwaitFalse(this Task task)
    {
        await task.ConfigureAwait(false);
    }
    
    // Batch processing for better throughput
    public static async Task<IEnumerable<TResult>> ProcessBatchAsync<TInput, TResult>(
        this IEnumerable<TInput> items,
        Func<TInput, Task<TResult>> processor,
        int maxConcurrency = Environment.ProcessorCount)
    {
        using var semaphore = new SemaphoreSlim(maxConcurrency);
        var tasks = items.Select(async item =>
        {
            await semaphore.WaitAsync().ConfigureAwaitFalse();
            try
            {
                return await processor(item).ConfigureAwaitFalse();
            }
            finally
            {
                semaphore.Release();
            }
        });
        
        return await Task.WhenAll(tasks).ConfigureAwaitFalse();
    }
}
```

## 🚀 Deployment Strategy

### Docker Configuration

```dockerfile
# Dockerfile.hybrid
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 8080

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy F# projects
COPY ["src/FSharp.Core/", "src/FSharp.Core/"]
# Copy C# projects  
COPY ["src/CSharp.Infrastructure/", "src/CSharp.Infrastructure/"]
# Copy shared contracts
COPY ["src/Shared/", "src/Shared/"]

# Restore dependencies
RUN dotnet restore "src/CSharp.Infrastructure/Automation.Worker/Automation.Worker.csproj"

# Build
COPY . .
RUN dotnet build "src/CSharp.Infrastructure/Automation.Worker/Automation.Worker.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "src/CSharp.Infrastructure/Automation.Worker/Automation.Worker.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Automation.Worker.dll"]
```

### Kubernetes Deployment

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: automation-worker
spec:
  replicas: 3
  selector:
    matchLabels:
      app: automation-worker
  template:
    metadata:
      labels:
        app: automation-worker
    spec:
      containers:
      - name: worker
        image: automation-solution:hybrid-latest
        env:
        - name: REDIS_CONNECTION_STRING
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: connection-string
        - name: AI_PROVIDERS__OPENAI__APIKEY
          valueFrom:
            secretKeyRef:
              name: ai-secrets
              key: openai-key
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

---

Esta guía técnica proporciona todos los detalles necesarios para implementar la migración híbrida F#/C# de manera exitosa, manteniendo la interoperabilidad y optimizando el rendimiento del sistema.
