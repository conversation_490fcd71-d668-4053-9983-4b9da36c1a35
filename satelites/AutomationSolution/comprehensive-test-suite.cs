using Microsoft.Playwright;
using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;

public class ComprehensiveTestResult
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TestSuiteName { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string Status { get; set; } = "Running";
    public string? ErrorMessage { get; set; }
    public List<string> Screenshots { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public List<string> TestSteps { get; set; } = new();
    public List<Dictionary<string, object>> SubTests { get; set; } = new();
}

class ComprehensiveTestSuite 
{
    static async Task Main() 
    {
        var testResult = new ComprehensiveTestResult
        {
            TestSuiteName = "Comprehensive Automation Test Suite",
            StartTime = DateTime.UtcNow
        };

        try 
        {
            Console.WriteLine("🚀 Starting Comprehensive Automation Test Suite");
            Console.WriteLine($"📋 Suite ID: {testResult.Id}");
            
            Environment.SetEnvironmentVariable("PLAYWRIGHT_BROWSERS_PATH", "/ms-playwright");
            
            using var playwright = await Playwright.CreateAsync();
            Console.WriteLine("✅ Playwright initialized!");
            
            var testCases = new[]
            {
                new { Type = "Web", Url = "https://example.com", Device = "Desktop", Name = "Web Desktop Test" },
                new { Type = "Mobile", Url = "https://m.wikipedia.org", Device = "iPhone 13", Name = "Mobile Wikipedia Test" },
                new { Type = "App", Url = "http://localhost:8083", Device = "Galaxy S8", Name = "App Simulation Test" },
                new { Type = "Web", Url = "https://github.com", Device = "Desktop", Name = "GitHub Desktop Test" },
                new { Type = "Mobile", Url = "https://duckduckgo.com", Device = "iPhone 12", Name = "Mobile Search Test" }
            };

            foreach (var testCase in testCases)
            {
                try
                {
                    Console.WriteLine($"\n🧪 Running {testCase.Type} test: {testCase.Name}");
                    var subTestResult = await RunSingleTest(playwright, testCase.Type, testCase.Url, testCase.Device, testCase.Name);
                    testResult.SubTests.Add(subTestResult);
                    testResult.TestSteps.Add($"{testCase.Type} test completed: {testCase.Name}");
                    
                    // Small delay between tests
                    await Task.Delay(2000);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Failed {testCase.Type} test: {ex.Message}");
                    testResult.TestSteps.Add($"{testCase.Type} test failed: {ex.Message}");
                }
            }

            // Generate comprehensive summary
            var totalTests = testResult.SubTests.Count;
            var passedTests = testResult.SubTests.Count(t => t.ContainsKey("status") && t["status"].ToString() == "Passed");
            var failedTests = totalTests - passedTests;
            var totalScreenshots = testResult.SubTests.Sum(t => t.ContainsKey("screenshots") ? ((List<string>)t["screenshots"]).Count : 0);
            
            testResult.Metadata["totalTests"] = totalTests;
            testResult.Metadata["passedTests"] = passedTests;
            testResult.Metadata["failedTests"] = failedTests;
            testResult.Metadata["totalScreenshots"] = totalScreenshots;
            testResult.Metadata["successRate"] = totalTests > 0 ? (double)passedTests / totalTests * 100 : 0;
            
            testResult.Status = failedTests == 0 ? "Passed" : "Partial";
            testResult.EndTime = DateTime.UtcNow;
            
            Console.WriteLine("\n🎯 COMPREHENSIVE TEST SUITE SUMMARY:");
            Console.WriteLine($"   Total Tests: {totalTests}");
            Console.WriteLine($"   ✅ Passed: {passedTests}");
            Console.WriteLine($"   ❌ Failed: {failedTests}");
            Console.WriteLine($"   📸 Screenshots: {totalScreenshots}");
            Console.WriteLine($"   📊 Success Rate: {testResult.Metadata["successRate"]:F1}%");
            
            if (failedTests == 0)
            {
                Console.WriteLine("\n🎉 All comprehensive tests passed! 🎉");
            }
            else
            {
                Console.WriteLine($"\n⚠️ {failedTests} test(s) failed. Check individual results for details.");
            }
        } 
        catch (Exception ex) 
        {
            testResult.Status = "Failed";
            testResult.ErrorMessage = ex.Message;
            testResult.EndTime = DateTime.UtcNow;
            
            Console.WriteLine($"❌ Comprehensive test suite failed: {ex.Message}");
        }
        
        // Save comprehensive test results
        var resultsDir = "/tmp/test-results";
        Directory.CreateDirectory(resultsDir);
        
        var resultPath = Path.Combine(resultsDir, $"{testResult.Id}.json");
        var json = JsonSerializer.Serialize(testResult, new JsonSerializerOptions 
        { 
            WriteIndented = true 
        });
        
        await File.WriteAllTextAsync(resultPath, json);
        Console.WriteLine($"\n💾 Comprehensive test results saved: {resultPath}");
    }

    static async Task<Dictionary<string, object>> RunSingleTest(IPlaywright playwright, string testType, string url, string device, string testName)
    {
        var result = new Dictionary<string, object>
        {
            ["id"] = Guid.NewGuid().ToString(),
            ["testType"] = testType,
            ["testName"] = testName,
            ["url"] = url,
            ["device"] = device,
            ["startTime"] = DateTime.UtcNow,
            ["screenshots"] = new List<string>(),
            ["steps"] = new List<string>()
        };

        try
        {
            IBrowserContext context;
            IPage page;

            if (testType == "Web")
            {
                // Desktop web test
                await using var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions { Headless = true });
                context = await browser.NewContextAsync();
                page = await context.NewPageAsync();
            }
            else
            {
                // Mobile/App test
                var deviceConfig = playwright.Devices[device];
                await using var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions { Headless = true });
                context = await browser.NewContextAsync(new BrowserNewContextOptions
                {
                    ViewportSize = deviceConfig.ViewportSize,
                    UserAgent = deviceConfig.UserAgent,
                    DeviceScaleFactor = deviceConfig.DeviceScaleFactor,
                    IsMobile = deviceConfig.IsMobile,
                    HasTouch = deviceConfig.HasTouch
                });
                page = await context.NewPageAsync();
            }

            // Navigate to URL
            await page.GotoAsync(url, new PageGotoOptions { WaitUntil = WaitUntilState.NetworkIdle, Timeout = 15000 });
            ((List<string>)result["steps"]).Add("Navigation completed");

            // Take screenshot
            var screenshotDir = "/tmp/screenshots";
            Directory.CreateDirectory(screenshotDir);
            
            var screenshotPath = Path.Combine(screenshotDir, $"{result["id"]}-{testType.ToLower()}-{SanitizeFilename(testName)}.png");
            await page.ScreenshotAsync(new PageScreenshotOptions { Path = screenshotPath, FullPage = true });
            ((List<string>)result["screenshots"]).Add(screenshotPath);

            // Get basic page info
            var title = await page.TitleAsync();
            var linkCount = await page.Locator("a").CountAsync();
            var imageCount = await page.Locator("img").CountAsync();
            var formCount = await page.Locator("form").CountAsync();

            result["title"] = title;
            result["linkCount"] = linkCount;
            result["imageCount"] = imageCount;
            result["formCount"] = formCount;

            // Type-specific tests
            if (testType == "App" && url.Contains("localhost:8083"))
            {
                // App-specific interactions
                try
                {
                    var fillButton = await page.WaitForSelectorAsync("button:has-text('Fill Demo Data')", new PageWaitForSelectorOptions { Timeout = 5000 });
                    await fillButton.ClickAsync();
                    ((List<string>)result["steps"]).Add("App interaction completed");
                }
                catch { }
            }
            else if (formCount > 0)
            {
                // Form interaction for web/mobile
                try
                {
                    var inputs = await page.Locator("input[type='text'], input[type='search'], input[type='email']").CountAsync();
                    if (inputs > 0)
                    {
                        var firstInput = page.Locator("input[type='text'], input[type='search'], input[type='email']").First;
                        await firstInput.FillAsync("test automation");
                        ((List<string>)result["steps"]).Add("Form interaction completed");
                    }
                }
                catch { }
            }

            result["status"] = "Passed";
            result["endTime"] = DateTime.UtcNow;

            await context.CloseAsync();
        }
        catch (Exception ex)
        {
            result["status"] = "Failed";
            result["error"] = ex.Message;
            result["endTime"] = DateTime.UtcNow;
        }

        return result;
    }

    static string SanitizeFilename(string filename)
    {
        var invalid = Path.GetInvalidFileNameChars();
        return string.Join("_", filename.Split(invalid, StringSplitOptions.RemoveEmptyEntries));
    }
}
