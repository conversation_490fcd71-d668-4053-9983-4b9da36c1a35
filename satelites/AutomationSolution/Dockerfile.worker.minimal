# =============================================================================
# Minimal Dockerfile for Automation.Worker.CSharp
# Lightweight version without browser dependencies for API-only workloads
# =============================================================================

FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS build
WORKDIR /src

# Copy and restore dependencies
COPY AutomationSolution.sln ./
COPY Directory.Build.props ./
COPY src/Automation.Contracts/Automation.Contracts.csproj ./src/Automation.Contracts/
COPY src/Automation.Utilities.CSharp/Automation.Utilities.CSharp.csproj ./src/Automation.Utilities.CSharp/
COPY src/Automation.AI.Infrastructure.CSharp/Automation.AI.Infrastructure.CSharp.csproj ./src/Automation.AI.Infrastructure.CSharp/
COPY src/Automation.Worker.CSharp/Automation.Worker.CSharp.csproj ./src/Automation.Worker.CSharp/

RUN dotnet restore src/Automation.Worker.CSharp/Automation.Worker.CSharp.csproj

# Copy source and build
COPY src/ ./src/
RUN dotnet publish src/Automation.Worker.CSharp/Automation.Worker.CSharp.csproj \
    -c Release \
    -o /app/publish \
    --self-contained false

# Runtime stage
FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine
WORKDIR /app

# Install minimal dependencies
RUN apk add --no-cache curl ca-certificates && \
    adduser -S -D -H -u 1001 automation

COPY --from=build --chown=automation:automation /app/publish ./

# Environment variables
ENV ASPNETCORE_ENVIRONMENT=Production \
    DOTNET_RUNNING_IN_CONTAINER=true \
    AUTOMATION_DISABLE_BROWSER_AUTOMATION=true

# Health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

EXPOSE 8080
USER automation

ENTRYPOINT ["dotnet", "Automation.Worker.CSharp.dll"]
