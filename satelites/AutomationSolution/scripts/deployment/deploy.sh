#!/bin/bash
# =============================================================================
# Deployment Script for Automation Solution
# Deploys the application to different environments
# =============================================================================

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
ENVIRONMENT="${ENVIRONMENT:-production}"
COMPOSE_FILE=""
ENV_FILE=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
Deployment Script for Automation Solution

Usage: $0 [OPTIONS] COMMAND

Commands:
    deploy      Deploy the application
    stop        Stop the application
    restart     Restart the application
    status      Show application status
    logs        Show application logs
    update      Update and restart the application
    backup      Create backup of data
    restore     Restore from backup

Options:
    -h, --help              Show this help message
    -e, --env ENVIRONMENT   Target environment (development, staging, production) [default: production]
    -v, --version VERSION   Application version to deploy
    -f, --force             Force deployment without confirmation
    --no-backup             Skip backup before deployment
    --scale REPLICAS        Scale worker service to specified replicas

Examples:
    $0 deploy                           # Deploy to production
    $0 -e development deploy            # Deploy to development
    $0 -v 1.0.0 deploy                 # Deploy specific version
    $0 --scale 3 deploy                 # Deploy with 3 worker replicas
    $0 logs                             # Show logs
    $0 backup                           # Create backup

EOF
}

# Parse command line arguments
FORCE_DEPLOY=false
SKIP_BACKUP=false
VERSION=""
SCALE_REPLICAS=""
COMMAND=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -f|--force)
            FORCE_DEPLOY=true
            shift
            ;;
        --no-backup)
            SKIP_BACKUP=true
            shift
            ;;
        --scale)
            SCALE_REPLICAS="$2"
            shift 2
            ;;
        deploy|stop|restart|status|logs|update|backup|restore)
            COMMAND="$1"
            shift
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate command
if [[ -z "$COMMAND" ]]; then
    log_error "No command specified"
    show_help
    exit 1
fi

# Set environment-specific configurations
case $ENVIRONMENT in
    development)
        COMPOSE_FILE="docker-compose.dev.yml"
        ENV_FILE="config/environments/.env.development"
        ;;
    staging)
        COMPOSE_FILE="docker-compose.production.yml"
        ENV_FILE="config/environments/.env.staging"
        ;;
    production)
        COMPOSE_FILE="docker-compose.production.yml"
        ENV_FILE="config/environments/.env.production"
        ;;
    *)
        log_error "Invalid environment: $ENVIRONMENT"
        log_error "Valid environments: development, staging, production"
        exit 1
        ;;
esac

# Change to project root
cd "$PROJECT_ROOT"

# Validate files exist
if [[ ! -f "$COMPOSE_FILE" ]]; then
    log_error "Compose file not found: $COMPOSE_FILE"
    exit 1
fi

if [[ ! -f "$ENV_FILE" ]]; then
    log_error "Environment file not found: $ENV_FILE"
    exit 1
fi

log_info "Deployment configuration:"
log_info "  Environment: $ENVIRONMENT"
log_info "  Compose file: $COMPOSE_FILE"
log_info "  Environment file: $ENV_FILE"
log_info "  Version: ${VERSION:-'latest'}"

# Docker Compose command with environment file
COMPOSE_CMD="docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE"

# Add version override if specified
if [[ -n "$VERSION" ]]; then
    export VERSION="$VERSION"
fi

# Add scale override if specified
SCALE_CMD=""
if [[ -n "$SCALE_REPLICAS" ]]; then
    SCALE_CMD="--scale automation-worker=$SCALE_REPLICAS"
fi

# Function to check if services are healthy
check_health() {
    local max_attempts=30
    local attempt=1
    
    log_info "Checking service health..."
    
    while [[ $attempt -le $max_attempts ]]; do
        if $COMPOSE_CMD ps | grep -q "healthy"; then
            log_success "Services are healthy"
            return 0
        fi
        
        log_info "Attempt $attempt/$max_attempts - waiting for services to be healthy..."
        sleep 10
        ((attempt++))
    done
    
    log_error "Services failed to become healthy within timeout"
    return 1
}

# Function to create backup
create_backup() {
    if [[ "$SKIP_BACKUP" == "true" ]]; then
        log_info "Skipping backup (--no-backup specified)"
        return 0
    fi
    
    local backup_dir="backups/$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$backup_dir"
    
    log_info "Creating backup in $backup_dir..."
    
    # Backup Redis data
    if $COMPOSE_CMD exec -T redis redis-cli BGSAVE; then
        $COMPOSE_CMD cp redis:/data/dump.rdb "$backup_dir/redis-dump.rdb"
        log_success "Redis backup created"
    else
        log_warning "Failed to create Redis backup"
    fi
    
    # Backup application logs
    if [[ -d "logs" ]]; then
        cp -r logs "$backup_dir/"
        log_success "Logs backup created"
    fi
    
    # Backup configuration
    cp -r config "$backup_dir/"
    log_success "Configuration backup created"
    
    log_success "Backup completed: $backup_dir"
}

# Execute command
case $COMMAND in
    deploy)
        log_info "Starting deployment to $ENVIRONMENT environment..."
        
        # Confirmation for production
        if [[ "$ENVIRONMENT" == "production" && "$FORCE_DEPLOY" != "true" ]]; then
            read -p "Are you sure you want to deploy to PRODUCTION? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log_info "Deployment cancelled"
                exit 0
            fi
        fi
        
        # Create backup
        create_backup
        
        # Pull latest images
        log_info "Pulling latest images..."
        $COMPOSE_CMD pull
        
        # Deploy services
        log_info "Deploying services..."
        $COMPOSE_CMD up -d $SCALE_CMD
        
        # Check health
        if check_health; then
            log_success "Deployment completed successfully!"
        else
            log_error "Deployment failed - services are not healthy"
            exit 1
        fi
        ;;
        
    stop)
        log_info "Stopping services..."
        $COMPOSE_CMD down
        log_success "Services stopped"
        ;;
        
    restart)
        log_info "Restarting services..."
        $COMPOSE_CMD restart
        if check_health; then
            log_success "Services restarted successfully"
        else
            log_error "Restart failed - services are not healthy"
            exit 1
        fi
        ;;
        
    status)
        log_info "Service status:"
        $COMPOSE_CMD ps
        echo
        log_info "Service logs (last 20 lines):"
        $COMPOSE_CMD logs --tail=20
        ;;
        
    logs)
        log_info "Showing service logs..."
        $COMPOSE_CMD logs -f
        ;;
        
    update)
        log_info "Updating application..."
        create_backup
        $COMPOSE_CMD pull
        $COMPOSE_CMD up -d $SCALE_CMD
        if check_health; then
            log_success "Update completed successfully!"
        else
            log_error "Update failed - services are not healthy"
            exit 1
        fi
        ;;
        
    backup)
        create_backup
        ;;
        
    restore)
        log_error "Restore functionality not yet implemented"
        log_info "To restore manually:"
        log_info "  1. Stop services: $0 stop"
        log_info "  2. Restore Redis data to volume"
        log_info "  3. Restore configuration files"
        log_info "  4. Start services: $0 deploy"
        exit 1
        ;;
        
    *)
        log_error "Unknown command: $COMMAND"
        show_help
        exit 1
        ;;
esac
