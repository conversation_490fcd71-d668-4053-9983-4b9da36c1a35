#!/bin/bash
# =============================================================================
# Playwright and Appium Initialization Script
# Ensures browsers and mobile automation tools are properly installed
# =============================================================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running in container
if [ ! -f /.dockerenv ]; then
    log_warning "Not running in a container, skipping container-specific setup"
    exit 0
fi

log_info "Initializing Playwright and Appium in container..."

# Check if Playwright browsers are already installed
PLAYWRIGHT_BROWSERS_PATH="${PLAYWRIGHT_BROWSERS_PATH:-/ms-playwright}"
log_info "Checking Playwright browsers in: $PLAYWRIGHT_BROWSERS_PATH"

if [ -d "$PLAYWRIGHT_BROWSERS_PATH" ] && [ "$(ls -A "$PLAYWRIGHT_BROWSERS_PATH" 2>/dev/null)" ]; then
    log_success "Playwright browsers already installed"

    # Ensure proper permissions
    if [ "$(id -u)" = "0" ]; then
        log_info "Setting Playwright permissions as root..."
        chown -R automation:automation "$PLAYWRIGHT_BROWSERS_PATH" 2>/dev/null || true
        chmod -R 755 "$PLAYWRIGHT_BROWSERS_PATH" 2>/dev/null || true
    fi
else
    # Install Playwright browsers if not already installed
    if command -v npx >/dev/null 2>&1; then
        log_info "Installing Playwright browsers..."

        # Set environment variables for installation
        export PLAYWRIGHT_BROWSERS_PATH="$PLAYWRIGHT_BROWSERS_PATH"
        export PLAYWRIGHT_NODEJS_PATH="/usr/bin/node"

        # Install browsers with dependencies
        npx playwright install --with-deps chromium firefox webkit || {
            log_warning "Failed to install all browsers, trying individual installation..."
            npx playwright install chromium || log_warning "Failed to install Chromium"
            npx playwright install firefox || log_warning "Failed to install Firefox"
            npx playwright install webkit || log_warning "Failed to install WebKit"
        }

        # Set permissions after installation
        if [ "$(id -u)" = "0" ] && [ -d "$PLAYWRIGHT_BROWSERS_PATH" ]; then
            log_info "Setting Playwright permissions after installation..."
            chown -R automation:automation "$PLAYWRIGHT_BROWSERS_PATH" 2>/dev/null || true
            chmod -R 755 "$PLAYWRIGHT_BROWSERS_PATH" 2>/dev/null || true
        fi

        log_success "Playwright browsers installation completed"
    else
        log_error "Node.js/npm not found, cannot install Playwright browsers"
    fi
fi

# Check Appium installation
if command -v appium >/dev/null 2>&1; then
    log_info "Appium is installed: $(appium --version)"
    
    # Run Appium doctor to check setup
    if command -v appium-doctor >/dev/null 2>&1; then
        log_info "Running Appium doctor..."
        appium-doctor --android || log_warning "Appium doctor found some issues"
    fi
else
    log_warning "Appium not found"
fi

# Check Android SDK setup
if [ -n "${ANDROID_HOME:-}" ]; then
    log_info "Android SDK path: $ANDROID_HOME"
    if [ -d "$ANDROID_HOME" ]; then
        log_success "Android SDK directory exists"
    else
        log_warning "Android SDK directory not found"
    fi
else
    log_warning "ANDROID_HOME not set"
fi

# Set up display for headless mode
if [ -z "${DISPLAY:-}" ]; then
    log_info "Setting up virtual display..."
    export DISPLAY=:99
    
    # Start Xvfb if available
    if command -v Xvfb >/dev/null 2>&1; then
        Xvfb :99 -screen 0 1920x1080x24 > /dev/null 2>&1 &
        log_success "Virtual display started on :99"
    else
        log_warning "Xvfb not available, headless mode may not work properly"
    fi
fi

# Create necessary directories
mkdir -p /app/logs /app/data /app/screenshots /app/test-results
log_success "Created application directories"

# Set permissions if running as root
if [ "$(id -u)" = "0" ]; then
    log_info "Running as root, setting up permissions..."
    
    # Create automation user if it doesn't exist
    if ! id automation >/dev/null 2>&1; then
        groupadd -g 1001 automation || true
        useradd -r -u 1001 -g automation -d /app -s /sbin/nologin automation || true
    fi
    
    # Set ownership
    chown -R automation:automation /app || log_warning "Failed to set ownership"
fi

log_success "Playwright and Appium initialization completed!"

# Print environment info
log_info "Environment Information:"
echo "  - Node.js: $(node --version 2>/dev/null || echo 'Not found')"
echo "  - npm: $(npm --version 2>/dev/null || echo 'Not found')"
echo "  - Playwright: $(npx playwright --version 2>/dev/null || echo 'Not found')"
echo "  - Appium: $(appium --version 2>/dev/null || echo 'Not found')"
echo "  - Java: $(java -version 2>&1 | head -n1 || echo 'Not found')"
echo "  - Python: $(python3 --version 2>/dev/null || echo 'Not found')"
echo "  - Display: ${DISPLAY:-'Not set'}"
echo "  - Android SDK: ${ANDROID_HOME:-'Not set'}"
