#!/bin/bash
# =============================================================================
# Docker Test Script for Automation Solution
# Runs integration tests in Docker containers
# =============================================================================

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
TEST_COMPOSE_FILE="docker-compose.dev.yml"
TEST_PROFILE="testing"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
Docker Test Script for Automation Solution

Usage: $0 [OPTIONS]

Options:
    -h, --help              Show this help message
    -c, --category CATEGORY Test category to run (Integration, Performance, Security, All) [default: All]
    -v, --verbose           Verbose test output
    --coverage              Generate code coverage report
    --clean                 Clean test environment before running
    --keep                  Keep test containers running after tests

Examples:
    $0                                  # Run all tests
    $0 -c Integration                   # Run integration tests only
    $0 -c Performance --verbose         # Run performance tests with verbose output
    $0 --coverage                       # Run tests with coverage report
    $0 --clean                          # Clean environment and run tests

EOF
}

# Parse command line arguments
TEST_CATEGORY="All"
VERBOSE=false
GENERATE_COVERAGE=false
CLEAN_ENV=false
KEEP_CONTAINERS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--category)
            TEST_CATEGORY="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        --coverage)
            GENERATE_COVERAGE=true
            shift
            ;;
        --clean)
            CLEAN_ENV=true
            shift
            ;;
        --keep)
            KEEP_CONTAINERS=true
            shift
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate test category
case $TEST_CATEGORY in
    Integration|Performance|Security|All)
        ;;
    *)
        log_error "Invalid test category: $TEST_CATEGORY"
        log_error "Valid categories: Integration, Performance, Security, All"
        exit 1
        ;;
esac

# Change to project root
cd "$PROJECT_ROOT"

log_info "Starting Docker test execution..."
log_info "Test category: $TEST_CATEGORY"
log_info "Verbose output: $VERBOSE"
log_info "Generate coverage: $GENERATE_COVERAGE"

# Docker Compose command
COMPOSE_CMD="docker-compose -f $TEST_COMPOSE_FILE --profile $TEST_PROFILE"

# Clean environment if requested
if [[ "$CLEAN_ENV" == "true" ]]; then
    log_info "Cleaning test environment..."
    $COMPOSE_CMD down -v --remove-orphans
    docker system prune -f
fi

# Ensure test results directory exists
mkdir -p test-results

# Start test dependencies
log_info "Starting test dependencies..."
$COMPOSE_CMD up -d redis-dev

# Wait for Redis to be ready
log_info "Waiting for Redis to be ready..."
timeout=30
while ! docker-compose -f $TEST_COMPOSE_FILE exec -T redis-dev redis-cli ping &>/dev/null; do
    if [[ $timeout -le 0 ]]; then
        log_error "Redis failed to start within timeout"
        exit 1
    fi
    sleep 1
    ((timeout--))
done

log_success "Redis is ready"

# Build test image
log_info "Building test image..."
if ! $COMPOSE_CMD build test-runner; then
    log_error "Failed to build test image"
    exit 1
fi

# Prepare test command
TEST_CMD="dotnet test tests/Automation.Integration.Tests/Automation.Integration.Tests.csproj"

# Add test category filter
if [[ "$TEST_CATEGORY" != "All" ]]; then
    TEST_CMD="$TEST_CMD --filter Category=$TEST_CATEGORY"
fi

# Add verbose output
if [[ "$VERBOSE" == "true" ]]; then
    TEST_CMD="$TEST_CMD --logger console;verbosity=detailed"
fi

# Add coverage collection
if [[ "$GENERATE_COVERAGE" == "true" ]]; then
    TEST_CMD="$TEST_CMD --collect:\"XPlat Code Coverage\" --results-directory /test-results"
fi

# Add test results output
TEST_CMD="$TEST_CMD --logger trx;LogFileName=test-results.trx --results-directory /test-results"

# Run tests
log_info "Running tests..."
log_info "Command: $TEST_CMD"

if $COMPOSE_CMD run --rm test-runner bash -c "$TEST_CMD"; then
    log_success "Tests completed successfully!"
    TEST_EXIT_CODE=0
else
    log_error "Tests failed!"
    TEST_EXIT_CODE=1
fi

# Copy test results
log_info "Copying test results..."
if [[ -d "test-results" ]]; then
    # Test results are already mounted to ./test-results
    log_success "Test results available in ./test-results/"
    
    # Show test summary
    if [[ -f "test-results/test-results.trx" ]]; then
        log_info "Test results summary:"
        # Parse TRX file for basic info (simplified)
        if command -v xmllint &> /dev/null; then
            xmllint --xpath "//TestRun/@*" test-results/test-results.trx 2>/dev/null || true
        fi
    fi
    
    # Generate coverage report if requested
    if [[ "$GENERATE_COVERAGE" == "true" ]] && command -v reportgenerator &> /dev/null; then
        log_info "Generating coverage report..."
        reportgenerator \
            -reports:"test-results/*/coverage.cobertura.xml" \
            -targetdir:"test-results/coverage-report" \
            -reporttypes:"Html;Badges" || log_warning "Failed to generate coverage report"
        
        if [[ -f "test-results/coverage-report/index.html" ]]; then
            log_success "Coverage report generated: test-results/coverage-report/index.html"
        fi
    fi
fi

# Show container logs if tests failed
if [[ $TEST_EXIT_CODE -ne 0 ]]; then
    log_info "Showing container logs for debugging..."
    $COMPOSE_CMD logs redis-dev
fi

# Cleanup containers unless --keep is specified
if [[ "$KEEP_CONTAINERS" != "true" ]]; then
    log_info "Cleaning up test containers..."
    $COMPOSE_CMD down
else
    log_info "Keeping test containers running (--keep specified)"
    log_info "To stop containers manually: docker-compose -f $TEST_COMPOSE_FILE --profile $TEST_PROFILE down"
fi

# Final status
if [[ $TEST_EXIT_CODE -eq 0 ]]; then
    log_success "All tests passed!"
    log_info "Test results: ./test-results/"
    if [[ "$GENERATE_COVERAGE" == "true" ]]; then
        log_info "Coverage report: ./test-results/coverage-report/index.html"
    fi
else
    log_error "Some tests failed!"
    log_info "Check test results in ./test-results/ for details"
fi

exit $TEST_EXIT_CODE
