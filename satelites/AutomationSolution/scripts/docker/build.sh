#!/bin/bash
# =============================================================================
# Docker Build Script for Automation Solution
# Builds and tags Docker images for different environments
# =============================================================================

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
IMAGE_NAME="automation-solution/worker"
REGISTRY="${DOCKER_REGISTRY:-}"
VERSION="${VERSION:-$(date +%Y%m%d-%H%M%S)}"
BUILD_TARGET="${BUILD_TARGET:-runtime}"
DOCKERFILE="${DOCKERFILE:-Dockerfile.worker}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
Docker Build Script for Automation Solution

Usage: $0 [OPTIONS]

Options:
    -h, --help              Show this help message
    -t, --target TARGET     Build target (runtime, development, testing) [default: runtime]
    -v, --version VERSION   Image version tag [default: current timestamp]
    -r, --registry REGISTRY Docker registry URL
    -f, --dockerfile FILE   Dockerfile to use [default: Dockerfile.worker]
    -p, --push              Push image to registry after build
    -c, --clean             Clean build (no cache)
    --minimal               Use minimal Dockerfile (no browser dependencies)

Examples:
    $0                                          # Build production image
    $0 -t development                           # Build development image
    $0 -v 1.0.0 -p                            # Build and push version 1.0.0
    $0 --minimal -v latest                      # Build minimal image
    $0 -t testing -c                           # Clean build for testing

EOF
}

# Parse command line arguments
PUSH_IMAGE=false
CLEAN_BUILD=false
USE_MINIMAL=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -t|--target)
            BUILD_TARGET="$2"
            shift 2
            ;;
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        -f|--dockerfile)
            DOCKERFILE="$2"
            shift 2
            ;;
        -p|--push)
            PUSH_IMAGE=true
            shift
            ;;
        -c|--clean)
            CLEAN_BUILD=true
            shift
            ;;
        --minimal)
            USE_MINIMAL=true
            DOCKERFILE="Dockerfile.worker.minimal"
            shift
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate build target
case $BUILD_TARGET in
    runtime|development|testing)
        ;;
    *)
        log_error "Invalid build target: $BUILD_TARGET"
        log_error "Valid targets: runtime, development, testing"
        exit 1
        ;;
esac

# Set image tags
if [[ -n "$REGISTRY" ]]; then
    FULL_IMAGE_NAME="$REGISTRY/$IMAGE_NAME"
else
    FULL_IMAGE_NAME="$IMAGE_NAME"
fi

IMAGE_TAG="$FULL_IMAGE_NAME:$VERSION"
LATEST_TAG="$FULL_IMAGE_NAME:latest"

# Change to project root
cd "$PROJECT_ROOT"

log_info "Starting Docker build process..."
log_info "Project root: $PROJECT_ROOT"
log_info "Dockerfile: $DOCKERFILE"
log_info "Build target: $BUILD_TARGET"
log_info "Image tag: $IMAGE_TAG"
log_info "Registry: ${REGISTRY:-'local'}"

# Validate Dockerfile exists
if [[ ! -f "$DOCKERFILE" ]]; then
    log_error "Dockerfile not found: $DOCKERFILE"
    exit 1
fi

# Build Docker image
log_info "Building Docker image..."

BUILD_ARGS=(
    "--file" "$DOCKERFILE"
    "--target" "$BUILD_TARGET"
    "--tag" "$IMAGE_TAG"
    "--tag" "$LATEST_TAG"
    "--build-arg" "BUILD_CONFIGURATION=Release"
    "--build-arg" "VERSION=$VERSION"
)

if [[ "$CLEAN_BUILD" == "true" ]]; then
    BUILD_ARGS+=("--no-cache")
    log_info "Clean build enabled (no cache)"
fi

# Add platform for multi-arch builds if needed
if [[ "${DOCKER_BUILDX:-false}" == "true" ]]; then
    BUILD_ARGS+=("--platform" "linux/amd64,linux/arm64")
fi

# Execute build
if docker build "${BUILD_ARGS[@]}" .; then
    log_success "Docker image built successfully"
    log_success "Image: $IMAGE_TAG"
else
    log_error "Docker build failed"
    exit 1
fi

# Show image information
log_info "Image information:"
docker images "$FULL_IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

# Push image if requested
if [[ "$PUSH_IMAGE" == "true" ]]; then
    if [[ -z "$REGISTRY" ]]; then
        log_error "Cannot push image: no registry specified"
        exit 1
    fi

    log_info "Pushing image to registry..."
    
    if docker push "$IMAGE_TAG" && docker push "$LATEST_TAG"; then
        log_success "Image pushed successfully"
        log_success "Registry: $REGISTRY"
        log_success "Tags: $VERSION, latest"
    else
        log_error "Failed to push image"
        exit 1
    fi
fi

# Security scan (if trivy is available)
if command -v trivy &> /dev/null; then
    log_info "Running security scan..."
    trivy image --exit-code 0 --severity HIGH,CRITICAL "$IMAGE_TAG" || log_warning "Security scan found issues"
fi

log_success "Build process completed successfully!"
log_info "Next steps:"
log_info "  - Test the image: docker run --rm -p 8080:8080 $IMAGE_TAG"
log_info "  - Deploy with compose: docker-compose -f docker-compose.production.yml up"
log_info "  - Check logs: docker logs <container-name>"
