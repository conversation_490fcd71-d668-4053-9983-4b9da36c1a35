// PromptSeeder.fsx
// Seeder script to populate initial Vision AI prompts into the SQLite database.
// Run with:  dotnet fsi scripts/PromptSeeder.fsx

#r "nuget: Microsoft.EntityFrameworkCore, 8.0.2"
#r "nuget: Microsoft.EntityFrameworkCore.Sqlite, 8.0.2"

#I "../src/Automation.Prompts/bin/Debug/net8.0"
#r "Automation.Prompts.dll"

open System
open System.Threading.Tasks
open Automation.Prompts.PromptStore

let prompts : (string * string) list =
    [
        "vision.element_detection", "You are a vision AI that detects UI elements in screenshots. Analyze the image and identify clickable elements like buttons, links, inputs, and other interactive components. Return a JSON response with element details including type, description, bounding box coordinates, and confidence score.";
        "vision.ocr", "You are a vision AI that extracts text from images. Analyze the image and extract all visible text, preserving layout and structure. Return extracted text with positional information.";
        "vision.selector_generation", "You are a vision AI that helps generate CSS selectors for UI elements. Find the element described as '{target}' in the screenshot and suggest the best CSS selector or XPath to locate it. Also provide click coordinates if the element is clickable.";
        "vision.layout_analysis", "You are a vision AI that analyzes UI layout. Examine the screenshot and describe the overall layout structure, identify different sections, and understand the hierarchy of elements.";
        "vision.content_extraction", "You are a vision AI that extracts structured content from screenshots. Analyze the image and extract meaningful content like forms, tables, lists, and other structured data."
    ]

let run () = task {
    for (id, text) in prompts do
        do! upsert id text
        printfn $"Seeded %s{id}"
}

run().GetAwaiter().GetResult()
